import { Injectable } from '@nestjs/common';

// Типы для условий поиска Prisma
export type SearchCondition<T> = {
  OR: Array<{
    [K in keyof T]?: {
      contains: string;
      mode: 'insensitive';
    };
  }>;
};

// Типы для условий диапазона дат
export type DateRangeCondition<T> = {
  [K in keyof T]?: {
    gte?: Date;
    lte?: Date;
  };
};

@Injectable()
export class QueryUtil {
  /**
   * Создает условия для поиска по строке в нескольких полях
   * @param searchTerm - строка для поиска
   * @param fields - массив полей модели, в которых нужно искать
   * @returns условие для Prisma where с типизацией
   */
  createSearchQuery<T>(
    searchTerm: string,
    fields: Array<keyof T & string>,
  ): SearchCondition<T> | Record<string, never> {
    if (!searchTerm || !fields.length) return {};

    return {
      OR: fields.map((field) => ({
        [field]: {
          contains: searchTerm,
          mode: 'insensitive',
        },
      })) as Array<{
        [K in keyof T]?: {
          contains: string;
          mode: 'insensitive';
        };
      }>,
    };
  }

  /**
   * Обрабатывает диапазоны дат для фильтрации
   * @param field - поле модели для фильтрации по дате
   * @param startDate - начальная дата диапазона
   * @param endDate - конечная дата диапазона
   * @returns условие для Prisma where с типизацией
   */
  createDateRangeQuery<T>(
    field: keyof T & string,
    startDate?: Date,
    endDate?: Date,
  ): DateRangeCondition<T> | Record<string, never> {
    if (!startDate && !endDate) return {};

    const dateFilter: {
      gte?: Date;
      lte?: Date;
    } = {};

    if (startDate) {
      dateFilter.gte = startDate;
    }

    if (endDate) {
      dateFilter.lte = endDate;
    }

    return {
      [field]: dateFilter,
    } as DateRangeCondition<T>;
  }

  /**
   * Создает условия для поиска по нескольким полям с разными значениями
   * @param fieldValues - объект, где ключи - имена полей, значения - искомые значения
   * @returns условие для Prisma where с типизацией
   */
  createMultiFieldQuery<T>(
    fieldValues: Partial<Record<keyof T & string, any>>,
  ): Partial<Record<keyof T & string, any>> | Record<string, never> {
    if (!fieldValues || Object.keys(fieldValues).length === 0) return {};

    const filteredQuery = Object.entries(fieldValues)
      .filter(([_, value]) => value !== null && value !== undefined)
      .reduce(
        (acc, [field, value]) => {
          acc[field] = value;
          return acc;
        },
        {} as Record<string, any>,
      );

    return filteredQuery as Partial<Record<keyof T & string, any>>;
  }
}
