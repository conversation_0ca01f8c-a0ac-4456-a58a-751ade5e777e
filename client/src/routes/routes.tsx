import {
	Dashboard,
	Login,
	OrganizationDetails,
	Organizations,
	OrganizationPositionTypes,
	OrganizationTypes,
	RecipientAsnwerType,
	Taskstates,
	Tasktypes,
	UnderControlOrganizations,
	Users,
	UserWorkingScheduleDay,
	TaskCreate,
	TaskOutgoing,
	TaskIncomming,
	UserDetails,
	TaskDetails,
	SubTaskCreate,
	Employee,
	EmployeeDetails,
	Profile,
	TaskRecipient,
	Attendance,
	Vacancies,
	Archives,
	AttendancePosition,
	SectionStatsVacancyTable,
	SectionStatsDisabledTable,
	SectionStatsEmployeeTable,
	SectionStatsInAreaTable,
	SectionStatsLateAttendanceTable,
	SectionStatsNoAttendanceTable,
	SectionStatsOutAreaTable,
	SectionStatsPatientsTable,
	SectionStatsPositionTable,
	SectionStatsSectionTable,
	SectionStatsVacationTable,
	MapLocationUser,
	Sectors,
	TaskController,
	Infraction,
	InfractionDetails
} from './lazy-pages'
import { Layout } from '@/components/layout/page-layout'
import AddAttendancePosition from '@/pages/attendance/add-attendance-position'
import RequireAuth from '@/pages/auth/ui/require-auth'
import SectionStatsShortVacationTable from '@/pages/dashboard-details/section-stats-short-vacation-table'
import FaceIdTable from '@/pages/face-id/face-id'
import Map from '@/pages/map/map'
import ProfileDetails from '@/pages/settings/profile/ui/profile-details'
import TaskControllerDetails from '@/pages/task-controller-details/task-controller-details'
import TaskControllerRecipient from '@/pages/task-controller-recipient/task-controller-recipient'
import { RequireToBeAdmin } from '@/shared/guards/requireToBeAdmin'
import { Suspense } from '@/shared/ui/suspense'
import { createBrowserRouter, Navigate } from 'react-router-dom'

export const router = createBrowserRouter([
	{
		path: '/auth/login',
		element: (
			<Suspense>
				<Login />
			</Suspense>
		)
	},
	{
		path: '',
		element: <RequireAuth />,
		children: [
			{
				path: '',
				element: <Navigate to='/personal' />
			},
			{
				path: 'personal',
				element: <Layout workspace='personal' />,
				children: [
					{
						path: 'tasks',
						children: [
							{
								path: 'tasks-incomming',
								element: (
									<Suspense>
										<TaskIncomming />
									</Suspense>
								)
							},
							{
								path: 'tasks-controller',
								element: (
									<Suspense>
										<TaskController />
									</Suspense>
								)
							},
							{
								path: 'task-controller/:id',
								element: (
									<Suspense>
										<TaskControllerDetails />
									</Suspense>
								)
							},
							{
								path: 'task-controller/:id/recipients/:recipientId',
								element: (
									<Suspense>
										<TaskControllerRecipient />
									</Suspense>
								)
							},
							{
								path: ':id',
								element: (
									<Suspense>
										<TaskDetails />
									</Suspense>
								)
							}
						]
					},
					{
						path: 'infraction',
						element: (
							<Suspense>
								<Infraction />
							</Suspense>
						)
					},
					{
						path: 'infraction/:id',
						element: (
							<Suspense>
								<InfractionDetails />
							</Suspense>
						)
					},
					{
						path: 'settings',
						children: [
							{
								path: 'profile',
								element: (
									<Suspense>
										<Profile />
									</Suspense>
								)
							},
							{
								path: 'profile/:id',
								element: (
									<Suspense>
										<ProfileDetails />
									</Suspense>
								)
							}
						]
					}
				]
			},
			{
				path: 'workspace',
				element: <Layout workspace='organization' />,
				children: [
					{
						path: 'map',
						element: (
							<Suspense>
								<Map />
							</Suspense>
						)
					},
					{
						path: 'map/location-user/:id',
						element: (
							<Suspense>
								<MapLocationUser />
							</Suspense>
						)
					},
					{
						path: '',
						element: (
							<Suspense>
								<Dashboard />
							</Suspense>
						)
					},
					{
						path: 'dashboard-details',
						children: [
							{
								path: 'vacancy',
								element: (
									<Suspense>
										<SectionStatsVacancyTable />
									</Suspense>
								)
							},
							{
								path: 'disabled',
								element: (
									<Suspense>
										<SectionStatsDisabledTable />
									</Suspense>
								)
							},
							{
								path: 'employee',
								element: (
									<Suspense>
										<SectionStatsEmployeeTable />
									</Suspense>
								)
							},
							{
								path: 'in-area',
								element: (
									<Suspense>
										<SectionStatsInAreaTable />
									</Suspense>
								)
							},
							{
								path: 'late-attendance',
								element: (
									<Suspense>
										<SectionStatsLateAttendanceTable />
									</Suspense>
								)
							},
							{
								path: 'no-attendance',
								element: (
									<Suspense>
										<SectionStatsNoAttendanceTable />
									</Suspense>
								)
							},
							{
								path: 'out-area',
								element: (
									<Suspense>
										<SectionStatsOutAreaTable />
									</Suspense>
								)
							},
							{
								path: 'patients',
								element: (
									<Suspense>
										<SectionStatsPatientsTable />
									</Suspense>
								)
							},
							{
								path: 'position',
								element: (
									<Suspense>
										<SectionStatsPositionTable />
									</Suspense>
								)
							},
							{
								path: 'section',
								element: (
									<Suspense>
										<SectionStatsSectionTable />
									</Suspense>
								)
							},
							{
								path: 'vacation',
								element: (
									<Suspense>
										<SectionStatsVacationTable />
									</Suspense>
								)
							},
							{
								path: 'short-vacation',
								element: (
									<Suspense>
										<SectionStatsShortVacationTable />
									</Suspense>
								)
							}
						]
					},
					{
						path: 'attendance',
						element: (
							<Suspense>
								<Attendance />
							</Suspense>
						)
					},
					{
						path: 'attendance/add-attendance-position',
						element: (
							<Suspense>
								<AddAttendancePosition />
							</Suspense>
						)
					},
					{
						path: 'sectors',
						element: (
							<Suspense>
								<Sectors />
							</Suspense>
						)
					},
					{
						path: 'infraction',
						element: (
							<Suspense>
								<Infraction />
							</Suspense>
						)
					},
					{
						path: 'infraction/:id',
						element: (
							<Suspense>
								<InfractionDetails />
							</Suspense>
						)
					},
					{
						path: 'attendance/position',
						element: (
							<Suspense>
								<AttendancePosition />
							</Suspense>
						)
					},
					{
						path: 'task-create',
						element: (
							<Suspense>
								<TaskCreate />
							</Suspense>
						)
					},
					{
						path: 'tasks',
						children: [
							{
								path: 'tasks-incomming',
								element: (
									<Suspense>
										<TaskIncomming />
									</Suspense>
								)
							},
							{
								path: 'tasks-outgoing',
								element: (
									<Suspense>
										<TaskOutgoing />
									</Suspense>
								)
							},
							{
								path: ':id',
								element: (
									<Suspense>
										<TaskDetails />
									</Suspense>
								)
							},
							{
								path: ':id/create-subtask',
								element: (
									<Suspense>
										<SubTaskCreate />
									</Suspense>
								)
							},
							{
								path: ':id/recipients/:recipientId',
								element: (
									<Suspense>
										<TaskRecipient />
									</Suspense>
								)
							}
						]
					},


					{
						path: 'organizations',
						children: [
							{
								path: '',
								element: (
									<Suspense>
										<Organizations />
									</Suspense>
								)
							},
							{
								path: 'under-control-organizations',
								element: (
									<Suspense>
										<UnderControlOrganizations />
									</Suspense>
								)
							},
							{
								path: ':id',
								element: (
									<Suspense>
										<OrganizationDetails />
									</Suspense>
								)
							}
						]
					},
					{
						path: 'settings',
						children: [
							{
								path: 'face-id',
								element: (
									<Suspense>
										<FaceIdTable />
									</Suspense>
								)
							},
							{
								path: 'employee',
								element: (
									<Suspense>
										<Employee />
									</Suspense>
								)
							},
							{
								path: 'vacancies',
								element: (
									<Suspense>
										<Vacancies />
									</Suspense>
								)
							},
							{
								path: 'employee/:id',
								element: (
									<Suspense>
										<EmployeeDetails />
									</Suspense>
								)
							},
							{
								path: '',
								element: <RequireToBeAdmin />,
								children: [
									{
										path: 'organization-types',
										element: (
											<Suspense>
												<OrganizationTypes />
											</Suspense>
										)
									},
									{
										path: 'organization-position-types',
										element: (
											<Suspense>
												<OrganizationPositionTypes />
											</Suspense>
										)
									},

									{
										path: 'task-types',
										element: (
											<Suspense>
												<Tasktypes />
											</Suspense>
										)
									},
									{
										path: 'task-states',
										element: (
											<Suspense>
												<Taskstates />
											</Suspense>
										)
									},
									{
										path: 'recipient-answer-type',
										element: (
											<Suspense>
												<RecipientAsnwerType />
											</Suspense>
										)
									},
									{
										path: 'users',
										element: (
											<Suspense>
												<Users />
											</Suspense>
										)
									},
									{
										path: 'users/:id',
										element: (
											<Suspense>
												<UserDetails />
											</Suspense>
										)
									},
									{
										path: 'user-working-schedule-day',
										element: (
											<Suspense>
												<UserWorkingScheduleDay />
											</Suspense>
										)
									},
									{
										path: 'archives',
										element: (
											<Suspense>
												<Archives />
											</Suspense>
										)
									}
								]
							}
						]
					}
				]
			}
		]
	},
	{
		path: '*',
		element: <Navigate to='/auth/login' />
	}
])
