import { ForbiddenException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../../common';
import { CreateTaskDto } from '../dto/task.dto';
import { RecipientDto } from '../dto/update-task-recipients.dto';
import { NotificationPayload } from '../../notification/interfaces/notification-type';
import { NotificationType } from '@prisma/client';
import { TelegramEventKeys } from '../../telegram-bot/interfaces/bot.interfaces';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { PrismaClient } from '@prisma/client';
type TransactionClient = Omit<PrismaClient, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'>;

@Injectable()
export class TaskHelperService {
  logger = new Logger(TaskHelperService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async validateTaskTypeAndParent(createTaskDto: CreateTaskDto, tx: TransactionClient = this.prisma) {
    if (createTaskDto.taskTypeId) {
      const existTaskType = await tx.taskType.findUnique({ where: { id: createTaskDto.taskTypeId } });
      if (!existTaskType) {
        this.logger.error(`TaskType with ID "${createTaskDto.taskTypeId}" not found`);
        throw new NotFoundException(`TaskType with ID "${createTaskDto.taskTypeId}" not found`);
      }
    }

    if (createTaskDto.parentId) {
      const existParentTask = await tx.task.findUnique({ where: { id: createTaskDto.parentId } });
      if (!existParentTask) {
        this.logger.error(`Parent Task with ID "${createTaskDto.parentId}" not found`);
        throw new NotFoundException(`Parent Task with ID "${createTaskDto.parentId}" not found`);
      }
    }
  }

  async createRecipientAndNotify({
    recipients,
    createdTask,
    taskId,
    userId,
    organizationId,
    prisma = this.prisma,
  }: {
    recipients: RecipientDto[];
    createdTask: any;
    taskId: string;
    userId: string;
    organizationId: string;
    prisma?: any;
  }) {
    // "all" bo'lsa barcha orglarni olish va recipient yaratish
    if (recipients.length === 1 && recipients.at(0).organizationId === 'all') {
      const allOrganizations = await prisma.organization.findMany({
        where: { id: organizationId },
        select: {
          id: true,
          Children: { select: { id: true } },
          UnderControl: { select: { id: true } },
        },
      });

      const allRecipientOrgs = [
        ...allOrganizations.map((org) => org.id),
        ...allOrganizations.flatMap((org) => org.Children.map((child) => child.id)),
        ...allOrganizations.flatMap((org) => org.UnderControl.map((underControl) => underControl.id)),
      ];

      const recipients = await Promise.all(
        allRecipientOrgs.map(async (orgId) => {
          const eventPayload: NotificationPayload = {
            type: NotificationType.TASK_ASSIGNED,
            message: createdTask.name,
            Content: {
              type: 'task_assigned',
              recipientOrgId: orgId,
              recipientUserId: [],
              id: taskId,
              workspace: 'organization',
            },
            organizationId: orgId,
            userId,
          };
          this.eventEmitter.emit(NotificationType.TASK_ASSIGNED, eventPayload);
          this.eventEmitter.emit(TelegramEventKeys.TELEGRAM_NEW_TASK, {
            organizationId: orgId,
            userId,
          });
          return await prisma.recipient.create({
            data: {
              organizationId: orgId,
              userId: null,
              taskId,
            },
          });
        }),
      );
      return recipients;
    }

    // Oddiy recipientlar uchun
    const organization = await prisma.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new NotFoundException(`Organization with ID "${organizationId}" not found`);
    }

    const createdRecipients = await Promise.all(
      recipients.map(async (recipient) => {
        const existOrganizationUser = await prisma.organization.findFirst({
          where: {
            OR: [
              { Children: { some: { id: recipient.organizationId } } },
              { UnderControl: { some: { id: recipient.organizationId } } },
              { id: recipient.organizationId },
            ],
          },
        });

        if (!existOrganizationUser) {
          throw new NotFoundException(
            `Organization with ID "${recipient.organizationId}" is not a child or under control`,
          );
        }

        if (recipient.userId?.length) {
          this.logger.debug(`Creating recipient for organization ID "${recipient.organizationId}" with users`);
          // Foydalanuvchilar uchun
          const recipientUsers = await Promise.all(
            recipient.userId.map(async (userIdItem) => {
              const existUser = await prisma.user.findUnique({
                include: { Organization: true, MainOrganization: true },
                where: { id: userIdItem },
              });
              if (!existUser) throw new NotFoundException(`User with ID "${userIdItem}" not found`);

              if (organization.id !== recipient.organizationId) {
                this.logger.debug(
                  `Checking if user with ID "${userIdItem}" is a worker of organization "${recipient.organizationId}"`,
                );
                const existCheckUser =
                  existUser.Organization.some((org) => org.id === recipient.organizationId) ||
                  existUser.MainOrganization.id === recipient.organizationId;
                if (!existCheckUser) {
                  throw new ForbiddenException(
                    `User with ID "${userIdItem}" is not a worker of organization "${recipient.organizationId}"`,
                  );
                }
              } else {
                this.logger.debug(
                  `Checking if user with ID "${userIdItem}" is an employee or worker of organization "${recipient.organizationId}"`,
                );
                const existCheckUser = await prisma.user.findFirst({
                  where: {
                    id: userIdItem,
                    OR: [
                      { mainOrganizationId: recipient.organizationId },
                      { Organization: { some: { id: recipient.organizationId } } },
                    ],
                  },
                });
                if (!existCheckUser) {
                  this.logger.error(
                    `User with ID "${userIdItem}" is not an Employee or Worker of organization "${recipient.organizationId}"`,
                  );
                  throw new ForbiddenException(
                    `User with ID "${userIdItem}" is not an Employee or Worker of organization "${recipient.organizationId}"`,
                  );
                }
              }

              const eventPayload: NotificationPayload = {
                type: NotificationType.TASK_ASSIGNED,
                message: createdTask.name,
                Content: {
                  type: 'task_assigned',
                  recipientOrgId: recipient.organizationId,
                  recipientUserId: recipient.userId,
                  id: taskId,
                  workspace: 'personal',
                },
                organizationId: recipient.organizationId,
                userId: userIdItem,
              };
              this.eventEmitter.emit(NotificationType.TASK_ASSIGNED, eventPayload);
              this.eventEmitter.emit(TelegramEventKeys.TELEGRAM_NEW_TASK, {
                organizationId: recipient.organizationId,
                userId: userIdItem,
              });
              return await prisma.recipient.create({
                data: {
                  organizationId: recipient.organizationId,
                  userId: userIdItem !== '' ? userIdItem : null,
                  taskId,
                },
              });
            }),
          );
          return recipientUsers;
        } else {
          // Faqat org uchun
          this.logger.debug(`Creating recipient for organization ID "${recipient.organizationId}" without user`);
          // if (organization.id === recipient.organizationId) {
          //   throw new ForbiddenException(
          //     `Organization with ID "${recipient.organizationId}" is the same as the creator`,
          //   );
          // }
          const eventPayload: NotificationPayload = {
            type: NotificationType.TASK_ASSIGNED,
            message: createdTask.name,
            Content: {
              type: 'task_assigned',
              recipientOrgId: recipient.organizationId,
              recipientUserId: recipient.userId,
              id: taskId,
              workspace: 'organization',
            },
            organizationId: recipient.organizationId,
            userId,
          };
          this.eventEmitter.emit(NotificationType.TASK_ASSIGNED, eventPayload);

          return await prisma.recipient.create({
            data: {
              organizationId: recipient.organizationId,
              userId: null,
              taskId,
            },
          });
        }
      }),
    );
    return createdRecipients;
  }
}
