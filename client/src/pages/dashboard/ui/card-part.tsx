import {
	Users,
	User,
	AlertCircle,
	UserCheck,
	UserRoundPen,
	Building,
	Timer,
	ClockAlert,
	ListChecks,
	AlignJustify
} from 'lucide-react'
import { useDashboardUsersCount } from '@/config/queries/dashboard/get.queries'
import { OrganizationHierarchyStatsType } from '@/config/queries/stats/get.queries'
import StatCard from './stat-card'

interface CardPartProps {
	organizationStats?: OrganizationHierarchyStatsType
}

const CardsPart = ({ organizationStats }: CardPartProps) => {
	const usersStats = useDashboardUsersCount()

	const cardsStatsData = [
		{
			title: "Mas'ullar soni",
			value: usersStats.data?.responsiblesCount,
			icon: (
				<UserRoundPen
					className='text-blue-600'
					size={24}
				/>
			),
			iconBg: 'bg-blue-200'
		},
		{
			title: 'Hodimlar soni',
			value: usersStats.data?.employeesCount,
			icon: (
				<Users
					className='text-blue-600'
					size={24}
				/>
			),
			iconBg: 'bg-blue-200'
		},
		{
			title: 'Ishchilar soni',
			value: organizationStats?.workers?.total,
			icon: (
				<User
					className='text-blue-600'
					size={24}
				/>
			),
			iconBg: 'bg-blue-200'
		},
		{
			title: 'Umumiy tashkilotlar soni',
			value: organizationStats?.organizations?.total,
			icon: (
				<Building
					className='text-blue-600'
					size={24}
				/>
			),
			iconBg: 'bg-blue-200'
		},
		{
			title: 'Quyi tashkilotlar soni',
			value: organizationStats?.organizations?.children?.length,
			icon: (
				<Building
					className='text-blue-600'
					size={24}
				/>
			),
			iconBg: 'bg-blue-200'
		},
		{
			title: 'Kelgan topshiriqlar',
			value: organizationStats?.tasks?.incomplete?.active,
			icon: (
				<AlignJustify
					className='text-blue-600'
					size={24}
				/>
			),
			iconBg: 'bg-blue-200'
		},
		{
			title: 'Berilgan topshiriqlar',
			value: organizationStats?.tasks?.incomplete?.expired,
			icon: (
				<ListChecks
					className='text-yellow-600'
					size={24}
				/>
			),
			iconBg: 'bg-yellow-200'
		},
		{
			title: 'Bugun qatnashganlar',
			value: organizationStats?.attendance?.onTime,
			icon: (
				<Timer
					className='text-green-600'
					size={24}
				/>
			),
			iconBg: 'bg-green-200'
		},
		{
			title: 'Kechga qolganlar',
			value: organizationStats?.attendance?.late,
			icon: (
				<ClockAlert
					className='text-red-600'
					size={24}
				/>
			),
			iconBg: 'bg-red-200'
		},
		{
			title: 'Kelmaganlar',
			value: organizationStats?.attendance?.absent || 0,
			icon: (
				<User
					className='text-red-600'
					size={24}
				/>
			),
			iconBg: 'bg-red-200'
		},
		{
			title: 'Bajarilgan topshiriqlar',
			value: organizationStats?.tasks?.completed?.total || 0,
			icon: (
				<ListChecks
					className='text-green-600'
					size={24}
				/>
			),
			iconBg: 'bg-green-200'
		},
		{
			title: 'Bajarilmagan topshiriqlar',
			value: organizationStats?.tasks?.incomplete?.total || 0,
			icon: (
				<AlertCircle
					className='text-yellow-600'
					size={24}
				/>
			),
			iconBg: 'bg-yellow-200'
		},
		{
			title: 'Jami topshiriqlar',
			value: organizationStats?.tasks?.total || 0,
			icon: (
				<UserCheck
					className='text-blue-600'
					size={24}
				/>
			),
			iconBg: 'bg-blue-200'
		}
	]
	return (
		<>
			<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6'>
				{cardsStatsData.map((stat, index) => (
					<StatCard
						key={index}
						title={stat.title}
						value={stat.value ?? 0}
						icon={stat.icon}
					/>
				))}
			</div>
		</>
	)
}

export default CardsPart
