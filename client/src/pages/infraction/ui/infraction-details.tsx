import { Card, Descriptions, Avatar, Typography, Empty, Button } from 'antd'
import { useParams, useNavigate, useLocation, Link } from 'react-router-dom'
import { ArrowLeft, Plus, FileText } from 'lucide-react'
import dayjs from 'dayjs'
import { DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'
import { useGetInfractionById, useGetInfractionByIdWorkspace } from '@/config/queries/infraction/get-all.queries'
import { useInfractionReasonModalStore } from '@/pages/infraction/store/infraction-reason-modal-store'
import { InfractionReasonModal } from './infraction-reason-modal'
import { getFileUrl } from '@/shared/utils/getFileUrl'

const { Title, Text } = Typography

const InfractionDetails = () => {
	const { id } = useParams<{ id: string }>()
	const navigate = useNavigate()
	const location = useLocation()
	const { onOpen } = useInfractionReasonModalStore()

	// Check if we're in personal workspace
	const isPersonalWorkspace = location.pathname.includes('/personal')

	const { data: infraction, error } = isPersonalWorkspace
		? useGetInfractionById(id!)
		: useGetInfractionByIdWorkspace(id!)



	if (error || !infraction) {
		return (
			<div className="p-6">
				<Card>
					<Empty description="Qoidabuzarlik ma'lumotlari topilmadi" />
				</Card>
			</div>
		)
	}

	return (
		<div className="p-6">
			<Card>
				<div className="mb-4 flex items-center justify-between">
					<div className="flex items-center gap-3">
						<ArrowLeft
							className="cursor-pointer hover:text-blue-500"
							onClick={() => navigate(-1)}
							size={20}
						/>
						<Title level={3} className="mb-0">
							Qoidabuzarlik tafsilotlari
						</Title>
					</div>
					{isPersonalWorkspace && (
						<Button
							type="primary"
							icon={<Plus size={16} />}
							onClick={() => onOpen(id!)}
						>
							Sabab qo'shish
						</Button>
					)}
				</div>

				<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
					{/* User Information */}
					<Card title="Foydalanuvchi ma'lumotlari" size="small">
						<div className="flex items-center gap-3 mb-4">
							<Avatar
								size={64}
								src={infraction.user?.avatar?.path}
							>
								{infraction.user?.fullName?.charAt(0)}
							</Avatar>
							<div>
								<Title level={4} className="mb-1">
									{infraction.user?.fullName}
								</Title>
								<Text type="secondary">{infraction.user?.phone}</Text>
							</div>
						</div>

						<Descriptions column={1} size="small">
							<Descriptions.Item label="Tashkilot">
								{infraction.user?.Organization?.[0]?.name || '-'}
							</Descriptions.Item>
							<Descriptions.Item label="Lavozim">
								{infraction.user?.position?.name || '-'}
							</Descriptions.Item>
						</Descriptions>
					</Card>

					{/* Infraction Information */}
					<Card title="Qoidabuzarlik ma'lumotlari" size="small">
						<Descriptions column={1} size="small">
							<Descriptions.Item label="Nomi">
								{infraction.name || '-'}
							</Descriptions.Item>
							<Descriptions.Item label="Izoh">
								{infraction.description || '-'}
							</Descriptions.Item>
							<Descriptions.Item label="Qoidabuzarlik sanasi">
								{dayjs(infraction.infractionDate).tz(DEFAULT_TIMEZONE).format('DD.MM.YYYY HH:mm')}
							</Descriptions.Item>
							
						</Descriptions>
					</Card>
				</div>

				{/* Infraction Reasons */}
				{infraction.InfractionReason && infraction.InfractionReason.length > 0 && (
					<Card title="Qoidabuzarlik sabablari" className="mt-6" size="small">
						<div className="space-y-3">
							{infraction.InfractionReason.map((reason, index) => (
								<Card key={reason.id} size="small" className="border border-gray-200">
									<div className="space-y-3">
										<div className="flex justify-between items-start">
											<div className="flex-1">
												<Text strong className="text-base">Sabab #{index + 1}</Text>
												{reason.name && (
													<div className="mt-2">
														<Text strong className="text-gray-800">Nomi: </Text>
														<Text>{reason.name}</Text>
													</div>
												)}
												{reason.description && (
													<div className="mt-2">
														<Text strong>Izoh: </Text>
														<Text>{reason.description}</Text>
													</div>
												)}
												<div className="mt-2">
													<Text type="secondary" className="text-xs">
														{dayjs(reason.createdAt).tz(DEFAULT_TIMEZONE).format('DD.MM.YYYY HH:mm')}
													</Text>
												</div>
											</div>
										</div>

										{/* File Display */}
										{reason.file && (
											<div className="border-t pt-3">
												<div className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
													<div className="flex items-center space-x-3">
														<div className="flex-shrink-0">
															<FileText className="w-8 h-8 text-blue-500" />
														</div>
														<div>
															<div className="text-sm font-medium text-gray-900">
																Biriktirilgan fayl
															</div>
															<div className="text-xs text-gray-500">
																Hujjat
															</div>
														</div>
													</div>
													<Link
														to={getFileUrl(reason.file.path)}
														target="_blank"
														className="px-3 py-1.5 bg-blue-100 text-blue-600 rounded text-sm hover:bg-blue-200 transition-colors"
													>
														Yuklab olish
													</Link>
												</div>
											</div>
										)}
									</div>
								</Card>
							))}
						</div>
					</Card>
				)}
			</Card>
			<InfractionReasonModal />
		</div>
	)
}

export default InfractionDetails