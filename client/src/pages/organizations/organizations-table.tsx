import {
	TOrganization,
	useGetAllOrganizations
} from '@/config/queries/organizations/get-all.queries'
import { EditOutlined, DeleteOutlined } from '@ant-design/icons'
import { Button, Space, Table, Popconfirm } from 'antd'
import { Eye } from 'lucide-react'
import { Link } from 'react-router-dom'
import { useOrganizationsModalStore } from './utils/organizations-modal-store'
import { OrganizationModal } from './ui/organization-modal'
import { usePermissions } from '@/shared/guards/usePermissions'
import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { useState } from 'react'
import { Search } from '@/shared/components/search/search'
import { useDeActiveOrganization } from '@/config/queries/organizations/update.queries'

export const OrganizationsTable = () => {
	const { hasPermission } = usePermissions()
	const { onOpen } = useOrganizationsModalStore()
	const { data: user } = useAuthMe()
	const [currentPage, setCurrentPage] = useState(1)
	const [pageSize, setPageSize] = useState(10)
	const { data: organizations, hasNextPage, fetchNextPage, setPage } = useGetAllOrganizations()
	const { mutateAsync: deactivateOrganization, isPending: isDeleting } = useDeActiveOrganization()

	const handleDelete = async (organizationId: string) => {
		try {
			await deactivateOrganization({
				id: organizationId,
				data: {} // Empty data object as required by the API
			})
		} catch (error) {
			console.error('Error deleting organization:', error)
		}
	}

	const columns = [
		{
			key: '#',
			title: '#',
			render: (_: any, _rec: any, index: number) => formatTableIndex(currentPage, pageSize, index)
		},
		{
			key: 'name',
			title: 'Nomi',
			dataIndex: 'name',
			render: (_: any, rec: { id: any; name: string }) => (
				<Link to={`/workspace/organizations/${rec.id}`}>{rec.name}</Link>
			)
		},

		{
			key: 'address',
			title: 'Manzili',
			dataIndex: 'address'
		},
		{
			key: 'description',
			title: 'Izoh',
			dataIndex: 'description'
		},
		{
			key: 'phone',
			title: 'Telefon raqami',
			dataIndex: 'phone'
		},
		{
			key: 'user-count',
			title: 'Foydalanuvchilar soni',
			dataIndex: 'userCount'
		},
		{
			key: 'actions',
			title: 'Amallar',
			render: (record: TOrganization | undefined) => (
				<Space>
					<Link to={`/workspace/organizations/${record?.id}`}>
						<Eye />
					</Link>
					{user && hasPermission(user.role, ['update:*', 'delete:*']) && (
						<>
							<Button
								type='primary'
								icon={<EditOutlined />}
								onClick={() => onOpen(record)}
							/>
							<Popconfirm
								title="Tashkilotni o'chirish"
								description="Haqiqatan ham bu tashkilotni o'chirmoqchimisiz?"
								onConfirm={() => handleDelete(record?.id || '')}
								okText='Ha'
								cancelText="Yo'q"
								okButtonProps={{ loading: isDeleting }}>
								<Button
									type='primary'
									danger
									icon={<DeleteOutlined />}
									loading={isDeleting}
								/>
							</Popconfirm>
						</>
					)}
				</Space>
			)
		}
	]

	const tableData = organizations?.pages?.flatMap(page => page.data) || []

	return (
		<Table
			title={() => (
				<div className='flex items-center justify-between'>
					<Search />
					<OrganizationModal />
				</div>
			)}
			size='small'
			pagination={{
				total: organizations?.pages?.[0]?.meta?.total || 0,
				pageSize: pageSize,
				current: currentPage,
				showSizeChanger: true,
				pageSizeOptions: [5, 10, 20, 50, 100],
				onChange: (page, size) => {
					setCurrentPage(page)
					setPageSize(size)
					setPage(page)
					if (
						Math.ceil((page * size) / size) > (organizations?.pages?.length || 0) &&
						hasNextPage
					) {
						fetchNextPage()
					}
				}
			}}
			scroll={{ x: 'max-content' }}
			dataSource={tableData}
			rowKey={(record: any) => record.id}
			columns={columns}
		/>
	)
}
