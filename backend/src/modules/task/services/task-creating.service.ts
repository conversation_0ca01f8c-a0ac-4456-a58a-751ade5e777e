import { BadRequestException, ForbiddenException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../../common';
import { TaskHelperService } from './task-helper.service';
import { CreateTaskDto } from '../dto/task.dto';
import { FileService } from '../../file/file.service';
import { UserData } from '@prisma/client';

@Injectable()
export class TaskCreatingService {
  private readonly logger = new Logger(TaskCreatingService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly taskHelperService: TaskHelperService,
    private readonly fileService: FileService,
  ) {}

  // async create(createTaskDto: CreateTaskDto, userId: string, organizationId: string) {
  //   const { recipients, ...newTask } = createTaskDto;

  //   return this.prisma.$transaction(async (prisma) => {
  //     const taskState = await prisma.taskState.findFirst({
  //       orderBy: { order: 'asc' },
  //     });
  //     if (!taskState) throw new NotFoundException(`TaskState not found`);

  //     await this.taskHelperService.validateTaskTypeAndParent(createTaskDto, prisma);

  //     let files: { id: string }[] = [];
  //     if (createTaskDto.files?.length) {
  //       files = await this.fileService.moveFileFromTempToSaved(createTaskDto.files);
  //     }

  //     const createdTask = await prisma.task.create({
  //       data: {
  //         ...newTask,
  //         files: { connect: files },
  //         taskStateId: taskState.id,
  //         createdById: userId,
  //         createdByOrganizationId: organizationId,
  //       },
  //     });

  //     const taskId = createdTask.id;

  //     const allowedOrganizations = await prisma.organization.findMany({
  //       where: {
  //         OR: [
  //           { id: organizationId },
  //           { parentId: organizationId },
  //           // { UnderControl: { some: { id: organizationId } } },
  //           { controlledById: organizationId },
  //         ],
  //         status: 'ACTIVE',
  //       },
  //       select: { id: true },
  //     });
  //     const allowedOrgIds = allowedOrganizations.map((o) => o.id);

  //     const finalRecipients = await Promise.all(
  //       recipients.map(async (recipient) => {
  //         if (recipient.userId?.length) {
  //           if (!recipient.organizationId || !allowedOrgIds.includes(recipient.organizationId)) {
  //             throw new ForbiddenException(`Organization "${recipient.organizationId}" is not allowed`);
  //           }

  //           return {
  //             userId: recipient.userId,
  //             organizationId: recipient.organizationId,
  //           };
  //         }

  //         if (recipient.positionId) {
  //           const position = await prisma.organizationTypePosition.findUnique({
  //             where: { id: recipient.positionId },
  //           });
  //           if (!position) {
  //             throw new NotFoundException(`Position "${recipient.positionId}" not found`);
  //           }

  //           const users = await prisma.user.findMany({
  //             where: {
  //               mainOrganizationId: { in: allowedOrgIds },
  //               positionId: recipient.positionId,
  //               status: 'ACTIVE',
  //             },
  //             select: { id: true, mainOrganizationId: true },
  //           });

  //           const groupedByOrg = users.reduce(
  //             (acc, user) => {
  //               if (!acc[user.mainOrganizationId]) acc[user.mainOrganizationId] = [];
  //               acc[user.mainOrganizationId].push(user.id);
  //               return acc;
  //             },
  //             {} as Record<string, string[]>,
  //           );

  //           return Object.entries(groupedByOrg).map(([orgId, userIds]) => ({
  //             userId: userIds,
  //             organizationId: orgId,
  //           }));
  //         }

  //         if (recipient.organizationId) {
  //           if (!allowedOrgIds.includes(recipient.organizationId)) {
  //             throw new ForbiddenException(`Organization "${recipient.organizationId}" is not allowed`);
  //           }

  //           const users = await prisma.user.findMany({
  //             where: {
  //               mainOrganizationId: recipient.organizationId,
  //               status: 'ACTIVE',
  //             },
  //             select: { id: true },
  //           });

  //           const userIds = users.map((u) => u.id);

  //           return {
  //             userId: userIds,
  //             organizationId: recipient.organizationId,
  //           };
  //         }

  //         throw new BadRequestException('Recipient must include userId, positionId, or organizationId');
  //       }),
  //     );

  //     const flatRecipients = finalRecipients.flat();

  //     await this.taskHelperService.createRecipientAndNotify({
  //       recipients: flatRecipients,
  //       createdTask,
  //       taskId,
  //       userId,
  //       organizationId,
  //       prisma,
  //     });

  //     this.logger.log(`Task with ID "${taskId}" created successfully`);
  //     return createdTask;
  //   });
  // }

  async create(createTaskDto: CreateTaskDto, userId: string, organizationId: string) {
    const { recipients, controllerId, ...newTask } = createTaskDto;

    return this.prisma.$transaction(async (prisma) => {
      const taskState = await prisma.taskState.findFirst({
        orderBy: { order: 'asc' },
      });
      if (!taskState) throw new NotFoundException(`TaskState not found`);

      await this.taskHelperService.validateTaskTypeAndParent(createTaskDto, prisma);

      // Validate controller if provided
      let validatedControllerId: string | undefined;
      if (controllerId) {
        validatedControllerId = await this.validateController(controllerId, organizationId, prisma);
      }

      let files: { id: string }[] = [];
      if (createTaskDto.files?.length) {
        files = await this.fileService.moveFileFromTempToSaved(createTaskDto.files);
      }

      const createdTask = await prisma.task.create({
        data: {
          ...newTask,
          files: { connect: files },
          taskStateId: taskState.id,
          createdById: userId,
          createdByOrganizationId: organizationId,
          userId: validatedControllerId, // Assign controller to task
        },
      });

      const taskId = createdTask.id;

      const allowedOrganizations = await prisma.organization.findMany({
        where: {
          OR: [{ id: organizationId }, { parentId: organizationId }, { controlledById: organizationId }],
          status: 'ACTIVE',
        },
        select: { id: true },
      });
      const allowedOrgIds = allowedOrganizations.map((o) => o.id);

      const finalRecipients = await Promise.all(
        recipients.map(async (recipient) => {
          if (recipient.userId?.length) {
            if (!recipient.organizationId || !allowedOrgIds.includes(recipient.organizationId)) {
              throw new ForbiddenException(`Organization "${recipient.organizationId}" is not allowed`);
            }

            return {
              userId: recipient.userId,
              organizationId: recipient.organizationId,
            };
          }

          if (recipient.positionId) {
            const position = await prisma.organizationTypePosition.findUnique({
              where: { id: recipient.positionId },
            });
            if (!position) {
              throw new NotFoundException(`Position "${recipient.positionId}" not found`);
            }

            const users = await prisma.user.findMany({
              where: {
                mainOrganizationId: { in: allowedOrgIds },
                positionId: recipient.positionId,
                status: 'ACTIVE',
              },
              select: { id: true, mainOrganizationId: true },
            });

            const groupedByOrg = users.reduce(
              (acc, user) => {
                if (!acc[user.mainOrganizationId]) acc[user.mainOrganizationId] = [];
                acc[user.mainOrganizationId].push(user.id);
                return acc;
              },
              {} as Record<string, string[]>,
            );

            return Object.entries(groupedByOrg).map(([orgId, userIds]) => ({
              userId: userIds,
              organizationId: orgId,
            }));
          }

          if (recipient.organizationId) {
            if (!allowedOrgIds.includes(recipient.organizationId)) {
              throw new ForbiddenException(`Organization "${recipient.organizationId}" is not allowed`);
            }

            const users = await prisma.user.findMany({
              where: {
                mainOrganizationId: recipient.organizationId,
                status: 'ACTIVE',
              },
              select: { id: true },
            });

            const userIds = users.map((u) => u.id);

            return {
              userId: userIds,
              organizationId: recipient.organizationId,
            };
          }

          throw new BadRequestException('Recipient must include userId, positionId, or organizationId');
        }),
      );

      const flatRecipients = finalRecipients.flat();

      await this.taskHelperService.createRecipientAndNotify({
        recipients: flatRecipients,
        createdTask,
        taskId,
        userId,
        organizationId,
        prisma,
      });

      this.logger.log(
        `Task with ID "${taskId}" created successfully${validatedControllerId ? ` with controller "${validatedControllerId}"` : ''}`,
      );
      return createdTask;
    });
  }

  private async validateController(controllerId: string, organizationId: string, prisma: any): Promise<string> {
    const controller = await prisma.user.findUnique({
      where: {
        id: controllerId,
        status: 'ACTIVE',
      },
      include: {
        Organization: {
          select: { id: true },
        },
      },
    });

    if (!controller) {
      throw new NotFoundException(`Controller with ID "${controllerId}" not found or inactive`);
    }

    const allowedOrganizations = await prisma.organization.findMany({
      where: {
        OR: [{ id: organizationId }, { parentId: organizationId }, { controlledById: organizationId }],
        status: 'ACTIVE',
      },
      select: { id: true },
    });
    const allowedOrgIds = allowedOrganizations.map((o) => o.id);

    const isWorkerInAllowedOrg = controller.Organization.some((org) => allowedOrgIds.includes(org.id));

    if (!isWorkerInAllowedOrg) {
      throw new ForbiddenException(`Controller "${controllerId}" is not a worker in any allowed organization`);
    }

    return controllerId;
  }
}
