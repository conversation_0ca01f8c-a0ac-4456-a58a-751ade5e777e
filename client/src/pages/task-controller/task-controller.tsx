import { Table, Typography, Card, Badge } from 'antd'
import { useGetTaskController } from '@/config/queries/task/get-task-controller'
import { useNavigate } from 'react-router-dom'
import dayjs from 'dayjs'
import { DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'
import { useAuthMe } from '@/config/queries/auth/verify.queries'


const { Title } = Typography

// Setup dayjs timezone
dayjs.tz.setDefault(DEFAULT_TIMEZONE)

const TaskController = () => {
  const navigate = useNavigate()

  // Get current user from auth query
  const { data: currentUser } = useAuthMe()

  const { data: tasks, } = useGetTaskController(currentUser?.id || '')

  // Handle task selection - navigate to personal task controller details page
  const handleTaskSelect = (taskId: string) => {
    navigate(`/personal/tasks/task-controller/${taskId}`)
  }

  const columns = [
    {
      title: 'ID',
      render: (_: any, __: any, index: number) => index + 1,
      width: 50
    },
    {
      title: 'Sarlav<PERSON>i',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <span
          className="cursor-pointer text-blue-600 hover:text-blue-800"
          onClick={() => handleTaskSelect(record.id)}
        >
          {text}
        </span>
      )
    },
    {
      title: 'Izoh',
      dataIndex: 'description',
      key: 'description',
      render: (text: string) => (
        <div className="max-w-xs truncate" title={text}>
          {text}
        </div>
      )
    },
    {
      title: 'Turi',
      dataIndex: ['TaskType', 'name'],
      key: 'TaskType'
    },
    {
      title: 'Muddati',
      dataIndex: 'dueDate',
      key: 'dueDate',
      render: (date: string) => dayjs(date).format('DD.MM.YYYY HH:mm')
    },
    {
      title: 'Holati',
      dataIndex: ['TaskState', 'name'],
      key: 'TaskState',
      render: (text: string, record: any) => (
        <Badge
          color={record.TaskState?.color || 'default'}
          text={text}
        />
      )
    }
  ]

  return (
    <Card>
      <Title level={4} className="dark:text-gray-200 mb-4">
        Nazorat qilayotgan topshiriqlar
      </Title>

      <Table
        columns={columns}
        dataSource={tasks || []}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          pageSizeOptions: [10, 20, 50],
          showTotal: (total) => `Jami: ${total} ta topshiriq`
        }}
        locale={{ emptyText: "Nazorat qilayotgan topshiriqlar topilmadi" }}
        scroll={{ x: 'max-content' }}
        onRow={(record) => ({
          onClick: () => handleTaskSelect(record.id),
          style: { cursor: 'pointer' }
        })}
      />
    </Card>
  )
}

export default TaskController