import { axiosPrivate } from '@/config/api'
import { taskEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { notification } from 'antd'
import { useParams } from 'react-router-dom'

type TAnswerTask = {
	recipientId: string
	description: string
	typeId?: string
	files?: string[]
}

export const useAnswerTask = () => {
	const queryClient = useQueryClient()
	const { id } = useParams<{ id: string }>()
	return useMutation({
		mutationFn: async (data: TAnswerTask) => {
			return await axiosPrivate.post(taskEndpoints.answer, data)
		},
		onSuccess: async _ => {
			notification.success({ message: 'Topshiriqga muaffaqiyatli javob yuborildi!' })
			queryClient.invalidateQueries({ queryKey: [taskEndpoints.one(id!)] })
		},
		onError: async () => {
			notification.error({ message: '<PERSON>shi<PERSON>q<PERSON> javob yozishda xatolik yuz berdi!' })
		}
	})
}
