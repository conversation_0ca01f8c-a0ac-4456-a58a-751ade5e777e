import { Col, Tabs } from 'antd'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-leaflet'
import GeojsonMapSector from './geojson-map-sector'

const Sectors = () => {
	return (
		<div>
			<Tabs
				type='card'
				items={[
					{
						key: '1',
						label: 'Birinchi sektor'
					},
					{
						key: '2',
						label: 'Ikkinchi sektor'
					},
					{
						key: '3',
						label: 'Uchinchi sektor'
					},
					{
						key: '4',
						label: "To'rtinchi sektor"
					}
				]}
			/>
			<div>
				<Col
					xs={24}
					className='relative rounded-2xl overflow-hidden h-full'>
					<MapContainer
						center={[41.311081, 69.240562]}
						zoom={6}
						style={{ height: '80vh', width: '100%' }}
						zoomControl={false}
						attributionControl={false}>
						<TileLayer
							attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
							url='https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
						/>
						<GeojsonMapSector />
					</MapContainer>
				</Col>
			</div>
		</div>
	)
}
export default Sectors
