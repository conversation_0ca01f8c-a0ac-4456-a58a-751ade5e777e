import { axiosPrivate } from '@/config/api'
import { organizationEndpoints } from '@/config/api/endpoints'
import { TApiErrorResponse } from '@/shared/types/api-error.type'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { notification } from 'antd'
import { AxiosError, AxiosResponse } from 'axios'
import { ROLE } from './get-users.queries'

type TData = {
	[key: string]: string[]
}

export type TRole = 'workers' | 'employees' | 'responsibles'

type TRemoveUsersFromOrganization = {
	data: TData
	orgId: string
	who: TRole
}

export const useRemoveUserFromOrganization = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({ data, orgId, who }: TRemoveUsersFromOrganization) => {
			let url = organizationEndpoints.removeWorkers(orgId)
			switch (who) {
				case 'workers':
					url = organizationEndpoints.removeWorkers(orgId)
					break
				case 'employees':
					url = organizationEndpoints.removeEmployees(orgId)
					break
				case 'responsibles':
					url = organizationEndpoints.removeResponsibles(orgId)
					break
				default:
					url = organizationEndpoints.removeWorkers(orgId)
			}
			return await axiosPrivate.post(url, data)
		},
		onSuccess: async (response: AxiosResponse, { orgId }) => {
			console.log(response)
			notification.success({ message: 'User removed successfully!' })
			queryClient.invalidateQueries({
				queryKey: [organizationEndpoints.usersByOrg(orgId, ROLE.worker)]
			})
			queryClient.invalidateQueries({
				queryKey: [organizationEndpoints.usersByOrg(orgId, ROLE.employee)]
			})
			queryClient.invalidateQueries({
				queryKey: [organizationEndpoints.usersByOrg(orgId, ROLE.responsible)]
			})
			queryClient.invalidateQueries({
				queryKey: [organizationEndpoints.usersByOrg(orgId)]
			})
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response?.data.message)
			notification.error({ message: 'Something went wrong while removing users!' })
		}
	})
}
