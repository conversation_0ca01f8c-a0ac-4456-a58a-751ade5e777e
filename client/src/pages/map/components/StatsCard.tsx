import { Card } from 'antd'

interface StatsCardProps {
	title: string
	value: number
	color: 'blue' | 'green' | 'red' | 'purple'
}

export default function StatsCard({ title, value, color }: StatsCardProps) {
	const colorClasses = {
		blue: 'from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 border-blue-200 dark:border-blue-700 text-blue-600 dark:text-blue-400',
		green:
			'from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 border-green-200 dark:border-green-700 text-green-600 dark:text-green-400',
		red: 'from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 border-red-200 dark:border-red-700 text-red-600 dark:text-red-400',
		purple:
			'from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 border-purple-200 dark:border-purple-700 text-purple-600 dark:text-purple-400'
	}

	return (
		<Card
			size='small'
			className={`bg-gradient-to-r ${colorClasses[color]}`}
			classNames={{
				body: '!p-2'
			}}>
			<div className='text-center'>
				<div className={`text-2xl font-bold ${colorClasses[color].split(' ').slice(-2).join(' ')}`}>
					{value}
				</div>
				<div className='text-xs text-gray-600 dark:text-gray-400'>{title}</div>
			</div>
		</Card>
	)
}
