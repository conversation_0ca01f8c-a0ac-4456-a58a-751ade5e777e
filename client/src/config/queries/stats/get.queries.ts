import { axiosPrivate } from '@/config/api'
import { statsEndPoints } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'

export interface OrganizationHierarchyStatsType {
	organizations: {
		current: string
		children: string[]
		total: number
	}
	workers: {
		total: number
	}
	tasks: {
		completed: {
			onTime: number
			late: number
			total: number
		}
		incomplete: {
			expired: number
			active: number
			total: number
		}
		total: number
	}
	attendance: {
		onTime: number
		late: number
		absent: number
		total: number
	}
}

export const useOrganizationHierarchyStats = (organizationId: string) => {
	return useQuery({
		queryKey: ['organizationHierarchyStats', organizationId],
		queryFn: async () => {
			const { data } = await axiosPrivate.get<OrganizationHierarchyStatsType>(
				statsEndPoints.findOrganizationHierarchy(organizationId)
			)
			return data
		},
		enabled: !!organizationId
	})
}

export interface OrganizationAttendanceStatsType {
	organizations: {
		current: string
		children: string[]
		total: number
	}
	workers: {
		totalWorkers: number
	}
	attendance: Array<{
		position: string
		absent: number
		onTime: number
		late: number
		totalWorkers: number
	}>
}

export const useOrganizationAttendanceStats = (organizationId: string) => {
	return useQuery({
		queryKey: ['organizationAttendanceStats', organizationId],
		queryFn: async () => {
			const { data } = await axiosPrivate.get<OrganizationAttendanceStatsType>(
				statsEndPoints.findOrganziationAttendance(organizationId)
			)
			return data
		},
		enabled: !!organizationId
	})
}

export interface Stats {
	statistics: Statistics
}

export interface Statistics {
	organizations: Organizations
	workers: Workers
	tasks: Tasks
	attendance: Attendance
}

export interface Organizations {
	count: number
	total: number
}

export interface Workers {
	total: number
}

export interface Tasks {
	completed: Completed
	incomplete: Incomplete
	total: number
}

export interface Completed {
	onTime: number
	late: number
	total: number
}

export interface Incomplete {
	expired: number
	active: number
	total: number
}

export interface Attendance {
	onTime: number
	late: number
	absent: number
	total: number
}

export const useGetStatsOrganizations = () => {
	const [search] = useSearchParams()

	const regionId = search.get('regionId')
	const districtId = search.get('districtId')
	const sectionId = search.get('sectionId')

	return useQuery({
		queryKey: [statsEndPoints.statsOrganization, regionId, districtId, sectionId],
		queryFn: async () => {
			const { data } = await axiosPrivate.get<Stats>(statsEndPoints.statsOrganization, {
				params: {
					regionId,
					districtId: districtId ? regionId + districtId : undefined,
					sectionId: sectionId && regionId ? regionId + districtId + sectionId : undefined
				}
			})
			return data
		}
	})
}
