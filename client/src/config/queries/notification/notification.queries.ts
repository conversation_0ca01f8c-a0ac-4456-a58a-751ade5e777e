import { axiosPrivate } from '@/config/api'
import { notificationEndpoints } from '@/config/api/endpoints'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useRef } from 'react'

export enum NotificationType {
	TASK_ASSIGNED = 'TASK_ASSIGNED',
	TASK_REJECTED = 'TASK_REJECTED',
	ANSWER_CREATED = 'ANSWER_CREATED',
	ANSWER_REJECTED = 'ANSWER_REJECTED',
	ANSWER_CONFIRMED = 'ANSWER_CONFIRMED'
}

export interface Notification {
	id: string
	type: NotificationType
	message: string
	read: false
	organizationId: string
	userId: string | undefined
	createdAt: string
	updatedAt: string
	Organization: { id: string; name: string }
	Content: any | null
}

export const useGetNotifications = () => {
	const cache = useRef<Notification[]>([])

	return useQuery({
		queryKey: [notificationEndpoints.all],
		queryFn: async () => {
			const response = await axiosPrivate.get<Notification[]>(notificationEndpoints.all)
			return response.data
		},
		refetchInterval: 60_000,
		select(data) {
			const audio = new Audio('/notification.wav')

			if (cache.current.length < data.length) {
				audio.play()
			}

			if (data) {
				cache.current = data
			}
			return data
		}
	})
}

export const useReadNotification = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async (id: string[]) => {
			const response = await axiosPrivate.post(notificationEndpoints.read, {
				id
			})
			return response.data
		},
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: [notificationEndpoints.all]
			})
		}
	})
}
