import { Controller, Get, Query, UseInterceptors } from '@nestjs/common';
import { SectionStatsService } from './section-stats.service';
import { ProtectedRoute } from 'src/common';
import { SectionStatsQuery } from './dto/section-stats.dto';
import { Context } from 'src/common/decorators/context.decorator';
import { FindAllDto } from './dto/find-all.dto';

@Controller('dashboard/section-stats')
@ProtectedRoute({ isPublic: false, context: 'WORKSPACE' })
export class SectionStatsController {
  constructor(private readonly sectionStatsService: SectionStatsService) {}

  // checked
  @Get()
  async getBasicSectionStats(
    @Query() { organizationId }: SectionStatsQuery,
    @Context('organizationId') xOrgId: string,
  ) {
    return await this.sectionStatsService.getSectionBaseStats(organizationId || xOrgId);
  }

  // checked
  @Get('section')
  async getSection(@Query() { organizationId, ...params }: FindAllDto, @Context('organizationId') xOrgId: string) {
    return this.sectionStatsService.getSection(organizationId || xOrgId, params);
  }

  // checked
  @Get('position')
  async getPositions(
    @Query() { organizationId, positionId, ...params }: FindAllDto,
    @Context('organizationId') xOrgId: string,
  ) {
    return this.sectionStatsService.getPositions(organizationId || xOrgId, params, positionId);
  }

  // checked
  @Get('attendance')
  async getAttendanceSectionStats(
    @Query() { organizationId }: SectionStatsQuery,
    @Context('organizationId') xOrgId: string,
  ) {
    return await this.sectionStatsService.getSectionAttendanceStats(organizationId || xOrgId);
  }

  // checked
  @Get('late-attendance')
  async getLateAttendance(
    @Query() { organizationId, positionId, ...params }: FindAllDto,
    @Context('organizationId') xOrgId: string,
  ) {
    return this.sectionStatsService.getLateAttendance(organizationId || xOrgId, params, positionId);
  }

  // checked
  @Get('no-attendance')
  async getNoAttendance(
    @Query() { organizationId, positionId, ...params }: FindAllDto,
    @Context('organizationId') xOrgId: string,
  ) {
    return this.sectionStatsService.getNoAttendance(organizationId || xOrgId, params, positionId);
  }

  // checked
  @Get('employee')
  async getEmployee(@Query() { organizationId, ...params }: FindAllDto, @Context('organizationId') xOrgId: string) {
    return this.sectionStatsService.getEmployee(organizationId || xOrgId, params);
  }

  // checked
  @Get('area')
  async getAreaSectionStats(@Query() { organizationId }: SectionStatsQuery, @Context('organizationId') xOrgId: string) {
    return await this.sectionStatsService.getSectionAreaStats(organizationId || xOrgId);
  }

  @Get('vacation')
  async getVacation(@Query() { organizationId, ...params }: FindAllDto, @Context('organizationId') xOrgId: string) {
    return this.sectionStatsService.getVacation(organizationId || xOrgId, params, 'VACATION');
  }

  @Get('vacancy')
  async getVacancy(@Query() { organizationId, ...params }: FindAllDto, @Context('organizationId') xOrgId: string) {
    return this.sectionStatsService.getVacancy(organizationId || xOrgId, params);
  }

  @Get('patient')
  async getPatient(@Query() { organizationId, ...params }: FindAllDto, @Context('organizationId') xOrgId: string) {
    return this.sectionStatsService.getVacation(organizationId || xOrgId, params, 'PATIONS');
  }

  @Get('short-vacation')
  async getShortVacation(
    @Query() { organizationId, ...params }: FindAllDto,
    @Context('organizationId') xOrgId: string,
  ) {
    return this.sectionStatsService.getVacation(organizationId || xOrgId, params, 'SHORT_VACATION');
  }

  @Get('in-area')
  async getInArea(
    @Query() { organizationId, positionId, ...params }: FindAllDto,
    @Context('organizationId') xOrgId: string,
  ) {
    return this.sectionStatsService.getArea(organizationId || xOrgId, params, 'IN', positionId);
  }

  @Get('out-area')
  async getOutArea(
    @Query() { organizationId, positionId, ...params }: FindAllDto,
    @Context('organizationId') xOrgId: string,
  ) {
    return this.sectionStatsService.getArea(organizationId || xOrgId, params, 'OUT', positionId);
  }

  @Get('disabled-area')
  async getDisabledArea(
    @Query() { organizationId, positionId, ...params }: FindAllDto,
    @Context('organizationId') xOrgId: string,
  ) {
    return this.sectionStatsService.getArea(organizationId || xOrgId, params, 'DISABLED', positionId);
  }
}
