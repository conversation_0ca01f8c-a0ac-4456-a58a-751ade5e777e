import { PlusOutlined } from '@ant-design/icons'
import { Button, Form, Modal } from 'antd'
import { useOrganizationsModalStore } from '../utils/organizations-modal-store'
import { getFormChanges } from '@/shared/utils/getFormChanges'
import { TOrganizationRequest } from '@/config/queries/organizations/create.queries'
import { useUpdateOrganization } from '@/config/queries/organizations/update.queries'
import { isNoChanges } from '@/shared/utils/isNoChanges'
import OrganizationForm from './organization-form'
import { getOrganizationFormValidation } from '../config/validations'
import { usePermissions } from '@/shared/guards/usePermissions'
import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { LoadingScreen } from '@/shared/ui/suspense'
import { useEffect } from 'react'
import { useParams } from 'react-router-dom'
import { useCreateChildOrganization } from '@/config/queries/organizations/create-children.queries'
import { UnderControlManagmentModal } from '@/pages/under-control-organization/ui/organization-modal'

type TOrganizationModal = {
	type: string
}

export const OrganizationModal = ({ type = 'child' }: TOrganizationModal) => {
	const { id } = useParams()
	const [form] = Form.useForm()
	const { mutateAsync: createOrganization, isPending: isCreating } = useCreateChildOrganization(id)
	const { mutateAsync: updateOrganization, isPending: isUpdating } = useUpdateOrganization()
	const { open, onOpen, onClose, data } = useOrganizationsModalStore()
	const organizationFormValidation = getOrganizationFormValidation(!!data?.id)
	const { data: user, isLoading } = useAuthMe()
	const { hasPermission } = usePermissions()

	useEffect(() => {
		if (data) {
			form.setFieldsValue(data)
		}
	}, [data, form])

	const close = () => {
		form.resetFields()
		onClose()
	}

	const onFinish = (dataValues: TOrganizationRequest) => {
		if (!id) return
		if (data?.id) {
			const updatedFields = getFormChanges(data, dataValues)
			if (isNoChanges(updatedFields)) return close()
			updateOrganization({
				id: data.id,
				data: updatedFields
			}).then(close)
		} else {
			createOrganization({ data: dataValues, id }).then(close)
		}
	}

	if (isLoading || !user?.role) return <LoadingScreen />

	return (
		<div className='flex items-center justify-between pb-2'>
			<h1 className='text-3xl font-medium'>
				{type === 'child' ? 'Quyi tashkilotlar' : 'Boshqa tashkilotlar'}
			</h1>

			{hasPermission(user.role, ['create:*']) && (
				<>
					<div className='flex items-center gap-x-3'>
						{type === 'undercontrol' ? (
							<UnderControlManagmentModal orgId={id!} />
						) : (
							<Button
								type='primary'
								icon={<PlusOutlined />}
								onClick={() => onOpen()}>
								Yangi Tashkilot
							</Button>
						)}
					</div>

					<Modal
						title={data ? 'Tashkilotni tahrirlash' : "Yangi Tashkilot qo'shish"}
						open={open}
						onCancel={close}
						onClose={close}
						destroyOnClose
						width={800}
						okText={data ? 'Saqlash' : "Qo'shish"}
						cancelText='Bekor qilish'
						confirmLoading={isCreating || isUpdating}
						onOk={() => form.submit()}>
						{open && (
							<Form
								form={form}
								layout='vertical'
								onFinish={onFinish}>
								<OrganizationForm validation={organizationFormValidation} />
							</Form>
						)}
					</Modal>
				</>
			)}
		</div>
	)
}
