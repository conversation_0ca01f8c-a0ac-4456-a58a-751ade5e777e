enum NotificationType {
  TASK_ASSIGNED
  TASK_REJECTED
  ANSWER_CREATED
  ANSWER_REJECTED
  ANSWER_CONFIRMED
}

model Notification {
  id String @id @default(cuid())

  type    NotificationType
  message String
  read    <PERSON><PERSON><PERSON>          @default(false)

  Content Json?

  Organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String

  User   User?   @relation(fields: [userId], references: [id])
  userId String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}
