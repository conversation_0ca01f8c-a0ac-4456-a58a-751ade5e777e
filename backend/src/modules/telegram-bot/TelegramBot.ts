import axios, { AxiosResponse } from 'axios';
import * as fs from 'fs';
import * as FormData from 'form-data';
import { InternalServerErrorException } from '@nestjs/common';

interface TelegramBotConfig {
  token: string;
  webhookUrl: string;
}

interface InlineKeyboardButton {
  text: string;
  callback_data?: string;
  url?: string;
}

interface ReplyKeyboardButton {
  text: string;
  request_contact?: boolean;
  request_location?: boolean;
}

interface WebhookInfo {
  url: string;
  has_custom_certificate: boolean;
  pending_update_count: number;
  max_connections?: number;
  ip_address?: string;
  last_error_date?: number;
  last_error_message?: string;
  last_synchronization_error_date?: number;
}

interface TelegramUser {
  id: number;
  is_bot: boolean;
  first_name: string;
  last_name?: string;
  username?: string;
  language_code?: string;
}

interface TelegramChat {
  id: number;
  type: 'private' | 'group' | 'supergroup' | 'channel';
  title?: string;
  username?: string;
  first_name?: string;
  last_name?: string;
}

interface TelegramMessage {
  message_id: number;
  from?: TelegramUser;
  chat: TelegramChat;
  date: number;
  text?: string;
  photo?: any[];
  document?: any;
  caption?: string;
}

interface TelegramUpdate {
  update_id: number;
  message?: TelegramMessage;
  callback_query?: {
    id: string;
    from: TelegramUser;
    message?: TelegramMessage;
    data?: string;
  };
}

interface SendMessageOptions {
  chat_id: number | string;
  text: string;
  parse_mode?: 'Markdown' | 'HTML';
  disable_web_page_preview?: boolean;
  reply_to_message_id?: number;
  reply_markup?: {
    inline_keyboard?: InlineKeyboardButton[][];
    keyboard?: ReplyKeyboardButton[][];
    resize_keyboard?: boolean;
    one_time_keyboard?: boolean;
    remove_keyboard?: boolean;
  };
}

export class TelegramBot {
  token: string;
  webhookUrl: string;
  gateway: string;

  constructor(config: TelegramBotConfig | string, webhookUrl?: string) {
    if (typeof config === 'string') {
      this.token = config;
      this.webhookUrl = webhookUrl || '';
    } else {
      this.token = config.token;
      this.webhookUrl = config.webhookUrl;
    }
    this.gateway = 'https://api.telegram.org';
  }

  /**
   * Отправляет запрос к Telegram Bot API
   */
  private async request<T = any>(method: string, params: Record<string, any> = {}, isFormData = false): Promise<T> {
    const url = `${this.gateway}/bot${this.token}/${method}`;
    try {
      let response: AxiosResponse;

      // Настройки для принудительного использования IPv4
      const axiosConfig = {};

      if (isFormData) {
        const formData = new FormData();

        Object.entries(params).forEach(([key, value]) => {
          if (typeof value === 'object' && !Buffer.isBuffer(value)) {
            formData.append(key, JSON.stringify(value));
          } else {
            formData.append(key, value);
          }
        });

        response = await axios.post(url, formData, {
          headers: { ...formData.getHeaders() },
          ...axiosConfig,
        });
      } else {
        response = await axios.post(url, params, axiosConfig);
      }

      if (response.data.ok) {
        return response.data.result;
      }

      throw new InternalServerErrorException(`Telegram API error: ${response.data.description}`);
    } catch (error) {
      throw new InternalServerErrorException(`Failed to call Telegram API method ${method}: ${error.message}`);
    }
  }

  /**
   * Устанавливает вебхук для бота
   */
  async setWebhook(
    url = this.webhookUrl,
    options: { max_connections?: number; allowed_updates?: string[] } = {},
  ): Promise<boolean> {
    try {
      const result = await this.request<boolean>('setWebhook', {
        url,
        ...options,
      });
      return result;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Удаляет вебхук бота
   */
  async deleteWebhook(): Promise<boolean> {
    try {
      const result = await this.request<boolean>('deleteWebhook');
      return result;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Получает информацию о вебхуке
   */
  async getWebhookInfo(): Promise<WebhookInfo> {
    try {
      return await this.request<WebhookInfo>('getWebhookInfo');
    } catch (error) {
      throw new InternalServerErrorException(`Failed to get webhook info: ${error.message}`);
    }
  }

  /**
   * Отправляет текстовое сообщение
   */
  async sendMessage(options: SendMessageOptions): Promise<TelegramMessage> {
    try {
      return await this.request<TelegramMessage>('sendMessage', options);
    } catch (error) {
      throw new InternalServerErrorException(`Failed to send message: ${error.message}`);
    }
  }

  /**
   * Создает клавиатуру с кнопками для сообщения
   */
  createInlineKeyboard(buttons: InlineKeyboardButton[][]) {
    return { inline_keyboard: buttons };
  }

  /**
   * Создает обычную клавиатуру
   */
  createReplyKeyboard(
    buttons: ReplyKeyboardButton[][],
    options: { resize_keyboard?: boolean; one_time_keyboard?: boolean } = {},
  ) {
    return {
      keyboard: buttons,
      ...options,
    };
  }

  /**
   * Отправляет фото пользователю
   */
  async sendPhoto(
    chatId: number | string,
    photo: string | Buffer | fs.ReadStream,
    options: {
      caption?: string;
      parse_mode?: 'Markdown' | 'HTML';
      reply_to_message_id?: number;
      reply_markup?: any;
    } = {},
  ): Promise<TelegramMessage> {
    try {
      const params: Record<string, any> = {
        chat_id: chatId,
        ...options,
      };

      if (typeof photo === 'string' && (photo.startsWith('http') || photo.startsWith('https'))) {
        params.photo = photo;
        return await this.request<TelegramMessage>('sendPhoto', params);
      }

      // Если фото - это файл или буфер
      params.photo = photo;
      return await this.request<TelegramMessage>('sendPhoto', params, true);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Отправляет документ пользователю
   */
  async sendDocument(
    chatId: number | string,
    document: string | Buffer | fs.ReadStream,
    options: {
      caption?: string;
      parse_mode?: 'Markdown' | 'HTML';
      reply_to_message_id?: number;
      reply_markup?: any;
    } = {},
  ): Promise<TelegramMessage> {
    try {
      const params: Record<string, any> = {
        chat_id: chatId,
        ...options,
      };

      if (typeof document === 'string' && (document.startsWith('http') || document.startsWith('https'))) {
        params.document = document;
        return await this.request<TelegramMessage>('sendDocument', params);
      }

      // Если документ - это файл или буфер
      params.document = document;
      return await this.request<TelegramMessage>('sendDocument', params, true);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Обработка входящего обновления от Telegram
   */
  handleUpdate(update: TelegramUpdate): TelegramUpdate {
    // Этот метод можно переопределить в подклассах для обработки обновлений
    return update;
  }

  /**
   * Получает информацию о боте
   */
  async getMe(): Promise<TelegramUser> {
    try {
      return await this.request<TelegramUser>('getMe');
    } catch (error) {
      throw error;
    }
  }

  /**
   * Отвечает на колбэк от inline кнопки
   */
  async answerCallbackQuery(
    callbackQueryId: string,
    options: {
      text?: string;
      show_alert?: boolean;
      url?: string;
      cache_time?: number;
    } = {},
  ): Promise<boolean> {
    try {
      return await this.request<boolean>('answerCallbackQuery', {
        callback_query_id: callbackQueryId,
        ...options,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Редактирует текстовое сообщение
   */
  async editMessageText(
    text: string,
    options: {
      chat_id?: number | string;
      message_id?: number;
      inline_message_id?: string;
      parse_mode?: 'Markdown' | 'HTML';
      disable_web_page_preview?: boolean;
      reply_markup?: any;
    },
  ): Promise<TelegramMessage | boolean> {
    try {
      return await this.request<TelegramMessage | boolean>('editMessageText', {
        text,
        ...options,
      });
    } catch (error) {
      throw error;
    }
  }

  /**
   * Удаляет сообщение
   */
  async deleteMessage(chatId: number | string, messageId: number): Promise<boolean> {
    try {
      return await this.request<boolean>('deleteMessage', {
        chat_id: chatId,
        message_id: messageId,
      });
    } catch (error) {
      throw error;
    }
  }
}
