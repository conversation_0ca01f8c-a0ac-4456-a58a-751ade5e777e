import * as multer from 'multer';
import * as path from 'path';
import * as fs from 'fs';
import { v4 as uuidv4 } from 'uuid';

export class MulterService {
  /**
   * Создает конфигурацию multer для одиночной загрузки файла
   * @param destination путь для сохранения файлов
   * @returns конфигурация multer
   */
  createMulterOptions(destination: string = 'static'): multer.Options {
    return {
      storage: this.getStorage(destination),
      fileFilter: this.fileFilter,
      limits: {
        fileSize: 10 * 1024 * 1024, // 10 MB
      },
    };
  }

  /**
   * Создает конфигурацию для загрузки только изображений
   * @param destination путь для сохранения файлов
   * @returns конфигурация multer
   */
  createImageOptions(destination: string = 'static/images'): multer.Options {
    return {
      storage: this.getStorage(destination),
      fileFilter: this.imageFileFilter,
      limits: {
        fileSize: 10 * 1024 * 1024, // 10 MB
      },
    };
  }

  /**
   * Создает конфигурацию для загрузки только документов
   * @param destination путь для сохранения файлов
   * @returns конфигурация multer
   */
  createDocumentOptions(destination: string = 'static/documents'): multer.Options {
    return {
      storage: this.getStorage(destination),
      fileFilter: this.documentFileFilter,
      limits: {
        fileSize: 10 * 1024 * 1024, // 10 MB
      },
    };
  }

  /**
   * Создает конфигурацию multer без сохранения файла на диск
   * Файл будет доступен только в памяти
   * @returns конфигурация multer
   */
  createMemoryOptions(): multer.Options {
    return {
      storage: multer.memoryStorage(),
      fileFilter: this.fileFilter,
      limits: {
        fileSize: 10 * 1024 * 1024, // 10 MB
      },
    };
  }

  /**
   * Создает конфигурацию для загрузки только изображений без сохранения на диск
   * @returns конфигурация multer
   */
  createMemoryImageOptions(): multer.Options {
    return {
      storage: multer.memoryStorage(),
      fileFilter: this.imageFileFilter,
      limits: {
        fileSize: 10 * 1024 * 1024, // 10 MB
      },
    };
  }

  /**
   * Создает конфигурацию для загрузки только архивов
   * @returns конфигурация multer
   * @param destination путь для сохранения файлов
   */
  createArchiveOptions(destination = 'static/archive'): multer.Options {
    return {
      storage: this.getStorage(destination),
      fileFilter: this.archiveFileFilter,
      limits: {
        fileSize: 10 * 1024 * 1024, // 10 MB
      },
    };
  }

  private archiveFileFilter(req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) {
    const allowedMimes = ['application/zip', 'application/x-rar-compressed'];
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Только архивы разрешены'));
    }
  }

  /**
   * Настраивает хранилище для файлов
   * @param destination путь для сохранения файлов
   * @returns хранилище multer
   */
  private getStorage(destination: string): multer.StorageEngine {
    return multer.diskStorage({
      destination: (req, file, cb) => {
        const uploadPath = path.join(process.cwd(), destination);

        // Создаем папку если она не существует
        if (!fs.existsSync(uploadPath)) {
          fs.mkdirSync(uploadPath, { recursive: true });
        }

        cb(null, uploadPath);
      },

      filename: (req, file, cb) => {
        // Генерация уникального имени файла с сохранением оригинального расширения
        const uniqueName = `${uuidv4()}${path.extname(file.originalname)}`;
        cb(null, uniqueName);
      },
    });
  }

  /**
   * Фильтр для всех типов файлов
   */
  private fileFilter(req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) {
    cb(null, true);
  }

  /**
   * Фильтр только для изображений
   */
  private imageFileFilter(req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) {
    const allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Только изображения разрешены'));
    }
  }

  /**
   * Фильтр только для документов
   */
  private documentFileFilter(req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) {
    const allowedMimes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ];
    if (allowedMimes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Только документы разрешены'));
    }
  }
}
