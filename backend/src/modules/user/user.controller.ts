import { Body, Controller, Delete, Get, Param, Patch, Post, Put, Query } from '@nestjs/common';
import { UserService } from './user.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { ProtectedRoute } from 'src/common';
import { AuthService } from '../auth/auth.service';

@Controller('user')
export class UserController {
  constructor(
    private readonly userService: UserService,
    private readonly authService: AuthService,
  ) {}

  @Post()
  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  async create(@Body() createUserDto: CreateUserDto) {
    return await this.userService.create(createUserDto);
  }

  @Get()
  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: false })
  async findAll(@Query() { page, limit, search, orgId, phone }) {
    return await this.userService.findAll(page, limit, orgId, search, phone);
  }

  @Get(':id')
  @ProtectedRoute({ isPublic: false, onlyAdmin: false })
  async findOne(@Param('id') id: string) {
    return await this.userService.findOne(id);
  }

  @Patch(':id')
  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: false })
  async update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto) {
    return await this.userService.update(id, updateUserDto);
  }

  @Delete(':id')
  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  async remove(@Param('id') id: string) {
    return await this.userService.remove(id);
  }

  @ProtectedRoute({ isPublic: true })
  @Put('add-date-username')
  async addDateUsername() {
    return await this.userService.addDateUsername();
  }
}
