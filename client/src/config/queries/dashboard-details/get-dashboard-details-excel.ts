import { useMutation } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'
import { dashboardDetails } from '../../api/endpoints'
import { downloadBlob } from '@/shared/utils/downloadBlob'
import { axiosPrivate } from '@/config/api'

export const useGetEmployeeExcel = () => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('organizationId')

	return useMutation({
		mutationFn: async (orgId?: string) => {
			return await axiosPrivate.get(dashboardDetails.employeeExcel(orgId || organizationId!), {
				responseType: 'blob'
			})
		},
		onSuccess: data => {
			console.log(data)
			downloadBlob(data.data, data.headers['content-disposition'] || 'employee-report.xlsx')
		}
	})
}

export const useGetVacationExcel = () => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('organizationId')

	return useMutation({
		mutationFn: async (orgId?: string) => {
			return await axiosPrivate.get(dashboardDetails.vacationExcel(orgId || organizationId!), {
				responseType: 'blob'
			})
		},
		onSuccess: data => {
			console.log(data)
			downloadBlob(data.data, data.headers['content-disposition'] || 'vacation-report.xlsx')
		}
	})
}
export const useGetShortVacationExcel = () => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('organizationId')

	return useMutation({
		mutationFn: async (orgId?: string) => {
			return await axiosPrivate.get(
				dashboardDetails.shortVacationvacationExcel(orgId || organizationId!),
				{
					responseType: 'blob'
				}
			)
		},
		onSuccess: data => {
			console.log(data)
			downloadBlob(data.data, data.headers['content-disposition'] || 'vacation-report.xlsx')
		}
	})
}

export const useGetPatientExcel = () => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('organizationId')

	return useMutation({
		mutationFn: async (orgId?: string) => {
			return await axiosPrivate.get(dashboardDetails.patientExcel(orgId || organizationId!), {
				responseType: 'blob'
			})
		},
		onSuccess: data => {
			console.log(data)
			downloadBlob(data.data, data.headers['content-disposition'] || 'patient-report.xlsx')
		}
	})
}

export const useGetVacancyExcel = () => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('organizationId')

	return useMutation({
		mutationFn: async (orgId?: string) => {
			return await axiosPrivate.get(dashboardDetails.vacancyExcel(orgId || organizationId!), {
				responseType: 'blob'
			})
		},
		onSuccess: data => {
			console.log(data)
			downloadBlob(data.data, data.headers['content-disposition'] || 'vacancy-report.xlsx')
		}
	})
}

export const useGetSectionExcel = () => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('organizationId')

	return useMutation({
		mutationFn: async (orgId?: string) => {
			return await axiosPrivate.get(dashboardDetails.sectionExcel(orgId || organizationId!), {
				responseType: 'blob'
			})
		},
		onSuccess: data => {
			console.log(data)
			downloadBlob(data.data, data.headers['content-disposition'] || 'section-report.xlsx')
		}
	})
}

export const useGetPositionExcel = () => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('organizationId')
	const positionId = searchParams.get('positionId')

	return useMutation({
		mutationFn: async ({ orgId, posId }: { orgId?: string; posId?: string } = {}) => {
			return await axiosPrivate.get(
				dashboardDetails.positionExcel(orgId || organizationId!, posId || positionId!),
				{
					responseType: 'blob'
				}
			)
		},
		onSuccess: data => {
			console.log(data)
			downloadBlob(data.data, data.headers['content-disposition'] || 'position-report.xlsx')
		}
	})
}

export const useGetLateAttendanceExcel = () => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('organizationId')
	const positionId = searchParams.get('positionId')

	return useMutation({
		mutationFn: async ({ orgId, posId }: { orgId?: string; posId?: string } = {}) => {
			return await axiosPrivate.get(
				dashboardDetails.lateAttendanceExcel(
					orgId || organizationId!,
					posId || positionId || undefined
				),
				{
					responseType: 'blob'
				}
			)
		},
		onSuccess: data => {
			console.log(data)
			downloadBlob(data.data, data.headers['content-disposition'] || 'late-attendance-report.xlsx')
		}
	})
}

export const useGetNoAttendanceExcel = () => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('organizationId')
	const positionId = searchParams.get('positionId')

	return useMutation({
		mutationFn: async ({ orgId, posId }: { orgId?: string; posId?: string } = {}) => {
			return await axiosPrivate.get(
				dashboardDetails.noAttendanceExcel(
					orgId || organizationId!,
					posId || positionId || undefined
				),
				{
					responseType: 'blob'
				}
			)
		},
		onSuccess: data => {
			console.log(data)
			downloadBlob(data.data, data.headers['content-disposition'] || 'no-attendance-report.xlsx')
		}
	})
}

export const useGetInAreaExcel = () => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('organizationId')
	const positionId = searchParams.get('positionId')

	return useMutation({
		mutationFn: async ({ orgId, posId }: { orgId?: string; posId?: string } = {}) => {
			return await axiosPrivate.get(
				dashboardDetails.inAreaExcel(orgId || organizationId!, posId || positionId || undefined),
				{
					responseType: 'blob'
				}
			)
		},
		onSuccess: data => {
			console.log(data)
			downloadBlob(data.data, data.headers['content-disposition'] || 'in-area-report.xlsx')
		}
	})
}

export const useGetOutAreaExcel = () => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('organizationId')
	const positionId = searchParams.get('positionId')

	return useMutation({
		mutationFn: async ({ orgId, posId }: { orgId?: string; posId?: string } = {}) => {
			return await axiosPrivate.get(
				dashboardDetails.outAreaExcel(orgId || organizationId!, posId || positionId || undefined),
				{
					responseType: 'blob'
				}
			)
		},
		onSuccess: data => {
			console.log(data)
			downloadBlob(data.data, data.headers['content-disposition'] || 'out-area-report.xlsx')
		}
	})
}
export const useGetDisabledAreaExcel = () => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('organizationId')
	const positionId = searchParams.get('positionId')

	return useMutation({
		mutationFn: async ({ orgId, posId }: { orgId?: string; posId?: string } = {}) => {
			return await axiosPrivate.get(
				dashboardDetails.disabledAreaExcel(
					orgId || organizationId!,
					posId || positionId || undefined
				),
				{
					responseType: 'blob'
				}
			)
		},
		onSuccess: data => {
			console.log(data)
			downloadBlob(data.data, data.headers['content-disposition'] || 'out-area-report.xlsx')
		}
	})
}
