import { Body, Controller, Get, Param, Patch, Post, Query } from '@nestjs/common';
import { WorkerService } from './worker.service';
import { CreateUserDto } from '../user/dto/create-user.dto';
import { ProtectedRoute } from 'src/common';
import { FindAllDto } from '../dashboard/section-stats/dto/find-all.dto';

@Controller('worker')
export class WorkerController {
  constructor(private readonly workerService: WorkerService) {}

  @Post()
  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: false })
  async create(@Query() { organizationId }: FindAllDto, @Body() createUserDto: CreateUserDto) {
    return this.workerService.create(createUserDto, organizationId);
  }

  @Get('')
  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: false })
  async findWorker(@Query() { phone }: FindAllDto) {
    return this.workerService.findByPhone(phone);
  }

  @Patch('/connect')
  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: false })
  async connectUserToOrg(@Query() { userId, organizationId }: FindAllDto) {
    return this.workerService.connectUserToOrganization(userId, organizationId);
  }
}
