@import 'tailwindcss';

@custom-variant dark (&:where(.dark, .dark *));

html,
body,
#root {
	height: 100%;
}

.container {
	width: 100%;
	max-width: 1250px;
	margin-inline: auto;
	padding-inline: 16px;
	background-color: #1e1f2e;
}

.ant-modal-confirm .ant-modal-content {
	@apply !bg-white dark:!bg-[#282A3F];
}

.ant-modal-confirm-title {
	@apply dark:!text-white;
}

.ant-modal-confirm-content {
	@apply dark:!text-white;
}

.ant-modal-confirm-btns .ant-btn.ant-btn-color-default {
	@apply !bg-blue-500 !border-none !text-white;
}

.ant-modal-confirm-btns .ant-btn.ant-btn-color-primary {
	@apply !bg-red-500 !border-none;
}

.ant-notification-notice {
	@apply !bg-[#282A3F];
}

.ant-notification-notice-message {
	@apply !text-white;
}

.ant-notification-notice-close {
	@apply !text-white;
}
.ant-card-body {
	@apply w-full;
}

.custom-popup .leaflet-popup-content-wrapper {
	border-radius: 8px;
	box-shadow: 0 3px 14px rgba(0, 0, 0, 0.2);
}
.custom-popup .leaflet-popup-content {
	margin: 0;
	padding: 0;
}

.custom-popup .leaflet-popup-tip {
	background-color: white;
}

.ant-table-cell.green-col {
	background-color: oklch(0.82 0.19 153.31) !important;
	@apply !text-black !font-bold;
}

.ant-table-cell.red-col {
	background-color: oklch(0.76 0.19 21.46) !important;
	@apply !text-black !font-bold;
}

.ant-table-cell.yellow-col {
	background-color: oklch(0.9 0.15 90.33) !important;
	@apply !text-black !font-bold;
}
