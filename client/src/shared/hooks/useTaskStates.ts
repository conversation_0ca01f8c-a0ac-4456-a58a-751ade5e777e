import { useGetTaskStates } from '@/config/queries/task-states/get-queries'
import { TTask } from '@/config/queries/task/getOne'
import { useCallback } from 'react'
import { useSearchParams } from 'react-router-dom'

export const useTaskStates = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const { data: taskStates, isLoading } = useGetTaskStates()
	const page = parseInt(searchParams.get('page') || '1', 10)
	const pageSize = parseInt(searchParams.get('pageSize') || '10', 10)
	const status = searchParams.get('status') ?? 'all'

	const filteredData = useCallback(
		<T>(data: T) => {
			if (!data) return []
			// @ts-ignore
			const transformedData = data.map(item => item)
			let dataToRender = []
			if (status === 'all') dataToRender = transformedData
			else if (status === 'completed')
				dataToRender = transformedData.filter((item: TTask) => item.isCompleted)
			else if (status === 'incompleted')
				dataToRender = transformedData.filter((item: TTask) => !item.isCompleted)
			else
				dataToRender = transformedData.filter(
					(item: TTask) => !item.isCompleted && item?.TaskState?.name === status
				)

			return dataToRender.map((item: TTask) => ({
				...item,
				TaskState: {
					...item.TaskState,
					name: item.isCompleted ? 'yakunlangan' : item.TaskState.name
				}
			}))
		},
		[status]
	)

	const handlePaginationChange = (newPage: number, newPageSize: number) => {
		setSearchParams({
			type: searchParams.get('type') ?? 'incomming',
			page: newPage.toString(),
			pageSize: newPageSize.toString(),
			status
		})
	}

	const handleSegmentChange = (value: string) => {
		setSearchParams({
			type: searchParams.get('type') ?? 'incomming',
			page: '1',
			pageSize: pageSize.toString(),
			status: value,
			filterState: value
		})
	}

	return {
		status,
		filteredData,
		handlePaginationChange,
		handleSegmentChange,
		pageSize,
		page,
		taskStates,
		segmentOptions: [
			{ label: 'Hammasi', value: 'all' },
			// taskStates?.pages?.[0]?.data
			// 	?.sort((acc, item) => acc.order - item.order)
			// 	.map(state => ({ label: state.name, value: state.key })),
			{ label: 'Jarayonda', value: 'incompleted' },
			{ label: 'Yakunlangan', value: 'completed' }
		].flat(1),
		isLoading
	}
}
