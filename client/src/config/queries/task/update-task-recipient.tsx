import { axiosPrivate } from '@/config/api'
import { taskEndpoints } from '@/config/api/endpoints'
import { TApiErrorResponse } from '@/shared/types/api-error.type'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { notification } from 'antd'
import { AxiosError, AxiosResponse } from 'axios'

type TRecipientUpdate = {
	organizationId?: string
	userId?: string[]
	positionId?: string
}

type TUseUpdateRecipient = {
	taskId: string
	data: {
		recipients: TRecipientUpdate[]
	}
}

export const useUpdateRecipient = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({ taskId, data }: TUseUpdateRecipient) => {
			return await axiosPrivate.put(taskEndpoints.updateRecipient(taskId), data)
		},
		onSuccess: async (response: AxiosResponse, { taskId }) => {
			console.log(response)
			notification.success({ message: "Topshiriqga yangi bajaruvchi muaffaqiyatli qo'shildi!" })
			queryClient.invalidateQueries({ queryKey: [taskEndpoints.one(taskId)] })
			queryClient.invalidateQueries({ queryKey: [taskEndpoints.all] })
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response)
			notification.error({ message: 'Topshiriqga yangi bajaruvchi qo`shisda xatolik yuz berdi!' })
		}
	})
}
