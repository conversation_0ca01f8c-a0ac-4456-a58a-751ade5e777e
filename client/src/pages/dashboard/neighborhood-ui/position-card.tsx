import { Card } from 'antd'
import { useNavigate, useSearchParams } from 'react-router-dom'

interface PositionItem {
	title: string
	value: string
	id?: string
}

interface NeighborhoodPositionCardProps {
	positions: PositionItem[]
}

const PositionCard = ({ positions }: NeighborhoodPositionCardProps) => {
	const navigate = useNavigate()
	const [searchParams] = useSearchParams()
	const orgId = searchParams.get('orgId')

	const handlePositionClick = (positionId: string) => {
		if (positionId && orgId) {
			navigate(`/workspace/dashboard-details/position?orgId=${orgId}&positionId=${positionId}`)
		}
	}

	return (
		<Card className='border shadow-sm w-full'>
			<div className='p-4'>
				<div className='flex flex-wrap gap-4'>
					{positions.map((position, index) => (
						<div
							key={index}
							className={`text-center flex-1 min-w-[100px] ${
								position.id ? 'cursor-pointer p-2 rounded-lg transition-colors' : ''
							}`}
							onClick={() => position.id && handlePositionClick(position.id)}>
							<h3 className='text-lg font-medium text-gray-500 dark:text-white mb-2 h-10 flex items-center justify-center'>
								{position.title}
							</h3>

							<div className='flex flex-col items-center justify-center'>
								<div className='flex justify-center mb-2'>
									<svg
										xmlns='http://www.w3.org/2000/svg'
										width='24'
										height='24'
										viewBox='0 0 24 24'
										fill='none'
										stroke='currentColor'
										strokeWidth='2'
										strokeLinecap='round'
										strokeLinejoin='round'
										className='text-blue-500'>
										<path d='M12 5v14' />
										<path d='m19 12-7 7-7-7' />
									</svg>
								</div>
								<p className='text-4xl font-bold text-blue-500'>{position.value}</p>
							</div>
						</div>
					))}
				</div>
			</div>
		</Card>
	)
}

export default PositionCard
