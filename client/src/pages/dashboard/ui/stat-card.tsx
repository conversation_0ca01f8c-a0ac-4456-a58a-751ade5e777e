import { Card, Typography } from 'antd'
import type { CSSProperties } from 'react'

const { Title, Text } = Typography

interface StatCardProps {
	title: string
	value: number
	icon: React.ReactNode
	color?: 'purple' | 'cyan'
}

const colors = {
	purple: {
		bg: '#f9f0ff',
		border: '#d3adf7',
		text: '#722ed1',
		darkBg: '#1a1325',
		darkText: '#642ab5'
	},
	cyan: {
		bg: '#e6fffb',
		border: '#87e8de',
		text: '#13c2c2',
		darkBg: '#112123',
		darkText: '#13a8a8'
	}
}

const StatCard = ({ title, value, icon, color = 'purple' }: StatCardProps) => {
	const colorSet = colors[color]

	const cardStyle: CSSProperties = {
		height: '100%',
		borderRadius: '8px',
		borderLeft: `3px solid ${colorSet.text}`,
		transition: 'all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1)'
	}

	const iconStyle: CSSProperties = {
		display: 'flex',
		alignItems: 'center',
		justifyContent: 'center',
		width: '48px',
		height: '48px',
		borderRadius: '8px',
		backgroundColor: colorSet.bg,
		color: colorSet.text,
		transition: 'all 0.3s'
	}

	return (
		<Card
			hoverable
			style={cardStyle}
			bodyStyle={{
				padding: '16px',
				height: '100%',
				display: 'flex',
				flexDirection: 'column'
			}}
			className='stat-card'>
			<div className='flex justify-between items-start mb-2'>
				<div style={iconStyle}>{icon}</div>
				<Title
					level={3}
					style={{ margin: 0, color: colorSet.text }}>
					{value.toLocaleString()}
				</Title>
			</div>
			<Text
				className='mt-auto'
				type='secondary'
				style={{ fontSize: '14px' }}>
				{title}
			</Text>
		</Card>
	)
}

export default StatCard
