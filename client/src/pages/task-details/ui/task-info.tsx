import { Card } from 'antd'
import { InfoItem } from '../ui/info-item'
import dayjs from 'dayjs'
import { DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'

interface TaskInfoProps {
	task: any
}

export const TaskInfo = ({ task }: TaskInfoProps) => {
	// Birinchi (root) taskni topish uchun rekursiv funksiya
	const findRootTask = (currentTask: any): any => {
		if (!currentTask.Parent) {
			return currentTask
		}
		return findRootTask(currentTask.Parent)
	}

	// Parent tasklar ierarxiyasini olish
	const getParentHierarchy = (currentTask: any): string[] => {
		const hierarchy: string[] = []
		let current = currentTask.Parent

		while (current) {
			if (current.CreatedByOrganization?.name) {
				hierarchy.push(current.CreatedByOrganization.name)
			}
			current = current.Parent
		}

		return hierarchy.reverse() // Yuqoridan pastga tartib
	}

	const rootTask = findRootTask(task)
	const parentHierarchy = getParentHierarchy(task)

	return (
		<Card className='w-full p-6 space-y-6 rounded-xl shadow-md bg-white dark:bg-[#1a1a1a] flex-1'>
			<h2 className='text-xl font-bold mb-4 text-gray-800 dark:text-gray-100'>Topshiriq haqida</h2>
			<div className='flex flex-col divide-y divide-gray-200 dark:divide-gray-700'>
				<div className='pb-4'>
					<InfoItem
						title='Topshiriq beruvchi'
						value={task.CreatedByOrganization?.name + ': ' + task.CreatedBy?.fullName}
					/>
				</div>

				{/* Nazoratchi tashkilot */}
				{rootTask && rootTask.id !== task.id && (
					<div className='py-4'>
						<InfoItem
							title='Nazoratchi tashkilot'
							value={rootTask.CreatedByOrganization?.name}
						/>
					</div>
				)}

				{/* Parent tasklar ierarxiyasi */}
				{parentHierarchy.length > 0 && (
					<div className='py-4'>
						<div className='flex flex-col gap-2'>
							<span className='text-gray-500 font-medium'>Ierarxiya:</span>
							<div className='flex flex-col gap-1'>
								{parentHierarchy.map((orgName, index) => (
									<div
										key={index}
										className='flex items-center gap-2'>
										<span
											className='text-gray-400'
											style={{ marginLeft: `${index * 16}px` }}>
											{'└─ '}
										</span>
										<span className='text-base font-semibold text-gray-900 dark:text-gray-100'>
											{orgName}
										</span>
									</div>
								))}
								<div className='flex items-center gap-2'>
									<span
										className='text-gray-400'
										style={{ marginLeft: `${parentHierarchy.length * 16}px` }}>
										{'└─ '}
									</span>
									<span className='text-base font-semibold text-blue-600 dark:text-blue-400'>
										{task.CreatedByOrganization?.name} (joriy)
									</span>
								</div>
							</div>
						</div>
					</div>
				)}

				<div className='py-4'>
					<InfoItem
						title='Topshiriq kategoriyasi'
						value={task.TaskType?.name}
					/>
				</div>
				<div className='py-4'>
					<InfoItem
						title='Topshiriq holati'
						value={task.TaskState?.name}
					/>
				</div>
				<div className='pt-4'>
					<InfoItem
						title='Muddati'
						value={
							task.dueDate
								? dayjs(task.dueDate).tz(DEFAULT_TIMEZONE).format('DD.MM.YYYY HH:mm')
								: 'Belgilanmagan'
						}
					/>
				</div>
			</div>
		</Card>
	)
}
