import { Form, Select, Col } from 'antd'
import { TUser } from '@/config/queries/users/get-all.queries'
import { TRecipent } from '../task-create'

type TEmployeeSelectorProps = {
	recipientType: string
	recipent: TRecipent
	recipentIndex: number
	users: { [key: string]: TUser[] }
	isPending: boolean
	setRecipents: React.Dispatch<React.SetStateAction<TRecipent[]>>
	hasNextPage?: boolean
	fetchNextPage?: () => void
}

export const EmployeeSelector = ({
	recipientType,
	recipent,
	recipentIndex,
	users,
	isPending,
	setRecipents,
	hasNextPage,
	fetchNextPage
}: TEmployeeSelectorProps) => {
	if (recipientType !== 'employees') return null

	return (
		<Col
			xs={24}
			sm={24}
			md={11}>
			<Form.Item
				label='Ishchisi'
				labelCol={{ span: 24 }}
				rules={[{ required: true }]}>
				<Select
					mode='multiple'
					loading={isPending}
					className='rounded-md'
					value={recipent?.userId || []}
					onDeselect={userId =>
						setRecipents(prev =>
							prev.map((item, index) =>
								index === recipentIndex
									? { ...item, userId: item.userId.filter(id => id !== userId) }
									: item
							)
						)
					}
					onSelect={userId =>
						setRecipents(prev =>
							prev.map((item, index) =>
								index === recipentIndex
									? { ...item, userId: [...(item?.userId ?? []), userId] }
									: item
							)
						)
					}
					onPopupScroll={e => {
						const target = e.target as HTMLDivElement
						if (
							target.scrollTop + target.clientHeight >= target.scrollHeight - 5 &&
							hasNextPage &&
							fetchNextPage
						) {
							fetchNextPage()
						}
					}}
					options={users?.[recipent.organizationId!]?.map(user => ({
						label: user.fullName,
						value: user.id
					}))}
					showSearch
				/>
			</Form.Item>
		</Col>
	)
}
