import { axiosPrivate } from '@/config/api'
import { organizationEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { TOrganization } from './get-all.queries'
import { AxiosError, AxiosResponse } from 'axios'
import { TApiErrorResponse } from '@/shared/types/api-error.type'
import { notification } from 'antd'
import { TApiUpdate } from '@/shared/types/api-update.type'

export type TOrganizationRequest = {
	name: string
	description: string
	address: string
	sector: number
	sectorResponsible: number
	typeId: string
	regionId: string
	gradeId: string
}
export type TOrganizationResponse = TOrganization

export const useCreateChildOrganization = (orgId?: string) => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({ data, id }: TApiUpdate<TOrganizationRequest>) => {
			return await axiosPrivate.post(organizationEndpoints.organizationChildrenCreate(id), data)
		},
		onSuccess: async (response: AxiosResponse<TOrganizationResponse>) => {
			console.log(response)
			queryClient.invalidateQueries({ queryKey: [organizationEndpoints.all] })
			if (orgId) {
				queryClient.invalidateQueries({ queryKey: [organizationEndpoints.one(orgId)] })
			}
			notification.success({ message: 'Tashkilot muaffaqiyatli yaratildi!' })
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response?.data.message)
			notification.error({ message: 'Tashkilot yaratishda xatolik yuz berdi!' })
		}
	})
}
