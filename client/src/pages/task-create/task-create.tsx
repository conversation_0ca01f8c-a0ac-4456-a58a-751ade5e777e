import TaskCreateForm from './ui/task-create-form'
import { useState } from 'react'

export type TRecipent = {
	organizationId: string
	userId: string[]
	selectedOrganizations?: string[]
	positionId?: string
}

const TaskCreate = () => {
	const [recipents, setRecipents] = useState<TRecipent[]>([
		{
			organizationId: '',
			userId: []
		}
	])

	return (
		<TaskCreateForm
			recipents={recipents}
			setRecipents={setRecipents}
		/>
	)
}

export default TaskCreate
