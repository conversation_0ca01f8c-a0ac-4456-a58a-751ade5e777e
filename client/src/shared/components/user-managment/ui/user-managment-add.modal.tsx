import { Form, Modal } from 'antd'
import { useFaceIDAddStore } from '../utils/face-id-add-modal-store'
import { FaceIdAddForm } from './user-managment-add.form'
import { getfaceIdFormValidation } from '../config/validations'
import { useState } from 'react'
import { useAddUsersToOrganization } from '@/config/queries/organizations/add-users.queries'

export const FaceIdAddModal = () => {
	const [form] = Form.useForm()
	const { open, id: orgId, onClose, who } = useFaceIDAddStore()
	const faceIdDeviceValidation = getfaceIdFormValidation()
	const [userIds, setUserIds] = useState<string[]>([])
	const { mutateAsync: addUser, isPending } = useAddUsersToOrganization(orgId!)

	const close = () => {
		setUserIds([])
		form.resetFields()
		onClose()
	}

	const onFinish = () => {
		if (orgId)
			addUser({
				orgId,
				data: {
					[who]: userIds
				},
				who
			}).then(close)
	}

	return (
		<Modal
			open={open}
			onClose={close}
			onCancel={close}
			okText='Yuborish'
			onOk={() => form.submit()}
			okButtonProps={{ disabled: isPending }}
			cancelButtonProps={{ disabled: isPending }}>
			<Form
				form={form}
				layout='vertical'
				onFinish={onFinish}>
				<FaceIdAddForm
					validation={faceIdDeviceValidation}
					userIds={userIds}
					setUserIds={setUserIds}
				/>
			</Form>
		</Modal>
	)
}
