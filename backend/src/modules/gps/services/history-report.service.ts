import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/common';

interface OrganizationChild {
  id: string;
  name: string;
  regionId: string | null;
  districtId: string | null;
  sectionId: string | null;
  grade: {
    level: number;
  };
}

interface HistoryReportResult {
  organization: {
    id: string;
    name: string;
    regionId: string | null;
    sectionId: string | null;
    districtId: string | null;
    grade: {
      level: number;
    };
  };
  children?: OrganizationChild[];
  length?: number;
  districts?: any[];
  sections?: any[];
  workers?: any[];
}

@Injectable()
export class HistoryReportService {
  logger = new Logger(HistoryReportService.name);

  constructor(private readonly prismaService: PrismaService) {}

  async getHistoryReport({
    organizationId,
    regionId,
    districtId,
    sectionId,
    isInArea,
    positionId,
    isDisabled,
  }: {
    organizationId: string;
    regionId?: string;
    districtId?: string;
    sectionId?: string;
    isInArea?: boolean;
    positionId?: string;
    isDisabled?: boolean;
  }) {
    const start = performance.now();
    this.logger.debug('getHistoryReport boshlandi');
    const organizationStart = performance.now();
    const organization = await this.prismaService.organization.findUnique({
      where: {
        id: organizationId,
      },
      select: {
        id: true,
        name: true,
        regionId: true,
        sectionId: true,
        districtId: true,
        grade: {
          select: {
            level: true,
          },
        },
      },
    });
    this.logger.debug(`organization.findUnique: ${(performance.now() - organizationStart).toFixed(2)} ms`);
    if (!organization) {
      throw new NotFoundException(`Organization with ID ${organizationId} not found`);
    }
    const level = organization.grade.level;
    const childrenStart = performance.now();
    // const children = await this.getChildrenByParentId(organizationId, level, regionId, districtId, sectionId);
    // this.logger.debug(`getChildrenByParentId: ${(performance.now() - childrenStart).toFixed(2)} ms`);
    // if (!sectionId) {
    //   children.push({
    //     id: organization.id,
    //   });
    // }

    const childrednew = await this.prismaService.organization.findMany({
      where: {
        status: 'ACTIVE',
        grade: {
          level: 30,
        },
        regionId: regionId ? regionId : undefined,
        districtId: districtId ? `${regionId}${districtId}` : undefined,
      },
      select: {
        id: true,
        name: true,
        regionId: true,
        sectionId: true,
        districtId: true,
        grade: {
          select: {
            level: true,
          },
        },
      },
    });

    this.logger.debug(childrednew.length);
    const usersStart = performance.now();
    // const users = await this.prismaService.user.findMany({
    //   where: {
    //     status: 'ACTIVE',
    //     Organization: {
    //       some: {
    //         id: {
    //           in: childrednew.map((child) => child.id),
    //         },
    //       },
    //     },
    //     positionId: positionId ? positionId : undefined,
    //     GPSLocationReport: {
    //       some: {
    //         isInArea: isInArea !== undefined ? isInArea : undefined,
    //       },
    //     },
    //   },
    //   select: {
    //     id: true,
    //     fullName: true,
    //     position: {
    //       select: {
    //         id: true,
    //         name: true,
    //       },
    //     },
    //     phone: true,
    //     Organization: {
    //       select: {
    //         id: true,
    //         name: true,
    //       },
    //     },
    //     GPSLocationReport: {
    //       orderBy: {
    //         id: 'desc',
    //       },
    //       take: 1,
    //       select: {
    //         id: true,
    //         lat: true,
    //         lng: true,
    //         isInArea: true,
    //         type: true,
    //         createdAt: true,
    //       },
    //     },
    //   },
    // });

    const result1 = (await this.prismaService.$queryRawUnsafe(
      `
        SELECT DISTINCT
        ON("User".id)
          "User".id,
          "User"."fullName",
          "GPSLocationReport"."lat",
          "GPSLocationReport"."lng",
          "GPSLocationReport"."createdAt",
          "GPSLocationReport"."isInArea",
          "GPSLocationReport"."type",
          "GPSLocationReport"."status",
          "GPSLocationReport"."userId",
          "OrganizationTypePosition"."name" AS "positionName",
          "OrganizationTypePosition"."id" AS "positionId",
          "Organization"."name" AS "organizationName"
        FROM "User"
          INNER JOIN "GPSLocationReport"
        ON "GPSLocationReport"."userId" = "User".id
          INNER JOIN "OrganizationTypePosition" ON "User"."positionId" = "OrganizationTypePosition".id
          JOIN "_User" uw ON "User".id = uw."B"
          INNER JOIN "Organization" ON "Organization".id = uw."A"
        WHERE
          "Organization"."id" IN (
          SELECT "Organization"."id"
          FROM "Organization"
          WHERE "gradeId" = (
          SELECT id FROM "Grade"
          WHERE level = '30'
          AND status = 'ACTIVE'
          LIMIT 1
          )
          AND "regionId" = $1
          AND "districtId" = $2
          AND "Organization".status = 'ACTIVE'

          )
          AND "User".status = 'ACTIVE'
        ORDER BY "User".id, "GPSLocationReport"."createdAt" DESC
      `,
      regionId,
      regionId + districtId,
    )) as any[];

    this.logger.debug(`user.findMany: ${(performance.now() - usersStart).toFixed(2)} ms`);
    const result = {
      history: result1
        .filter((user) => {
          if (isDisabled !== undefined) {
            const location = user;
            if (isDisabled) {
              return location.type === 'DISABLED';
            }
            return location.type !== 'DISABLED';
          }
          return true;
        })
        .filter((user) => {
          if (positionId) {
            return user.positionId === positionId;
          }
          return true;
        })
        .filter((user) => {
          if (isInArea !== undefined) {
            const location = user;
            return location.isInArea === isInArea;
          }
          return true;
        })
        .map((user) => {
          const location = user;
          return {
            id: user.id,
            fullName: user.fullName,
            position: { name: user.positionName },
            organization: [{ name: user.organizationName }],
            lat: location?.lat ?? 0,
            lng: location?.lng ?? 0,
            createdAt: location.createdAt,
            isInArea: location.isInArea,
            type: location.type,
          };
        }),
    };
    this.logger.debug(`getHistoryReport jami: ${(performance.now() - start).toFixed(2)} ms`);
    return result;
  }

  private async getChildrenByParentId(
    parentId: string,
    parentGrade: number,
    regionId?: string,
    districtId?: string,
    sectionId?: string,
  ) {
    const start = performance.now();
    this.logger.debug('getChildrenByParentId boshlandi');
    const result = [];
    const visited = new Set<string>();
    await this.collectChildrenRecursively(parentId, parentGrade, result, visited, regionId, districtId, sectionId);
    this.logger.debug(`getChildrenByParentId jami: ${(performance.now() - start).toFixed(2)} ms`);
    return result;
  }

  private async collectChildrenRecursively(
    parentId: string,
    parentGrade: number,
    result: OrganizationChild[],
    visited: Set<string>,
    regionId?: string,
    districtId?: string,
    sectionId?: string,
  ): Promise<void> {
    const start = performance.now();
    if (visited.has(parentId)) {
      return;
    }
    visited.add(parentId);
    const whereCondition: any = {
      parentId: parentId,
      status: 'ACTIVE',
      grade: {
        level: {
          gt: parentGrade,
        },
      },
    };
    if (regionId) {
      whereCondition.regionId = regionId;
    }
    if (districtId) {
      whereCondition.districtId = `${regionId}${districtId}`;
    }
    if (sectionId) {
      whereCondition.sectionId = `${regionId}${districtId}${sectionId}`;
    }
    const childrenStart = performance.now();
    const children = await this.prismaService.organization.findMany({
      where: whereCondition,
      select: {
        id: true,
        name: true,
        regionId: true,
        grade: {
          select: {
            level: true,
          },
        },
        districtId: true,
        sectionId: true,
      },
      orderBy: {
        name: 'asc',
      },
    });
    this.logger.debug(
      `organization.findMany (parentId=${parentId}): ${(performance.now() - childrenStart).toFixed(2)} ms`,
    );
    for (const child of children) {
      result.push(child);
      await this.collectChildrenRecursively(
        child.id,
        child.grade.level,
        result,
        visited,
        regionId,
        districtId,
        sectionId,
      );
    }
    this.logger.debug(
      `collectChildrenRecursively (parentId=${parentId}): ${(performance.now() - start).toFixed(2)} ms`,
    );
  }
}
