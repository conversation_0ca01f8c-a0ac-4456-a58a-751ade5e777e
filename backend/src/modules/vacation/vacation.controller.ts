import { <PERSON>, Get, Post, Body, Patch, Param, Delete, Query, Put } from '@nestjs/common';
import { VacationService } from './vacation.service';
import { CreateVacationDto } from './dto/create-vacation.dto';
import { UpdateVacationDto } from './dto/update-vacation.dto';
import { ProtectedRoute } from 'src/common';
import { BaseQueryParams } from 'src/helpers/types';

@Controller('vacation')
@ProtectedRoute({ isPublic: false })
export class VacationController {
  constructor(private readonly vacationService: VacationService) {}

  @Post()
  create(@Body() createVacationDto: CreateVacationDto) {
    return this.vacationService.create(createVacationDto);
  }

  @Get()
  findAll(@Query() query: BaseQueryParams) {
    return this.vacationService.findAll(query);
  }

  @Get('cron')
  cron() {
    this.vacationService.cron();
    return new Date();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.vacationService.findOne(id);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateVacationDto: UpdateVacationDto) {
    return this.vacationService.update(id, updateVacationDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.vacationService.remove(id);
  }
}
