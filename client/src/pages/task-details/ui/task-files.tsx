import { Divider } from 'antd'
import { Link } from 'react-router-dom'
import { getFileUrl } from '@/shared/utils/getFileUrl'

interface TaskFilesProps {
	files: any[]
}

export const TaskFiles = ({ files }: TaskFilesProps) => {
	return (
		<>
			<Divider />
			<div className='px-3'>
				<h1 className='font-bold text-lg mb-4'>To`liqroq ma`lumot uchun fayllar: </h1>
				<ul className='flex flex-col gap-y-3'>
					{files.map(file => (
						<Link
							key={file.id}
							className='w-fit'
							to={getFileUrl(file.path)}
							target='_blank'>
							{file.slug}
						</Link>
					))}
				</ul>
			</div>
			<Divider />
		</>
	)
}
