import { Inject, Injectable, Logger, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { AuthDto } from './dto/auth.dto';
import { PrismaService } from 'src/common';
import * as bcrypt from 'bcrypt';
import { JwtService } from '@nestjs/jwt';
import { SetLocationBodyDto } from './dto/sethistory.dto';
import { CoordsService } from '../coords/coords.service';
import { ChangePassword } from './dto/change-password';
import { ConfigService } from '@nestjs/config';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { GPS_STATUS } from './interfaces/gps';
import {
  GPS_INFRACTION_CHECK,
  GPS_OFFLINE_CHECK,
  GPS_OFFLINE_CHECK_TIMEOUT_MINUTES,
  GpsOfflineCheckData,
} from './interfaces/gps-status';
import { DEFAULT_TIMEZONE, TimezoneHelper } from 'src/helpers/timezone.helper';
import { HistoryReportService } from './services/history-report.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { TelegramEventKeys } from '../telegram-bot/interfaces/bot.interfaces';
import * as dayjs from 'dayjs';

interface GetHistoryParams {
  organizationId: string;
  regionId?: string;
  districtId?: string;
  sectionId?: string;
  isInArea?: boolean;
  positionId?: string;
  isDisabled?: boolean;
}

interface LocationData {
  userId: string;
  lat: number;
  lng: number;
  isInArea: boolean;
  type: 'IN' | 'OUT';
  todayWorkingSchedule?: {
    day: number;
    startTime: string;
    endTime: string;
  };
}

@Injectable()
export class GpsService {
  private readonly OFFLINE_TIMEOUT_MINUTES = GPS_OFFLINE_CHECK_TIMEOUT_MINUTES;
  private readonly logger = new Logger(GpsService.name);

  constructor(
    private prismaService: PrismaService,
    private jwtService: JwtService,
    private coordsService: CoordsService,
    private configService: ConfigService,
    private historyReportService: HistoryReportService,
    private eventEmitter: EventEmitter2,
    @Inject(CACHE_MANAGER) private readonly cacheService: Cache,
    @InjectQueue(GPS_STATUS) private readonly gpsStatusQueue: Queue,
  ) {}

  async auth(authDto: AuthDto) {
    const { username, password, device_id } = authDto;

    const isExistsUser = await this.prismaService.user.findUnique({
      where: {
        username,
      },
    });

    if (!isExistsUser) {
      return {
        errors: [
          {
            field: 'username',
            message: 'Пользователь не найден',
          },
        ],
      };
    }

    const isPasswordCorrect = await bcrypt.compare(password, isExistsUser.password);

    if (!isPasswordCorrect) {
      return {
        errors: [
          {
            field: 'password',
            message: 'Неверный пароль',
          },
        ],
      };
    }

    if (device_id !== isExistsUser.imei) {
      return {
        errors: [
          {
            field: 'device_id',
            message: 'Неверный device_id',
          },
        ],
      };
    }

    const token = this.jwtService.sign({ id: isExistsUser.id });

    return {
      token,
    };
  }

  async me(token: string) {
    const payload = this.jwtService.decode(token) as { id: string };
    const isExistsUser = await this.prismaService.user.findUnique({
      where: {
        id: payload.id,
      },
    });

    if (!isExistsUser) {
      throw new UnauthorizedException('Пользователь не найден');
    }
    return {
      username: isExistsUser.username,
      name: isExistsUser.fullName,
      image: 'http://192.168.3.4:3000/static/avatar.png',
    };
  }

  async getCoords(token: string) {
    const payload = this.jwtService.decode(token) as { id: string };
    const user = await this.prismaService.user.findUnique({
      where: {
        id: payload.id,
      },
      select: {
        id: true,
        Organization: {
          where: {
            grade: { level: 30 },
            sectionId: {
              not: null,
            },
          },
          select: {
            id: true,
            name: true,
            sectionId: true,
          },
        },
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const sections = user.Organization.map((org) => {
      return org.sectionId;
    });

    return {
      coords: await Promise.all(
        sections.map(async (section) => {
          const [regionId, districtId, sectionId] = [section.slice(0, 4), section.slice(4, 7), section.slice(7)];
          const res = await this.coordsService.getCoords(regionId, districtId, sectionId);
          return res[0].area[0];
        }),
      ),
    };
  }

  async setLocation(token: string, history: SetLocationBodyDto) {
    const payload = this.jwtService.decode(token) as { id: string };

    const isExistsUser = await this.prismaService.user.findUnique({
      where: {
        id: payload.id,
      },
      select: {
        id: true,
        UserWorkingSchedule: {
          select: {
            days: {
              select: {
                day: true,
                startTime: true,
                endTime: true,
              },
            },
          },
        },
      },
    });

    if (!isExistsUser) {
      throw new UnauthorizedException('Пользователь не найден');
    }

    const todayNumber = TimezoneHelper.now().day();
    const today = todayNumber === 0 ? 7 : todayNumber;
    const todayWorkingSchedule = isExistsUser.UserWorkingSchedule.days.find((uw) => uw.day == today);

    const locationData: LocationData[] = history.locations.map((location) => {
      return {
        userId: isExistsUser.id,
        lat: +location.lat,
        lng: +location.lng,
        isInArea: location.in_area,
        type: location.in_area ? 'IN' : 'OUT',
        todayWorkingSchedule,
      };
    });

    await this.prismaService.gPSLocation.createMany({
      data: locationData.map((location) => ({
        userId: location.userId,
        lat: location.lat,
        lng: location.lng,
        isInArea: location.isInArea,
      })),
    });

    const now = new Date();

    await this.scheduleOfflineCheck(isExistsUser.id, now);

    await this.processLocationReports(isExistsUser, locationData);

    const cacheKey = `user-gps-status-${isExistsUser.id}`;
    await this.cacheService.del(cacheKey);

    const lastLocationData = locationData.at(-1);

    if (lastLocationData.type === 'OUT') {
      await this.gpsStatusQueue.add(
        GPS_INFRACTION_CHECK,
        {
          userId: lastLocationData.userId,
          outsideStartTime: new Date(),
          lat: lastLocationData.lat,
          lng: lastLocationData.lng,
        },
        {
          delay: 15 * 60 * 1000,
          jobId: `infraction-check-${lastLocationData.userId}`,
          removeOnComplete: 50,
          removeOnFail: 10,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
        },
      );
    } else if (lastLocationData.type === 'IN') {
      try {
        await this.gpsStatusQueue.remove(`infraction-check-${lastLocationData.userId}`);
        this.logger.warn(`Removed previous job: infraction-check-${lastLocationData.userId}`);
      } catch (removeError) {
        this.logger.warn(`Could not remove previous job: ${removeError.message}`);
      }
    }

    return {
      success: true,
    };
  }

  private async scheduleOfflineCheck(userId: string, lastLocationTime: Date) {
    try {
      const jobId = `offline-check-${userId}`;
      const activeJobs = await this.gpsStatusQueue.getJob(jobId);

      if (activeJobs) {
        try {
          await activeJobs.remove();
        } catch (removeError) {
          this.logger.warn(`Could not remove previous job: ${removeError.message}`);
        }
      }

      await this.gpsStatusQueue.add(
        GPS_OFFLINE_CHECK,
        {
          userId,
          lastLocationTime: lastLocationTime,
          timeoutMinutes: this.OFFLINE_TIMEOUT_MINUTES,
        } as GpsOfflineCheckData,
        {
          delay: this.OFFLINE_TIMEOUT_MINUTES * 60 * 1000,
          removeOnComplete: 50,
          removeOnFail: 10,
          jobId: jobId,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
        },
      );
    } catch (error) {
      this.logger.error(`Error scheduling offline check for user ${userId}: ${error.message}`, JSON.stringify(error));
      throw error;
    }
  }

  private async processLocationReports(user: any, locationData: LocationData[]) {
    const lastActiveReport = await this.prismaService.gPSLocationReport.findFirst({
      where: {
        status: 'ACTIVE',
        userId: user.id,
      },
      orderBy: { createdAt: 'desc' },
      select: {
        type: true,
        isInArea: true,
        createdAt: true,
      },
    });

    const lastLocation = locationData.at(-1);
    const now = TimezoneHelper.now();

    if (!lastLocation.todayWorkingSchedule) {
      if (lastActiveReport?.type !== 'NOT_WORKING_TIME') {
        await this.prismaService.gPSLocationReport.create({
          data: {
            userId: user.id,
            type: 'NOT_WORKING_TIME',
            isInArea: lastLocation.isInArea,
            lat: lastLocation.lat,
            lng: lastLocation.lng,
          },
        });
      }
      return;
    }

    if (lastLocation.todayWorkingSchedule) {
      const startTime = dayjs(lastLocation.todayWorkingSchedule.startTime, 'HH:mm').tz(DEFAULT_TIMEZONE);
      const endTime = dayjs(lastLocation.todayWorkingSchedule.endTime, 'HH:mm').tz(DEFAULT_TIMEZONE);
      const isWorkingTime = now.isBetween(startTime, endTime);

      if (!isWorkingTime) {
        if (lastActiveReport?.type !== 'NOT_WORKING_TIME') {
          await this.prismaService.gPSLocationReport.create({
            data: {
              userId: user.id,
              type: 'NOT_WORKING_TIME',
              isInArea: lastLocation.isInArea,
              lat: lastLocation.lat,
              lng: lastLocation.lng,
            },
          });
        }
        return;
      }
    }

    if (lastActiveReport?.type === 'DISABLED') {
      await this.prismaService.gPSLocationReport.create({
        data: {
          userId: user.id,
          type: lastLocation.type,
          isInArea: lastLocation.isInArea,
          lat: lastLocation.lat,
          lng: lastLocation.lng,
        },
      });

      if (!lastLocation.isInArea) {
        this.eventEmitter.emit(TelegramEventKeys.TELEGRAM_OUT, { userId: user.id });
      }

      return;
    }

    const lastType = lastActiveReport?.type;
    const currentType = lastLocation.type;

    if (lastType !== currentType) {
      await this.prismaService.gPSLocationReport.create({
        data: {
          userId: user.id,
          type: currentType,
          isInArea: lastLocation.isInArea,
          lat: lastLocation.lat,
          lng: lastLocation.lng,
        },
      });

      if (currentType === 'OUT') {
        this.eventEmitter.emit(TelegramEventKeys.TELEGRAM_OUT, { userId: user.id });
      }
    }
  }

  async getHistory(params: GetHistoryParams) {
    const { organizationId, sectionId, districtId, regionId, isInArea, positionId, isDisabled } = params;
    return this.historyReportService.getHistoryReport({
      organizationId,
      regionId,
      districtId,
      sectionId,
      isInArea,
      positionId,
      isDisabled,
    });
  }

  async getUserDailyHistory(userId: string, day?: string, from?: string, to?: string) {
    const correctFrom = from ? from.split(':').map(Number) : [0, 0];
    const correctTo = to ? to.split(':').map(Number) : [23, 59];
    const startDateWithFromTime = dayjs(day).add(correctFrom[0], 'hour').add(correctFrom[1], 'minute');
    const endDateWithToTime = dayjs(day).add(correctTo[0], 'hour').add(correctTo[1], 'minute');
    const startDate = startDateWithFromTime.tz(DEFAULT_TIMEZONE);
    const endDate = endDateWithToTime.tz(DEFAULT_TIMEZONE);

    const history = await this.prismaService.gPSLocation.findMany({
      where: {
        userId: userId,
        createdAt: {
          gte: startDate.toDate(),
          lt: endDate.toDate(),
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    const historyReport = await this.prismaService.gPSLocationReport.findMany({
      where: {
        userId: userId,
        createdAt: {
          gte: startDate.toDate(),
          lt: endDate.toDate(),
        },
      },
      include: {
        User: {
          select: {
            id: true,
            fullName: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    const user = await this.prismaService.user.findUnique({
      where: { id: userId },
      select: {
        fullName: true,
        position: {
          select: {
            name: true,
          },
        },
        phone: true,
        Organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return {
      history,
      historyReport,
      user: {
        id: userId,
        fullName: user.fullName,
        position: user.position.name,
        phone: user.phone,
        organization: user.Organization,
      },
    };
  }

  async changePassword(token: string, { new_password, old_password }: ChangePassword) {
    const { id: userId } = this.jwtService.decode(token) as { id: string };
    const user = await this.prismaService.user.findUnique({ where: { status: 'ACTIVE', id: userId } });

    if (!user || !(await bcrypt.compare(old_password, user.password))) {
      throw new UnauthorizedException('Username yoki parol noto‘g‘ri');
    }

    await this.prismaService.user.update({
      where: { id: userId },
      data: { password: bcrypt.hashSync(new_password, 10) },
    });

    const payload = {
      id: user.id,
    };

    const accessToken = this.jwtService.sign(payload, {
      secret: this.configService.get<string>('JWT_SECRET'),
      expiresIn: this.configService.get<string>('JWT_EXPIRED'),
    });

    const newRefreshToken = this.jwtService.sign(payload, {
      secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
      expiresIn: this.configService.get<string>('JWT_REFRESH_EXPIRED'),
    });

    await this.cacheService.set(`user-${user.id}`, newRefreshToken);

    return { accessToken, refreshToken: newRefreshToken };
  }

  async getUserGpsStatus(userId: string) {
    const lastLocation = await this.prismaService.gPSLocation.findFirst({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      select: {
        createdAt: true,
        isInArea: true,
      },
    });

    const lastReport = await this.prismaService.gPSLocationReport.findFirst({
      where: {
        userId,
        status: 'ACTIVE',
      },
      orderBy: { createdAt: 'desc' },
      select: {
        type: true,
        createdAt: true,
      },
    });

    const now = new Date();
    const isOffline = lastLocation
      ? (now.getTime() - lastLocation.createdAt.getTime()) / (1000 * 60) > this.OFFLINE_TIMEOUT_MINUTES
      : true;

    return {
      userId,
      lastLocation,
      lastReport,
      isOffline,
      offlineMinutes: lastLocation ? (now.getTime() - lastLocation.createdAt.getTime()) / (1000 * 60) : null,
    };
  }

  async getAllUsersGpsStatus(organizationId?: string) {
    const whereClause = organizationId
      ? {
          OR: [
            { Organization: { some: { id: organizationId } } },
            { mainOrganizationId: organizationId },
            { ResponsibleFor: { some: { id: organizationId } } },
          ],
        }
      : {};

    const users = await this.prismaService.user.findMany({
      where: {
        status: 'ACTIVE',
        ...whereClause,
      },
      select: {
        id: true,
        fullName: true,
        position: true,
        GPSLocation: {
          orderBy: { createdAt: 'desc' },
          take: 1,
          select: {
            createdAt: true,
            isInArea: true,
          },
        },
        GPSLocationReport: {
          where: { status: 'ACTIVE' },
          orderBy: { createdAt: 'desc' },
          take: 1,
          select: {
            type: true,
            createdAt: true,
          },
        },
      },
    });

    const now = new Date();

    return users.map((user) => {
      const lastLocation = user.GPSLocation[0];
      const lastReport = user.GPSLocationReport[0];

      const isOffline = lastLocation
        ? (now.getTime() - lastLocation.createdAt.getTime()) / (1000 * 60) > this.OFFLINE_TIMEOUT_MINUTES
        : true;

      return {
        userId: user.id,
        fullName: user.fullName,
        position: user.position,
        lastLocation,
        lastReport,
        isOffline,
        offlineMinutes: lastLocation ? (now.getTime() - lastLocation.createdAt.getTime()) / (1000 * 60) : null,
      };
    });
  }

  async getAllUserWithLastLocation(organizationId: string) {
    const organization = await this.prismaService.organization.findUnique({
      where: { id: organizationId },
      select: {
        regionId: true,
        districtId: true,
      },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const districtId = `${organization.districtId}`;

    const allSectors = await this.prismaService.organization.findMany({
      where: {
        sectionId: {
          startsWith: districtId,
        },
        grade: {
          level: 30,
        },
      },
      select: {
        Worker: {
          where: {
            status: 'ACTIVE',
          },
          select: {
            id: true,
            fullName: true,
            position: {
              select: {
                name: true,
              },
            },
            phone: true,
            Organization: {
              select: {
                name: true,
              },
            },
            GPSLocationReport: {
              orderBy: {
                createdAt: 'desc',
              },
              take: 1,
              select: {
                type: true,
                isInArea: true,
                createdAt: true,
              },
            },
          },
        },
      },
    });

    const uniqueWorkers = new Map<string, (typeof allSectors)[number]['Worker'][number]>();
    allSectors.forEach((sector) => {
      sector.Worker.forEach((worker) => {
        if (!uniqueWorkers.has(worker.id)) {
          uniqueWorkers.set(worker.id, worker);
        }
      });
    });

    const workersArray = Array.from(uniqueWorkers.values());

    const hasLocation = workersArray.filter((worker) => worker.GPSLocationReport.length > 0);
    const noLocation = workersArray.filter((worker) => worker.GPSLocationReport.length === 0);
    return {
      hasLocation,
      noLocation,
    };
  }
}
