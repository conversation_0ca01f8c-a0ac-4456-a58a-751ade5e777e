import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>NotEmpty, IsString } from 'class-validator';
import { PartialType } from '@nestjs/mapped-types';

export class CreateUserWorkingScheduleDto {
  @IsString()
  @IsNotEmpty()
  userId: string;

  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  days: string[];
}

export class UpdateUserWorkingScheduleDto extends PartialType(CreateUserWorkingScheduleDto) {}
