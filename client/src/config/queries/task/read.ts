import { axiosPrivate } from '@/config/api'
import { taskEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'

export const useReadTask = () => {
	const queryClient = useQueryClient()
	const { id } = useParams<{ id: string }>()

	return useMutation({
		mutationFn: async (recipientId: string) => {
			return await axiosPrivate.post(taskEndpoints.read, { recipientId })
		},
		onSuccess: () => {
			// Invalidate the task query to refresh the data
			if (id) {
				queryClient.invalidateQueries({ queryKey: [taskEndpoints.one(id)] })
			}
		}
	})
}
