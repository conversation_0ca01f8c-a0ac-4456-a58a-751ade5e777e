import { lazy } from 'react'
export const Dashboard = lazy(() => import('@/pages/dashboard/dashboard'))
export const Login = lazy(() => import('@/pages/auth/login'))
export const Organizations = lazy(() => import('@/pages/organizations/organizations'))
export const Tasktypes = lazy(() => import('@/pages/settings/task-types/task-types'))
export const Taskstates = lazy(() => import('@/pages/settings/task-states/task-states'))
export const Users = lazy(() => import('@/pages/settings/users/users'))
export const OrganizationTypes = lazy(
	() => import('@/pages/settings/organization-types/organization-types')
)
export const RecipientAsnwerType = lazy(
	() => import('@/pages/settings/recipient-answer-type/recipient-asnwer-type')
)
export const UnderControlOrganizations = lazy(
	() => import('@/pages/under-control-organization/under-control-organization')
)
export const OrganizationPositionTypes = lazy(
	() => import('@/pages/settings/organization-position-types/organization-position-types')
)
export const OrganizationDetails = lazy(
	() => import('@/pages/organization-details/organization-details')
)
export const UserWorkingScheduleDay = lazy(
	() => import('@/pages/settings/user-working-schedule-day/user-working-schedule-day')
)
export const TaskCreate = lazy(() => import('@/pages/task-create/task-create'))
export const TaskOutgoing = lazy(() => import('@/pages/tasks-outgoing/task-outgoing'))
export const TaskIncomming = lazy(() => import('@/pages/tasks-incomming/task-incomming'))
export const UserDetails = lazy(() => import('@/pages/settings/users/ui/user-details'))
export const TaskDetails = lazy(() => import('@/pages/task-details/task-details'))
export const SubTaskCreate = lazy(() => import('@/pages/subtask-create/subtask-create'))
export const Employee = lazy(() => import('@/pages/settings/employee/employee'))
export const EmployeeDetails = lazy(() => import('@/pages/settings/employee/ui/employee-details'))
export const Profile = lazy(() => import('@/pages/settings/profile/profile'))
export const TaskRecipient = lazy(() => import('@/pages/task-recipient/task-recipient'))
export const FaceId = lazy(() => import('@/pages/face-id/face-id'))
export const Map = lazy(() => import('@/pages/map/map'))
export const MapLocationUser = lazy(() => import('@/pages/map-location-user/map-location-user'))
export const Attendance = lazy(() => import('@/pages/attendance/attendance'))
export const Vacancies = lazy(() => import('@/pages/settings/vacancies/vacancies'))
export const Archives = lazy(() => import('@/pages/archives/archives'))
export const AttendancePosition = lazy(() => import('@/pages/attendance/attendance-position'))
export const SectionStatsVacancyTable = lazy(
	() => import('@/pages/dashboard-details/section-stats-vacancy-table')
)
export const SectionStatsDisabledTable = lazy(
	() => import('@/pages/dashboard-details/section-stats-disabled-table')
)
export const SectionStatsEmployeeTable = lazy(
	() => import('@/pages/dashboard-details/section-stats-employee-table')
)
export const SectionStatsInAreaTable = lazy(
	() => import('@/pages/dashboard-details/section-stats-in-area-table')
)
export const SectionStatsLateAttendanceTable = lazy(
	() => import('@/pages/dashboard-details/section-stats-late-attendance-table')
)
export const SectionStatsNoAttendanceTable = lazy(
	() => import('@/pages/dashboard-details/section-stats-no-attendance-table')
)
export const SectionStatsOutAreaTable = lazy(
	() => import('@/pages/dashboard-details/section-stats-out-area-table')
)
export const SectionStatsPatientsTable = lazy(
	() => import('@/pages/dashboard-details/section-stats-patients-table')
)
export const SectionStatsPositionTable = lazy(
	() => import('@/pages/dashboard-details/section-stats-position-table')
)
export const SectionStatsSectionTable = lazy(
	() => import('@/pages/dashboard-details/section-stats-section-table')
)
export const SectionStatsVacationTable = lazy(
	() => import('@/pages/dashboard-details/section-stats-vacation-table')
)
export const Sectors = lazy(() => import('@/pages/sectors/sectors'))
export const TaskController = lazy(() => import('@/pages/task-controller/task-controller'))
export const Infraction = lazy(() => import('@/pages/infraction/infraction'))
export const InfractionDetails = lazy(() => import('@/pages/infraction/ui/infraction-details'))
