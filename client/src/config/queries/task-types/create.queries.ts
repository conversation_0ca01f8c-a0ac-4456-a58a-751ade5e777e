import { axiosPrivate } from '@/config/api'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { taskTypesEndpoints } from '@/config/api/endpoints'
import { notification } from 'antd'

export interface TaskTypes {
	id?: string
	name: string
}

export const useCreateTaskType = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async (data: TaskTypes) =>
			(await axiosPrivate.post<ApiResponse<TaskTypes>>(taskTypesEndpoints.all, data)).data,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [taskTypesEndpoints.all] })
			notification.success({
				message: '<PERSON><PERSON><PERSON>aq<PERSON><PERSON>i yaratildi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: '<PERSON><PERSON><PERSON><PERSON> xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
