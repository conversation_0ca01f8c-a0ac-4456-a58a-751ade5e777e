import { RecipientAnswerState } from '@prisma/client';
import { IsEnum, IsOptional, IsString } from 'class-validator';

export class AnswerDto {
  @IsString()
  recipientId: string;

  @IsString()
  description: string;

  @IsString()
  @IsOptional()
  typeId?: string;

  @IsString({ each: true })
  @IsOptional()
  files?: string[];
}

export class AnswerStateDto {
  @IsEnum(RecipientAnswerState)
  state: RecipientAnswerState;

  @IsString()
  @IsOptional()
  rejectReason?: string;
}
