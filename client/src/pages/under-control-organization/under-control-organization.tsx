import { UserManagmentTab } from '@/shared/components/user-managment-tab/user-managment-modal'
import { OrganizationDetails } from '../organizations/ui/organization-details'
import { UnderControlOrganizationTable } from './under-control-organization-table'
import { Tabs } from 'antd'

const UnderControlOrganization = () => {
	return (
		<main>
			<OrganizationDetails />
			<div className='w-full flex flex-col gap-y-4'>
				<Tabs
					type='card'
					items={[
						{
							key: 'organizations',
							label: 'Boshqa tashkilotlar',
							children: <UnderControlOrganizationTable />
						},
						{
							key: 'users',
							label: 'Foydalanuvchilar',
							children: <UserManagmentTab />
						}
					]}
				/>
			</div>
		</main>
	)
}

export default UnderControlOrganization
