import { Badge, Tooltip } from 'antd'
import { DailyReport } from '@/config/queries/attendance/getAll'

interface DayStatusBadgeProps {
	report: DailyReport
}

export const DayStatusBadge = ({ report }: DayStatusBadgeProps) => {
	if (!report.isWorkDay) {
		return (
			<Badge
				status='default'
				text='Ish kuni emas'
				className='text-gray-400'
			/>
		)
	}

	if (!report.cameToWork) {
		return (
			<Badge
				status='error'
				text='Kelmagan'
			/>
		)
	}

	if (report.lateMinutes > 0) {
		return (
			<Tooltip title={`${report.lateMinutes} daqiqa kech kelgan`}>
				<Badge
					status='warning'
					text='Kech kelgan'
				/>
			</Tooltip>
		)
	}

	if (report.earlyExitMinutes > 0) {
		return (
			<Tooltip title={`${report.earlyExitMinutes} daqiqa erta ketgan`}>
				<Badge
					status='processing'
					text='Erta ketgan'
				/>
			</Tooltip>
		)
	}

	return (
		<Badge
			status='success'
			text='Normal'
		/>
	)
}
