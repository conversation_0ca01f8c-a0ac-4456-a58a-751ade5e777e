import { TValidation } from '@/shared/types/validation.type'

export const getfaceIdFormValidation = (isEditing: boolean = false) => {
	const faceIdFormValidations: TValidation = {
		MAC: [{ required: !isEditing, message: 'MAC addressni kiriting' }],
		IP: [{ required: !isEditing, message: 'IP addressni kiriting' }],
		type: [{ required: !isEditing, message: 'Type ni tanlang' }]
	}
	return faceIdFormValidations
}
