import { axiosPrivate } from '@/config/api'
import { taskEndpoints } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'

export interface TaskControllerResponse {
	id: string
	name: string
	description: string
	dueDate: string
	createdAt: string
	updatedAt: string
	taskType: {
		id: string
		name: string
	}
	taskState: {
		id: string
		name: string
		color: string
	}
	organization: {
		id: string
		name: string
	}
	creator: {
		id: string
		fullName: string
	}
	controller: {
		id: string
		fullName: string
	}
	recipients: Array<{
		id: string
		organizationId?: string
		userId?: string
		positionId?: string
		organization?: {
			id: string
			name: string
		}
		user?: {
			id: string
			fullName: string
		}
		position?: {
			id: string
			name: string
		}
	}>
}

export const useGetTaskController = (userId: string) => {
	return useQuery({
		queryKey: ['task-controller', userId],
		queryFn: async () => {
			const response = await axiosPrivate.get<TaskControllerResponse[]>(
				taskEndpoints.taskController(userId)
			)
			return response.data
		},
		enabled: !!userId,
		staleTime: 5 * 60 * 1000, // 5 minutes
		refetchOnWindowFocus: false
	})
}
