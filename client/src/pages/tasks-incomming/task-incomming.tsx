import { useLocation, useNavigate } from 'react-router-dom'
import { useEffect } from 'react'
import { Table, Badge, Segmented, Card } from 'antd'
import { useTaskStates } from '@/shared/hooks/useTaskStates'
import { TTask } from '@/config/queries/task/getOne'
import { useGetTasks } from '@/config/queries/task/getAllByOrgId'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { usePagination } from '@/shared/hooks/usePagination'
import { useQueryClient } from '@tanstack/react-query'
import { taskEndpoints } from '@/config/api/endpoints'
import { Search } from '@/shared/components/search/search'
import { DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'

dayjs.extend(timezone)
dayjs.tz.setDefault(DEFAULT_TIMEZONE)

const Tasks = () => {
	const navigate = useNavigate()
	const location = useLocation()
	const workspace = location.pathname.startsWith('/personal') ? 'personal' : 'organization'
	const { status, handleSegmentChange } = useTaskStates()
	const { setTotal, pagination, handlePaginationChange } = usePagination()
	const { data: tasks } = useGetTasks(workspace, 'incomming', status)
	const queryClient = useQueryClient()
	const allTasksData = tasks?.pages?.flatMap(page => page.data) || []

	useEffect(() => {
		queryClient.invalidateQueries({ queryKey: [taskEndpoints.all] })
		queryClient.invalidateQueries({ queryKey: ['userTasks'] })
		queryClient.invalidateQueries({ queryKey: ['orgTasks'] })
	}, [queryClient])

	useEffect(() => {
		if (tasks?.pages?.[0]?.meta) {
			setTotal(tasks.pages[0].meta.total)
		}
	}, [tasks, setTotal])
	const handleRowClick = (record: { id: string | number }) => {
		const orgId = localStorage.getItem('organizationId')
		if (orgId) {
			navigate(`/workspace/tasks/${record.id}`)
		} else {
			navigate(`/personal/tasks/${record.id}`)
		}
	}

	const getDueDateColor = (dueDate: string) => {
		const now = new Date()
		const due = new Date(dueDate)
		const diffDays = Math.ceil((due.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

		if (diffDays < 3) return '#ff4d4f' // red
		if (diffDays < 7) return '#faad14' // yellow
		return '#52c41a' // green
	}

	const columns = [
		{
			title: 'ID',
			dataIndex: 'id',
			key: 'id',
			width: 10,
			render: (_: unknown, __: unknown, index: number) =>
				formatTableIndex(pagination.current, pagination.pageSize, index)
		},
		{
			title: 'Sarlavhasi',
			dataIndex: 'name',
			key: 'name',
			width: 150,
			className: 'text-start truncate max-w-24'
		},
		{
			title: 'Izoh',
			dataIndex: 'description',
			key: 'description',
			width: 200,
			className: 'text-start truncate max-w-24'
		},
		{
			title: 'Tashkilot',
			dataIndex: ['CreatedByOrganization', 'name'],
			key: 'createdByOrg',
			width: 150,
			className: 'text-start truncate max-w-24'
		},
		{
			title: 'Turi',
			dataIndex: ['TaskType', 'name'],
			key: 'taskType',
			width: 150,
			className: 'text-start truncate max-w-24'
		},
		{
			title: 'Muddati',
			dataIndex: 'dueDate',
			key: 'dueDate',
			width: 150,
			render: (dueDate: string) => (
				<span style={{ color: getDueDateColor(dueDate) }}>
					{dayjs(dueDate).tz(DEFAULT_TIMEZONE).format('DD.MM.YYYY HH:mm')}
				</span>
			)
		},
		{
			title: 'Holati',
			key: 'state',
			width: 150,
			render: (rec: TTask) =>
				rec.TaskState ? (
					<Badge
						style={{
							backgroundColor: '#6366F1',
							borderRadius: '4px',
							display: 'inline-block',
							textAlign: 'center',
							fontSize: '14px',
							padding: '1px 8px'
						}}
						count={rec.TaskState.name}
					/>
				) : null
		}
	]

	return (
		<Card className=' p-4 '>
			<div className='p-3 rounded-lg shadow-sm dark:bg-primary'>
				<div className='mb-4'>
					<Segmented
						// @ts-ignore
						options={[
							{ label: 'Hammasi', value: 'all' },
							{ label: 'Jarayonda', value: 'PENDING' },
							{ label: 'Rad Etilgan', value: 'REJECTED' },
							{ label: 'Tasdiqlangan', value: 'CONFIRMED' },
							{ label: 'Yakunlangan', value: 'completed' }
						]}
						value={status}
						onChange={handleSegmentChange}
					/>
				</div>
				<Table
					title={() => (
						<div className='flex'>
							<Search />
						</div>
					)}
					columns={columns}
					scroll={{ x: 'max-content' }}
					dataSource={allTasksData}
					rowKey='id'
					pagination={pagination}
					onChange={handlePaginationChange}
					onRow={(record: TTask) => ({
						onClick: () => handleRowClick(record),
						style: { cursor: 'pointer' }
					})}
				/>
			</div>
		</Card>
	)
}
export default Tasks
