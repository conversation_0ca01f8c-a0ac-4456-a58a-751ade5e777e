import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PaginationUtil, PrismaService, QueryUtil } from 'src/common';
import { CreateAttendanceDto, FindAttendanceDto } from '../dto/create-attendance.dto';
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import 'dayjs/locale/uz-latn';

dayjs.extend(utc);
dayjs.locale('uz-latn');

@Injectable()
export class AttendanceBasicService {
  logger = new Logger(AttendanceBasicService.name);

  constructor(
    private prismaService: PrismaService,
    private pagination: PaginationUtil,
    private queryUtil: QueryUtil,
  ) {}

  // async createAttendance(data: Omit<CreateAttendanceDto, 'time' | 'description'>) {
  //   const { userId } = data;

  //   const user = await this.prismaService.user.findUnique({
  //     where: { id: userId },
  //     select: { id: true, Organization: { select: { id: true } } },
  //   });

  //   if (!user) {
  //     throw new NotFoundException('Foydalanuvchi topilmadi');
  //   }

  //   const orgIds = user.Organization.map((org) => org.id);

  //   if (orgIds.length === 0) {
  //     throw new NotFoundException('Foydalanuvchi hech qaysi tashkilotga tegishli emas');
  //   }
  //   const organization = await this.prismaService.organization.findFirst({
  //     where: {
  //       id: {
  //         in: user.Organization.map((org) => org.id),
  //       },
  //       FaceIDDevice: {
  //         some: {
  //           MAC: {
  //             notIn: ['', 'null'],
  //           },
  //         },
  //       },
  //     },
  //     select: {
  //       id: true,
  //       FaceIDDevice: {
  //         select: {
  //           id: true,
  //           MAC: true,
  //           IP: true,
  //         },
  //       },
  //     },
  //   });

  //   if (!organization) {
  //     throw new NotFoundException('Organization topilamadi yoki FaceID qurilmalari mavjud emas');
  //   }

  //   return {
  //     user: user,
  //     organization: organization,
  //   };
  // }

  async createAttendance(data: Omit<CreateAttendanceDto, 'time' | 'description'>) {
    const { userId } = data;

    const user = await this.prismaService.user.findFirst({
      where: { id: userId },
      include: {
        Organization: {
          select: { id: true },
        },
      },
    });

    if (!user) {
      this.logger.warn(`Foydalanuvchi ${userId} topilmadi`);
      return null;
    }

    const orgIds = user.Organization.map((org) => org.id);

    if (!orgIds || orgIds.length === 0) {
      this.logger.warn(`Foydalanuvchi ${user.id} hech qaysi tashkilotga tegishli emas`);
      return null;
    }

    const organization = await this.prismaService.organization.findFirst({
      where: {
        id: {
          in: orgIds,
        },
        FaceIDDevice: {
          some: {
            MAC: {
              notIn: ['', 'null'],
            },
          },
        },
      },
      select: {
        id: true,
        FaceIDDevice: {
          select: {
            id: true,
            MAC: true,
            IP: true,
          },
        },
      },
    });

    if (!organization) {
      this.logger.warn(`User ${user.id} uchun tashkilot topilmadi yoki FaceID qurilma mavjud emas`);
      return null;
    }

    return {
      user,
      organization,
    };
  }

  async getUserAttendance(userId: string, page: string, limit: string) {
    const user = await this.prismaService.user.findFirst({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const $page = Number(page) || 1;
    const $limit = Number(limit) || 10;

    const date = new Date();
    date.setMonth(date.getMonth() - 1);

    const model = this.prismaService.attendance;

    const withPagination = this.pagination.paginate(
      model,
      { page: $page, limit: $limit },
      {
        createdAt: {
          gte: date,
        },
        userId,
      },
      {
        User: true,
        Organization: true,
      },
    );

    return withPagination;
  }

  async getAttendance(id: string, query: FindAttendanceDto) {
    const $page = Number(query.page) || 1;
    const $limit = Number(query.limit) || 10;

    const orgIds = query.organizationId ? query.organizationId.split(',') : [id];

    // Используем UTC для начала и конца дня
    const startDate = dayjs.utc(query.startDate).startOf('day').toDate();
    const endDate = dayjs.utc(query.endDate).endOf('day').toDate();

    const querySearch = this.queryUtil.createDateRangeQuery('date', startDate, endDate);

    const user = await this.pagination.paginate(
      this.prismaService.user,
      { page: $page, limit: $limit },
      {
        Organization: {
          some: {
            id: { in: orgIds },
          },
        },
        AttendanceReport: query.endDate || query.startDate ? { some: querySearch } : undefined,
        status: 'ACTIVE',
      },
      null,
      null,
      {
        id: true,
        fullName: true,
        username: true,
        phone: true,
        position: { select: { id: true, name: true, description: true, type: true } },
        role: true,
        AttendanceReport: {
          select: { date: true, enter: true, exit: true, minutes: true, id: true, description: true },
        },
        Organization: { select: { id: true, name: true, phone: true, address: true, description: true, grade: true } },
      },
    );

    return user;
  }
}
