import { PrismaService } from '../prisma/prisma.service';

export class BaseRepository<T> {
  constructor(
    protected readonly prisma: PrismaService,
    private readonly model: any,
  ) {}

  async findAll(
    params: {
      skip?: number;
      take?: number;
      where?: any;
      include?: any;
    } = {},
  ): Promise<T[]> {
    const { skip, take, where, include } = params;
    return this.model.findMany({ skip, take, where, include });
  }

  async findOne(where: any, include?: any): Promise<T | null> {
    return this.model.findFirst({
      where,
      include,
    });
  }

  async create(data: any): Promise<T> {
    return this.model.create({
      data,
    });
  }

  async update(where: any, data: any): Promise<T> {
    return this.model.update({
      where,
      data,
    });
  }

  async delete(where: any): Promise<T> {
    return this.model.delete({
      where,
    });
  }
}
