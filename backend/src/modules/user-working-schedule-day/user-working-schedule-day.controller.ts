import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common';
import { UserWorkingScheduleDayService } from './user-working-schedule-day.service';
import { CreateUserWorkingScheduleDayDto, UpdateUserWorkingScheduleDayDto } from './dto/user-working-schedule-day.dto';
import { ProtectedRoute } from '../../common';
import { BaseQueryParams } from '../../helpers/types';

@Controller('user-working-schedule-day')
export class UserWorkingScheduleDayController {
  constructor(private readonly userWorkingScheduleDayService: UserWorkingScheduleDayService) {}

  @ProtectedRoute({
    isPublic: false,
  })
  @Post()
  async create(@Body() createUserWorkingScheduleDayDto: CreateUserWorkingScheduleDayDto) {
    return await this.userWorkingScheduleDayService.create(createUserWorkingScheduleDayDto);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Get()
  async findAll(@Query() query: BaseQueryParams) {
    return await this.userWorkingScheduleDayService.findAll(query.search);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Get(':id')
  async findOne(@Param('id') id: string) {
    return await this.userWorkingScheduleDayService.findOne(id);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Get('user/:id')
  async findOneByUser(@Param('id') id: string) {
    return await this.userWorkingScheduleDayService.findOneByUser(id);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateUserWorkingScheduleDayDto: UpdateUserWorkingScheduleDayDto) {
    return await this.userWorkingScheduleDayService.update(id, updateUserWorkingScheduleDayDto);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Delete(':id')
  async remove(@Param('id') id: string) {
    return await this.userWorkingScheduleDayService.remove(id);
  }
}
