/*
  Warnings:

  - You are about to drop the column `stataus` on the `Attendance` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `AttendanceReport` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `FaceIDDevice` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `File` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `GPSLocation` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `GPSLocationReport` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `Grade` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `Imei` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `Organization` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `OrganizationType` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `OrganizationTypePosition` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `Recipient` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `RecipientAnswer` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `RecipientAnswerType` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `Region` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `TaskState` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `TaskType` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `User` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `UserWorkingSchedule` table. All the data in the column will be lost.
  - You are about to drop the column `stataus` on the `UserWorkingScheduleDay` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[name]` on the table `TaskState` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[key]` on the table `TaskState` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `status` to the `Recipient` table without a default value. This is not possible if the table is not empty.

*/
-- CreateEnum
CREATE TYPE "Status" AS ENUM ('ACTIVE', 'INACTIVE');

-- AlterTable
ALTER TABLE "Attendance" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "AttendanceReport" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "District" ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "FaceIDDevice" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "File" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "GPSLocation" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "GPSLocationReport" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "Grade" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "Imei" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "Organization" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "OrganizationType" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "OrganizationTypePosition" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "Recipient" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL;

-- AlterTable
ALTER TABLE "RecipientAnswer" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "RecipientAnswerType" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "Region" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "Section" ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "Task" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "TaskState" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "TaskType" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "User" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "UserWorkingSchedule" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- AlterTable
ALTER TABLE "UserWorkingScheduleDay" DROP COLUMN "stataus",
ADD COLUMN     "status" "Status" NOT NULL DEFAULT 'ACTIVE';

-- CreateIndex
CREATE UNIQUE INDEX "TaskState_name_key" ON "TaskState"("name");

-- CreateIndex
CREATE UNIQUE INDEX "TaskState_key_key" ON "TaskState"("key");
