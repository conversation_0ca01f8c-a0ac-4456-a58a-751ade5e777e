import { Module } from '@nestjs/common';
import { AttendanceService } from './attendance.service';
import { AttendanceController } from './attendance.controller';
import { BullModule } from '@nestjs/bullmq';
import { ATTENDANCE, ATTENDANCE_REPORT } from './interface/attendance';
import { AttendanceProcessor } from './attendance.processor';
import { BullBoardModule } from '@bull-board/nestjs';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { AttendanceHandler } from './attendance.handler';
import { AttendanceExcelService } from './attendace-excel.service';
import { AttendanceBasicService } from './services/attendance-basic.service';
import { AttendanceHelperService } from './services/attendance-helper.service';
import { AttendanceTableService } from './services/attendance-table.service';
import { AttendanceReportService } from './services/attendance-report.service';
import { AttendanceEmployeeReportService } from './services/attendance-employee-report.service';
import { AttendanceReportProcessor } from './attendance-report.processor';

@Module({
  imports: [
    BullModule.registerQueue({
      name: ATTENDANCE,
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    }),
    BullModule.registerQueue({
      name: ATTENDANCE_REPORT,
      defaultJobOptions: {
        removeOnComplete: 100,
        removeOnFail: 50,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    }),
    BullBoardModule.forFeature({
      name: ATTENDANCE,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: ATTENDANCE_REPORT,
      adapter: BullMQAdapter,
    }),
  ],
  controllers: [AttendanceController],
  providers: [
    AttendanceService,
    AttendanceBasicService,
    AttendanceHelperService,
    AttendanceTableService,
    AttendanceReportService,
    AttendanceEmployeeReportService,
    AttendanceProcessor,
    AttendanceHandler,
    AttendanceExcelService,
    AttendanceReportProcessor,
  ],
})
export class AttendanceModule {}
