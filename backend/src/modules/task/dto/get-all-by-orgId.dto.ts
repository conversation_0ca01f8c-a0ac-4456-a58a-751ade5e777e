import { IsEnum, IsOptional, IsString } from 'class-validator';

export enum TaskVariant {
  OUTGOING = 'outgoing',
  INCOMMING = 'incomming',
}

export enum EnumFilter {
  completed = 'completed',
  incompleted = 'incompleted',
}

export class GetAllTasksByOrgId {
  @IsEnum(TaskVariant)
  @IsOptional()
  type?: TaskVariant;

  @IsString()
  @IsOptional()
  page?: string;

  @IsString()
  @IsOptional()
  limit?: string;

  @IsOptional()
  @IsString()
  state?: string;

  @IsOptional()
  @IsEnum(EnumFilter)
  filterState?: EnumFilter;

  @IsOptional()
  @IsString()
  complatedState?: string;

  @IsOptional()
  @IsString()
  search?: string;
}
