describe('create user spec', () => {
	beforeEach(() => {
		cy.login()
		cy.visit('/users', {
			timeout: 500000
		})
		cy.getById('user-form-add-button').click()
	})

	it('should validate inputs', () => {
		cy.getById('user-form-modal').should('be.visible')
		cy.get('.ant-modal-footer > .ant-btn-primary').click()
		cy.get('#username_help > .ant-form-item-explain-error').should('be.visible')
		cy.get('#fullname_help > .ant-form-item-explain-error').should('be.visible')
		cy.get('#phone_help > .ant-form-item-explain-error').should('be.visible')
		cy.get('#role_help > .ant-form-item-explain-error').should('be.visible')
		cy.get('#password_help > .ant-form-item-explain-error').should('be.visible')
	})

	it('should create user', () => {
		cy.getById('user-form-username-input').type('cypress test user')
		cy.getById('user-form-fullname-input').type('cypress test user')
		cy.getById('user-form-phone-input').type('112223344')
		cy.getById('user-form-role-select').click()
		cy.get('[title="User"]').click()
		cy.getById('user-form-password-input').type('1234')
		cy.get('.ant-modal-footer > .ant-btn-primary').click()
		cy.get('.ant-notification-notice-success').should('be.visible')
	})
})
