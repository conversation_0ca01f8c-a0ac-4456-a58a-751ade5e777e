import { Modu<PERSON> } from '@nestjs/common';
import { OrganizationService } from './organization.service';
import { OrganizationController } from './organization.controller';
import { OrganizationTypesModule } from './organization-types/organization-types.module';
import { OrganizationTypePositionModule } from './organization-type-position/organization-type-position.module';

@Module({
  controllers: [OrganizationController],
  providers: [OrganizationService],
  imports: [OrganizationTypesModule, OrganizationTypePositionModule],
})
export class OrganizationModule {}
