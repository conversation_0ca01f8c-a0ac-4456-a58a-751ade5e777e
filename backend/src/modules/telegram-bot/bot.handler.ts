import { Injectable } from '@nestjs/common';
import { PrismaService } from '../../common';
import { OnEvent } from '@nestjs/event-emitter';
import { TelegramEventKeys } from './interfaces/bot.interfaces';
import { BotService } from './bot.service';

@Injectable()
export class BotHandler {
  constructor(
    private prismaService: PrismaService,
    private botService: BotService,
  ) {}

  @OnEvent(TelegramEventKeys.TELEGRAM_NEW_TASK, { async: true })
  async onNewTask(payload: any) {
    const responsibles = await this.prismaService.user.findMany({
      where: {
        ResponsibleFor: {
          some: {
            id: payload.organizationId,
          },
        },
      },
      select: {
        id: true,
        telegramId: true,
      },
    });

    const user = await this.prismaService.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        telegramId: true,
      },
    });

    const uniqueUsers = new Map<string, string>();

    if (user?.telegramId) {
      uniqueUsers.set(user.id, user.telegramId);
    }

    for (const responsible of responsibles) {
      if (responsible.telegramId) {
        uniqueUsers.set(responsible.id, responsible.telegramId);
      }
    }

    const message = `Sizga yangi topshiriq yuborildi`;

    for (const [key, value] of uniqueUsers) {
      if (value) {
        await this.botService.bot.sendMessage({
          chat_id: value,
          text: message,
        });
      }
    }
  }

  @OnEvent(TelegramEventKeys.TELEGRAM_OUT, { async: true })
  async onUserOut(payload: any) {
    const { telegramIds, user, userOrganizationNames } = await this.getTelegramIdsFromUserId(payload.userId);

    const message = `*Hududdan Tashqarida*\n\n*Hodim: *${user.fullName}*\n\nLavozim: *${user.position.name}*\n\nTashkilot: *${userOrganizationNames}*`;

    for (const telegramId of telegramIds) {
      await this.botService.bot.sendMessage({
        chat_id: telegramId,
        text: message,
        parse_mode: 'Markdown',
      });
    }
  }

  @OnEvent(TelegramEventKeys.TELEGRAM_OFFLINE, { async: true })
  async onUserOffline(payload: any) {
    const { telegramIds, user, userOrganizationNames } = await this.getTelegramIdsFromUserId(payload.userId);

    const message = `*Foydalanuvchi offline*\n\n*Hodim:* ${user.fullName}\n\n*Lavozim:* ${user.position.name}\n\n*Tashkilot:* ${userOrganizationNames}`;

    for (const telegramId of telegramIds) {
      await this.botService.bot.sendMessage({
        chat_id: telegramId,
        text: message,
        parse_mode: 'Markdown',
      });
    }
  }

  private async getTelegramIdsFromUserId(userId: string) {
    const user = await this.prismaService.user.findUnique({
      where: { id: userId },
      select: {
        telegramId: true,
        fullName: true,
        position: {
          select: {
            name: true,
          },
        },
        Organization: {
          select: {
            id: true,
            name: true,
            Responsible: {
              select: {
                id: true,
                telegramId: true,
              },
            },
            Parent: {
              select: {
                Responsible: {
                  select: {
                    id: true,
                    telegramId: true,
                  },
                },
              },
            },
          },
        },
        MainOrganization: {
          select: {
            id: true,
            name: true,
            Responsible: {
              select: {
                id: true,
                telegramId: true,
              },
            },
          },
        },
      },
    });

    if (!user) {
      throw new Error(`User with ID ${userId} not found`);
    }

    const telegramIds = new Set<string>();

    if (user.telegramId) {
      telegramIds.add(user.telegramId);
    }

    user.Organization.forEach((org) => {
      org.Responsible.forEach((responsible) => {
        if (responsible.telegramId) {
          telegramIds.add(responsible.telegramId);
        }
      });
      if (org.Parent) {
        org.Parent.Responsible.forEach((responsible) => {
          if (responsible.telegramId) {
            telegramIds.add(responsible.telegramId);
          }
        });
      }
    });

    user.MainOrganization?.Responsible?.forEach((responsible) => {
      if (responsible.telegramId) {
        telegramIds.add(responsible.telegramId);
      }
    });

    const userOrganizationNames = user.Organization.map((org) => org.name).join(', ') || 'Tashkilot mavjud emas';
    return {
      user: user,
      telegramIds: Array.from(telegramIds),
      userOrganizationNames: userOrganizationNames,
    };
  }
}
