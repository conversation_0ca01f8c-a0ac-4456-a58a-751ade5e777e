import { Form, Select, Typography } from 'antd'
import { useMemo } from 'react'
import { useGetWorkersForController } from '@/config/queries/organizations/get-workers-simple.queries'

const { Text } = Typography

type TTaskControllerSelectProps = {
	organizationId: string
}

export function TaskControllerSelect({ organizationId }: TTaskControllerSelectProps) {
	const { data: workersData, isLoading } = useGetWorkersForController(organizationId)

	// Get workers from the {Worker: [...]} response structure
	const workers = useMemo(() => {
		if (!workersData || !workersData.Worker) return []
		return workersData.Worker.filter(worker => worker && worker.id && worker.fullName)
	}, [workersData])

	return (
		<div className='mb-6'>
			<Text className='block mb-2 font-medium text-gray-700 dark:text-gray-300'>
				Nazoratchi <span className='text-red-500'>*</span>
			</Text>
			<Form.Item
				name='controllerId'
				rules={[{ required: true, message: 'Nazoratchi tanlang!' }]}>
				<Select
					placeholder='Nazoratchi tanlang'
					loading={isLoading}
					showSearch
					filterOption={(input, option) =>
						String(option?.label ?? '')
							.toLowerCase()
							.includes(input.toLowerCase())
					}
					options={workers.map(worker => ({
						label: worker.fullName,
						value: worker.id
					}))}
				/>
			</Form.Item>
		</div>
	)
}
