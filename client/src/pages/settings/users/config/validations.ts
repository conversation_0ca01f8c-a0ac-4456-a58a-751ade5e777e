import { TValidation } from '@/shared/types/validation.type'

export const getUserFormValidation = (isEditing: boolean) => {
	const userFormValidations: TValidation = {
		name: [{ required: !isEditing, message: 'Tashkilot toliq nomini kiriting' }],
		username: [{ required: !isEditing, message: 'Telefon raqamni kiriting' }],
		description: [{ required: !isEditing, message: "Tashkilot haqida ma'lumot kiriting" }],
		region: [{ required: !isEditing, message: 'Viloyatni tanlang' }],
		organizationType: [{ required: !isEditing, message: 'Tashkilot lavozimi turini  tanlang' }]
	}
	return userFormValidations
}
