import { Form, Modal } from 'antd'
import { useFaceIDAddStore } from '../utils/face-id-add-modal-store'
import { FaceIdAddForm } from './user-managment-add.form'
import { getfaceIdFormValidation } from '../config/validations'
import { useState } from 'react'
import { useAddUsersToOrganization } from '@/config/queries/organizations/add-users.queries'
import { useParams, useSearchParams } from 'react-router-dom'

export const FaceIdAddModal = () => {
	const [form] = Form.useForm()
	const { id: orgId } = useParams()
	const xOrgId = localStorage.getItem('organizationId')
	const { open, onClose, who } = useFaceIDAddStore()
	const faceIdDeviceValidation = getfaceIdFormValidation()
	const [userIds, setUserIds] = useState<string[]>([])
	const { mutateAsync: addUser, isPending } = useAddUsersToOrganization(orgId!)
	const [searchParams, setSearchParams] = useSearchParams()

	// Confirmation modal state
	const [confirmationModal, setConfirmationModal] = useState({
		open: false,
		userData: null as any
	})

	const close = () => {
		setUserIds([])
		form.resetFields()
		onClose()
		searchParams.delete('search')
		setSearchParams(searchParams.toString())
		// Also close confirmation modal if it's open
		setConfirmationModal({
			open: false,
			userData: null
		})
	}

	const closeConfirmationModal = () => {
		setConfirmationModal({
			open: false,
			userData: null
		})
		// Close the main modal as well
		close()
	}

	const handleUserFound = (userData: any) => {
		// Show confirmation modal when user is found (only for phone search)
		if (who !== 'responsibles') {
			setConfirmationModal({
				open: true,
				userData
			})
		}
	}

	const handleConfirmConnection = async () => {
		try {
			if (confirmationModal.userData) {
				const effectiveOrgId = orgId || (xOrgId ? JSON.parse(xOrgId) : '')

				await addUser({
					orgId: effectiveOrgId,
					data: {
						[who]: [confirmationModal.userData.id]
					},
					who
				})

				closeConfirmationModal()
			}
		} catch (error) {
			console.error('Xatolik:', error)
		}
	}

	const onFinish = () => {
		// Check if we have users to add
		if (userIds.length === 0) {
			return
		}

		if (orgId)
			return addUser({
				orgId,
				data: {
					[who]: userIds
				},
				who
			}).then(close)

		if (xOrgId)
			return addUser({
				orgId: JSON.parse(xOrgId),
				data: {
					[who]: userIds
				},
				who
			}).then(close)
	}

	// For responsible users, we don't need confirmation modal
	const isResponsibleMode = who === 'responsibles'
	const shouldDisableOk = isPending || (isResponsibleMode ? userIds.length === 0 : false)

	return (
		<>
			<Modal
				open={open}
				onClose={close}
				onCancel={close}
				okText='Yuborish'
				onOk={() => form.submit()}
				okButtonProps={{ disabled: shouldDisableOk }}
				cancelButtonProps={{ disabled: isPending }}
				destroyOnClose={true}>
				<Form
					form={form}
					layout='vertical'
					onFinish={onFinish}>
					<FaceIdAddForm
						validation={faceIdDeviceValidation}
						userIds={userIds}
						setUserIds={setUserIds}
						onUserFound={handleUserFound}
					/>
				</Form>
			</Modal>

			{/* Confirmation Modal - Only show for non-responsible users */}
			{!isResponsibleMode && (
				<Modal
					open={confirmationModal.open}
					onCancel={closeConfirmationModal}
					width={500}
					title='Tashkilotga biriktirish'
					destroyOnClose={true}
					footer={[
						<button
							key='cancel'
							className='px-4 py-2 mr-2 bg-red-500 text-white rounded cursor-pointer hover:bg-red-600'
							onClick={closeConfirmationModal}>
							Yo'q, kerak emas
						</button>,
						<button
							key='confirm'
							className='px-4 py-2 text-white bg-blue-600 rounded hover:bg-blue-700'
							onClick={handleConfirmConnection}
							disabled={isPending}>
							{isPending ? 'Biriktirilmoqda...' : 'Ha, biriktirish'}
						</button>
					]}>
					<div className='py-4'>
						<p className='text-lg text-center'>
							Siz rostdan ham ushbu xodimni bu tashkilotga biriktirmoqchimisiz?
						</p>
					</div>
				</Modal>
			)}
		</>
	)
}
