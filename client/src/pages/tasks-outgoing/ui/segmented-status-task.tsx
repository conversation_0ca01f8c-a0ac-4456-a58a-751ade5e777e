import { Segmented } from 'antd'
import { useSearchParams } from 'react-router-dom'

function SegmentedStatusTask() {
	const [searchParams, setSearchParams] = useSearchParams()
	const status = searchParams.get('status') ?? 'all'

	const handleSegmentChange = (value: string) => {
		setSearchParams({
			type: searchParams.get('type') ?? 'incomming',
			page: '1',
			pageSize: searchParams.get('pageSize') ?? '10',
			status: value,
			filterState: value
		})
	}

	const segmentOptions = [
		{ label: 'Hammasi', value: 'all' },
		{ label: '<PERSON><PERSON><PERSON><PERSON>', value: 'incompleted' },
		{ label: 'Yakunlangan', value: 'completed' }
	]

	return (
		<Segmented
			options={segmentOptions}
			value={status}
			onChange={handleSegmentChange}
		/>
	)
}

export default SegmentedStatusTask
