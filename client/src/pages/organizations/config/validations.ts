import { TValidation } from '@/shared/types/validation.type'

export const getOrganizationFormValidation = (isEditing: boolean) => {
	const organizationFormValidations: TValidation = {
		name: [{ required: !isEditing, message: 'Tashkilot nomini kiriting' }],
		gradeId: [{ required: !isEditing, message: 'Tashkilot lavozimini tanlang' }],
		description: [{ required: !isEditing, message: "Tashkilot haqida ma'lumot kiriting" }],
		address: [{ required: !isEditing, message: 'Tashkilot manzilini kiriting' }],
		regionId: [{ required: !isEditing, message: 'Viloyatni tanlang' }],
		typeId: [{ required: !isEditing, message: 'Tashkilot turini tanlang' }]
	}
	return organizationFormValidations
}
