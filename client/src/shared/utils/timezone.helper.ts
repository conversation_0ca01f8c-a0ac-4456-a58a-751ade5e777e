import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import 'dayjs/locale/uz-latn'

// Dayjs pluginlarini o'rnatish
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.locale('uz-latn')

export const DEFAULT_TIMEZONE = 'Asia/Tashkent'

/**
 * Frontend uchun timezone yordamchi funksiyalar
 */
export class TimezoneHelper {
	/**
	 * UTC vaqtni mahalliy vaqtga o'tkazish
	 */
	static toLocalTime(utcDate: Date | string): dayjs.Dayjs {
		return dayjs.utc(utcDate).tz(DEFAULT_TIMEZONE)
	}

	/**
	 * Mahalliy vaqtni UTC ga o'tkazish
	 */
	static toUTC(localDate: Date | string | dayjs.Dayjs): dayjs.Dayjs {
		if (dayjs.isDayjs(localDate)) {
			return localDate.utc()
		}
		return dayjs.tz(localDate, DEFAULT_TIMEZONE).utc()
	}

	/**
	 * Ku<PERSON> boshi (00:00:00) - mahalliy vaqtda
	 */
	static getStartOfDay(date?: Date | string | dayjs.Dayjs): dayjs.Dayjs {
		const dayObj = date ? dayjs(date) : dayjs()
		return dayObj.tz(DEFAULT_TIMEZONE).startOf('day')
	}

	/**
	 * Kunning oxiri (23:59:59.999) - mahalliy vaqtda
	 */
	static getEndOfDay(date?: Date | string | dayjs.Dayjs): dayjs.Dayjs {
		const dayObj = date ? dayjs(date) : dayjs()
		return dayObj.tz(DEFAULT_TIMEZONE).endOf('day')
	}

	/**
	 * Joriy mahalliy vaqtni olish
	 */
	static now(): dayjs.Dayjs {
		return dayjs().tz(DEFAULT_TIMEZONE)
	}
	/**
	 * Vaqtni mahalliy formatda ko'rsatish
	 */
	static formatLocal(
		date: Date | string | null | undefined,
		format: string = 'YYYY-MM-DD HH:mm:ss'
	): string | null {
		console.log('formatLocal', date)
		if (!date) return null
		return this.toLocalTime(date).format(format)
	}

	/**
	 * Faqat vaqtni ko'rsatish (HH:mm)
	 */
	static formatTime(date: Date | string | null | undefined): string | null {
		if (!date) return null
		return this.toLocalTime(date).format('HH:mm')
	}

	/**
	 * Frontend uchun format
	 */
	static formatForDisplay(date: Date | string): string {
		return this.toLocalTime(date).format('DD.MM.YYYY HH:mm')
	}

	/**
	 * ISO format bilan UTC ga yuborish uchun
	 */
	static formatForAPI(date: dayjs.Dayjs): string {
		return date.tz(DEFAULT_TIMEZONE).format('YYYY-MM-DDTHH:mm:ssZ')
	}

	/**
	 * Debug uchun vaqt zonalari bilan format
	 */
	static debugFormat(date: Date | string): string {
		const utc = dayjs.utc(date)
		const local = this.toLocalTime(date)
		return `UTC: ${utc.format('YYYY-MM-DD HH:mm:ss')} | Local: ${local.format('YYYY-MM-DD HH:mm:ss')} (${DEFAULT_TIMEZONE})`
	}

	/**
	 * DatePicker uchun range format
	 */
	static formatDateRange(dates: [dayjs.Dayjs, dayjs.Dayjs] | null): {
		startDate: string
		endDate: string
	} {
		if (!dates) {
			return { startDate: '', endDate: '' }
		}
		return {
			startDate: this.formatForAPI(dates[0]),
			endDate: this.formatForAPI(dates[1])
		}
	}

	/**
	 * URL parametrlaridan sana parse qilish
	 */
	static parseFromURLParam(dateString: string | null): dayjs.Dayjs | null {
		if (!dateString) return null
		try {
			const parsed = dayjs(dateString)
			return parsed.isValid() ? parsed.tz(DEFAULT_TIMEZONE) : null
		} catch {
			return null
		}
	}
}
