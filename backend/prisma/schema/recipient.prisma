model Recipient {
  id             String       @id @default(cuid())
  Organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String

  User   User?   @relation(fields: [userId], references: [id])
  userId String?

  status Status @default(ACTIVE)

  isRead      Boolean @default(false)
  isCompleted Boolean @default(false)

  Answer RecipientAnswer[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  Task   Task   @relation(fields: [taskId], references: [id])
  taskId String
}

model RecipientAnswer {
  id          String    @id @default(cuid())
  recipientId String
  recipient   Recipient @relation(fields: [recipientId], references: [id])
  status      Status    @default(ACTIVE)

  description String

  files File[]

  type   RecipientAnswerType? @relation(fields: [typeId], references: [id])
  typeId String?

  state RecipientAnswerState
  rejectReason String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model RecipientAnswerType {
  id     String @id @default(cuid())
  name   String @unique
  status Status @default(ACTIVE)

  RecipientAnswer RecipientAnswer[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum RecipientAnswerState {
  CONFIRMED
  REJECTED
  PENDING
}
