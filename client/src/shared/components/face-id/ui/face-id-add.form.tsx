import { FaceIDType } from '@/shared/types/face-id.type'
import { TValidationFormProps } from '@/shared/types/validation-form.type'
import { Form, Input, Select } from 'antd'

const { Item } = Form

export const FaceIdAddForm = ({ validation }: TValidationFormProps) => {
	return (
		<div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '16px' }}>
			<Item
				name='MAC'
				label='MAC addressni kiriting'
				rules={validation.MAC}>
				<Input />
			</Item>

			<Item
				name='IP'
				label='IP addressni kiriting'
				rules={validation.IP}>
				<Input />
			</Item>

			<Item
				name='type'
				label='Turini ni tanlang'
				rules={validation.type}>
				<Select
					options={[
						{
							value: FaceIDType.ENTER,
							label: 'Kirish'
						},
						{
							value: FaceIDType.EXIT,
							label: 'Chiqish'
						}
					]}
				/>
			</Item>
		</div>
	)
}
