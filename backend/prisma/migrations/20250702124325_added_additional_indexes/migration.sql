-- CreateIndex
CREATE INDEX "Attendance_userId_type_time_idx" ON "Attendance"("userId", "type", "time");

-- CreateIndex
CREATE INDEX "Attendance_type_time_idx" ON "Attendance"("type", "time");

-- CreateIndex
CREATE INDEX "Attendance_userId_time_idx" ON "Attendance"("userId", "time");

-- CreateIndex
CREATE INDEX "Attendance_organizationId_type_time_idx" ON "Attendance"("organizationId", "type", "time");

-- CreateIndex
CREATE INDEX "Attendance_time_type_userId_idx" ON "Attendance"("time", "type", "userId");

-- CreateIndex
CREATE INDEX "AttendanceReport_userId_date_idx" ON "AttendanceReport"("userId", "date");

-- CreateIndex
CREATE INDEX "AttendanceReport_date_userId_idx" ON "AttendanceReport"("date", "userId");

-- CreateIndex
CREATE INDEX "AttendanceReport_isLate_idx" ON "AttendanceReport"("isLate");

-- CreateIndex
CREATE INDEX "AttendanceReport_isEarlyExit_idx" ON "AttendanceReport"("isEarlyExit");

-- CreateIndex
CREATE INDEX "AttendanceReport_dayNumber_date_idx" ON "AttendanceReport"("dayNumber", "date");

-- CreateIndex
CREATE INDEX "AttendanceReport_enter_exit_idx" ON "AttendanceReport"("enter", "exit");
