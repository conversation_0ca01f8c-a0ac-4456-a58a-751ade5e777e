import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  Logger,
  NotAcceptableException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { PaginationUtil, PrismaService, QueryUtil } from '../../common';
import { UpdateTaskDto } from './dto/update-task.dto';
import { CreateTaskDto } from './dto/task.dto';
import { AnswerDto, AnswerStateDto } from './dto/answer.dto';
import { EnumFilter, GetAllTasksByOrgId, TaskVariant } from './dto/get-all-by-orgId.dto';
import { ReadTaskDto } from './dto/read-task.dto';
import { FileService } from '../file/file.service';
import { Prisma, RecipientAnswerState } from '@prisma/client';
import { TaskExcelService } from './task-excel.service';
import { CompleteTaskDto } from './dto/complete-task.dto';
import { RecipientDto } from './dto/update-task-recipients.dto';

interface IId {
  id: string;
}

// Добавление вспомогательной функции перед методом findAllByOrg
interface TotalAnswers {
  PENDING: number;
  REJECTED: number;
  CONFIRMED: number;
}

@Injectable()
export class TaskService {
  logger = new Logger(TaskService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly queryUtil: QueryUtil,
    private readonly pagination: PaginationUtil,
    private readonly fileService: FileService,
    private readonly taskExcelService: TaskExcelService,
  ) {}

  // async getControlledTasksByUserId(userId: string) {
  //   const user = await this.prisma.user.findUnique({
  //     where: { id: userId },
  //     include: {
  //       MainOrganization: true,
  //       Organization: true,
  //     },
  //   });

  //   if (!user) {
  //     throw new NotFoundException('User not found');
  //   }

  //   const hasOrganization = user.mainOrganizationId || (user.Organization && user.Organization.length > 0);

  //   if (!hasOrganization) {
  //     throw new UnauthorizedException('Organization topilmadi');
  //   }

  //   const controlledTasks = await this.prisma.task.findMany({
  //     where: {
  //       controller: {
  //         id: userId,
  //       },
  //     },
  //     include: {
  //       CreatedBy: true,
  //       CreatedByOrganization: true,
  //       TaskState: true,
  //       TaskType: true,
  //       controller: true,
  //       Recipients: true,
  //       SubTasks: true,
  //       Parent: true,
  //     },
  //   });

  //   return controlledTasks;
  // }

  async getControlledTasksOnly(userId: string) {
    const userExists = await this.prisma.user.findUnique({
      where: { id: userId },
      select: { id: true },
    });

    if (!userExists) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    const tasks = await this.prisma.task.findMany({
      where: {
        userId: userId,
        status: 'ACTIVE',
      },
      include: {
        CreatedBy: {
          select: {
            id: true,
            fullName: true,
            username: true,
          },
        },
        CreatedByOrganization: {
          select: {
            id: true,
            name: true,
          },
        },
        TaskState: true,
        TaskType: true,
        files: true,
        CompleteFile: true,
        Recipients: {
          include: {
            User: {
              select: {
                id: true,
                fullName: true,
                username: true,
              },
            },
            Organization: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        SubTasks: true,
        Parent: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    return tasks;
  }

  async createByAnswer(createAnswerDto: AnswerDto) {
    const recipient = await this.prisma.recipient.findUnique({
      where: { id: createAnswerDto.recipientId },
    });

    if (!recipient) {
      throw new NotFoundException(`Recipient with ID "${createAnswerDto.recipientId}" not found`);
    }

    let typeConnect = {};
    if (createAnswerDto?.typeId) {
      const type = await this.prisma.recipientAnswerType.findUnique({
        where: { id: createAnswerDto.typeId },
      });

      if (!type) {
        throw new NotFoundException(`AnswerType with ID "${createAnswerDto.typeId}" not found`);
      }

      typeConnect = { type: { connect: { id: createAnswerDto.typeId } } };
    }

    let files: IId[] = [];

    if (createAnswerDto.files) {
      files = await this.fileService.moveFileFromTempToSaved(createAnswerDto.files);
    }

    const answer = await this.prisma.recipientAnswer.create({
      data: {
        state: 'PENDING',
        files: {
          connect: files,
        },
        recipient: { connect: { id: createAnswerDto.recipientId } },
        description: createAnswerDto.description,
        ...typeConnect,
      },
    });

    return answer;
  }

  async updateByAnswerState({ state, rejectReason }: AnswerStateDto, id: string, userId: string) {
    const recipientAnswer = await this.prisma.recipientAnswer.findUnique({
      where: { id },
      select: { recipientId: true },
    });

    if (!recipientAnswer) {
      throw new NotFoundException(`RecipientAnswer with ID "${id}" not found`);
    }

    const recipient = await this.prisma.recipient.findUnique({
      where: { id: recipientAnswer.recipientId },
      select: { Task: { select: { CreatedBy: { select: { id: true } } } } },
    });

    if (recipient.Task.CreatedBy.id !== userId) {
      throw new ForbiddenException(`User with ID "${userId}" is not the creator of the task`);
    }

    if (state === 'REJECTED' && !rejectReason) {
      throw new BadRequestException('rejectReason must be provided when state is rejected');
    }

    const data: Record<string, string> = { state };

    if (rejectReason && state === 'REJECTED') {
      data.rejectReason = rejectReason;
    }

    await this.prisma.recipientAnswer.update({ where: { id }, data });
    return 'updated answer state';
  }

  private calculateTotalAnswers(recipients: { Answer: { state: string }[] }[]): TotalAnswers {
    const totalAnswers: TotalAnswers = {
      PENDING: 0,
      REJECTED: 0,
      CONFIRMED: 0,
    };

    for (const recipient of recipients) {
      for (const answer of recipient.Answer) {
        totalAnswers[answer.state] += 1;
      }
    }

    return totalAnswers;
  }

  async findAllByOrg(
    orgId: string,
    { page, limit, type, state: s, filterState, complatedState, search }: GetAllTasksByOrgId,
    userId: string,
  ) {
    const $page = Number(page) || 1;
    const $limit = Number(limit) || 10;
    const skip = ($page - 1) * $limit;

    const searchWhere = {
      ...this.queryUtil.createSearchQuery(search, ['name', 'id', 'description']),
    };

    const state = s && (s.toUpperCase() as RecipientAnswerState | 'COMPLETED');

    // Helper function for pagination
    const getPaginatedResult = async (where: any, include: any, withTotalAnswers = false) => {
      const totalCount = await this.prisma.task.count({ where });

      const tasks = await this.prisma.task.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip,
        take: $limit,
        include,
      });

      let data: any = tasks;

      if (withTotalAnswers) {
        data = tasks.map((task) => {
          const totalAnswers = this.calculateTotalAnswers(task.Recipients as any);
          const { Recipients, ...taskWithoutRecipients } = task;
          return {
            ...taskWithoutRecipients,
            totalAnswers,
          };
        });
      }

      return {
        data,
        meta: {
          page: $page,
          limit: $limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / $limit),
          hasMore: skip + data.length < totalCount,
        },
      };
    };

    // Standard includes for different scenarios
    const outgoingTasksInclude = {
      Recipients: {
        include: {
          Answer: true,
        },
      },
      CreatedBy: true,
      CreatedByOrganization: true,
      TaskState: true,
      TaskType: true,
      Parent: true,
    };

    const incomingTasksInclude = {
      TaskState: true,
      TaskType: true,
      CreatedByOrganization: true,
    };

    // Handle OUTGOING tasks
    if (type === TaskVariant.OUTGOING) {
      const baseWhere: any = {
        createdByOrganizationId: orgId,
        status: 'ACTIVE',
        isCompleted: false,
        ...searchWhere,
      };

      // Handle completed tasks
      if (filterState === EnumFilter.completed) {
        baseWhere.isCompleted = true;
        if (complatedState) {
          baseWhere.TaskState = { key: complatedState };
        }
      }
      // Handle incompleted tasks
      else if (filterState === EnumFilter.incompleted) {
        baseWhere.isCompleted = false;
        if (complatedState) {
          baseWhere.TaskState = { key: complatedState };
        }
      }
      // Handle tasks with specific state
      else if (state) {
        baseWhere.TaskState = { key: state };
      }

      return await getPaginatedResult(baseWhere, outgoingTasksInclude, true);
    }

    // Handle INCOMING tasks
    const baseWhere: any = {
      Recipients: {
        some: {
          organizationId: orgId,
        },
      },
      status: 'ACTIVE',
      ...searchWhere,
    };

    // Handle completed tasks
    if (state && state === 'COMPLETED') {
      baseWhere.isCompleted = true;
      return await getPaginatedResult(baseWhere, incomingTasksInclude);
    }

    // Handle all tasks (no state filter)
    if (!state) {
      return await getPaginatedResult(baseWhere, incomingTasksInclude);
    }

    // Handle tasks with specific answer state
    const recipients = await this.prisma.recipient.findMany({
      where: {
        organizationId: orgId,
        Answer: {
          some: {
            state: state as any,
          },
        },
      },
      select: { id: true }, // Only select id for performance
    });

    const recipientIds = recipients.map((recipient) => recipient.id);

    // If no recipients with such state, return empty result
    if (recipientIds.length === 0) {
      return {
        data: [],
        meta: {
          page: $page,
          limit: $limit,
          totalCount: 0,
          pageCount: 0,
        },
      };
    }

    // Build query for tasks with specific answer state
    const whereWithStatus: Prisma.TaskWhereInput = {
      Recipients: {
        some: {
          id: {
            in: recipientIds,
          },
        },
      },
      status: 'ACTIVE',
      ...searchWhere,
    };

    return await getPaginatedResult(whereWithStatus, outgoingTasksInclude, true);
  }

  async completeTask(taskId: string, userId: string, completeTaskDto: CompleteTaskDto) {
    const task = await this.prisma.task.findFirst({ where: { id: taskId, createdById: userId } });

    if (!task) throw new NotFoundException('task not found');

    const taskState = await this.prisma.taskState.findUnique({
      where: {
        id: completeTaskDto.stateId,
      },
    });

    if (completeTaskDto.file) {
      completeTaskDto.file = (await this.fileService.moveFileFromTempToSaved([completeTaskDto.file]))[0].id;
    }

    await this.prisma.task.update({
      where: { id: taskId },
      data: {
        isCompleted: true,
        completedAt: new Date(),
        TaskState: {
          connect: { id: taskState.id },
        },
        completeDesc: completeTaskDto.description,
        ...(completeTaskDto.file
          ? {
              CompleteFile: {
                connect: {
                  id: completeTaskDto.file,
                },
              },
            }
          : {}),
      },
    });
    return 'task completed successfully';
  }

  async findAllByUser(userId: string, { page, limit, type, state: s, search }: GetAllTasksByOrgId) {
    const model = this.prisma.task;
    const $page = Number(page) || 1;
    const $limit = Number(limit) || 10;

    const where: any = {
      ...this.queryUtil.createSearchQuery(search, ['name', 'id', 'description']),
      status: 'ACTIVE',
    };

    const state = s && (s.toUpperCase() as RecipientAnswerState | 'COMPLETED');

    if (type === TaskVariant.OUTGOING) {
      const withPagination = this.pagination.paginate(
        model,
        { page: $page, limit: $limit },
        { createdById: userId, ...where },
        {
          TaskState: true,
          TaskType: true,
          CreatedByOrganization: true,
        },
        { createdAt: 'desc' },
      );

      return withPagination;
    }

    if (state && state === 'COMPLETED') {
      const withPagination = this.pagination.paginate(
        model,
        { page: $page, limit: $limit },
        {
          isCompleted: true,
          Recipients: {
            some: {
              userId,
            },
          },
          ...where,
        },
        {
          TaskState: true,
          TaskType: true,
          CreatedByOrganization: true,
        },
        { createdAt: 'desc' },
      );

      return withPagination;
    }

    if (!state) {
      const withPagination = this.pagination.paginate(
        model,
        { page: $page, limit: $limit },
        {
          Recipients: {
            some: {
              userId,
            },
          },
          ...where,
        },
        {
          TaskState: true,
          TaskType: true,
          CreatedByOrganization: true,
        },
        { createdAt: 'desc' },
      );

      return withPagination;
    } //fix

    const recipients = await this.prisma.recipient.findMany({
      where: { userId },
      include: {
        Answer: {
          take: 1,
          orderBy: {
            updatedAt: 'desc',
          },
        },
      },
    });

    const recipientIds = recipients
      .filter((recipient) => recipient.Answer?.[0]?.state === state)
      .map((recipient) => recipient.id);

    const withPagination = this.pagination.paginate(
      model,
      { page: $page, limit: $limit },
      {
        Recipients: {
          some: {
            id: {
              in: recipientIds,
            },
          },
        },
        ...where,
      },
      {
        TaskState: true,
        TaskType: true,
        CreatedByOrganization: true,
      },
      { createdAt: 'desc' },
    );

    return withPagination;
  }

  async findOne({ id, userId, orgId }: { id: string; userId?: string; orgId?: string }) {
    const isPersonal = !orgId;

    const task = await this.prisma.task.findUnique({
      where: { id },
      include: {
        TaskState: true,
        TaskType: true,
        SubTasks: {
          include: {
            CreatedByOrganization: {
              include: {
                grade: true,
              },
            },
            TaskType: true,
            TaskState: true,
            Parent: {
              include: {
                CreatedBy: {
                  include: { position: true },
                },
                CreatedByOrganization: {
                  include: { grade: true },
                },
                TaskType: true,
                TaskState: true,
                files: true,
              },
            },
          },
        },
        CreatedByOrganization: {
          include: {
            grade: true,
          },
        },
        Parent: {
          include: {
            CreatedBy: {
              include: { position: true },
            },
            CreatedByOrganization: {
              include: { grade: true },
            },
            TaskType: true,
            TaskState: true,
            files: true,
          },
        },
        Recipients: {
          include: {
            User: {
              include: { position: true },
            },
            Answer: {
              include: {
                files: true,
                type: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
            Organization: {
              include: {
                grade: true,
              },
            },
          },
        },
        CreatedBy: {
          include: { position: true },
        },
        files: true,
      },
    });

    if (!task) {
      throw new NotFoundException(`Task with ID "${id}" not found`);
    }

    return task;
  }

  async update(id: string, updateTaskDto: UpdateTaskDto, orgId: string) {
    const task = await this.prisma.task.findUnique({
      where: { id, createdByOrganizationId: orgId },
    });

    if (!task) {
      throw new NotFoundException(`Task with ID "${id}" not found`);
    }

    return this.prisma.task.update({
      where: { id },
      data: updateTaskDto,
    });
  }

  async createSubtask(createTaskDto: CreateTaskDto, parentId: string, userId: string, organizationId: string) {
    const { recipients, controllerId, ...newTask } = createTaskDto;

    const existOrganization = await this.prisma.organization.findUnique({
      where: { id: organizationId },
      include: { grade: true, Responsible: true, Children: true, UnderControl: true },
    });

    if (!existOrganization) throw new NotFoundException(`Organization with ID "${organizationId}" not found`);

    if (
      !recipients.every((recipient) => {
        if (recipient.organizationId === 'all') return true;
        return (
          existOrganization.Children.some((child) => child.id === recipient.organizationId) ||
          existOrganization.UnderControl.some((underControl) => underControl.id === recipient.organizationId) ||
          existOrganization.id === recipient.organizationId
        );
      })
    )
      throw new NotAcceptableException(`Organization can only give tasks to children or under control organizations`);

    if (!existOrganization.Responsible.some((responsible) => responsible.id === userId))
      throw new NotAcceptableException(`None Responsible User cannot give tasks`);

    const existParentTask = await this.prisma.task.findUnique({ where: { id: parentId } });

    if (!existParentTask) throw new NotFoundException(`Parent Task with ID "${parentId}" not found`);

    const taskState = await this.prisma.taskState.findFirst({ orderBy: { order: 'asc' } });

    if (!taskState) throw new NotFoundException(`TaskState not found`);

    if (createTaskDto.taskTypeId) {
      const existTaskType = await this.prisma.taskType.findUnique({ where: { id: createTaskDto.taskTypeId } });

      if (!existTaskType) throw new NotFoundException(`TaskType with ID "${createTaskDto.taskTypeId}" not found`);
    }

    if (createTaskDto.parentId) {
      const existParentTask = await this.prisma.task.findUnique({ where: { id: createTaskDto.parentId } });

      if (!existParentTask) throw new NotFoundException(`Parent Task with ID "${createTaskDto.parentId}" not found`);
    }

    let files: IId[] = [];

    if (createTaskDto.files) {
      files = await this.fileService.moveFileFromTempToSaved(createTaskDto.files);
    }

    // Create the task and get the task ID
    const createdSubTask = await this.prisma.task.create({
      data: {
        ...newTask,
        files: {
          connect: files,
        },
        taskStateId: taskState.id,
        parentId,
        createdById: userId,
        createdByOrganizationId: organizationId,
        userId: controllerId,
      },
    });

    const taskId = createdSubTask.id;

    for (const recipient of recipients) {
      const organization = existOrganization;

      if (recipient.organizationId === 'all') {
        const orgs = [...organization.Children, ...organization.UnderControl];

        for (const org of orgs) {
          await this.prisma.recipient.create({
            data: {
              organizationId: org.id,
              userId: null,
              taskId: taskId, // Ensure taskId is assigned
            },
          });
        }

        break;
      }

      const existOrganizationUser = await this.prisma.organization.findFirst({
        where: {
          OR: [
            { Children: { some: { id: recipient.organizationId } } },
            { UnderControl: { some: { id: recipient.organizationId } } },
            { id: recipient.organizationId },
          ],
        },
      });

      if (!existOrganizationUser) {
        throw new NotFoundException(
          `Organization with ID "${recipient.organizationId}" is not a child or under control`,
        );
      }

      if (recipient.userId?.length) {
        for (const userId of recipient.userId) {
          const existUser = await this.prisma.user.findUnique({
            include: { Organization: true },
            where: { id: userId },
          });

          if (!existUser) {
            throw new NotFoundException(`User with ID "${userId}" not found`);
          }

          if (organization.id !== recipient.organizationId) {
            const existCheckUser = existUser.Organization.some((org) => org.id === recipient.organizationId);

            if (!existCheckUser) {
              throw new ForbiddenException(
                `User with ID "${userId}" is not a worker of organization "${recipient.organizationId}"`,
              );
            }
          } else {
            const existCheckUser = await this.prisma.user.findFirst({
              where: {
                OR: [
                  { mainOrganizationId: recipient.organizationId },
                  { Organization: { some: { id: recipient.organizationId } } },
                ],
                id: userId,
              },
            });

            if (!existCheckUser) {
              throw new ForbiddenException(
                `User with ID "${userId}" is not an Employee of organization "${recipient.organizationId}"`,
              );
            }
          }

          await this.prisma.recipient.create({
            data: {
              organizationId: recipient.organizationId,
              userId: userId !== '' ? userId : null, // Ensure userId is null if empty
              taskId: taskId, // Ensure taskId is assigned
            },
          });
        }
      } else {
        if (organization.id === recipient.organizationId) {
          throw new ForbiddenException(`Organization with ID "${recipient.organizationId}" is the same as the creator`);
        }

        await this.prisma.recipient.create({
          data: {
            organizationId: recipient.organizationId,
            userId: null,
            taskId: taskId, // Ensure taskId is assigned
          },
        });
      }
    }

    return createdSubTask;
  }

  async readTask({ recipientId }: ReadTaskDto) {
    const recipient = await this.prisma.recipient.findFirst({
      where: { id: recipientId },
    });

    if (!recipient) {
      throw new NotFoundException('Recipient not found');
    }

    await this.prisma.recipient.update({
      where: {
        id: recipientId,
      },
      data: {
        isRead: true,
      },
    });
  }

  async getTaskStatistics(orgId: string, startDate?: string, endDate?: string) {
    // Start can be today 00:00:00 if not provided
    // End can be today 23:59:59 if not provided
    const start = startDate ? new Date(startDate) : new Date(new Date().setHours(0, 0, 0, 0));
    const end = endDate ? new Date(endDate) : new Date(new Date().setHours(23, 59, 59, 999));

    const tasks = await this.prisma.task.findMany({
      where: {
        createdByOrganizationId: orgId,
        createdAt: {
          gte: start.toISOString(),
          lte: end.toISOString(),
        },
      },
      select: {
        name: true,
        createdAt: true,
        description: true,
        CreatedByOrganization: {
          select: {
            name: true,
            region: true,
            district: true,
            section: true,
            address: true,
          },
        },
        isCompleted: true,
        CreatedBy: {
          select: {
            fullName: true,
            phone: true,
            position: true,
          },
        },
        completedAt: true,
        dueDate: true,
        TaskState: {
          select: {
            name: true,
            key: true,
          },
        },
        Recipients: {
          select: {
            isRead: true,
            isCompleted: true,
            Organization: {
              select: {
                name: true,
                region: true,
                district: true,
                section: true,
                address: true,
              },
            },
            User: {
              select: {
                fullName: true,
                phone: true,
                position: true,
              },
            },
            Answer: {
              select: {
                description: true,
                state: true,
                createdAt: true,
                rejectReason: true,
                type: true,
              },
            },
          },
        },
      },
    });
    const excel = await this.taskExcelService.exportTasksToExcel(tasks);
    return excel;
  }

  // async updateTaskRecipients(taskId: string, recipients: RecipientDto[]) {
  //   if (!taskId) throw new Error('taskId is required');

  //   if (!Array.isArray(recipients) || recipients.length === 0) {
  //     throw new Error('recipients must be a non-empty array');
  //   }

  //   const task = await this.prisma.task.findUnique({ where: { id: taskId } });
  //   if (!task) throw new NotFoundException(`Task with id ${taskId} not found`);

  //   for (const recipient of recipients) {
  //     const organization = await this.prisma.organization.findUnique({
  //       where: { id: recipient.organizationId },
  //     });
  //     if (!organization) {
  //       throw new NotFoundException(`Organization with id ${recipient.organizationId} not found`);
  //     }

  //     if (recipient.userId && recipient.userId.length > 0) {
  //       for (const userId of recipient.userId) {
  //         const user = await this.prisma.user.findUnique({ where: { id: userId } });
  //         if (!user) {
  //           throw new NotFoundException(`User with id ${userId} not found`);
  //         }

  //         const existingRecipient = await this.prisma.recipient.findFirst({
  //           where: {
  //             taskId,
  //             organizationId: recipient.organizationId,
  //             userId,
  //           },
  //         });

  //         if (!existingRecipient) {
  //           await this.prisma.recipient.create({
  //             data: {
  //               organizationId: recipient.organizationId,
  //               userId,
  //               taskId,
  //             },
  //           });
  //         }
  //       }
  //     } else {
  //       const existingRecipient = await this.prisma.recipient.findFirst({
  //         where: {
  //           taskId,
  //           organizationId: recipient.organizationId,
  //           userId: null,
  //         },
  //       });

  //       if (!existingRecipient) {
  //         await this.prisma.recipient.create({
  //           data: {
  //             organizationId: recipient.organizationId,
  //             userId: null,
  //             taskId,
  //           },
  //         });
  //       }
  //     }
  //   }

  //   const updatedTask = await this.prisma.task.findUnique({
  //     where: { id: taskId },
  //     include: { Recipients: true },
  //   });

  //   return {
  //     id: updatedTask.id,
  //     name: updatedTask.name,
  //     Recipients: updatedTask.Recipients.map((r) => ({
  //       id: r.id,
  //       organizationId: r.organizationId,
  //       userId: r.userId,
  //       taskId: r.taskId,
  //     })),
  //   };
  // }

  // DTO fayli: update-task-recipients.dto.ts

  // Service fayli

  // async updateTaskRecipients(taskId: string, recipients: RecipientDto[]) {
  //   if (!taskId) throw new Error('taskId is required');

  //   if (!Array.isArray(recipients) || recipients.length === 0) {
  //     throw new Error('recipients must be a non-empty array');
  //   }

  //   const task = await this.prisma.task.findUnique({ where: { id: taskId } });
  //   if (!task) throw new NotFoundException(`Task with id ${taskId} not found`);

  //   for (const recipient of recipients) {
  //     const organization = await this.prisma.organization.findUnique({
  //       where: { id: recipient.organizationId },
  //     });
  //     if (!organization) {
  //       throw new NotFoundException(`Organization with id ${recipient.organizationId} not found`);
  //     }

  //     if (recipient.positionId) {
  //       const position = await this.prisma.organizationTypePosition.findUnique({
  //         where: { id: recipient.positionId },
  //       });
  //       if (!position) {
  //         throw new NotFoundException(`Position with id ${recipient.positionId} not found`);
  //       }

  //       const usersWithPosition = await this.prisma.user.findMany({
  //         where: {
  //           positionId: recipient.positionId,
  //         },
  //       });

  //       for (const user of usersWithPosition) {
  //         const existingRecipient = await this.prisma.recipient.findFirst({
  //           where: {
  //             taskId,
  //             organizationId: user.mainOrganizationId,
  //             userId: user.id,
  //           },
  //         });

  //         if (!existingRecipient) {
  //           await this.prisma.recipient.create({
  //             data: {
  //               organizationId: user.mainOrganizationId,
  //               userId: user.id,
  //               taskId,
  //             },
  //           });
  //         }
  //       }
  //     } else if (recipient.userId && recipient.userId.length > 0) {
  //       for (const userId of recipient.userId) {
  //         const user = await this.prisma.user.findUnique({ where: { id: userId } });
  //         if (!user) {
  //           throw new NotFoundException(`User with id ${userId} not found`);
  //         }

  //         const existingRecipient = await this.prisma.recipient.findFirst({
  //           where: {
  //             taskId,
  //             organizationId: recipient.organizationId,
  //             userId,
  //           },
  //         });

  //         if (!existingRecipient) {
  //           await this.prisma.recipient.create({
  //             data: {
  //               organizationId: recipient.organizationId,
  //               userId,
  //               taskId,
  //             },
  //           });
  //         }
  //       }
  //     } else {
  //       const existingRecipient = await this.prisma.recipient.findFirst({
  //         where: {
  //           taskId,
  //           organizationId: recipient.organizationId,
  //           userId: null,
  //         },
  //       });

  //       if (!existingRecipient) {
  //         await this.prisma.recipient.create({
  //           data: {
  //             organizationId: recipient.organizationId,
  //             userId: null,
  //             taskId,
  //           },
  //         });
  //       }
  //     }
  //   }

  //   const updatedTask = await this.prisma.task.findUnique({
  //     where: { id: taskId },
  //     include: { Recipients: true },
  //   });

  //   return {
  //     id: updatedTask.id,
  //     name: updatedTask.name,
  //     Recipients: updatedTask.Recipients.map((r) => ({
  //       id: r.id,
  //       organizationId: r.organizationId,
  //       userId: r.userId,
  //       taskId: r.taskId,
  //     })),
  //   };
  // }

  // async updateTaskRecipients(taskId: string, recipients: RecipientDto[]) {
  //   if (!taskId) throw new Error('taskId is required');
  //   if (!Array.isArray(recipients) || recipients.length === 0) {
  //     throw new Error('recipients must be a non-empty array');
  //   }

  //   const task = await this.prisma.task.findUnique({ where: { id: taskId } });
  //   if (!task) throw new NotFoundException(`Task with id ${taskId} not found`);

  //   for (const recipient of recipients) {
  //     const { organizationId, userId, positionId } = recipient;

  //     // 1. Tashkilot tekshiruvi (agar organizationId bor bo‘lsa)
  //     if (organizationId) {
  //       const organization = await this.prisma.organization.findUnique({
  //         where: { id: organizationId },
  //       });

  //       if (!organization) {
  //         throw new NotFoundException(`Organization with id ${organizationId} not found`);
  //       }
  //     }

  //     // 2. Agar positionId ko‘rsatilgan bo‘lsa
  //     if (positionId) {
  //       const position = await this.prisma.organizationTypePosition.findUnique({
  //         where: { id: positionId },
  //       });

  //       if (!position) {
  //         throw new NotFoundException(`Position with id ${positionId} not found`);
  //       }

  //       const usersWithPosition = await this.prisma.user.findMany({
  //         where: { positionId },
  //       });

  //       for (const user of usersWithPosition) {
  //         if (!user.mainOrganizationId) {
  //           throw new BadRequestException(`User with id ${user.id} does not have a mainOrganizationId`);
  //         }

  //         const exists = await this.prisma.recipient.findFirst({
  //           where: {
  //             taskId,
  //             userId: user.id,
  //             organizationId: user.mainOrganizationId,
  //           },
  //         });

  //         if (!exists) {
  //           await this.prisma.recipient.create({
  //             data: {
  //               taskId,
  //               userId: user.id,
  //               organizationId: user.mainOrganizationId,
  //             },
  //           });
  //         }
  //       }
  //     }

  //     // 3. Agar userId[] berilgan bo‘lsa (individual userlar)
  //     else if (userId && userId.length > 0) {
  //       for (const uid of userId) {
  //         const user = await this.prisma.user.findUnique({ where: { id: uid } });

  //         if (!user) {
  //           throw new NotFoundException(`User with id ${uid} not found`);
  //         }

  //         const orgId = organizationId ?? user.mainOrganizationId;
  //         if (!orgId) {
  //           throw new BadRequestException(`Organization ID is required for user ${uid}`);
  //         }

  //         const exists = await this.prisma.recipient.findFirst({
  //           where: {
  //             taskId,
  //             userId: uid,
  //             organizationId: orgId,
  //           },
  //         });

  //         if (!exists) {
  //           await this.prisma.recipient.create({
  //             data: {
  //               taskId,
  //               userId: uid,
  //               organizationId: orgId,
  //             },
  //           });
  //         }
  //       }
  //     }

  //     // 4. Faqat organizationId berilgan bo‘lsa (userId va positionId yo‘q)
  //     else if (organizationId) {
  //       const exists = await this.prisma.recipient.findFirst({
  //         where: {
  //           taskId,
  //           organizationId,
  //           userId: null,
  //         },
  //       });

  //       if (!exists) {
  //         await this.prisma.recipient.create({
  //           data: {
  //             taskId,
  //             organizationId,
  //             userId: null,
  //           },
  //         });
  //       }
  //     }

  //     // 5. Noto‘g‘ri recipient holati
  //     else {
  //       throw new BadRequestException('Each recipient must have either positionId, userId[], or organizationId');
  //     }
  //   }

  //   // Yangilangan taskni qaytarish
  //   const updatedTask = await this.prisma.task.findUnique({
  //     where: { id: taskId },
  //     include: { Recipients: true },
  //   });

  //   return {
  //     id: updatedTask.id,
  //     name: updatedTask.name,
  //     Recipients: updatedTask.Recipients.map((r) => ({
  //       id: r.id,
  //       organizationId: r.organizationId,
  //       userId: r.userId,
  //       taskId: r.taskId,
  //     })),
  //   };
  // }

  async getAvailableControllers(organizationId: string) {
    const controllers = await this.prisma.user.findMany({
      where: {
        Organization: {
          some: {
            id: organizationId,
          },
        },
        status: 'ACTIVE',
      },
      select: {
        id: true,
        fullName: true,
        username: true,
        position: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        fullName: 'asc',
      },
    });

    return controllers;
  }

  async updateTaskRecipients(taskId: string, recipients: RecipientDto[]) {
    if (!taskId) throw new Error('taskId is required');
    if (!Array.isArray(recipients) || recipients.length === 0) {
      throw new Error('recipients must be a non-empty array');
    }

    const task = await this.prisma.task.findUnique({ where: { id: taskId } });
    if (!task) throw new NotFoundException(`Task with id ${taskId} not found`);

    for (const recipient of recipients) {
      const { organizationId, userId, positionId } = recipient;

      if (organizationId) {
        const organization = await this.prisma.organization.findUnique({
          where: { id: organizationId },
        });

        if (!organization) {
          throw new NotFoundException(`Organization with id ${organizationId} not found`);
        }
      }

      if (positionId) {
        const position = await this.prisma.organizationTypePosition.findUnique({
          where: { id: positionId },
        });

        if (!position) {
          throw new NotFoundException(`Position with id ${positionId} not found`);
        }

        const usersWithPosition = await this.prisma.user.findMany({
          where: { positionId },
        });

        for (const user of usersWithPosition) {
          if (!user.mainOrganizationId) {
            // console.warn(`User with id ${user.id} does not have a mainOrganizationId — skipping.`);
            continue;
          }

          const exists = await this.prisma.recipient.findFirst({
            where: {
              taskId,
              userId: user.id,
              organizationId: user.mainOrganizationId,
            },
          });

          if (!exists) {
            await this.prisma.recipient.create({
              data: {
                taskId,
                userId: user.id,
                organizationId: user.mainOrganizationId,
              },
            });
          }
        }
      } else if (userId && userId.length > 0) {
        for (const uid of userId) {
          const user = await this.prisma.user.findUnique({ where: { id: uid } });

          if (!user) {
            throw new NotFoundException(`User with id ${uid} not found`);
          }

          const orgId = organizationId ?? user.mainOrganizationId;
          if (!orgId) {
            throw new BadRequestException(`Organization ID is required for user ${uid}`);
          }

          const exists = await this.prisma.recipient.findFirst({
            where: {
              taskId,
              userId: uid,
              organizationId: orgId,
            },
          });

          if (!exists) {
            await this.prisma.recipient.create({
              data: {
                taskId,
                userId: uid,
                organizationId: orgId,
              },
            });
          }
        }
      } else if (organizationId) {
        const exists = await this.prisma.recipient.findFirst({
          where: {
            taskId,
            organizationId,
            userId: null,
          },
        });

        if (!exists) {
          await this.prisma.recipient.create({
            data: {
              taskId,
              organizationId,
              userId: null,
            },
          });
        }
      } else {
        throw new BadRequestException('Each recipient must have either positionId, userId[], or organizationId');
      }
    }

    const updatedTask = await this.prisma.task.findUnique({
      where: { id: taskId },
      include: { Recipients: true },
    });

    return {
      id: updatedTask.id,
      name: updatedTask.name,
      Recipients: updatedTask.Recipients.map((r) => ({
        id: r.id,
        organizationId: r.organizationId,
        userId: r.userId,
        taskId: r.taskId,
      })),
    };
  }
}
