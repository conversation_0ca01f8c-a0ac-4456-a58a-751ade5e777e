import { applyDecorators, SetMetadata } from '@nestjs/common';

interface ProtectRouteOption {
  isPublic?: boolean;
  context?: 'WORKSPACE' | 'PERSONAL';
  onlyAdmin?: boolean;
}

/**
 * ProtectRoute decorator
 * @param {ProtectRouteOption} options - Configuration options
 * @param {('WORKSPACE'|'PERSONAL')} [options.context='PERSONAL'] - Context for the route
 * @param {boolean} [options.isPublic=false] - Whether the route is publicly accessible
 * @param {boolean} [options.onlyAdmin=false] - Whether the route is restricted to admins only
 * @returns {MethodDecorator} Decorator function
 *
 * @example
 * ```typescript
 * @ProtectedRoute({ isPublic: false, context: 'WORKSPACE' })
 * @Get()
 * async getAll(@Query() query: BaseQueryParams, @Context('organizationId') organizationId: string) {
 *   return this.organizationService.getAll({ organizationId, ...query });
 * }
 * ```
 *
 * @example
 * ```typescript
 * // Public endpoint
 * @ProtectedRoute({ isPublic: true })
 * @Get('public-data')
 * getPublicData() {
 *   return this.dataService.getPublicInfo();
 * }
 * ```
 *
 * @example
 * ```typescript
 * // Admin-only endpoint
 * @ProtectedRoute({ onlyAdmin: true, context: 'WORKSPACE' })
 * @Post('admin-action')
 * adminAction(@Body() payload: AdminPayloadDto) {
 *   return this.adminService.processAction(payload);
 * }
 * ```
 */
export const ProtectedRoute = ({ context = 'PERSONAL', isPublic = false, onlyAdmin = false }: ProtectRouteOption) => {
  const decorators = [];

  if (isPublic) {
    decorators.push(SetMetadata('IS_PUBLIC', true));
  } else {
    decorators.push(
      SetMetadata('IS_PUBLIC', false),
      SetMetadata('CONTEXT', context),
      SetMetadata('ONLY_ADMIN', onlyAdmin),
    );
  }

  return applyDecorators(...decorators);
};
