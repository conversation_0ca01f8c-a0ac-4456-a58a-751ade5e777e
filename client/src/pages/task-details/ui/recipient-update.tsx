import { useState } from 'react'
import TaskCreateForm from './recipient-update-form'

export type TRecipent = {
	organizationId: string
	userId: string[]
	positionId?: string
}

const RecipientUpdate = () => {
	const [recipents, setRecipents] = useState<TRecipent[]>([
		{
			organizationId: '',
			userId: []
		}
	])

	return (
		<TaskCreateForm
			recipents={recipents}
			setRecipents={setRecipents}
		/>
	)
}

export default RecipientUpdate
