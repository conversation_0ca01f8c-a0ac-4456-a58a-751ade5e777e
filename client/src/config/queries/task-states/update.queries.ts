import { axiosPrivate } from '@/config/api'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { taskStatesEndpoints } from '@/config/api/endpoints'
import { notification } from 'antd'
import { TaskStates } from './create.queries'

export const useUpdateTaskStates = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async ({ id, ...data }: { id: string }) =>
			(await axiosPrivate.patch<ApiResponse<TaskStates>>(taskStatesEndpoints.one(id), data)).data,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [taskStatesEndpoints.all] })
			notification.success({
				message: 'Muvaffaqiyatli yangilandi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: '<PERSON><PERSON><PERSON><PERSON> xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
