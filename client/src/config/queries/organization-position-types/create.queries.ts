import { axiosPrivate } from '@/config/api'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { organizationPositionTypeEndpoints } from '@/config/api/endpoints'
import { notification } from 'antd'

export interface OrganizationPositionTypes {
	type?: any
	id?: string
	name: string
	description: string
	typeId: string
}

export const useCreateOrganizationPositionType = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async (data: OrganizationPositionTypes) =>
			await axiosPrivate.post<ApiResponse<OrganizationPositionTypes>>(
				organizationPositionTypeEndpoints.all,
				data
			),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [organizationPositionTypeEndpoints.all] })
			notification.success({
				message: 'Muvaffaqiyatli yaratildi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: '<PERSON><PERSON><PERSON><PERSON> xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
