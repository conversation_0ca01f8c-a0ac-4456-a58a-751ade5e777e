import Layout from 'antd/es/layout'
import { useLocation } from 'react-router-dom'
import { Button, Menu, type MenuProps } from 'antd'
import { buildMenuItems, clientEndpoints } from '@/config/constants.tsx'
import { useContext, useInsertionEffect, useMemo, useState } from 'react'
import { ThemeContext } from '@/providers/theme-providers.tsx'
import { X } from 'lucide-react'

import type {
	MenuDividerType,
	MenuItemGroupType,
	MenuItemType,
	SubMenuType
} from 'antd/es/menu/interface'
import { useSidebar } from './utils/use-sidebar'
import { useHeaderTitle } from '@/providers/header-title-providers'
import { WorkspaceSelect } from '@/shared/ui/workspace-select'
import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { ROLE } from '@/shared/types/role.type'

type MenuItem =
	| MenuDividerType
	| MenuItemType
	| MenuItemGroupType<MenuItemType>
	| SubMenuType<MenuItemType>

type TSidebar = {
	workspace?: 'personal' | 'organization'
}

export const Sidebar = ({ workspace = 'personal' }: TSidebar) => {
	const { setHeaderTitle } = useHeaderTitle()
	const { collapsed, setLg, toggleSidebar } = useSidebar()
	const [activeKey, setActiveKey] = useState('Dashboard')
	const location = useLocation()
	const theme = useContext(ThemeContext)
	const { data: user } = useAuthMe()
	const organizationId = localStorage.getItem('organizationId')

	const menuItems: MenuProps['items'] = useMemo(() => {
		const accessToShow = (item: (typeof clientEndpoints)[number]) => {
			// Check for admin-only items
			if (item.adminOnly) {
				return (
					user?.role === ROLE.ADMIN &&
					user.ResponsibleFor.find((org: { id: string }) => org.id === JSON.parse(organizationId!))
						?.grade.level === -1
				)
			}

			// Check for Sectorlar - show only if grade level is 10 or less
			if (item.key === 'Sektorlar') {
				const currentOrg = user?.ResponsibleFor.find(
					(org: { id: string }) => org.id === JSON.parse(organizationId!)
				)
				return currentOrg?.grade.level !== undefined && currentOrg.grade.level <= 10
			}

			return true
		}

		return buildMenuItems(
			clientEndpoints
				.filter(item => ['all', workspace].includes(item.workspace))
				.filter(item => accessToShow(item))
				.map(item => ({
					...item,
					children: item.children?.filter(child => ['all', workspace].includes(child.workspace))
				}))
				.map(item => ({
					...item,
					children: item.children?.filter(each => accessToShow(each))
				}))
		)
	}, [workspace, user, organizationId])

	const filteredMenuItems = useMemo(() => {
		return menuItems?.reduce((acc: MenuItem[], item) => {
			if (!item) return acc
			acc.push(item as MenuItem)
			return acc
		}, [])
	}, [menuItems])

	useInsertionEffect(() => {
		const path = location.pathname
		const active = clientEndpoints.find(item => {
			return item.path === path
		})
		if (active) {
			setActiveKey(active.key)
			setHeaderTitle(active.key)
			return
		}

		const subActive = clientEndpoints.find(item => {
			return item.children?.find(child => child.path === path)
		})
		if (subActive) {
			setActiveKey(subActive.key)
			setHeaderTitle(subActive.key)
		}
	}, [location.pathname, setHeaderTitle])

	return (
		<Layout.Sider
			style={{
				height: '100vh',
				position: 'fixed',
				overflow: 'auto',
				insetInlineStart: 0,
				top: 0,
				bottom: 0,
				scrollbarWidth: 'thin',
				scrollbarGutter: 'stable',
				scrollbarColor: '#6366F1 #6366F1',
				zIndex: 2
			}}
			width={240}
			breakpoint='lg'
			onBreakpoint={broken => {
				setLg(broken)
			}}
			collapsedWidth={0}
			trigger={null}
			collapsible
			collapsed={collapsed}
			className='relative flex flex-col h-full text-white custom-scrollbar'>
			<div className='relative flex flex-col px-2 py-3 border-b border-gray-800'>
				<div className='flex items-center justify-between'>
					<WorkspaceSelect />
					<Button
						type='text'
						className='lg:!hidden absolute top-1 right-2 text-gray-400 hover:text-white hover:bg-gray-800 p-2'
						onClick={() => toggleSidebar()}
						icon={<X size={18} />}
					/>
				</div>
			</div>
			<div className='flex-1 overflow-y-auto'>
				<Menu
					mode='inline'
					theme={theme.theme}
					key={activeKey}
					defaultSelectedKeys={[activeKey]}
					onClick={({ key }) => {
						setHeaderTitle(key)
					}}
					items={filteredMenuItems}
					className='!bg-transparent border-r-0'
				/>
			</div>
		</Layout.Sider>
	)
}
