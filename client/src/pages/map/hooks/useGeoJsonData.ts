import { useMemo } from 'react'
import { FeatureCollection, Feature } from 'geojson'
import { Response } from '@/config/queries/map/map.queries'

/**
 * Хук для преобразования данных API в формат GeoJSON
 */
export const useGeoJsonData = (data: Response[] | undefined) => {
	return useMemo((): FeatureCollection | null => {
		if (!data || !Array.isArray(data)) return null

		const features: Feature[] = []
		let featureIndex = 0

		data.forEach(item => {
			if (item.area && Array.isArray(item.area)) {
				item.area.forEach(areaItem => {
					if (areaItem.geometry) {
						const feature: Feature = {
							type: 'Feature',
							geometry: areaItem.geometry as GeoJSON.Geometry,
							properties: {
								id: item.id,
								region_id: item.region_id,
								district_id: item.district_id,
								section_id: item.section_id,
								type: item.type,
								lat: item.lat,
								lng: item.lng,
								colorIndex: featureIndex
							}
						}
						features.push(feature)
						featureIndex++
					}
				})
			}
		})

		return {
			type: 'FeatureCollection',
			features
		}
	}, [data])
}
