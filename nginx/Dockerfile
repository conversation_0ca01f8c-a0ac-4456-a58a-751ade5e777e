FROM nginx:alpine

# Nginx konfiguratsiya fayllarini o'chirish
RUN rm /etc/nginx/conf.d/default.conf

# <PERSON>i konfiguratsiya fayllarini ko'chirish
COPY conf.d/default.conf /etc/nginx/conf.d/

# Nginx user permissions
RUN chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d

# Update permissions for certbot challenges
RUN mkdir -p /var/www/certbot && \
    chown -R nginx:nginx /var/www/certbot

EXPOSE 80
EXPOSE 443

CMD ["nginx", "-g", "daemon off;"]