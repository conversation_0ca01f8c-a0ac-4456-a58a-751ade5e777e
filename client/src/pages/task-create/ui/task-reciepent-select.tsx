import { Form, Radio } from 'antd'

type TRecipientTypeSelectorProps = {
	recipientType: string
	handleRecipientTypeChange: (e: any) => void
	isSelfOrganization: boolean
}

export const RecipientTypeSelector = ({
	recipientType,
	handleRecipientTypeChange,
	isSelfOrganization
}: TRecipientTypeSelectorProps) => {
	return (
		<Form.Item
			label='Qabul qiluvchi turi'
			name='recipientType'
			initialValue='organizations'>
			<Radio.Group
				value={recipientType}
				onChange={handleRecipientTypeChange}>
				<Radio value='organizations'>Tashkilotlarga</Radio>
				{!isSelfOrganization && <Radio value='employees'>Tashkilot hodimlariga</Radio>}
			</Radio.Group>
		</Form.Item>
	)
}
