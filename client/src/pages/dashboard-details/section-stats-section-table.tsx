import { useGetDashboardDetailsSection } from '@/config/queries/dashboard-details/get-dashboard-details'
import { useGetSectionExcel } from '@/config/queries/dashboard-details/get-dashboard-details-excel'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { Button, Table } from 'antd'
import { DownloadOutlined } from '@ant-design/icons'
import { useSearchParams } from 'react-router-dom'

const SectionStatsTable = () => {
	const { data: section, isLoading } = useGetDashboardDetailsSection()
	const [searchParams, setSearchParams] = useSearchParams()
	const organizationId = searchParams.get('orgId')
	const sectionExcel = useGetSectionExcel()

	const currentPage = parseInt(searchParams.get('page') || '1')
	const pageSize = parseInt(searchParams.get('limit') || '10')

	const handleDownloadExcel = () => {
		sectionExcel.mutate(organizationId || undefined)
	}

	const columns = [
		{
			key: '#',
			title: '#',
			render: (_: any, _rec: any, index: number) => formatTableIndex(currentPage, pageSize, index)
		},
		{
			title: 'Nomi',
			dataIndex: 'name',
			key: 'name'
		},
		{
			title: 'Manzil',
			dataIndex: 'address',
			key: 'address'
		},
		{
			title: 'Tuman',
			dataIndex: ['district', 'name'],
			key: 'districtName'
		},
		{
			title: 'Viloyat',
			dataIndex: ['region', 'name'],
			key: 'regionName'
		},
		{
			title: 'Turi',
			dataIndex: ['type', 'name'],
			key: 'typeName'
		}
	]

	const handleTableChange = (pagination: any) => {
		const newPage = pagination.current
		const newPageSize = pagination.pageSize

		setSearchParams(prev => {
			const newParams = new URLSearchParams(prev)
			newParams.set('page', newPage.toString())
			newParams.set('limit', newPageSize.toString())
			return newParams
		})
	}

	return (
		<div className='p-4'>
			<div className='flex justify-between items-center mb-4'>
				<h1 className='text-2xl font-bold'>Barcha mahallalar ro`yhati</h1>
				<Button
					type='primary'
					icon={<DownloadOutlined />}
					onClick={handleDownloadExcel}
					loading={sectionExcel.isPending}>
					Excel yuklab olish
				</Button>
			</div>
			<Table
				columns={columns}
				dataSource={section?.data || []}
				rowKey='id'
				pagination={{
					current: currentPage,
					pageSize: pageSize,

					total: section?.meta?.total || 0,
					showTotal: (total, range) => `${range[0]}-${range[1]} tadan ${total}  ta`,
					showSizeChanger: true
				}}
				loading={isLoading}
				onChange={handleTableChange}
			/>
		</div>
	)
}

export default SectionStatsTable
