import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../common';
import { CreateInfractionReasonDto } from './dto/create-infraction-reason.dto';
import { UpdateInfractionReasonDto } from './dto/update-infraction-reason.dto';
import { FileService } from '../file/file.service';

interface IId {
  id: string;
}

@Injectable()
export class InfractionReasonService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly fileService: FileService,
  ) {}

  async create(dto: CreateInfractionReasonDto) {
    const infraction = await this.prisma.infraction.findUnique({
      where: { id: dto.infractionId },
    });

    if (!infraction) {
      throw new NotFoundException(`Infraction with ID ${dto.infractionId} does not exist`);
    }

    let fileId: string | undefined;

    if (dto.files?.length) {
      const movedFiles = await this.fileService.moveFileFromTempToSaved(dto.files);
      if (movedFiles.length) {
        fileId = movedFiles[0].id;
      }
    }

    return this.prisma.infractionReason.create({
      data: {
        name: dto.name,
        description: dto.description,
        infractionId: dto.infractionId,
        fileId,
      },
      include: {
        file: {
          select: {
            id: true,
          },
        },
      },
    });
  }

  async findAll() {
    return this.prisma.infractionReason.findMany({
      include: {
        file: true,
        Infraction: true,
      },
    });
  }

  async findOne(id: string) {
    const reason = await this.prisma.infractionReason.findUnique({
      where: { id },
      include: {
        file: {
          select: {
            id: true,
          },
        },
        Infraction: true,
      },
    });

    if (!reason) {
      throw new NotFoundException(`InfractionReason with id ${id} not found`);
    }

    return reason;
  }

  async update(id: string, dto: UpdateInfractionReasonDto) {
    const existingReason = await this.prisma.infractionReason.findUnique({
      where: { id },
    });

    if (!existingReason) {
      throw new NotFoundException(`InfractionReason with ID ${id} not found`);
    }

    if (dto.infractionId) {
      const infraction = await this.prisma.infraction.findUnique({
        where: { id: dto.infractionId },
      });

      if (!infraction) {
        throw new NotFoundException(`Infraction with ID ${dto.infractionId} not found`);
      }
    }

    let fileId: string | undefined;
    if (dto.files?.length) {
      const movedFiles = await this.fileService.moveFileFromTempToSaved(dto.files);
      if (movedFiles.length) {
        fileId = movedFiles[0].id;
      }
    }

    return this.prisma.infractionReason.update({
      where: { id },
      data: {
        name: dto.name,
        description: dto.description,
        infractionId: dto.infractionId,
        fileId,
      },
      include: {
        file: {
          select: {
            id: true,
            path: true,
          },
        },
      },
    });
  }

  async remove(id: string) {
    await this.findOne(id); // Throws if not found

    return this.prisma.infractionReason.delete({
      where: { id },
    });
  }
}
