import { useMemo, useCallback } from 'react'

export const useLocationFilters = (data: any) => {
	// Извлекаем уникальные позиции для фильтра
	const uniquePositions = useMemo(() => {
		if (!data?.history) return []
		const positions = [...new Set(data.history.map((location: any) => location.position.name))]
		return positions.sort()
	}, [data])

	// Фильтрованные и отсортированные данные
	const getFilteredData = useCallback(
		(
			searchTerm: string,
			statusFilter: 'all' | 'active' | 'inactive',
			positionFilter: string,
			organizationFilter: string,
			sortBy: 'name' | 'position' | 'time' | 'status',
			sortOrder: 'asc' | 'desc'
		) => {
			if (!data?.history) return []
			const filtered = data.history.filter((location: any) => {
				const matchesSearch =
					location.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
					location.position.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
					location.organization.some((org: any) =>
						org.name.toLowerCase().includes(searchTerm.toLowerCase())
					)

				const matchesStatus =
					statusFilter === 'all' ||
					(statusFilter === 'active' && location.isInArea) ||
					(statusFilter === 'inactive' && !location.isInArea)

				const matchesPosition =
					positionFilter === 'all' || location.position.name === positionFilter

				const matchesOrganization =
					organizationFilter === 'all' ||
					location.organization.some((org: any) => org.name === organizationFilter)

				return matchesSearch && matchesStatus && matchesPosition && matchesOrganization
			})

			// Сортировка
			filtered.sort((a: any, b: any) => {
				let compareValue = 0

				switch (sortBy) {
					case 'name':
						compareValue = a.fullName.localeCompare(b.fullName)
						break
					case 'position':
						compareValue = a.position.name.localeCompare(b.position.name)
						break
					case 'time':
						compareValue = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
						break
					case 'status':
						compareValue = Number(b.isInArea) - Number(a.isInArea)
						break
					default:
						compareValue = 0
				}

				return sortOrder === 'asc' ? compareValue : -compareValue
			})

			return filtered
		},
		[data]
	)

	return { uniquePositions, getFilteredData }
}
