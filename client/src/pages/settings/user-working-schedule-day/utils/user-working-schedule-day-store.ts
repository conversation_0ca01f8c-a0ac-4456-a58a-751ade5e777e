import { UserWorkingScheduleTypes } from '@/config/queries/user-working-schedule-day/create.queries'
import { create } from 'zustand'

interface UserWorkingScheduleDayModalStore {
	open: boolean
	data: UserWorkingScheduleTypes | null
	onOpen: (data?: UserWorkingScheduleTypes) => void
	onClose: () => void
}

export const useUserWorkingScheduleDayModalStore = create<UserWorkingScheduleDayModalStore>(
	set => ({
		open: false,
		data: null,
		onOpen: data => set({ open: true, data: data || null }),
		onClose: () => set({ open: false, data: null })
	})
)
