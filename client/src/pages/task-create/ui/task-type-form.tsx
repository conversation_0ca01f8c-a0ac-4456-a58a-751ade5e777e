import { Form, Select, Row, Col, DatePicker } from 'antd'
import { TaskTypes } from '@/config/queries/task-types/create.queries'

type TTaskTypeAndDateProps = {
	taskTypeData: any
}

export const TaskTypeAndDate = ({ taskTypeData }: TTaskTypeAndDateProps) => {
	return (
		<Row gutter={[14, 14]}>
			<Col
				xs={24}
				sm={24}
				md={12}>
				<Form.Item
					name='taskTypeId'
					label='Topshiriq turi'
					labelCol={{ span: 24 }}
					rules={[{ required: true }]}>
					<Select
						className='rounded-md'
						options={taskTypeData?.pages[0]?.data?.map((type: TaskTypes) => ({
							label: type.name,
							value: type.id
						}))}
					/>
				</Form.Item>
			</Col>

			<Col
				xs={24}
				sm={24}
				md={12}>
				<Form.Item
					name='dueDate'
					label='Muddati'
					labelCol={{ span: 24 }}
					rules={[{ required: true }]}>
					<DatePicker
						showTime
						className='w-full rounded-md'
						format='YYYY-MM-DD HH:mm'
					/>
				</Form.Item>
			</Col>
		</Row>
	)
}
