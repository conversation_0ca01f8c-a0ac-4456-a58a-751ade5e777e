import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { CreateFaceIdDto, FaceIdQueryDto } from './dto/create-face-id.dto';
import { UpdateFaceIdDto } from './dto/update-face-id.dto';
import { PaginationUtil, PrismaService, QueryUtil } from '../../common';
import { BaseQueryParams } from '../../helpers/types';
import { Prisma } from '@prisma/client';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Injectable()
export class FaceIdService {
  constructor(
    private prismaService: PrismaService,
    private pagination: PaginationUtil,
    private query: QueryUtil,
    @Inject(CACHE_MANAGER) private readonly cacheService: Cache,
  ) {}

  async create(orgId: string, createFaceIdDto: Omit<CreateFaceIdDto, 'orgId'>) {
    const organization = await this.prismaService.organization.findUnique({
      where: {
        id: orgId,
      },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    return this.prismaService.faceIDDevice.create({
      data: {
        ...createFaceIdDto,
        organizationId: orgId,
      },
    });
  }

  // async findAll(query: BaseQueryParams & { onlineStatus?: 'true' | 'false' }, orgId?: string) {
  //   const where: Prisma.FaceIDDeviceWhereInput = {};

  //   if (query.search) {
  //     where.OR = [
  //       {
  //         MAC: { contains: query.search, mode: Prisma.QueryMode.insensitive },
  //       },
  //       {
  //         IP: { contains: query.search, mode: Prisma.QueryMode.insensitive },
  //       },
  //       {
  //         Organization: {
  //           name: { contains: query.search, mode: Prisma.QueryMode.insensitive },
  //         },
  //       },
  //     ];
  //   }

  //   if (orgId) {
  //     const orgExists = await this.prismaService.organization.findUnique({
  //       where: { id: orgId },
  //       select: { id: true },
  //     });

  //     if (!orgExists) {
  //       throw new NotFoundException(`Organization with id '${orgId}' not found`);
  //     }

  //     const relatedOrgIds = await this.getAllRelatedOrganizationIds(orgId);
  //     where.organizationId = { in: relatedOrgIds };
  //   }

  //   const paginate = {
  //     page: query.page ? parseInt(query.page) : 1,
  //     limit: query.limit ? parseInt(query.limit) : 10,
  //   };

  //   const withPagination = await this.pagination.paginate(this.prismaService.faceIDDevice, paginate, where, {
  //     Organization: { select: { name: true } },
  //   });

  //   const dataWithHeartbeat = await Promise.all(
  //     withPagination.data.map(async (device) => {
  //       const deviceHeartbeat = await this.cacheService.get(`device-heartbeat-${device.MAC}`);
  //       const isOnline = !!deviceHeartbeat;

  //       return {
  //         ...device,
  //         heartbeat: deviceHeartbeat || null,
  //         onlineStatus: isOnline,
  //       };
  //     }),
  //   );

  //   // 🔁 onlineStatus filtering
  //   const parsedOnlineStatus =
  //     query.onlineStatus === 'true' ? true : query.onlineStatus === 'false' ? false : undefined;

  //   const filteredData =
  //     typeof parsedOnlineStatus === 'boolean'
  //       ? dataWithHeartbeat.filter((d) => d.onlineStatus === parsedOnlineStatus)
  //       : dataWithHeartbeat;

  //   return {
  //     data: filteredData,
  //     meta: withPagination.meta,
  //   };
  // }

  async findAll(query: BaseQueryParams & { onlineStatus?: 'true' | 'false' }, organizationId?: string) {
    const where: Prisma.FaceIDDeviceWhereInput = {};

    if (query.search) {
      where.OR = [
        {
          MAC: { contains: query.search, mode: Prisma.QueryMode.insensitive },
        },
        {
          IP: { contains: query.search, mode: Prisma.QueryMode.insensitive },
        },
        {
          Organization: {
            name: { contains: query.search, mode: Prisma.QueryMode.insensitive },
          },
        },
      ];
    }

    if (organizationId) {
      const org = await this.prismaService.organization.findUnique({
        where: { id: organizationId },
        select: { id: true },
      });

      if (!org) {
        throw new NotFoundException(`Organization with id '${organizationId}' not found`);
      }

      const relatedOrgIds = await this.getAllRelatedOrganizationIds(organizationId);
      where.organizationId = { in: relatedOrgIds };
    }

    const paginate = {
      page: query.page ? parseInt(query.page.toString()) : 1,
      limit: query.limit ? parseInt(query.limit.toString()) : 10,
    };

    // 📦 Ma’lumotlarni olish
    const withPagination = await this.pagination.paginate(this.prismaService.faceIDDevice, paginate, where, {
      Organization: { select: { name: true } },
    });

    const dataWithHeartbeat = await Promise.all(
      withPagination.data.map(async (device) => {
        const deviceHeartbeat = await this.cacheService.get(`device-heartbeat-${device.MAC}`);
        return {
          ...device,
          heartbeat: deviceHeartbeat || null,
        };
      }),
    );

    const parsedOnlineStatus =
      query.onlineStatus === 'true' ? true : query.onlineStatus === 'false' ? false : undefined;

    const filteredData =
      typeof parsedOnlineStatus === 'boolean'
        ? dataWithHeartbeat.filter((d) => !!d.heartbeat === parsedOnlineStatus)
        : dataWithHeartbeat;

    return {
      data: filteredData,
      meta: withPagination.meta,
    };
  }

  findAllByOrganization(orgId: string, query: BaseQueryParams) {
    const where = this.query.createSearchQuery(query.search, ['MAC', 'IP']);

    const paginate = {
      page: query.page ? parseInt(query.page) : 1,
      limit: query.limit ? parseInt(query.limit) : 10,
    };

    const withPagination = this.pagination.paginate(this.prismaService.faceIDDevice, paginate, {
      ...where,
      organizationId: orgId,
    });

    return withPagination;
  }

  async findOne(id: string) {
    const device = await this.prismaService.faceIDDevice.findUnique({
      where: {
        id,
      },
    });

    if (!device) {
      throw new NotFoundException('Device not found');
    }

    return device;
  }

  update(id: string, updateFaceIdDto: UpdateFaceIdDto) {
    return `This action updates a #${id} faceId`;
  }

  async remove(id: string) {
    const device = await this.prismaService.faceIDDevice.findUnique({
      where: {
        id,
      },
    });

    if (!device) {
      throw new NotFoundException('Device not found');
    }

    return this.prismaService.faceIDDevice.update({
      where: {
        id,
      },
      data: {
        status: 'INACTIVE',
      },
    });
  }

  private async getAllRelatedOrganizationIds(orgId: string): Promise<string[]> {
    const visited = new Set<string>();
    const queue = [orgId];

    while (queue.length > 0) {
      const currentId = queue.shift();
      if (!currentId || visited.has(currentId)) continue;
      visited.add(currentId);

      const org = await this.prismaService.organization.findUnique({
        where: { id: currentId },
        select: {
          id: true,
          Children: { select: { id: true } },
          UnderControl: { select: { id: true } },
        },
      });

      if (!org) continue;

      const childIds = org.Children.map((child) => child.id);
      const underControlIds = org.UnderControl.map((uc) => uc.id);

      for (const id of [...childIds, ...underControlIds]) {
        if (!visited.has(id)) {
          queue.push(id);
        }
      }
    }

    return Array.from(visited);
  }
}
