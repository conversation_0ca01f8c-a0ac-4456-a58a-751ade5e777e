import { useGetUnderControlOrganizations } from '@/config/queries/under-control-organizations/get-queries'
import { DisconnectOutlined } from '@ant-design/icons'
import { Button, Modal, Space, Table } from 'antd'
import { Eye } from 'lucide-react'
import { UnderControlManagmentModal } from './ui/organization-modal'
import { usePermissions } from '@/shared/guards/usePermissions'
import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { LoadingScreen } from '@/shared/ui/suspense'
import { Link } from 'react-router-dom'
import { useRemoveUnderControlOrganization } from '@/config/queries/organizations/remove-under-control.queries'
import { useState } from 'react'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { Search } from '@/shared/components/search/search'

export const UnderControlOrganizationTable = () => {
	const orgId = localStorage.getItem('organizationId')
	const { hasPermission } = usePermissions()

	const [currentPage, setCurrentPage] = useState(1)
	const [pageSize, setPageSize] = useState(10)
	const {
		data: underContol,
		hasNextPage,
		fetchNextPage,
		setPage
	} = useGetUnderControlOrganizations()

	const { data: user, isLoading } = useAuthMe()
	const { mutateAsync: removeUnderControlOrganization, isPending } =
		useRemoveUnderControlOrganization()

	const handleDelete = (organizationId: string) => {
		Modal.confirm({
			title: 'Haqiqatan ham bu Tashkilotni ajratmoqchimisiz?',
			okText: 'Ajratish',
			cancelText: 'Bekor qilish',
			onOk: () =>
				removeUnderControlOrganization({
					orgId: JSON.parse(orgId!),
					data: { organizations: [organizationId] }
				})
		})
	}

	if (isLoading || !user?.role) return <LoadingScreen />

	const tableData = underContol?.pages?.flatMap(page => page.data) || []

	return (
		<Table
			title={() => (
				<div className='flex items-center justify-between'>
					<Search />
					<UnderControlManagmentModal orgId={JSON.parse(orgId!)} />
				</div>
			)}
			pagination={{
				total: underContol?.pages?.[0]?.meta?.total || 0,
				pageSize: pageSize,
				current: currentPage,
				showSizeChanger: true,
				pageSizeOptions: [5, 10, 20, 50, 100],
				onChange: (page, size) => {
					setCurrentPage(page)
					setPageSize(size)
					setPage(page)
					if (Math.ceil((page * size) / size) > (underContol?.pages?.length || 0) && hasNextPage) {
						fetchNextPage()
					}
				}
			}}
			size='small'
			scroll={{ x: 'max-content' }}
			dataSource={tableData}
			rowKey={(rec: { id?: string }) => rec.id || ''}
			columns={[
				{
					key: '#',
					title: '#',
					render: (_: any, _rec: any, index: number) =>
						formatTableIndex(currentPage, pageSize, index)
				},
				{
					key: 'name',
					title: 'Nomi',
					dataIndex: 'name',
					render: (_: any, rec: any) => (
						<Link to={`/workspace/organizations/${rec.id}`}>{rec.name}</Link>
					)
				},
				{
					key: 'phone',
					title: 'Telefon raqami',
					dataIndex: 'phone'
				},
				{
					key: 'address',
					title: 'Manzili',
					dataIndex: 'address'
				},
				{
					key: 'grade',
					title: 'Lavozimi',
					dataIndex: 'grade'
				},
				{
					key: 'user-count',
					title: 'Foydalanuvchilar soni',
					dataIndex: 'userCount'
				},
				{
					key: 'actions',
					title: 'Amallar',
					render: (_, record: any) => (
						<Space>
							<Link to={`/workspace/organizations/${record?.id}`}>
								<Eye />
							</Link>
							{user && hasPermission(user.role, ['update:*', 'delete:*']) && (
								<>
									<Button
										danger
										loading={isPending}
										icon={<DisconnectOutlined />}
										onClick={() => handleDelete(record.id)}
									/>
								</>
							)}
						</Space>
					)
				}
			]}
		/>
	)
}
