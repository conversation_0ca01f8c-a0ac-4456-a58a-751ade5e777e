import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common';
import { FaceIdService } from './face-id.service';
import { CreateFaceIdDto } from './dto/create-face-id.dto';
import { UpdateFaceIdDto } from './dto/update-face-id.dto';
import { ProtectedRoute } from 'src/common';
import { BaseQueryParams } from 'src/helpers/types';
import { Context } from '../../common/decorators/context.decorator';

@Controller('face-id')
export class FaceIdController {
  constructor(private readonly faceIdService: FaceIdService) {}

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Post()
  async create(@Body() { orgId, ...createFaceIdDto }: CreateFaceIdDto) {
    return await this.faceIdService.create(orgId, createFaceIdDto);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: false })
  @Get()
  async findAll(
    @Query() query: BaseQueryParams & { onlineStatus?: 'true' | 'false' },
    @Context('organizationId') orgId: string,
  ) {
    return this.faceIdService.findAll(query, orgId);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Get(':id')
  async findOne(@Param('id') id: string) {
    return await this.faceIdService.findOne(id);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Get('organization/:orgId')
  async findAllByOrganization(@Param('orgId') orgId: string, @Query() query: BaseQueryParams) {
    return await this.faceIdService.findAllByOrganization(orgId, query);
  }

  @Put(':id')
  async update(@Param('id') id: string, @Body() updateFaceIdDto: UpdateFaceIdDto) {
    return await this.faceIdService.update(id, updateFaceIdDto);
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    return await this.faceIdService.remove(id);
  }
}
