import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateAttendanceDto, FindAttendanceDto } from './dto/create-attendance.dto';
import { AttendanceReportQueryDto } from './dto/attendance-report.dto';
import {
  AttendanceBasicService,
  AttendanceTableService,
  AttendanceReportService,
  AttendanceEmployeeReportService,
} from './services';
import { PrismaService } from 'src/common';

@Injectable()
export class AttendanceService {
  constructor(
    private basicService: AttendanceBasicService,
    private tableService: AttendanceTableService,
    private reportService: AttendanceReportService,
    private employeeReportService: AttendanceEmployeeReportService,
    private prismaService: PrismaService,
  ) {}

  // Базовые операции
  async createAttendance(data: Omit<CreateAttendanceDto, 'time' | 'description'>) {
    return this.basicService.createAttendance(data);
  }

  async getUsersByPosition(positionId: string) {
    const position = await this.prismaService.organizationTypePosition.findUnique({
      where: { id: positionId },
    });

    if (!position) {
      throw new NotFoundException(`Position with id ${positionId} not found`);
    }

    const users = await this.prismaService.user.findMany({
      where: {
        positionId: positionId,
        status: 'ACTIVE',
      },
      select: {
        id: true,
        fullName: true,
        positionId: true,
      },
    });

    return users;
  }

  async getUserAttendance(userId: string, page: string, limit: string) {
    return this.basicService.getUserAttendance(userId, page, limit);
  }

  async getAttendance(id: string, query: FindAttendanceDto) {
    return this.basicService.getAttendance(id, query);
  }

  // Табличные отчеты
  async getAttendanceTable(id: string, query: FindAttendanceDto) {
    return this.tableService.getAttendanceTable(id, query);
  }

  // Отчеты по организациям
  async getAttendanceReport(orgIds: string[], startDate?: string, endDate?: string) {
    return this.reportService.getAttendanceReport(orgIds, startDate, endDate);
  }

  async getAttendanceReportByOrganizationTypePosition(
    orgId: string,
    positionId: string,
    startDate?: string,
    endDate?: string,
    page: number = 1,
    limit: number = 20,
  ) {
    return this.reportService.getAttendanceReportByOrganizationTypePosition(
      orgId,
      positionId,
      startDate,
      endDate,
      page,
      limit,
    );
  }

  async getOptimizedAttendanceReport(query: AttendanceReportQueryDto) {
    return this.reportService.getOptimizedAttendanceReport(query);
  }

  // Детальные отчеты по сотрудникам
  async getEmployeeDetailedAttendanceReport(query: AttendanceReportQueryDto) {
    return this.employeeReportService.getEmployeeDetailedAttendanceReport(query);
  }
}
