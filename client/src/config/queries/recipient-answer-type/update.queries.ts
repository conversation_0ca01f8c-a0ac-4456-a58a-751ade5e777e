import { axiosPrivate } from '@/config/api'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { recipientAnswerTypeEndpoints } from '@/config/api/endpoints'
import { notification } from 'antd'
import { RecipientAnswerType } from './create.queries'

export const useUpdateRecipientAnswerType = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async ({ id, ...data }: { id: string }) =>
			(
				await axiosPrivate.patch<ApiResponse<RecipientAnswerType>>(
					recipientAnswerTypeEndpoints.one(id),
					data
				)
			).data,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [recipientAnswerTypeEndpoints.all] })
			notification.success({
				message: 'Muvaffaqiyatli yangilandi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: 'Yangilashda xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
