import { UseInterceptors, applyDecorators } from '@nestjs/common';
import {
  FileInterceptor,
  FilesInterceptor,
  FileFieldsInterceptor,
  AnyFilesInterceptor,
} from '@nestjs/platform-express';
import { MulterOptions } from '@nestjs/platform-express/multer/interfaces/multer-options.interface';
import { MulterService } from './multer.service';

const multerService = new MulterService();

/**
 * Декоратор для загрузки одиночного файла
 * @param fieldName имя поля формы для загрузки файла
 * @param destination путь назначения для сохраняемых файлов
 */
export function UploadFile(fieldName: string = 'file', destination: string = 'uploads'): MethodDecorator {
  return applyDecorators(UseInterceptors(FileInterceptor(fieldName, multerService.createMulterOptions(destination))));
}

/**
 * Декоратор для загрузки нескольких файлов
 * @param fieldName имя поля формы для загрузки файлов
 * @param maxCount максимальное количество файлов
 * @param destination путь назначения для сохраняемых файлов
 */
export function UploadFiles(
  fieldName: string = 'files',
  maxCount: number = 10,
  destination: string = 'uploads',
): MethodDecorator {
  return applyDecorators(
    UseInterceptors(FilesInterceptor(fieldName, maxCount, multerService.createMulterOptions(destination))),
  );
}

/**
 * Декоратор для загрузки одиночного изображения
 * @param fieldName имя поля формы для загрузки изображения
 * @param destination путь назначения для сохраняемых изображений
 */
export function UploadImage(fieldName: string = 'image', destination: string = 'uploads/images'): MethodDecorator {
  return applyDecorators(UseInterceptors(FileInterceptor(fieldName, multerService.createImageOptions(destination))));
}

/**
 * Декоратор для загрузки нескольких изображений
 * @param fieldName имя поля формы для загрузки изображений
 * @param maxCount максимальное количество изображений
 * @param destination путь назначения для сохраняемых изображений
 */
export function UploadImages(
  fieldName: string = 'images',
  maxCount: number = 10,
  destination: string = 'uploads/images',
): MethodDecorator {
  return applyDecorators(
    UseInterceptors(FilesInterceptor(fieldName, maxCount, multerService.createImageOptions(destination))),
  );
}

/**
 * Декоратор для загрузки одиночного документа
 * @param fieldName имя поля формы для загрузки документа
 * @param destination путь назначения для сохраняемых документов
 */
export function UploadDocument(
  fieldName: string = 'document',
  destination: string = 'uploads/documents',
): MethodDecorator {
  return applyDecorators(UseInterceptors(FileInterceptor(fieldName, multerService.createDocumentOptions(destination))));
}

/**
 * Декоратор для загрузки нескольких документов
 * @param fieldName имя поля формы для загрузки документов
 * @param maxCount максимальное количество документов
 * @param destination путь назначения для сохраняемых документов
 */
export function UploadDocuments(
  fieldName: string = 'documents',
  maxCount: number = 10,
  destination: string = 'uploads/documents',
): MethodDecorator {
  return applyDecorators(
    UseInterceptors(FilesInterceptor(fieldName, maxCount, multerService.createDocumentOptions(destination))),
  );
}

/**
 * Декоратор для загрузки нескольких полей с файлами
 * @param uploadFields конфигурация полей для загрузки
 * @param destination путь назначения для сохраняемых файлов
 */
export function UploadFields(
  uploadFields: { name: string; maxCount?: number }[],
  destination: string = 'uploads',
): MethodDecorator {
  return applyDecorators(
    UseInterceptors(FileFieldsInterceptor(uploadFields, multerService.createMulterOptions(destination))),
  );
}

/**
 * Декоратор для загрузки произвольного количества файлов с произвольными именами полей
 * @param destination путь назначения для сохраняемых файлов
 */
export function UploadAnyFiles(destination: string = 'uploads'): MethodDecorator {
  return applyDecorators(UseInterceptors(AnyFilesInterceptor(multerService.createMulterOptions(destination))));
}

/**
 * Декоратор для загрузки файла с пользовательскими опциями Multer
 * @param fieldName имя поля формы для загрузки файла
 * @param options пользовательские опции Multer
 */
export function UploadFileCustom(fieldName: string = 'file', options: MulterOptions): MethodDecorator {
  return applyDecorators(UseInterceptors(FileInterceptor(fieldName, options)));
}

/**
 * Декоратор для загрузки файла без сохранения на диск
 * Файл будет доступен только в памяти
 * @param fieldName имя поля формы для загрузки файла
 */
export function UploadAny(fieldName: string = 'file'): MethodDecorator {
  return applyDecorators(UseInterceptors(FileInterceptor(fieldName, multerService.createMemoryOptions())));
}

/**
 * Декоратор для загрузки нескольких файлов без сохранения на диск
 * Файлы будут доступны только в памяти
 * @param fieldName имя поля формы для загрузки файлов
 * @param maxCount максимальное количество файлов
 */
export function UploadAnyMultiple(fieldName: string = 'files', maxCount: number = 10): MethodDecorator {
  return applyDecorators(UseInterceptors(FilesInterceptor(fieldName, maxCount, multerService.createMemoryOptions())));
}

/**
 * Декоратор для загрузки изображения без сохранения на диск
 * Изображение будет доступно только в памяти
 * @param fieldName имя поля формы для загрузки изображения
 */
export function UploadAnyImage(fieldName: string = 'image'): MethodDecorator {
  return applyDecorators(UseInterceptors(FileInterceptor(fieldName, multerService.createMemoryImageOptions())));
}
