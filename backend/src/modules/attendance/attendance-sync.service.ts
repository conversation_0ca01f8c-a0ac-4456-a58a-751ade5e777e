import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { PrismaService } from 'src/common';

@Injectable()
export class AttendanceSyncService implements OnModuleInit {
  private readonly logger = new Logger(AttendanceSyncService.name);

  constructor(private prismaService: PrismaService) {}

  async onModuleInit() {
    this.logger.log('AttendanceSyncService инициализирован');
    this.logger.log('AttendanceSyncService инициализацияси tugadi');
    // await this.syncAttendanceReportsAll();
  }

  async syncAttendanceReportsAll() {
    this.logger.log('To‘liq sync: barcha Attendance -> AttendanceReport');
    try {
      // Barcha attendance larni olamiz
      const allAttendances = await this.prismaService.attendance.findMany({
        orderBy: [{ userId: 'asc' }, { time: 'asc' }],
      });
      // userId va sana bo‘yicha guruhlash
      const grouped: Record<string, Record<string, { enter: Date[]; exit: Date[] }>> = {};
      for (const att of allAttendances) {
        const userId = att.userId;
        const dateKey = att.time.toISOString().slice(0, 10); // YYYY-MM-DD
        if (!grouped[userId]) grouped[userId] = {};
        if (!grouped[userId][dateKey]) grouped[userId][dateKey] = { enter: [], exit: [] };
        if (att.type === 'ENTER') grouped[userId][dateKey].enter.push(att.time);
        if (att.type === 'EXIT') grouped[userId][dateKey].exit.push(att.time);
      }
      let created = 0;
      let updated = 0;
      let skipped = 0;
      for (const userId of Object.keys(grouped)) {
        for (const dateKey of Object.keys(grouped[userId])) {
          const { enter, exit } = grouped[userId][dateKey];
          if (!enter.length) {
            skipped++;
            continue;
          }
          const firstEnter = enter[0];
          const lastExit = exit.length ? exit[exit.length - 1] : firstEnter;
          const minutes = Math.floor((lastExit.getTime() - firstEnter.getTime()) / (1000 * 60));
          // Sana uchun UTC 00:00:00
          const date = new Date(dateKey + 'T00:00:00.000Z');
          // Mavjud reportni topamiz
          const existingReport = await this.prismaService.attendanceReport.findFirst({
            where: { userId, date: { gte: date, lt: new Date(date.getTime() + 24 * 60 * 60 * 1000) } },
          });
          if (existingReport) {
            await this.prismaService.attendanceReport.update({
              where: { id: existingReport.id },
              data: { enter: firstEnter, exit: lastExit, minutes },
            });
            updated++;
          } else {
            await this.prismaService.attendanceReport.create({
              data: { userId, enter: firstEnter, exit: lastExit, date, minutes },
            });
            created++;
          }
        }
      }
      this.logger.log(`Sync tugadi: created=${created}, updated=${updated}, skipped=${skipped}`);
    } catch (error) {
      this.logger.error(`To‘liq sync xatosi: ${error.message}`, error.stack);
    }
  }
}
