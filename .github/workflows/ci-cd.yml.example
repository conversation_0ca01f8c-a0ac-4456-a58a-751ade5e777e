name: CI/CD Pipeline

on:
  push:
    branches:
      - deploy

jobs:
  check-commit:
    runs-on: ubuntu-latest
    outputs:
      build_backend: ${{ steps.check-message.outputs.build_backend }}
      build_client: ${{ steps.check-message.outputs.build_client }}
      build_all: ${{ steps.check-message.outputs.build_all }}
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Check commit message
        id: check-message
        run: |
          COMMIT_MSG=$(git log --format=%B -n 1 ${{ github.sha }})
          echo "Commit message: $COMMIT_MSG"
          
          if echo "$COMMIT_MSG" | grep -iE "^(feat|fix|refactor)\(backend\)|^(feat|fix|refactor)\(api\)|^(feat|fix|refactor)\(server\)"; then
            echo "build_backend=true" >> $GITHUB_OUTPUT
          else
            echo "build_backend=false" >> $GITHUB_OUTPUT
          fi
          
          if echo "$COMMIT_MSG" | grep -iE "^(feat|fix|refactor)\(client\)|^(feat|fix|refactor)\(frontend\)|^(feat|fix|refactor)\(web\)"; then
            echo "build_client=true" >> $GITHUB_OUTPUT
          else
            echo "build_client=false" >> $GITHUB_OUTPUT
          fi
          
          if echo "$COMMIT_MSG" | grep -iE "BREAKING CHANGE:|^[a-zA-Z]*(\([a-zA-Z]*\))!:"; then
            echo "build_all=true" >> $GITHUB_OUTPUT
          else
            echo "build_all=false" >> $GITHUB_OUTPUT
          fi

  deploy:
    needs: check-commit
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Install sshpass
        run: sudo apt-get install -y sshpass
      
      - name: Deploy to Server
        env:
          HOST: ${{ secrets.SERVER_IP }}
          PASSWORD: ${{ secrets.SERVER_PASSWORD }}
          BUILD_BACKEND: ${{ needs.check-commit.outputs.build_backend }}
          BUILD_CLIENT: ${{ needs.check-commit.outputs.build_client }}
          BUILD_ALL: ${{ needs.check-commit.outputs.build_all }}
        run: |
          export SSHPASS="${PASSWORD}"
          
          sshpass -e ssh -o StrictHostKeyChecking=no root@${HOST} '
            cd /home/<USER>
            
            # Repositoryni yangilash
            git fetch origin deploy && \
            git reset --hard origin/deploy && \
            
            # Environment faylini saqlash
            if [ -f ".env.production" ]; then
              cp .env.production .env
            fi && \
            
            # Commit message ga qarab build qilish
            if [ "${{ needs.check-commit.outputs.build_all }}" == "true" ]; then
              echo "Building all services..."
              docker compose down && \
              docker compose build --no-cache && \
              docker compose up -d
            else
              # Kerakli servicelarni to''xtatish
              if [ "${{ needs.check-commit.outputs.build_backend }}" == "true" ]; then
                echo "Building backend..."
                docker compose stop backend && \
                docker compose build backend && \
                docker compose up -d backend
              fi
              
              if [ "${{ needs.check-commit.outputs.build_client }}" == "true" ]; then
                echo "Building client..."
                docker compose stop frontend && \
                docker compose build frontend && \
                docker compose up -d frontend
              fi
            fi && \
            
            # Container statuslarini tekshirish
            echo "=== Checking container statuses ===" && \
            docker compose ps
            
            # Status tekshiruvi
            if [ "${{ needs.check-commit.outputs.build_all }}" == "true" ]; then
              CONTAINERS=(backend frontend postgres redis nginx)
            elif [ "${{ needs.check-commit.outputs.build_backend }}" == "true" ]; then
              CONTAINERS=(backend)
            elif [ "${{ needs.check-commit.outputs.build_client }}" == "true" ]; then
              CONTAINERS=(frontend)
            fi
            
            for container in "${CONTAINERS[@]}"; do
              STATUS=$(docker inspect --format="{{.State.Status}}" e-muhokama-$container 2>/dev/null || echo "not-found")
              
              if [ "$STATUS" != "running" ]; then
                echo "Container $container is not running! Rolling back..."
                if [ "${{ needs.check-commit.outputs.build_all }}" == "true" ]; then
                  docker compose down && \
                  git checkout HEAD~ && \
                  docker compose build --no-cache && \
                  docker compose up -d
                else
                  docker compose stop $container && \
                  git checkout HEAD~ && \
                  docker compose build $container && \
                  docker compose up -d $container
                fi
                exit 1
              fi
            done
          '