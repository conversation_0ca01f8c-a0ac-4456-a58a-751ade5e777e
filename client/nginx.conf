# Оптимизированная конфигурация Nginx для фронтенд-приложения
# с учетом ограниченных ресурсов (2 CPU, 4GB RAM)

# Настройки для кэширования
map $sent_http_content_type $expires {
    default                    off;
    text/html                  epoch;
    text/css                   max;
    application/javascript     max;
    ~image/                    max;
    ~font/                     max;
}

server {
    listen 80;
    server_name _;
    root /usr/share/nginx/html;
    index index.html;

    # Включаем expires из map выше
    expires $expires;

    # Настройка сжатия
    gzip on;
    gzip_comp_level 5;
    gzip_min_length 256;
    gzip_proxied any;
    gzip_vary on;
    gzip_types
        application/javascript
        application/json
        application/x-javascript
        application/xml
        text/css
        text/javascript
        text/plain
        text/xml;
    
    # Настройки безопасности
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header X-Frame-Options SAMEORIGIN;
    
    # Настройка для логов
    access_log off;
    error_log /var/log/nginx/error.log crit;
    
    # Размеры буферов
    client_body_buffer_size 10k;
    client_header_buffer_size 1k;
    client_max_body_size 8m;
    large_client_header_buffers 2 1k;
    
    # Кэширование открытых файлов
    open_file_cache max=1000 inactive=20s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;
    
    # Конфигурация для SPA (маршрутизация на стороне клиента)
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # Отдельные правила для статики
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        try_files $uri =404;
        access_log off;
        add_header Cache-Control "public, max-age=31536000";
    }
    
    # Запрещаем доступ к скрытым файлам
    location ~ /\.(?!well-known) {
        deny all;
        access_log off;
        log_not_found off;
    }
}