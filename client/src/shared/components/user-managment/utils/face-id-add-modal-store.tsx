import { create } from 'zustand'

interface FaceIDStore {
	open: boolean
	id: string | null
	who: 'workers' | 'employees' | 'responsibles'
	onOpen: (id?: string, who?: 'workers' | 'employees' | 'responsibles') => void
	onClose: () => void
}

export const useFaceIDAddStore = create<FaceIDStore>(set => ({
	open: false,
	id: null,
	who: 'workers',
	onOpen: (id, who) => set({ open: true, id: id ?? null, who: who ?? 'workers' }),
	onClose: () => set({ open: false, id: null })
}))
