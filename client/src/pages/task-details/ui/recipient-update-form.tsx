import { Form, Select, Row, <PERSON>, <PERSON><PERSON>, Modal, <PERSON> } from 'antd'
import { useState, useEffect } from 'react'
import {
	TOrganization,
	useGetAllOrganizations
} from '@/config/queries/organizations/get-all.queries'
import { LoadingScreen } from '@/shared/ui/suspense'
import { useGetUnderControlOrganizations } from '@/config/queries/under-control-organizations/get-queries'
import { mergeArraysById } from '@/shared/utils/arrayMerge'
import { useGetOrganizationWorkers } from '@/config/queries/organizations/get-workers.queries'
import { TUser } from '@/config/queries/users/get-all.queries'
import { useGetOneOrganization } from '@/config/queries/organizations/getOne'
import { removeDuplicates } from '@/shared/utils/removeDuplicatedData'
import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { TRecipent } from './recipient-update'
import { Plus, XCircleIcon } from 'lucide-react'
import { useUpdateRecipient } from '@/config/queries/task/update-task-recipient'
import { useParams } from 'react-router-dom'
import TaskPositionSelect from '@/pages/task-create/ui/task-position-select'

type TUsersState = {
	[key: string]: TUser[]
}

type TTaskCreateForm = {
	recipents: TRecipent[]
	setRecipents: React.Dispatch<React.SetStateAction<TRecipent[]>>
}

type TEmployeeSelectorProps = {
	recipientType: string
	recipent: TRecipent
	recipentIndex: number
	users: { [key: string]: TUser[] }
	isPending: boolean
	setRecipents: React.Dispatch<React.SetStateAction<TRecipent[]>>
}

const EmployeeSelector = ({
	recipientType,
	recipent,
	recipentIndex,
	users,
	isPending,
	setRecipents
}: TEmployeeSelectorProps) => {
	if (recipientType !== 'employees') return null

	return (
		<Col
			xs={24}
			sm={24}
			md={11}>
			<Form.Item
				label='Ishchisi'
				labelCol={{ span: 24 }}
				rules={[{ required: true }]}>
				<Select
					mode='multiple'
					loading={isPending}
					className='rounded-md'
					value={recipent?.userId || []}
					onDeselect={userId =>
						setRecipents(prev =>
							prev.map((item, index) =>
								index === recipentIndex
									? { ...item, userId: item.userId.filter(id => id !== userId) }
									: item
							)
						)
					}
					onSelect={userId =>
						setRecipents(prev =>
							prev.map((item, index) =>
								index === recipentIndex
									? { ...item, userId: [...(item?.userId ?? []), userId] }
									: item
							)
						)
					}
					options={users?.[recipent.organizationId!]?.map(user => ({
						label: user.fullName,
						value: user.id
					}))}
					showSearch
				/>
			</Form.Item>
		</Col>
	)
}

export default function TaskCreateForm({ recipents, setRecipents }: TTaskCreateForm) {
	const { id: taskId } = useParams()
	const orgId = localStorage.getItem('organizationId')
	const parsedOrgId = JSON.parse(orgId!)

	const { data: selfOrganization, isLoading: isLoadingSelfOrganization } =
		useGetOneOrganization(parsedOrgId)
	const { data: children, isLoading: isLoadingChildren } = useGetAllOrganizations()
	const { data: underControls, isLoading: isLoadingUnderControls } =
		useGetUnderControlOrganizations()
	const { isLoading: isLoadingUser } = useAuthMe()

	const [users, setUsers] = useState<TUsersState>({})
	const [selectedOrgForWorkers, setSelectedOrgForWorkers] = useState<string>('')
	const workersQuery = useGetOrganizationWorkers(selectedOrgForWorkers)
	const [recipientType, setRecipientType] = useState('organizations')

	useEffect(() => {
		if (workersQuery.data && selectedOrgForWorkers) {
			const allWorkers = workersQuery.data.pages.flatMap(page => page.data)
			setUsers(prev => ({
				...prev,
				[selectedOrgForWorkers]: removeDuplicates(allWorkers)
			}))
		}
	}, [workersQuery.data, selectedOrgForWorkers])

	const { mutateAsync: updateRecipient } = useUpdateRecipient()
	const [form] = Form.useForm()
	const [recipientOpen, setRecipientOpen] = useState(false)

	if (isLoadingChildren || isLoadingUnderControls || isLoadingSelfOrganization || isLoadingUser)
		return <LoadingScreen />

	const allOrganizations = [
		...(recipientType === 'employees'
			? [
					{
						...selfOrganization,
						id: parsedOrgId,
						name: 'Mazkur tashkilotning o`zi'
					}
				]
			: []),
		...mergeArraysById<TOrganization>([
			[
				...(children?.pages?.flatMap(page => page.data) ?? []),
				...(underControls?.pages?.flatMap(page => page.data) ?? [])
			]
		])
	]

	const handleRecipientTypeChange = (e: any) => {
		const newType = e.target.value
		setRecipientType(newType)
		setRecipents([
			{
				organizationId: '',
				userId: [],
				...(newType === 'positions' && { positionId: '' })
			}
		])
	}

	const handleOrganizationSelect = async (organizationId: string, recipentIndex: number) => {
		if (recipientType === 'organizations') {
			setRecipents(
				recipents.map((item, index) =>
					index === recipentIndex ? { ...item, organizationId, userId: [] } : item
				)
			)
		} else {
			setSelectedOrgForWorkers(organizationId)
			setRecipents(
				recipents.map((item, index) =>
					index === recipentIndex ? { ...item, organizationId, userId: [] } : item
				)
			)
		}
	}

	const addNewRecipient = () => {
		const hasIncompleteRecipient = recipents.some(item =>
			recipientType === 'positions' ? !item.positionId : !item.organizationId
		)

		if (!hasIncompleteRecipient) {
			setRecipents([
				...recipents,
				{
					organizationId: '',
					userId: [],
					...(recipientType === 'positions' && { positionId: '' })
				}
			])
		}
	}

	const removeRecipient = (recipentIndex: number) => {
		setRecipents(recipents.filter((_, index) => index !== recipentIndex))
	}

	const handlePositionSelect = (positionId: string, recipentIndex: number) => {
		setRecipents(prev =>
			prev.map((item, index) => (index === recipentIndex ? { ...item, positionId } : item))
		)
	}

	const resetForm = () => {
		form.resetFields()
		setRecipents([
			{
				organizationId: '',
				userId: [],
				...(recipientType === 'positions' && { positionId: '' })
			}
		])
		setUsers({})
	}

	return (
		<>
			<Button
				onClick={() => setRecipientOpen(true)}
				type='primary'>
				Yangi bajaruvchi qo'shish
			</Button>

			<Modal
				title='Bajaruvchi qo`shish'
				width={1000}
				footer={null}
				open={recipientOpen}
				onCancel={() => {
					setRecipientOpen(false)
					resetForm()
				}}>
				<Form
					form={form}
					onFinish={async () => {
						if (!taskId) return

						// Check if positions are selected (not supported by update endpoint)
						// if (recipientType === 'positions') {
						// 	message.error(
						// 		"Lavozim kesimida bajaruvchilarni yangilash hozircha qo'llab-quvvatlanmaydi. Yangi topshiriq yaratishda lavozimlarni tanlang."
						// 	)
						// 	return
						// }

						const filteredRecipients = recipents
							.filter(item => {
								if (recipientType === 'positions') {
									return item.positionId
								}
								return item.organizationId
							})
							.map(item => {
								if (recipientType === 'positions') {
									return { positionId: item.positionId }
								}
								if (recipientType === 'organizations') {
									return { organizationId: item.organizationId }
								}
								// employees
								return {
									organizationId: item.organizationId,
									userId: item.userId.length ? [item.userId[0]] : ['']
								}
							})

						updateRecipient({
							taskId,
							data: {
								recipients: filteredRecipients
							}
						}).then(() => {
							setRecipientOpen(false)
							resetForm()
						})
					}}>
					<Form.Item label='Qabul qiluvchi turi'>
						<Radio.Group
							value={recipientType}
							onChange={handleRecipientTypeChange}>
							<Radio value='organizations'>Tashkilotlarga</Radio>
							<Radio value='employees'>Tashkilot hodimlariga</Radio>
							<Radio value='positions'>Lavozim kesimida</Radio>
						</Radio.Group>
					</Form.Item>

					{recipents.map((recipent, recipentIndex) => (
						<Row
							key={recipentIndex}
							gutter={[14, 14]}
							className='flex items-center'>
							{recipientType !== 'positions' && (
								<Col
									xs={24}
									sm={24}
									md={recipientType === 'organizations' ? 24 : 11}>
									<Form.Item
										label='Tashkilot'
										labelCol={{ span: 24 }}
										rules={[{ required: true }]}>
										<Select
											value={recipent?.organizationId || undefined}
											placeholder='Tashkilotni tanlang'
											className='rounded-md'
											onSelect={(organizationId: string) =>
												handleOrganizationSelect(organizationId, recipentIndex)
											}
											options={allOrganizations.map(item => ({
												...item,
												disabled: recipents.some(
													(each, index) =>
														index !== recipentIndex && each.organizationId === item.id
												)
											}))}
											fieldNames={{ label: 'name', value: 'id' }}
										/>
									</Form.Item>
								</Col>
							)}

							<EmployeeSelector
								recipientType={recipientType}
								recipent={recipent}
								recipentIndex={recipentIndex}
								users={users}
								isPending={workersQuery.isFetching}
								setRecipents={setRecipents}
							/>

							{recipientType === 'positions' && (
								<Col md={recipents.length > 1 ? 21 : 24}>
									<Form.Item
										label='Lavozim'
										labelCol={{ span: 24 }}
										rules={[{ required: true, message: 'Lavozimni tanlang' }]}>
										<TaskPositionSelect
											value={recipent.positionId}
											onChange={(positionId: string) =>
												handlePositionSelect(positionId, recipentIndex)
											}
										/>
									</Form.Item>
								</Col>
							)}

							{recipientType === 'employees' && (
								<Col
									xs={24}
									sm={24}
									md={2}
									className='mt-6'>
									<Button
										disabled={recipents.length < 2}
										onClick={() => removeRecipient(recipentIndex)}>
										<XCircleIcon />
									</Button>
								</Col>
							)}
						</Row>
					))}

					<Row
						gutter={[14, 14]}
						className='mb-5'>
						{recipientType === 'employees' && (
							<Button
								disabled={recipents.some(item => !item.organizationId)}
								onClick={addNewRecipient}
								className='mx-2 w-full'
								type='dashed'>
								<Plus />
								<span className='text-md'>Qabul qiluvchini qo'shish</span>
							</Button>
						)}
					</Row>

					<Row className='flex items-center justify-end'>
						<Button
							disabled={
								!recipents.filter(item =>
									recipientType === 'positions' ? !!item.positionId : !!item.organizationId
								).length
							}
							type='primary'
							htmlType='submit'>
							Yuborish
						</Button>
					</Row>
				</Form>
			</Modal>
		</>
	)
}
