import { Module } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtService } from '@nestjs/jwt';
import { UserRepository } from 'src/common/repositories/user.repository';

@Module({
  exports: [AuthService],
  providers: [AuthService, JwtService, UserRepository],
  controllers: [AuthController],
})
export class AuthModule {}
