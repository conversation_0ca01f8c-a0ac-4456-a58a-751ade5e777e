import { Response } from 'express';
import * as ExcelJS from 'exceljs';

type ColumnDef = { header: string; key: string; width?: number };
type RowMapper<T = any> = (item: T, index: number) => Record<string, any>;

export async function writeExcelResponse<T = any>({
  res,
  data,
  filename,
  worksheetName = 'Sheet1',
  columns,
  mapper,
}: {
  res: Response;
  data: T[] | { data: T[] };
  filename: string;
  worksheetName?: string;
  columns: ColumnDef[];
  mapper?: RowMapper<T>;
}) {
  const rows = Array.isArray(data) ? data : Array.isArray(data?.data) ? data.data : [];

  if (!Array.isArray(rows)) {
    throw new Error('writeExcelResponse expects array or { data: array }');
  }

  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet(worksheetName);
  worksheet.columns = columns;

  rows.forEach((item, idx) => {
    const row = mapper ? mapper(item, idx) : item;
    worksheet.addRow(row);
  });

  const buffer = await workbook.xlsx.writeBuffer();
  res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
  res.end(buffer);
}
