import dayjs from 'dayjs';
import { LateAttendanceDto } from './excel.mapper';
import { writeExcelResponse } from 'src/common/interceptors/excel-export.interceptor';
import { Response } from 'express';

export const sectionColumns = [
  { header: 'Tartib raqam', key: 'index', width: 10 },
  { header: 'Tashkilot nomi', key: 'name', width: 30 },
  { header: 'Man<PERSON><PERSON>', key: 'address', width: 25 },
  { header: 'Izoh', key: 'description', width: 25 },
  { header: 'Hudud', key: 'region', width: 20 },
  { header: 'Tuman', key: 'district', width: 20 },
  { header: 'MFY nomi', key: 'section', width: 25 },
  { header: 'Tashkilot turi', key: 'type', width: 20 },
  { header: 'Telefon', key: 'phone', width: 20 },
  { header: 'Yaratilgan sana', key: 'createdAt', width: 20 },
];

export const positionExcelColumns = [
  { header: 'Tartib raqam', key: 'index', width: 10 },
  { header: 'F.I.Sh.', key: 'fullName', width: 30 },
  { header: 'Telefon raqami', key: 'phone', width: 20 },
  { header: 'Tashkilot', key: 'organization', width: 30 },
  { header: 'Lavozimi', key: 'position', width: 25 },
];

export const lateAttendanceColumns = [
  { header: 'F.I.Sh.', key: 'fullName', width: 30 },
  { header: 'Telefon', key: 'phone', width: 15 },
  { header: 'Lavozim', key: 'position', width: 25 },
  { header: 'Tashkilot', key: 'organization', width: 25 },
  { header: 'Sana', key: 'date', width: 15 },
  { header: 'Kirgan vaqt', key: 'enter', width: 15 },
  { header: 'Chiqgan vaqt', key: 'exit', width: 15 },
];

export const lateAttendanceMapper = (item: any) => ({
  fullName: item.fullName,
  phone: item.phone,
  position: item.position,
  organization: item.organization,
  date: item.date,
  enter: item.enter,
  exit: item.exit,
});

export const noAttendanceColumns = [
  { header: 'F.I.Sh.', key: 'fullName', width: 30 },
  { header: 'Telefon', key: 'phone', width: 15 },
  { header: 'Lavozim', key: 'position', width: 25 },
  { header: 'Tashkilot', key: 'organization', width: 25 },
];

export const employeeColumns = [
  { header: 'F.I.Sh.', key: 'fullName', width: 25 },
  { header: 'Telefon', key: 'phone', width: 15 },
  { header: 'Lavozim', key: 'position', width: 20 },
  { header: 'Tashkilot', key: 'organization', width: 30 },
];

export const vacationColumns = [
  { header: 'F.I.Sh.', key: 'fullName', width: 30 },
  { header: 'Lavozimi', key: 'position', width: 20 },
  { header: 'Tashkilot', key: 'organization', width: 30 },
  { header: 'Telefon', key: 'phone', width: 15 },
  { header: 'Boshlanish sanasi', key: 'begin', width: 20 },
  { header: 'Tugash sanasi', key: 'end', width: 20 },
  { header: 'Izoh', key: 'description', width: 30 },
];

export const shortVacationColumns = [
  { header: 'F.I.Sh.', key: 'fullName', width: 30 },
  { header: 'Lavozimi', key: 'position', width: 20 },
  { header: 'Tashkilot', key: 'organization', width: 30 },
  { header: 'Telefon', key: 'phone', width: 15 },
  { header: 'Boshlanish sanasi', key: 'begin', width: 20 },
  { header: 'Tugash sanasi', key: 'end', width: 20 },
  { header: 'Izoh', key: 'description', width: 30 },
];

export const patientColumns = [
  { header: 'F.I.Sh.', key: 'fullName', width: 30 },
  { header: 'Lavozimi', key: 'position', width: 20 },
  { header: 'Tashkilot', key: 'organization', width: 30 },
  { header: 'Telefon', key: 'phone', width: 15 },
  { header: 'Boshlanish sanasi', key: 'begin', width: 20 },
  { header: 'Tugash sanasi', key: 'end', width: 20 },
  { header: 'Izoh', key: 'description', width: 30 },
];

export const vacancyColumns = [
  { header: 'Lavozim', key: 'position', width: 25 },
  { header: 'Tashkilot nomi', key: 'organization', width: 30 },
  { header: 'Viloyat', key: 'region', width: 20 },
  { header: 'Tuman', key: 'district', width: 20 },
  { header: 'MFY', key: 'section', width: 25 },
  { header: 'Manzil', key: 'address', width: 30 },
  { header: 'Qo‘shimcha', key: 'description', width: 30 },
  { header: 'Yaratilgan sana', key: 'createdAt', width: 20 },
];

export const inAreaColumns = [
  { header: 'F.I.Sh', key: 'fullName', width: 30 },
  { header: 'Foydalanuvchi nomi', key: 'username', width: 25 },
  { header: 'Telefon raqami', key: 'phone', width: 20 },
  { header: 'Lavozim', key: 'position', width: 25 },
  { header: 'Tashkilot', key: 'organization', width: 30 },
  { header: 'So‘nggi kirish vaqti', key: 'lastInTime', width: 25 },
];

export const outAreaColumns = [
  { header: 'F.I.Sh', key: 'fullName', width: 30 },
  { header: 'Foydalanuvchi nomi', key: 'username', width: 25 },
  { header: 'Telefon raqami', key: 'phone', width: 20 },
  { header: 'Lavozim', key: 'position', width: 25 },
  { header: 'Tashkilot', key: 'organization', width: 30 },
  { header: 'So‘nggi chiqish vaqti', key: 'lastOutTime', width: 25 },
];

export const disabledAreaColumns = [
  { header: 'F.I.Sh', key: 'fullName', width: 30 },
  { header: 'Foydalanuvchi nomi', key: 'username', width: 25 },
  { header: 'Telefon raqami', key: 'phone', width: 20 },
  { header: 'Lavozim', key: 'position', width: 25 },
  { header: 'Tashkilot', key: 'organization', width: 30 },
  { header: 'So‘nggi DISABLED vaqti', key: 'lastDisabledTime', width: 25 },
];
