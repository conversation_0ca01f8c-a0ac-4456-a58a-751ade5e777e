-- DropForeignKey
ALTER TABLE "User" DROP CONSTRAINT "User_mainOrganizationId_fkey";

-- AlterTable
ALTER TABLE "Attendance" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "AttendanceReport" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "FaceIDDevice" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "File" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "GPSLocation" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "GPSLocationReport" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "Grade" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "Imei" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "Organization" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "OrganizationType" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "OrganizationTypePosition" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "Recipient" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "RecipientAnswer" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "RecipientAnswerType" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "Region" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "Task" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "TaskState" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "TaskType" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "User" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true,
ALTER COLUMN "mainOrganizationId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "UserWorkingSchedule" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AlterTable
ALTER TABLE "UserWorkingScheduleDay" ADD COLUMN     "stataus" BOOLEAN NOT NULL DEFAULT true;

-- AddForeignKey
ALTER TABLE "User" ADD CONSTRAINT "User_mainOrganizationId_fkey" FOREIGN KEY ("mainOrganizationId") REFERENCES "Organization"("id") ON DELETE SET NULL ON UPDATE CASCADE;
