import { Badge, Card } from 'antd'
import { Link } from 'react-router-dom'
import { getFileUrl } from '@/shared/utils/getFileUrl'
import { FileOutlined } from '@ant-design/icons'

interface TaskResultsProps {
	task: any
	user: any
	orgId: string | null
}

export const TaskResults = ({ task, user, orgId }: TaskResultsProps) => {
	const organizationId = JSON.parse(orgId!)
	const recipient = task.Recipients?.find(
		(recipient: { userId: any; organizationId: any }) =>
			recipient.userId === user?.id || recipient.organizationId === organizationId
	)

	const answers = recipient?.Answer || []

	const getStateText = (state: string) => {
		switch (state) {
			case 'REJECTED':
				return 'Rad etildi'
			case 'PENDING':
				return 'Tasdiqlanishi kutilyapti'
			case 'CONFIRMED':
				return 'Yakunlandi'
			default:
				return state
		}
	}

	return (
		<Card className='w-full p-6 space-y-6 rounded-xl h-96 overflow-y-auto shadow-md bg-white dark:bg-[#1a1a1a]'>
			<h2 className='text-xl font-bold mb-4 text-gray-800 dark:text-gray-100'>
				Topshiriq natijalari
			</h2>
			{!answers.length && <p className='text-gray-400 text-center py-8'>Natijalar mavjud emas</p>}
			{answers.map((answer: any) => (
				<div
					key={answer.id}
					className='border border-gray-200 dark:border-gray-700 rounded-lg p-5 mb-4 last:mb-0 bg-gray-50 dark:bg-[#23272f] shadow-sm hover:shadow-lg transition-shadow'>
					<div className='flex flex-col sm:flex-row sm:justify-between sm:items-center mb-3 gap-2'>
						<span className='font-medium text-base text-gray-900 dark:text-gray-100'>
							{answer.description}
						</span>
						<div className='flex items-center gap-2'>
							<Badge
								status={
									answer.state === 'CONFIRMED'
										? 'success'
										: answer.state === 'PENDING'
											? 'processing'
											: 'error'
								}
								text={getStateText(answer.state)}
							/>
							{answer.type?.name && (
								<span className='ml-2 px-2 py-0.5 rounded bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 text-xs font-semibold'>
									{answer.type.name}
								</span>
							)}
						</div>
					</div>
					{!!answer.files?.length && (
						<ul className='flex flex-col gap-y-2 mt-2'>
							{answer.files.map((file: any) => (
								<li key={file.id}>
									<Link
										to={getFileUrl(file.path)}
										target='_blank'
										className='flex items-center gap-2 text-blue-600 dark:text-blue-400 hover:underline hover:text-blue-800 transition-colors'>
										<FileOutlined />
										<span>{file.slug}</span>
									</Link>
								</li>
							))}
						</ul>
					)}
					{answer.rejectReason && (
						<div className='mt-4 text-red-600 bg-red-50 dark:bg-red-900/30 p-3 rounded-lg border border-red-200 dark:border-red-700'>
							<p className='font-semibold mb-1'>Rad etish sababi:</p>
							<p className='text-sm'>{answer.rejectReason}</p>
						</div>
					)}
				</div>
			))}
		</Card>
	)
}
