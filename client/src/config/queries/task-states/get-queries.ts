import { axiosPrivate } from '@/config/api'
import { taskStatesEndpoints } from '@/config/api/endpoints'
import { useInfiniteQuery } from '@tanstack/react-query'
import { TaskStates } from './create.queries'
import { TPaginationWrapper } from '@/shared/types/pagination.type'
import { useSearchParams } from 'react-router-dom'

export const useGetTaskStates = () => {
	const limit = 10
	const page = 1

	const [searchParams] = useSearchParams()
	const search = searchParams.get('search')

	return useInfiniteQuery({
		queryKey: [taskStatesEndpoints.all, 'infinite', search, page, limit],
		queryFn: async ({ pageParam }) => {
			return (
				await axiosPrivate.get<TPaginationWrapper<TaskStates[]>>(taskStatesEndpoints.all, {
					params: {
						page: pageParam,
						limit,
						search
					}
				})
			).data
		},
		initialPageParam: page,
		getNextPageParam: lastPage => {
			if (lastPage?.meta?.page === lastPage?.meta?.totalPages) return undefined
			return lastPage?.meta?.page + 1
		},
		getPreviousPageParam: lastPage => {
			if (lastPage?.meta?.page === 1) return undefined
			return lastPage?.meta?.page - 1
		}
	})
}
