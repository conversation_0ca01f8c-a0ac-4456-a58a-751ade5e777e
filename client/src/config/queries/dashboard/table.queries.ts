import { axiosPrivate } from '@/config/api'
import { dashboardEndpoints } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'

export type TDashboardTable = {
	id: string
	name: string
	pationsCount: number
	vacationCount: number
	vacancyCount: number
	employee: number
	level: number
}

export const useGetDashboardTable = () => {
	const [searchParams] = useSearchParams()

	const orgId = searchParams.get('orgId')

	return useQuery({
		queryKey: [dashboardEndpoints.table, { orgId }],
		queryFn: async () => {
			return (await axiosPrivate.get<TDashboardTable[]>(dashboardEndpoints.table(orgId ?? ''))).data
		}
	})
}
