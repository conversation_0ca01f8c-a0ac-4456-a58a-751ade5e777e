import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { NotificationType } from '@prisma/client';
import { PrismaService } from 'src/common';
import { NotificationPayload } from './interfaces/notification-type';

@Injectable()
export class NotificationService {
  logger = new Logger('NotificationService');

  constructor(private prismaService: PrismaService) {}

  @OnEvent(NotificationType.TASK_ASSIGNED)
  async handleTaskAssigned(payload: NotificationPayload) {
    try {
      await this.prismaService.notification.create({
        data: {
          type: payload.type,
          message: payload.message,
          ...(payload.userId && {
            User: {
              connect: {
                id: payload.userId,
              },
            },
          }),
          Organization: {
            connect: {
              id: payload.organizationId,
            },
          },
          Content: payload.Content,
        },
      });
    } catch (error) {
      this.logger.error(error);
    }
  }

  async readNotification(id: string[]) {
    const notification = await this.prismaService.notification.updateMany({
      where: {
        id: {
          in: id,
        },
      },
      data: {
        read: true,
      },
    });
    return notification;
  }

  async getNotifications(userId: string) {
    const user = await this.prismaService.user.findUnique({
      where: {
        id: userId,
      },
      include: {
        Organization: true,
      },
    });

    if (!user) {
      throw new Error('User not found');
    }

    const notifications = await this.prismaService.notification.findMany({
      where: {
        Organization: {
          id: {
            in: user.Organization.map((org) => org.id),
          },
        },
        read: false,
      },
      include: {
        Organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    return notifications;
  }
}
