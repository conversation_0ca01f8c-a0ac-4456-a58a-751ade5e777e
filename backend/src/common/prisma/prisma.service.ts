import { Injectable, OnModule<PERSON><PERSON><PERSON>, <PERSON><PERSON>, OnModule<PERSON><PERSON>roy, OnApplicationShutdown } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import * as process from 'node:process';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy, OnApplicationShutdown {
  private readonly logger = new Logger(PrismaService.name);

  constructor() {
    super({
      log: ['error', 'warn'],
    });

    this.$extends({
      model: {
        $allModels: {
          async findMany({ args, query, model }) {
            if (!args.orderBy) {
              args.orderBy = { id: 'desc' };
            }
            return query(args);
          },
        },
      },
    });
    this.setupProcessListeners();
  }

  async onModuleInit() {
    await this.$connect();
  }

  async onModuleDestroy() {
    await this.$disconnect();
  }

  async onApplicationShutdown(signal: string) {
    this.logger.log(`Received shutdown signal: ${signal}`);
    await this.$disconnect();
  }

  private setupProcessListeners() {
    process.on('SIGTERM', async () => {
      this.logger.log('Received SIGTERM, closing database connections...');
      await this.gracefulShutdown();
    });

    process.on('SIGINT', async () => {
      this.logger.log('Received SIGINT, closing database connections...');
      await this.gracefulShutdown();
    });

    process.on('uncaughtException', async (error) => {
      this.logger.error('Uncaught Exception:', error);
      await this.gracefulShutdown();
      process.exit(1);
    });

    process.on('unhandledRejection', async (reason, promise) => {
      this.logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      await this.gracefulShutdown();
      process.exit(1);
    });
  }

  private async gracefulShutdown() {
    try {
      await this.$disconnect();
      this.logger.log('Database connections closed successfully');
    } catch (error) {
      this.logger.error('Error during graceful shutdown:', error);
    }
  }
  S;
}
