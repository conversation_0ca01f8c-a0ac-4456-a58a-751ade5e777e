# Minimal va eng yengil Node.js versiyasidan foydalanamiz
FROM node:20-alpine AS builder

# Qurilish uchun zarur bo'lgan paketlarni o'rnatamiz
RUN apk add --no-cache openssl python3 make g++ dos2unix

WORKDIR /app

# package.json va yarn.lock fayllarini oldin ko'chiramiz, chunki agar dependencies o'zgarmagan bo'lsa,
# kechadan foydalanib, qayta o'rnat<PERSON>dan saqlanishimiz mumkin
COPY package*.json yarn.lock ./

# Dependency'larni o'rnatamiz
RUN yarn install --frozen-lockfile

# Prisma schema va migrations'larni ko'chiramiz
COPY prisma ./prisma/

# Loyiha fayllarini ko'chiramiz (kod)
COPY . .
COPY .env.development.local .env
# Prisma generate qilish (migratsiyalarni keyinroq bajarishimiz uchun)
RUN yarn prisma generate
RUN yarn build

# Final image uchun
FROM node:20-alpine

WORKDIR /app

# Runtime uchun kerakli paketlar
RUN apk add --no-cache netcat-openbsd dos2unix

# Ko'chirilgan fayllarni konteynerga joylash
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/yarn.lock ./
COPY --from=builder /app/. /app
COPY --from=builder /app/static ./static
COPY --from=builder /app/uploads ./uploads

# Skriptlarni ko'chirish va ruxsat berish
COPY wait-for-db.sh /usr/local/bin/
COPY docker-entrypoint.sh /usr/local/bin/

RUN dos2unix /usr/local/bin/wait-for-db.sh /usr/local/bin/docker-entrypoint.sh \
	&& chmod +x /usr/local/bin/wait-for-db.sh /usr/local/bin/docker-entrypoint.sh

# Konteyner portini ochamiz
EXPOSE 8001

# Asosiy entrypoint script
ENTRYPOINT ["sh", "/usr/local/bin/docker-entrypoint.sh"]
