import { Module } from '@nestjs/common';
import { InfractionService } from './infraction.service';
import { InfractionController } from './infraction.controller';
import { InfractionCreateService } from './service/infraction-create.service';
import { InfractionGetService } from './service/infraction-get.service';

@Module({
  controllers: [InfractionController],
  providers: [InfractionService, InfractionCreateService, InfractionGetService],
  exports: [InfractionCreateService, InfractionService],
})
export class InfractionModule {}
