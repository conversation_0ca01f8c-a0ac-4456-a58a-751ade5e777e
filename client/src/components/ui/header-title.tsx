import { Typography } from 'antd'
import { useSidebar } from '../utils/use-sidebar'
import { useHeaderTitle } from '@/providers/header-title-providers'

export const HeaderTitle = () => {
	const { headerTitle } = useHeaderTitle()
	const { isLg } = useSidebar()
	return (
		<Typography.Title
			level={isLg ? 3 : 2}
			className='pb-0 !mb-0 text-lg font-bold'>
			{headerTitle}
		</Typography.Title>
	)
}
