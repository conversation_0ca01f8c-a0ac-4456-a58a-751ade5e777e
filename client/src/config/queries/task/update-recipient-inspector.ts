import { axiosPrivate } from '@/config/api'
import { taskEndpoints } from '@/config/api/endpoints'
import { TApiErrorResponse } from '@/shared/types/api-error.type'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { notification } from 'antd'
import { AxiosError, AxiosResponse } from 'axios'

type TRecipientUpdate = {
	organizationId?: string
	userId?: string[]
	positionId?: string
}

type TUseUpdateRecipientInspector = {
	taskId: string
	data: {
		recipients: TRecipientUpdate[]
	}
}

export const useUpdateRecipientInspector = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({ taskId, data }: TUseUpdateRecipientInspector) => {
			return await axiosPrivate.put(taskEndpoints.updateRecipientInspector(taskId), data)
		},
		onSuccess: async (response: AxiosResponse, { taskId }) => {
			console.log(response)

			queryClient.invalidateQueries({ queryKey: [taskEndpoints.one(taskId)] })
			queryClient.invalidateQueries({ queryKey: [taskEndpoints.all] })
			notification.success({ message: "Topshiriqga yangi bajaruvchi muaffaqiyatli qo'shildi!" })
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response)
			notification.error({ message: 'Topshiriqga yangi bajaruvchi qo`shisda xatolik yuz berdi!' })
		}
	})
}
