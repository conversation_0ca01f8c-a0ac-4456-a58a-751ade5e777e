import { axiosPrivate } from '@/config/api'
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import { organizationEndpoints } from '@/config/api/endpoints'
import { TOrganization } from '../organizations/get-all.queries'
import { TPaginationWrapper } from '@/shared/types/pagination.type'
import { useState } from 'react'
import { useSearchParams } from 'react-router-dom'

export const useGetUnderControlOrganizations = (_?: string) => {
	const limit = 10
	const [page, setPage] = useState(1)

	const [searchParams] = useSearchParams()
	const search = searchParams.get('search')

	const query = useInfiniteQuery({
		queryKey: [
			organizationEndpoints.underContolOrganization,
			search,
			'infinite',
			page,
			limit,
			search
		],
		queryFn: async ({ pageParam }) => {
			return (
				await axiosPrivate.get<TPaginationWrapper<TOrganization[]>>(
					organizationEndpoints.underContolOrganization(),
					{
						params: {
							page: pageParam,
							limit,
							search
						}
					}
				)
			).data
		},
		initialPageParam: page,
		getNextPageParam: lastPage => {
			if (lastPage?.meta?.page === lastPage?.meta?.totalPages) return undefined
			return lastPage?.meta?.page + 1
		},
		getPreviousPageParam: lastPage => {
			if (lastPage?.meta?.page === 1) return undefined
			return lastPage?.meta?.page - 1
		}
	})
	return {
		...query,
		setPage
	}
}

// Hook to fetch all under-control organizations at once
export const useGetAllUnderControlOrganizations = () => {
	const [searchParams] = useSearchParams()
	const search = searchParams.get('search')

	return useQuery({
		queryKey: [organizationEndpoints.underContolOrganization, 'all-under-control', search],
		queryFn: async () => {
			// First, get the total count
			const firstPage = await axiosPrivate.get<TPaginationWrapper<TOrganization[]>>(
				organizationEndpoints.underContolOrganization(),
				{
					params: {
						page: 1,
						limit: 1,
						search
					}
				}
			)

			const totalCount = firstPage.data.meta.total

			// If no organizations, return empty array
			if (totalCount === 0) {
				return []
			}

			// Fetch all organizations in one request with high limit
			const allOrgsResponse = await axiosPrivate.get<TPaginationWrapper<TOrganization[]>>(
				organizationEndpoints.underContolOrganization(),
				{
					params: {
						page: 1,
						limit: totalCount,
						search
					}
				}
			)

			return allOrgsResponse.data.data
		}
	})
}
