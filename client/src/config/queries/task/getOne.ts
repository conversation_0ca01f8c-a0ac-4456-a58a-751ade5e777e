import { axiosPrivate } from '@/config/api'
import { taskEndpoints } from '@/config/api/endpoints'
import { STATUS } from '@/shared/types/status.type'
import { useQuery } from '@tanstack/react-query'
import { TaskStates } from '../task-states/create.queries'
import { TaskTypes } from '../task-types/create.queries'
import { TOrganization } from '../organizations/get-all.queries'
import { TUser } from '../users/get-all.queries'
import { STATE } from '@/shared/types/state.type'
import { useParams } from 'react-router-dom'

type TAnswer = {
	type: any
	id: string
	recipientId: string
	status: STATUS
	description: string
	typeId: string | null
	state: STATE
	files: TFile[]
	rejectReason: string | null
	createdAt: string
	updatedAt: string
}

export type TRecipient = {
	createdAt: string
	id: string
	isCompleted: false
	isRead: false
	organizationId: string
	status: STATUS
	taskId: string
	updatedAt: string
	userId: string | null
	User: TUser
	Organization: TOrganization
	Answer: TAnswer[]
}

export type TFile = {
	id: string
	slug: string
	path: string
	size: number
	status: STATUS
	mimeType: string
	createdAt: string
	updatedAt: string
}

export type TTask = {
	createdById: string
	createdByOrganizationId: string
	description: string
	dueDate: string
	id: string
	totalAnswers: {
		CONFIRMED: number
		REJECTED: number
		PENDING: number
	}
	name: string
	isCompleted: boolean
	parentId: string | null
	status: STATUS
	taskStateId: string
	taskTypeId: string
	createdAt: string
	updatedAt: string
	TaskState: TaskStates
	TaskType: TaskTypes
	CreatedByOrganization: TOrganization
	CreatedBy: TUser
	Recipients: TRecipient[]
	SubTasks: TTask[]
	files: TFile[]
}

export const useGetOneTask = () => {
	const { id } = useParams()
	return useQuery({
		enabled: !!id,
		queryKey: [taskEndpoints.one(id!)],
		queryFn: async () => {
			return (await axiosPrivate.get<TTask>(taskEndpoints.one(id!))).data
		}
	})
}
