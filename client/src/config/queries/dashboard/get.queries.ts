import { axiosPrivate } from '@/config/api'
import { dashboardEndpoints } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'

interface DashboardUsersCountType {
	workersCount: number
	employeesCount: number
	responsiblesCount: number
}

interface DashboardChildrenCountType {
	childrenCount: number
}

interface DashboardTasksCountType {
	inTask: number
	outTask: number
}

interface DashboardTasksStateType {
	name: string
	count: number
}

interface DashboardAttendanceType {
	todayAttendance: number
	lateAttendance: number
}

interface DashboardGpsInfoType {
	inCount: number
	outCount: number
	disabledCount: number
	inCountWithPositions: { positionName: string; count: number }[]
	outCountWithPositions: { positionName: string; count: number }[]
	disabledCountWithPositions: { positionName: string; count: number }[]
}

export const useDashboardUsersCount = () => {
	return useQuery({
		queryKey: [dashboardEndpoints.dashboardUsersCount],
		queryFn: async () => {
			const { data } = await axiosPrivate.get<DashboardUsersCountType>(
				dashboardEndpoints.dashboardUsersCount
			)
			return data
		}
	})
}

export const useDashboardChildrenCount = () => {
	return useQuery({
		queryKey: [dashboardEndpoints.dashboardChildrenCount],
		queryFn: async () => {
			const { data } = await axiosPrivate.get<DashboardChildrenCountType>(
				dashboardEndpoints.dashboardChildrenCount
			)
			return data
		}
	})
}

export const useDashboardTasksCount = () => {
	return useQuery({
		queryKey: [dashboardEndpoints.dashboardTasksCount],
		queryFn: async () => {
			const { data } = await axiosPrivate.get<DashboardTasksCountType>(
				dashboardEndpoints.dashboardTasksCount
			)
			return data
		}
	})
}

export const useDashboardTasksState = () => {
	return useQuery({
		queryKey: [dashboardEndpoints.dashboardTasksState],
		queryFn: async () => {
			const { data } = await axiosPrivate.get<DashboardTasksStateType>(
				dashboardEndpoints.dashboardTasksState
			)
			return data
		}
	})
}

export const useDashboardAttendance = () => {
	return useQuery({
		queryKey: [dashboardEndpoints.dashboardAttendance],
		queryFn: async () => {
			const { data } = await axiosPrivate.get<DashboardAttendanceType>(
				dashboardEndpoints.dashboardAttendance
			)
			return data
		}
	})
}

export const useDashboardGpsInfo = () => {
	return useQuery({
		queryKey: [dashboardEndpoints.dashboardGpsInfo],
		queryFn: async () => {
			const { data } = await axiosPrivate.get<DashboardGpsInfoType>(
				dashboardEndpoints.dashboardGpsInfo
			)
			return data
		}
	})
}
