import { useSearchParams } from 'react-router-dom'
import { Segmented } from 'antd'
import { useGetTaskStates } from '@/config/queries/task-states/get-queries.ts'

function SegmentedCompletedStatus() {
	const [searchParams, setSearchParams] = useSearchParams()
	const { data } = useGetTaskStates()
	const status = searchParams.get('status') ?? 'all'

	const completedState = searchParams.get('complatedState') ?? 'all'

	const handleSegmentChange = (value: string) => {
		searchParams.set('complatedState', value)
		setSearchParams(searchParams)
	}

	if (status === 'completed') {
		return (
			<Segmented
				block
				options={[
					{ label: 'Hammasi', value: 'all' },
					...(data?.pages
						.flatMap(page => page.data)
						.map(page => ({
							label: page.name,
							value: page.key
						})) ?? [])
				]}
				value={completedState}
				onChange={handleSegmentChange}
			/>
		)
	}
	return null
}

export default SegmentedCompletedStatus
