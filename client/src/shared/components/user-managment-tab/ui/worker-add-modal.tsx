import { Form, Modal } from 'antd'
import { useEffect, useState } from 'react'
import { getUserFormValidation } from '@/pages/settings/users/config/validations'
import { useConnectUserOrg, useConnectFindUser } from '@/config/queries/users/create.queries'
import { useParams } from 'react-router-dom'
import { useWorkerAddModalStore } from '../utils/worker-add-store'
import { WorkerAddForm } from './worker-add-form'
import { useFileUpload } from '../../file-upload/file-upload'

export const WorkerAddModal = () => {
	const [form] = Form.useForm()
	const { mutateAsync: connectUserOrg, isPending: isConnectingNewUser } = useConnectUserOrg()
	const { mutateAsync: connectFindUser, isPending: isConnectingExistingUser } = useConnectFindUser()
	const { open, onClose, data } = useWorkerAddModalStore()
	const { id: orgId } = useParams()
	const storedOrgId = localStorage.getItem('organizationId')
	const storedOrgIdValue = storedOrgId ? JSON.parse(storedOrgId) : ''
	const effectiveOrgId = orgId || storedOrgIdValue || ''
	const userFormValidation = getUserFormValidation(false)
	const { fileIds, clearFiles, render: Uploader } = useFileUpload()

	// Confirmation modal state
	const [confirmationModal, setConfirmationModal] = useState({
		open: false,
		userData: null as any,
		formData: null as any
	})

	const isLoading = isConnectingNewUser || isConnectingExistingUser

	const close = () => {
		form.resetFields()
		clearFiles()
		onClose()
	}

	const closeConfirmationModal = () => {
		setConfirmationModal({
			open: false,
			userData: null,
			formData: null
		})
		// Close the main modal when confirmation modal is closed
		close()
	}

	useEffect(() => {
		if (data) {
			form.setFieldsValue(data)
		}
	}, [data, form])

	// Helper function to clean phone number format
	const cleanPhoneNumber = (phone: string) => {
		return phone?.replace(/^\+998\s*/, '').replace(/[^\d]/g, '') || ''
	}

	const handleUserFound = (userData: any, formData: any) => {
		// Show confirmation modal immediately when user is found
		setConfirmationModal({
			open: true,
			userData,
			formData
		})
	}

	const handleConfirmConnection = async () => {
		try {
			await connectFindUser({
				organizationId: effectiveOrgId,
				userId: confirmationModal.userData.id
			})
			closeConfirmationModal()
		} catch (error) {
			console.error('Error connecting user to organization:', error)
		}
	}

	const onFinish = async (formData: any) => {
		try {
			const processedFormData = {
				...formData,
				phone: cleanPhoneNumber(formData.phone),
				username: cleanPhoneNumber(formData.username) || cleanPhoneNumber(formData.phone)
			}

			// Only handle new user creation here, existing user is handled by confirmation modal
			if (!formData.userId) {
				// Remove fields that shouldn't be in the request body
				const { organizationId, userId, mainOrganizationId, searchPhone, ...createUserData } =
					processedFormData

				await connectUserOrg({
					organizationId: effectiveOrgId, // This will be used as query param
					...createUserData,
					avatarId: fileIds[0],
					password: createUserData.password
				})

				close()
			}
		} catch (error) {
			console.error('Error connecting user to organization:', error)
		}
	}

	return (
		<>
			<Modal
				open={open}
				width={800}
				onCancel={close}
				destroyOnClose
				onOk={form.submit}
				title="Ishchini tashkilotga qo'shish"
				okText="Qo'shish"
				cancelText='Bekor qilish'
				confirmLoading={isLoading}
				modalRender={node => (
					<Form
						layout='vertical'
						size='large'
						onFinish={onFinish}
						form={form}>
						{node}
					</Form>
				)}>
				<WorkerAddForm
					validation={userFormValidation}
					form={form}
					Uploader={Uploader}
					onUserFound={handleUserFound}
				/>
			</Modal>

			{/* Confirmation Modal */}
			<Modal
				open={confirmationModal.open}
				onCancel={closeConfirmationModal}
				width={500}
				title='Mahallaga biriktirish ?'
				footer={[
					<button
						key='cancel'
						className='px-4 py-2 mr-2 bg-red-500 rounded cursor-pointer'
						onClick={closeConfirmationModal}>
						Yo'q, kerak emas
					</button>,
					<button
						key='confirm'
						className='px-4 py-2 text-white bg-blue-600 rounded hover:bg-blue-700'
						onClick={handleConfirmConnection}
						disabled={isConnectingExistingUser}>
						{isConnectingExistingUser ? 'Biriktirilmoqda...' : 'Ha, biriktirish'}
					</button>
				]}>
				<div className='py-4'>
					<p className='text-lg text-center'>
						Siz rostdan ham ushbu xodimni bu tashkilotga biriktirmoqchimisiz?
					</p>
				</div>
			</Modal>
		</>
	)
}
