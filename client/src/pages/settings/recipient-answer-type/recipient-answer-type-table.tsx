import { EditOutlined } from '@ant-design/icons'
import { Button, Space, Table } from 'antd'
import { useRecipientAnswerTypesModalStore } from './utils/recipient-answer-type-store'
import { useRecipientAnswerTypes } from '@/config/queries/recipient-answer-type/get.queries'
import RecipientAsnwerTypeModal from './ui/recipient-asnwer-type.modal'

import { useState } from 'react'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { Search } from '@/shared/components/search/search'

export const RecipientAsnwerTypeTable = () => {
	const [currentPage, setCurrentPage] = useState(1)
	const [pageSize, setPageSize] = useState(10)
	const { onOpen } = useRecipientAnswerTypesModalStore()

	const { data: recipientAnswerTypes, hasNextPage, fetchNextPage } = useRecipientAnswerTypes()

	return (
		<>
			<Table
				pagination={{
					total: recipientAnswerTypes?.pages?.[0]?.meta?.total || 0,
					pageSize: pageSize,
					current: currentPage,
					showSizeChanger: true,
					pageSizeOptions: [5, 10, 20, 50, 100],
					onChange: (page, size) => {
						setCurrentPage(page)
						setPageSize(size)
						if (
							Math.ceil((page * size) / size) > (recipientAnswerTypes?.pages?.length || 0) &&
							hasNextPage
						) {
							fetchNextPage()
						}
					}
				}}
				title={() => (
					<div className='flex items-center justify-between'>
						<Search />
						<RecipientAsnwerTypeModal />
					</div>
				)}
				size='small'
				scroll={{ x: 'max-content' }}
				dataSource={recipientAnswerTypes?.pages?.flatMap(page => page.data) || []}
				rowKey={(rec: { id?: string }) => rec.id || ''}
				columns={[
					{
						key: '#',
						title: '#',
						render: (_, _rec, index) => formatTableIndex(currentPage, pageSize, index)
					},
					{
						key: 'name',
						title: 'Nomi',
						dataIndex: 'name'
					},

					{
						key: 'actions',
						title: 'Amallar',
						render: record => (
							<Space>
								<Button
									type='primary'
									icon={<EditOutlined />}
									onClick={() => onOpen(record)}
								/>
							</Space>
						)
					}
				]}
			/>
		</>
	)
}
