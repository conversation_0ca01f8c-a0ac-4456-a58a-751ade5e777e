import React from 'react'
import { MapPin } from 'lucide-react'

export const MapLegend: React.FC = () => {
	return (
		<div className='absolute bottom-4 right-4 z-[1000] bg-white rounded-lg shadow-lg border border-gray-200'>
			<div className='p-3'>
				<h4 className='text-xs font-semibold text-gray-800 mb-2 flex items-center'>
					<MapPin className='w-3 h-3 mr-1' />
					Легенда
				</h4>
				<div className='space-y-1 text-xs'>
					<div className='flex items-center space-x-2'>
						<div className='w-3 h-3 bg-blue-500 rounded-sm border border-blue-700'></div>
						<span className='text-gray-600'>Клик - подробная информация</span>
					</div>
					<div className='flex items-center space-x-2'>
						<div className='w-3 h-3 bg-green-500 rounded-sm border border-green-700'></div>
						<span className='text-gray-600'>Наведение - подсветка</span>
					</div>
					<div className='flex items-center space-x-2'>
						<div className='w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-sm border border-purple-700'></div>
						<span className='text-gray-600'>Каждая область - уникальный цвет</span>
					</div>
				</div>
			</div>
		</div>
	)
}
