/* eslint-disable react-hooks/exhaustive-deps */
import { Col, Form, Row, Select } from 'antd'
import { useCallback, useMemo, useState } from 'react'
import {
	TOrganization,
	useGetAllOrganizations
} from '@/config/queries/organizations/get-all.queries'
import { useGetOneOrganization } from '@/config/queries/organizations/getOne'
import { useGetUnderControlOrganizations } from '@/config/queries/under-control-organizations/get-queries'
import { mergeArraysById } from '@/shared/utils/arrayMerge'
import { LoadingScreen } from '@/shared/ui/suspense'
import { debounce } from 'lodash'
import { useGetAllOrganizationTypePostionByTypeId } from '@/config/queries/organization-position-types/getById.queries'
import { useSearchParams } from 'react-router-dom'

export default function VacanciesForm() {
	const { data: children } = useGetAllOrganizations()
	const [selectedOrganizationType, setSelectedOrganizationType] = useState<string>()
	const { data: organizationPositionTypes, fetchNextPage } =
		useGetAllOrganizationTypePostionByTypeId(selectedOrganizationType)
	const orgId = localStorage.getItem('organizationId')
	const { data: selfOrganization, isLoading: isLoadingSelfOrganization } = useGetOneOrganization(
		JSON.parse(orgId!)
	)
	const { data: underControlsData, isLoading: isLoadingUnderControls } =
		useGetUnderControlOrganizations()
	const [searchParams, setSearchParams] = useSearchParams()

	const underControls = underControlsData?.pages?.flatMap(page => page.data) ?? []

	const mergedOrganizations = [
		{ ...selfOrganization, name: 'Mazkur tashkilotning o`zi' },
		...mergeArraysById<TOrganization>([
			children?.pages?.flatMap(page => page.data) ?? [],
			underControls
		])
	]

	const handleOrgChange = (value: string) => {
		const organization = mergedOrganizations.find(org => org.id === value)
		setSelectedOrganizationType(organization?.typeId)
	}

	const handlePopupScroll = useCallback(
		debounce((e: React.UIEvent<HTMLDivElement>) => {
			const target = e.target as HTMLDivElement
			const isNearBottom = target.scrollTop + target.offsetHeight >= target.scrollHeight - 10
			if (isNearBottom) {
				fetchNextPage()
			}
		}, 100),
		[]
	)

	const handleSearch = useCallback((value: string) => {
		searchParams.set('search', value.trim())
		setSearchParams(searchParams.toString())
	}, [])

	const handleClear = useCallback(() => {
		searchParams.delete('search')
		setSearchParams(searchParams.toString())
	}, [])

	const debouncedSearch = useMemo(() => debounce(handleSearch, 300), [handleSearch])

	if (isLoadingSelfOrganization || isLoadingUnderControls) return <LoadingScreen />

	return (
		<Row gutter={[16, 16]}>
			<Col span={24}>
				<Form.Item
					name='organizationId'
					label='Asosiy tashkiloti'
					rules={[{ required: true, message: 'Tashkilotni tanlang' }]}>
					<Select
						allowClear
						options={mergedOrganizations}
						fieldNames={{ label: 'name', value: 'id' }}
						onChange={handleOrgChange}
					/>
				</Form.Item>
			</Col>

			<Col span={24}>
				<Form.Item
					name='positionId'
					label='Lavozim'
					rules={[{ required: true, message: 'Lavozimni tanlang' }]}>
					<Select
						allowClear
						showSearch
						options={organizationPositionTypes?.pages.flatMap(page => page.data)}
						fieldNames={{ label: 'name', value: 'id' }}
						onPopupScroll={handlePopupScroll}
						onSearch={debouncedSearch}
						onClear={handleClear}
					/>
				</Form.Item>
			</Col>
		</Row>
	)
}
