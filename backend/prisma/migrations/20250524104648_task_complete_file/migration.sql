/*
  Warnings:

  - You are about to drop the column `completeFile` on the `Task` table. All the data in the column will be lost.
  - You are about to drop the `_FileToTask` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "_FileToTask" DROP CONSTRAINT "_FileToTask_A_fkey";

-- DropForeignKey
ALTER TABLE "_FileToTask" DROP CONSTRAINT "_FileToTask_B_fkey";

-- AlterTable
ALTER TABLE "Task" DROP COLUMN "completeFile",
ADD COLUMN     "completeFileId" TEXT;

-- DropTable
DROP TABLE "_FileToTask";

-- CreateTable
CREATE TABLE "_files" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_files_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_files_B_index" ON "_files"("B");

-- AddForeignKey
ALTER TABLE "Task" ADD CONSTRAINT "Task_completeFileId_fkey" FOREIGN KEY ("completeFileId") REFERENCES "File"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_files" ADD CONSTRAINT "_files_A_fkey" FOREIGN KEY ("A") REFERENCES "File"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_files" ADD CONSTRAINT "_files_B_fkey" FOREIGN KEY ("B") REFERENCES "Task"("id") ON DELETE CASCADE ON UPDATE CASCADE;
