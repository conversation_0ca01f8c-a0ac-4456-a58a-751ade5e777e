import { axiosPrivate } from '@/config/api'
import { userWorkingScheduleDayEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { notification } from 'antd'

export const useDeleteUserWorkingScheduleDay = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async (id: string) => {
			return await axiosPrivate.delete(userWorkingScheduleDayEndpoints.one(id))
		},
		onSuccess: async () => {
			queryClient.invalidateQueries({ queryKey: [userWorkingScheduleDayEndpoints.all] })
			notification.success({
				message: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o`chirildi'
			})
		},
		onError: async () => {
			notification.error({
				message: 'O`chirishda xatolik yuz berdi'
			})
		}
	})
}
