import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/common';
import { AttendanceReportQueryDto } from '../dto/attendance-report.dto';
import { AttendanceHelperService } from './attendance-helper.service';
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import 'dayjs/locale/uz-latn';

dayjs.extend(utc);
dayjs.locale('uz-latn');

interface Worker {
  id: string;
  fullName: string;
  position: {
    id: string;
    name: string;
  };
  UserWorkingSchedule: {
    id: string;
    days: {
      id: string;
      day: number;
      startTime: string;
      endTime: string;
    }[];
  };
  AttendanceReport: {
    id: string;
    description: string;
    date: Date;
    minutes: number;
    enter: Date;
    exit: Date;
    earlyExit: number;
    lateEnter: number;
    scheduledEnter: Date;
    scheduledExit: Date;
    scheduledMinutes: number;
    dayNumber: number;
    underTimeMinutes: number;
    isLate: boolean;
    isEarlyExit: boolean;
  }[];
}

@Injectable()
export class AttendanceReportService {
  constructor(
    private prismaService: PrismaService,
    private helperService: AttendanceHelperService,
  ) {}

  async getAttendanceReport(orgIds: string[], startDate?: string, endDate?: string) {
    if (!orgIds || orgIds.length === 0) {
      throw new NotFoundException('Organization IDs not provided');
    }

    // Правильная обработка временной зоны
    const { startDateTime, endDateTime } = this.helperService.processDateRange(startDate, endDate);
    const dateFilter = this.helperService.createDateFilter(startDateTime, endDateTime);

    const organizations = await this.prismaService.organization.findMany({
      where: { id: { in: orgIds } },
      select: {
        id: true,
        name: true,
        Worker: {
          select: {
            id: true,
            fullName: true,
            position: {
              select: {
                id: true,
                name: true,
              },
            },
            AttendanceReport: {
              where: dateFilter,
              select: {
                id: true,
                date: true,
                enter: true,
                exit: true,
                minutes: true,
                description: true,
                earlyExit: true,
                lateEnter: true,
                scheduledEnter: true,
                scheduledExit: true,
                scheduledMinutes: true,
                dayNumber: true,
                underTimeMinutes: true,
                isLate: true,
                isEarlyExit: true,
              },
            },
            UserWorkingSchedule: {
              select: {
                id: true,
                days: {
                  select: {
                    id: true,
                    day: true,
                    startTime: true,
                    endTime: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!organizations || organizations.length === 0) {
      throw new NotFoundException('Organizations not found');
    }

    const allEmployeesReport = [];

    for (const organization of organizations) {
      const mainEmployeesReport = organization.Worker.map((worker) => {
        const employeeReport = this.analyzeEmployeeAttendance(worker, startDateTime, endDateTime);
        return {
          ...employeeReport,
          organization: {
            id: organization.id,
            name: organization.name,
          },
        };
      });

      allEmployeesReport.push(...mainEmployeesReport);
    }

    return {
      reportPeriod: {
        startDate: startDateTime.tz('Asia/Tashkent').format('YYYY-MM-DDTHH:mm:ssZ'),
        endDate: endDateTime.tz('Asia/Tashkent').format('YYYY-MM-DDTHH:mm:ssZ'),
      },
      workers: allEmployeesReport,
    };
  }

  /**
   * Получает оптимизированный отчет о посещаемости с пагинацией по сотрудникам
   * Данный метод возвращает сводную информацию по сотрудникам без детализации по дням
   */
  async getAttendanceReportByOrganizationTypePosition(
    orgId: string,
    positionId: string,
    startDate?: string,
    endDate?: string,
    page: number = 1,
    limit: number = 20,
  ) {
    try {
      const position = await this.prismaService.organizationTypePosition.findUnique({
        where: {
          id: positionId,
        },
        select: {
          id: true,
          name: true,
          description: true,
          type: true,
        },
      });

      if (!position) {
        throw new NotFoundException('Position not found');
      }

      // Правильная обработка временной зоны
      const { startDateTime, endDateTime } = this.helperService.processDateRange(startDate, endDate);
      const dateFilter = this.helperService.createDateFilter(startDateTime, endDateTime);

      const organizations = await this.prismaService.$queryRawUnsafe<any>(
        `
  WITH RECURSIVE org_tree AS (
    SELECT * FROM "Organization" WHERE id = $1
    UNION ALL
    SELECT child.* FROM "Organization" child
    INNER JOIN org_tree parent ON child."parentId" = parent.id
  )
  SELECT org_tree.id, org_tree.name, org_tree."parentId", "Grade".level, "Grade".name as "gradeName", org_tree.* 
  FROM org_tree
  INNER JOIN "Grade" ON "Grade".id = org_tree."gradeId"
  WHERE "Grade".level = 30;
`,
        orgId,
      );

      if (!organizations) {
        throw new NotFoundException('Organization not found');
      }

      const organizationIds = organizations.map((org) => org.id);

      // Получаем общее количество пользователей для пагинации
      const totalUsers = await this.prismaService.user.count({
        where: {
          Organization: {
            some: {
              id: {
                in: organizationIds,
              },
            },
          },
          positionId: positionId,
          status: 'ACTIVE',
        },
      });

      // Применяем пагинацию
      const skip = (page - 1) * limit;

      // Получаем пользователей с базовой информацией для сводного отчета с пагинацией
      const users = await this.prismaService.user.findMany({
        where: {
          Organization: {
            some: {
              id: {
                in: organizationIds,
              },
            },
          },
          positionId: positionId,
          status: 'ACTIVE',
        },
        select: {
          id: true,
          fullName: true,
          username: true,
          phone: true,
          position: {
            select: {
              id: true,
              name: true,
              description: true,
              type: true,
            },
          },
          role: true,
          // Вместо получения всех отчетов, получаем только агрегированные данные
          _count: {
            select: {
              AttendanceReport: {
                where: dateFilter,
              },
            },
          },
          // Для расчета сводных показателей все еще нужны отчеты
          AttendanceReport: {
            where: dateFilter,
            select: {
              exit: true,
              minutes: true,
              description: true,
              earlyExit: true,
              lateEnter: true,
              scheduledEnter: true,
              scheduledExit: true,
              scheduledMinutes: true,
              dayNumber: true,
              underTimeMinutes: true,
              isLate: true,
              isEarlyExit: true,
            },
          },
          UserWorkingSchedule: {
            select: {
              id: true,
              days: {
                select: {
                  id: true,
                  day: true,
                  startTime: true,
                  endTime: true,
                },
              },
            },
          },
          Organization: {
            select: {
              id: true,
              name: true,
              phone: true,
              address: true,
              description: true,
              grade: true,
            },
          },
          MainOrganization: {
            select: {
              name: true,
            },
          },
        },
      });

      const allEmployeesReport = users.map((user: any) => {
        const employeeReport = this.analyzeEmployeeAttendance(user, startDateTime, endDateTime);
        return {
          ...employeeReport,
          position: position, // Добавляем информацию о выбранной должности
        };
      });

      return {
        reportPeriod: {
          startDate: startDateTime.toDate(),
          endDate: endDateTime.toDate(),
        },
        position: position, // Добавляем информацию о выбранной должности
        workers: allEmployeesReport,
      };
    } catch (error) {
      throw new NotFoundException('Error fetching attendance report by organization type position: ' + error.message);
    }
  }

  /**
   * Получает оптимизированный отчет о посещаемости с пагинацией по сотрудникам
   * Возвращает сводную информацию по сотрудникам без детализации по дням
   */
  async getOptimizedAttendanceReport(query: AttendanceReportQueryDto) {
    try {
      const { organizationId, positionId, startDate, endDate, page = 1, limit = 20 } = query;

      if (!positionId) {
        throw new NotFoundException('Position ID is required');
      }

      const position = await this.prismaService.organizationTypePosition.findUnique({
        where: { id: positionId },
        select: {
          id: true,
          name: true,
          description: true,
          type: true,
        },
      });

      if (!position) {
        throw new NotFoundException('Position not found');
      }

      // Правильная обработка временной зоны
      const { startDateTime, endDateTime } = this.helperService.processDateRange(startDate, endDate);
      const dateFilter = this.helperService.createDateFilter(startDateTime, endDateTime);

      // Получаем организации
      const organizations = await this.prismaService.$queryRawUnsafe<any>(
        `
  WITH RECURSIVE org_tree AS (
    SELECT * FROM "Organization" WHERE id = $1
    UNION ALL
    SELECT child.* FROM "Organization" child
    INNER JOIN org_tree parent ON child."parentId" = parent.id
  )
  SELECT org_tree.id, org_tree.name, org_tree."parentId", "Grade".level, "Grade".name as "gradeName", org_tree.* 
  FROM org_tree
  INNER JOIN "Grade" ON "Grade".id = org_tree."gradeId"
  WHERE "Grade".level = 30;
`,
        organizationId,
      );

      if (!organizations || organizations.length === 0) {
        throw new NotFoundException('Organizations not found');
      }

      const organizationIds = organizations.map((org) => org.id);

      // Считаем общее количество для пагинации
      const totalUsers = await this.prismaService.user.count({
        where: {
          Organization: {
            some: {
              id: { in: organizationIds },
            },
          },
          positionId: positionId,
          status: 'ACTIVE',
        },
      });

      // Применяем пагинацию
      const skip = (page - 1) * limit;

      // Получаем пользователей с пагинацией
      const users = await this.prismaService.user.findMany({
        skip,
        take: limit,
        where: {
          Organization: {
            some: {
              id: { in: organizationIds },
            },
          },
          positionId: positionId,
          status: 'ACTIVE',
        },
        select: {
          id: true,
          fullName: true,
          username: true,
          phone: true,
          position: {
            select: {
              id: true,
              name: true,
              description: true,
              type: true,
            },
          },
          role: true,
          MainOrganization: {
            select: {
              id: true,
              name: true,
            },
          },
          Organization: {
            select: {
              id: true,
              name: true,
            },
            take: 1,
          },
          UserWorkingSchedule: {
            select: {
              id: true,
              days: {
                select: {
                  id: true,
                  day: true,
                  startTime: true,
                  endTime: true,
                },
              },
            },
          },
          AttendanceReport: {
            where: dateFilter,
            select: {
              id: true,
              date: true,
              enter: true,
              exit: true,
              minutes: true,
              description: true,
              earlyExit: true,
              lateEnter: true,
              scheduledEnter: true,
              scheduledExit: true,
              scheduledMinutes: true,
              dayNumber: true,
              underTimeMinutes: true,
              isLate: true,
              isEarlyExit: true,
            },
            orderBy: {
              date: 'asc',
            },
          },
        },
      });

      // Для каждого пользователя получаем агрегированные данные о посещаемости и ежедневный отчет
      const userSummaries = await Promise.all(
        users.map(async (user) => {
          const attendanceStats = await this.prismaService.$queryRaw`
          SELECT 
            COUNT(DISTINCT date) as attendance_days,
            SUM(minutes) as total_minutes_worked,
            SUM(CASE WHEN "isLate" = true THEN "lateEnter" ELSE 0 END) as total_late_minutes,
            SUM(CASE WHEN "isEarlyExit" = true THEN "earlyExit" ELSE 0 END) as total_early_exit,
            COUNT(CASE WHEN "isLate" = true THEN 1 END) as late_count,
            COUNT(CASE WHEN "isEarlyExit" = true THEN 1 END) as early_exit_count
          FROM "AttendanceReport"
          WHERE "userId" = ${user.id}
            AND date >= ${startDateTime.toDate()}
            AND date <= ${endDateTime.toDate()}
        `;

          // Рассчитываем количество рабочих дней для пользователя в указанный период
          const workDaysCount = this.helperService.calculateWorkDays(
            user.UserWorkingSchedule?.days || [],
            startDateTime,
            endDateTime,
          );

          // Создаем объект для отображения ежедневной посещаемости
          const dailyAttendanceMap = {};

          // Создаем мапу существующих отчетов по дням
          user.AttendanceReport.forEach((report) => {
            const dateKey = this.helperService.formatDate(report.date);
            dailyAttendanceMap[dateKey] = {
              date: report.date,
              enter: report.enter,
              exit: report.exit,
              minutes: report.minutes,
              isLate: report.isLate,
              isEarlyExit: report.isEarlyExit,
              lateMinutes: report.lateEnter,
              earlyExitMinutes: report.earlyExit,
            };
          });

          // Создаем массив всех дней в периоде
          const dailyAttendance = [];
          let currentDate = dayjs.utc(startDateTime);
          while (currentDate.isBefore(endDateTime) || currentDate.isSame(endDateTime, 'day')) {
            const dateStr = this.helperService.formatDate(currentDate.toDate());
            const dayNumber = currentDate.day() === 0 ? 7 : currentDate.day();
            const scheduleForDay = user.UserWorkingSchedule?.days?.find((day) => Number(day.day) === dayNumber);

            // Проверяем, есть ли отчет для этой даты
            if (dailyAttendanceMap[dateStr]) {
              // Сотрудник присутствовал в этот день
              dailyAttendance.push({
                date: dateStr,
                dayOfWeek: dayNumber,
                dayName: this.helperService.getDayName(dayNumber),
                status: 'PRESENT',
                isWorkday: true,
                enter: dailyAttendanceMap[dateStr].enter,
                exit: dailyAttendanceMap[dateStr].exit,
                minutes: dailyAttendanceMap[dateStr].minutes,
                isLate: dailyAttendanceMap[dateStr].isLate,
                isEarlyExit: dailyAttendanceMap[dateStr].isEarlyExit,
                lateMinutes: dailyAttendanceMap[dateStr].lateMinutes,
                earlyExitMinutes: dailyAttendanceMap[dateStr].earlyExitMinutes,
              });
            } else if (scheduleForDay) {
              // Рабочий день, но сотрудник отсутствовал
              dailyAttendance.push({
                date: dateStr,
                dayOfWeek: dayNumber,
                dayName: this.helperService.getDayName(dayNumber),
                status: 'ABSENT',
                isWorkday: true,
                enter: null,
                exit: null,
                minutes: 0,
                isLate: false,
                isEarlyExit: false,
                lateMinutes: 0,
                earlyExitMinutes: 0,
              });
            } else {
              // Выходной день
              dailyAttendance.push({
                date: dateStr,
                dayOfWeek: dayNumber,
                dayName: this.helperService.getDayName(dayNumber),
                status: 'DAY_OFF',
                isWorkday: false,
                enter: null,
                exit: null,
                minutes: 0,
                isLate: false,
                isEarlyExit: false,
                lateMinutes: 0,
                earlyExitMinutes: 0,
              });
            }

            currentDate = currentDate.add(1, 'day');
          }

          // Форматируем данные для возврата
          return {
            employee: {
              id: user.id,
              fullName: user.fullName,
              phone: user.phone,
              position: user.position,
              organization: user.Organization[0] || user.MainOrganization,
            },
            summary: {
              workDaysInPeriod: workDaysCount,
              attendanceDays: Number(attendanceStats[0]?.attendance_days || 0),
              totalMinutesWorked: Number(attendanceStats[0]?.total_minutes_worked || 0),
              totalLateMinutes: Number(attendanceStats[0]?.total_late_minutes || 0),
              totalEarlyExitMinutes: Number(attendanceStats[0]?.total_early_exit || 0),
              lateCount: Number(attendanceStats[0]?.late_count || 0),
              earlyExitCount: Number(attendanceStats[0]?.early_exit_count || 0),
              attendanceRate:
                workDaysCount > 0 ? (Number(attendanceStats[0]?.attendance_days || 0) / workDaysCount) * 100 : 0,
            },
            dailyAttendance: dailyAttendance, // Добавляем ежедневные данные
          };
        }),
      );

      return {
        data: {
          reportPeriod: {
            startDate: startDateTime.toDate(),
            endDate: endDateTime.toDate(),
          },
          position,
          workers: userSummaries,
        },
        meta: {
          total: totalUsers,
          page: Number(page),
          limit: Number(limit),
          totalPages: Math.ceil(totalUsers / Number(limit)),
          hasMore: skip + users.length < totalUsers,
        },
      };
    } catch (error) {
      throw new NotFoundException(`Error fetching attendance report: ${error.message}`);
    }
  }

  /**
   * Создает структуру отчета с reportPeriod и workers
   */
  private createReportResponse(startDateTime: dayjs.Dayjs, endDateTime: dayjs.Dayjs, workers: any[], position?: any) {
    const response: any = {
      reportPeriod: {
        startDate: startDateTime.toDate(),
        endDate: endDateTime.toDate(),
      },
      workers,
    };

    if (position) {
      response.position = position;
    }

    return response;
  }

  /**
   * Создает стандартный объект дневного отчета
   */
  private createDayReport(
    dayNumber: number,
    date: dayjs.Dayjs,
    formattedDate: string,
    status: string,
    isWorkDay: boolean,
    cameToWork: boolean,
    options: {
      scheduledStartTime?: any;
      scheduledEndTime?: any;
      actualEnterTime?: any;
      actualExitTime?: any;
      minutesWorked?: number;
      lateMinutes?: number;
      earlyExitMinutes?: number;
      scheduledEnter?: any;
      scheduledExit?: any;
      scheduledMinutes?: number;
      underTimeMinutes?: number;
      isLate?: boolean;
      isEarlyExit?: boolean;
      earlyExitValue?: any;
      lateEnterValue?: any;
      expectedWorkday?: boolean;
    } = {},
  ) {
    return {
      day: dayNumber,
      date: date.toDate(),
      formattedDate,
      status,
      isWorkDay,
      cameToWork,
      scheduledStartTime: options.scheduledStartTime || null,
      scheduledEndTime: options.scheduledEndTime || null,
      actualEnterTime: options.actualEnterTime || null,
      actualExitTime: options.actualExitTime || null,
      minutesWorked: options.minutesWorked || 0,
      lateMinutes: options.lateMinutes || 0,
      earlyExitMinutes: options.earlyExitMinutes || 0,
      scheduledEnter: options.scheduledEnter || null,
      scheduledExit: options.scheduledExit || null,
      scheduledMinutes: options.scheduledMinutes || null,
      underTimeMinutes: options.underTimeMinutes || null,
      isLate: options.isLate || false,
      isEarlyExit: options.isEarlyExit || false,
      earlyExitValue: options.earlyExitValue || null,
      lateEnterValue: options.lateEnterValue || null,
      ...(options.expectedWorkday !== undefined && { expectedWorkday: options.expectedWorkday }),
    };
  }

  /**
   * Создает объект расписания работы
   */
  private createWorkSchedule(employee: any) {
    return (
      employee.UserWorkingSchedule?.days?.map((day: any) => ({
        dayNumber: Number(day.day),
        dayName: this.helperService.getDayName(Number(day.day)),
        startTime: day.startTime,
        endTime: day.endTime,
      })) || []
    );
  }

  /**
   * Создает объект расписания перерывов
   */
  private createBreakSchedule(userBreak: any) {
    return {
      startTime: `${userBreak.startHour.toString().padStart(2, '0')}:${userBreak.startMinute.toString().padStart(2, '0')}`,
      endTime: `${userBreak.endHour.toString().padStart(2, '0')}:${userBreak.endMinute.toString().padStart(2, '0')}`,
    };
  }

  /**
   * Создает объект суммарной статистики
   */
  private createSummary(
    daysAnalysis: any[],
    scheduledMinutes: number,
    totalWorkMinutesPlanned: number,
    totalMinutesWorked: number,
  ) {
    const totalWorkDays = daysAnalysis.filter((day: any) => day.isWorkDay).length;
    const totalAttendanceDays = daysAnalysis.filter((day: any) => day.cameToWork).length;
    const totalLateMinutes = daysAnalysis.reduce((sum: number, day: any) => sum + (day.lateMinutes || 0), 0);
    const totalEarlyExitMinutes = daysAnalysis.reduce((sum, day) => sum + (day.earlyExitMinutes || 0), 0);
    const factHours = Math.floor(totalMinutesWorked / 60) + ':' + (totalMinutesWorked % 60).toString().padStart(2, '0');

    return {
      totalWorkDays,
      totalAttendanceDays,
      totalLateMinutes,
      totalEarlyExitMinutes,
      totalMinutesWorked,
      totalWorkMinutesPlanned,
      attendanceRate: totalWorkDays > 0 ? (totalAttendanceDays / totalWorkDays) * 100 : 100,
      missedWorkdays: totalWorkDays - totalAttendanceDays - 1,
      planHours: scheduledMinutes
        ? Math.floor(scheduledMinutes / 60) + ':' + (scheduledMinutes % 60).toString().padStart(2, '0')
        : '0',
      factHours,
    };
  }

  private analyzeEmployeeAttendance(employee: Worker, startDateTime: dayjs.Dayjs, endDateTime: dayjs.Dayjs) {
    const periodDates = this.helperService.createPeriodDates(startDateTime, endDateTime);
    const userScheduleDays = employee.UserWorkingSchedule?.days || [];

    let currentDate = this.helperService.currentDate(startDateTime);
    let scheduledMinutes = 0;

    while (currentDate.isBefore(endDateTime) || currentDate.isSame(endDateTime, 'day')) {
      const dayNumber = currentDate.day() === 0 ? 7 : currentDate.day();
      const scheduleForDay = userScheduleDays.find((day: any) => {
        return Number(day.day) === dayNumber;
      });

      if (scheduleForDay) {
        const startTime = this.helperService.extractTimeOnly(scheduleForDay.startTime);
        const endTime = this.helperService.extractTimeOnly(scheduleForDay.endTime);
        if (startTime && endTime) {
          const startDateTime = dayjs.utc(currentDate).set('hour', startTime.hour).set('minute', startTime.minute);
          const endDateTime = dayjs.utc(currentDate).set('hour', endTime.hour).set('minute', endTime.minute);
          const minutes = endDateTime.diff(startDateTime, 'minute');
          scheduledMinutes += minutes - 60;
        }
      }
      currentDate = currentDate.add(1, 'day');
    }

    const userBreak = {
      startHour: 8,
      startMinute: 0,
      endHour: 9,
      endMinute: 0,
    };

    const daysAnalysis = periodDates.map((date: dayjs.Dayjs) => {
      const dayNumber = date.day() === 0 ? 7 : date.day();
      const formattedDate = this.helperService.formatDate(date.toDate());
      const report = employee.AttendanceReport.find((report: any) => {
        const reportDate = dayjs.utc(report.date);
        return this.helperService.formatDate(reportDate.toDate()) === formattedDate;
      });
      const scheduleForDay = employee.UserWorkingSchedule?.days?.find((day: any) => {
        return Number(day.day) === dayNumber;
      });

      if (dayNumber === 7) {
        return this.createDayReport(dayNumber, date, formattedDate, 'Dam olish kuni', false, false);
      }

      if (!scheduleForDay && report) {
        // Ish jadvali yo'q, lekin kelgan
        return this.createDayReport(dayNumber, date, formattedDate, 'Xodim ishga keldi', true, true, {
          actualEnterTime: report.enter,
          actualExitTime: report.exit,
          minutesWorked: report.minutes || 0,
          lateMinutes: report.lateEnter || 0,
          earlyExitMinutes: report.earlyExit || 0,
          scheduledEnter: report.scheduledEnter,
          scheduledExit: report.scheduledExit,
          scheduledMinutes: report.scheduledMinutes,
          underTimeMinutes: report.underTimeMinutes,
          isLate: report.isLate,
          isEarlyExit: report.isEarlyExit,
          earlyExitValue: report.earlyExit,
          lateEnterValue: report.lateEnter,
        });
      }

      if (!scheduleForDay) {
        // Ish jadvali yo'q va kelmagan
        return this.createDayReport(dayNumber, date, formattedDate, 'Dam olish kuni', false, false);
      }

      if (!report) {
        // Ish kuni, lekin kelmagan
        return this.createDayReport(dayNumber, date, formattedDate, 'Ishga kelmadi', true, false, {
          scheduledStartTime: scheduleForDay.startTime,
          scheduledEndTime: scheduleForDay.endTime,
          expectedWorkday: true,
        });
      }

      const cameToWork = !!(report.enter || report.exit || (report.minutes && report.minutes > 0));

      return {
        day: report.dayNumber || dayNumber,
        date: report.date,
        formattedDate: formattedDate,
        status: cameToWork ? 'Ishga keldi' : 'Ishga kelmadi',
        isWorkDay: true,
        cameToWork: cameToWork,
        scheduledStartTime: report.scheduledEnter,
        scheduledEndTime: report.scheduledExit,
        actualEnterTime: report.enter,
        actualExitTime: report.exit,
        minutesWorked: report.minutes || 0,
        lateMinutes: report.lateEnter || 0,
        earlyExitMinutes: report.earlyExit || 0,
        scheduledEnter: report.scheduledEnter,
        scheduledExit: report.scheduledExit,
        scheduledMinutes: report.scheduledMinutes,
        underTimeMinutes: report.underTimeMinutes,
        isLate: report.isLate,
        isEarlyExit: report.isEarlyExit,
        earlyExitValue: report.earlyExit,
        lateEnterValue: report.lateEnter,
      };
    });

    // Summary faqat AttendanceReport maydonlari asosida
    const totalMinutesWorked = daysAnalysis.reduce((sum: number, day: any) => sum + (day.minutesWorked || 0), 0);
    const totalWorkMinutesPlanned = daysAnalysis
      .filter((day: any) => day.isWorkDay)
      .reduce((sum: number, day: any) => sum + (day.scheduledMinutes || 0), 0);

    const summary = this.createSummary(daysAnalysis, scheduledMinutes, totalWorkMinutesPlanned, totalMinutesWorked);
    const workSchedule = this.createWorkSchedule(employee);
    const breakSchedule = this.createBreakSchedule(userBreak);

    return {
      employee: {
        id: employee.id,
        fullName: employee.fullName,
        position: employee.position,
      },
      workSchedule,
      breakSchedule,
      summary,
      dailyReports: daysAnalysis,
    };
  }
}
