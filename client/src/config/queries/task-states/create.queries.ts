import { axiosPrivate } from '@/config/api'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { taskStatesEndpoints } from '@/config/api/endpoints'
import { notification } from 'antd'

export interface TaskStates {
	id?: string
	name: string
	key: string
	order: number
}

export const useCreateTaskStates = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async (data: TaskStates) =>
			(await axiosPrivate.post<ApiResponse<TaskStates>>(taskStatesEndpoints.all, data)).data,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [taskStatesEndpoints.all] })
			notification.success({
				message: 'Mu<PERSON>ffaqiyatli yaratildi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: '<PERSON><PERSON><PERSON><PERSON> xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
