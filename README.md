# MNazorat-NodeJS Loyihasi

Bu hujjat loyiha bilan ishlash uchun zarur bo'lgan barcha ma'lumotlarni o'z ichiga oladi.

## Mundarija
- [<PERSON><PERSON><PERSON> haqida](#loyiha-haqida)
- [<PERSON><PERSON><PERSON><PERSON>](#talablar)
- [<PERSON><PERSON><PERSON> struktura<PERSON>](#loyiha-strukturasi)
- [<PERSON><PERSON>](#muhit-sozlamalari)
- [Docker bilan ishlash](#docker-bilan-ishlash)
- [<PERSON><PERSON><PERSON> ishga tushirish](#loyihani-ishga-tushirish)
- [Ma'lumotlar bazasi](#malumotlar-bazasi)
- [Git bilan ishlash](#git-bilan-ishlash)
- [Kod standartlari](#kod-standartlari)
- [Test qilish](#test-qilish)
- [Deploy qilish](#deploy-qilish)
- [Foydalanilgan texnologiyalar](#foydalanilgan-texnologiyalar)
- [Savollar va Muammolar](#savollar-va-muammolar)

## <PERSON><PERSON><PERSON> haqida

MNazorat - bu monitoring va nazorat qilish tizimi bo'lib, tashkilotlarning ish jarayonlarini nazorat qilish va kuzatishga mo'ljallangan. Bu tizim orqali boshqaruv va hisobotlarni samarali tashkil etish imkoniyati mavjud.

## Talablar

Loyiha bilan ishlash uchun quyidagi dasturiy ta'minotlar o'rnatilgan bo'lishi kerak:

- Node.js (v14 yoki yuqori)
- Docker va Docker Compose
- Git
- PostgreSQL (lokal ishlatmoqchi bo'lsangiz)
- Redis (lokal ishlatmoqchi bo'lsangiz)
- Yarn (tavsiya etiladi)

## Loyiha strukturasi

```
mnazorat-node/
├── backend/           # Backend kod (NestJS)
│   ├── prisma/        # Database sxemalari va migratsiyalar
│   ├── src/           # Asosiy kod
│   │   ├── common/    # Umumiy komponentlar
│   │   ├── modules/   # Modul-asoslangan kod tuzilmasi
│   │   └── helpers/   # Yordamchi funksiyalar
│   ├── json/          # Ma'lumotlar va konfiguratsiya JSON fayllari
│   ├── seed/          # Ma'lumotlar bazasi uchun boshlang'ich ma'lumotlar
│   └── static/        # Statik fayllar
├── client/            # Frontend kod (React)
│   ├── src/           # React asosiy kod
│   │   ├── components/ # React komponentlari
│   │   ├── pages/     # Sahifalar
│   │   ├── routes/    # Marshrut yo'naltirish
│   │   └── shared/    # Umumiy komponentlar
│   └── public/        # Statik fayllar
├── nginx/             # NGINX konfiguratsiyasi
├── docker-compose.dev.yml        # Rivojlantirish muhiti uchun Docker
├── docker-compose.yml            # Ishlab chiqarish muhiti uchun Docker
└── postgres-init.sql             # PostgreSQL boshlang'ich sozlamalari
```

## Muhit sozlamalari

### Backend muhiti

Backend uchun muhit sozlamalari `.env` faylida konfiguratsiya qilinadi (fayl yaratish kerak):

```
# PostgreSQL ulash ma'lumotlari
DATABASE_URL=postgresql://username:password@localhost:5432/mnazorat_db

# JWT sozlamalari
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=1d

# Redis sozlamalari
REDIS_HOST=localhost
REDIS_PORT=6379

# Boshqa sozlamalar
PORT=3000
NODE_ENV=development
```

### Frontend muhiti

Frontend uchun muhit sozlamalari `.env` faylida konfiguratsiya qilinadi (fayl yaratish kerak):

```
VITE_API_URL=http://localhost:3000/api
VITE_SOCKET_URL=http://localhost:3000
```

## Docker bilan ishlash

### Rivojlantirish muhitini ishga tushirish

```bash
docker-compose -f docker-compose.dev.yml up
```

### Ishlab chiqarish muhitini ishga tushirish

```bash
docker-compose up -d
```

### Docker konteynerlar

- **backend**: NestJS API serveri
- **client**: React frontend
- **postgres**: PostgreSQL ma'lumotlar bazasi
- **redis**: Redis kesh serveri
- **nginx**: NGINX proksi-server

## Loyihani ishga tushirish

### Docker bilan

```bash
# Rivojlantirish muhiti
docker-compose -f docker-compose.dev.yml up

# Ishlab chiqarish muhiti
docker-compose up -d
```

### Lokal muhitda (backend)

```bash
cd backend
yarn install
yarn start:dev
```

### Lokal muhitda (frontend)

```bash
cd client
yarn install
yarn dev
```

## Ma'lumotlar bazasi

### Migratsiyalarni bajarish

```bash
cd backend
npx prisma migrate dev
```

### Ma'lumotlar bazasini boshlang'ich ma'lumotlar bilan to'ldirish

```bash
cd backend
yarn seed:all
```

Alohida seed skriptlarni bajarish uchun:

```bash
yarn seed:user   # Foydalanuvchilar
yarn seed:grade  # Baholash ma'lumotlari
```

## Git bilan ishlash

Loyihada Conventional Commits standartidan foydalaniladi:

```bash
git add .
yarn commit  # Commitizen orqali standartlashtirilgan commit
git push
```

### Branch strategiyasi

- `main` - asosiy, ishlab chiqarish muhitiga deploy qilinadigan branch
- `develop` - rivojlantirish uchun asosiy branch
- `feature/*` - yangi funksiyalar uchun branchlar
- `fix/*` - xatolar tuzatish uchun branchlar

## Kod standartlari

- ESLint orqali kod sifatini nazorat qilish
- Prettier orqali kod formatini standartlashtirish
- Husky va lint-staged orqali commit qilishda avtomatik tekshiruv

## Test qilish

### Backend testlari

```bash
cd backend
yarn test          # Barcha testlarni bajarish
yarn test:watch    # Testlarni kuzatish rejimida bajarish
yarn test:cov      # Test qamrovini tekshirish
```

### Frontend testlari

```bash
cd client
yarn test          # Barcha testlarni bajarish
yarn test:e2e      # End-to-end testlarni bajarish
```

## Deploy qilish

### Lokal serverga deploy qilish

```bash
./deploy-local-server.sh
```

### Production serverga deploy qilish

CI/CD tizimi orqali avtomatik deploy qilinadi (GitLab/GitHub Actions).

## Foydalanilgan texnologiyalar

### Backend
- NestJS - asosiy framework
- Prisma - ORM va ma'lumotlar bazasi migratsiyalari
- PostgreSQL - asosiy ma'lumotlar bazasi
- Redis - kesh va queue management
- BullMQ - background job processing
- JWT - autentifikatsiya

### Frontend
- React - asosiy UI kutubxonasi
- Vite - build tool
- TypeScript - type checking
- Cypress - end-to-end testlash

### DevOps
- Docker / Docker Compose - konteynerizatsiya
- NGINX - proksi-server va statik fayllarni uzatish
- Husky / Commitlint - git hook'lar va commit standartlari

## Savollar va Muammolar

Loyiha bilan bog'liq savollar bo'lsa, iltimos GitHub'da issue ochib murojaat qiling.
