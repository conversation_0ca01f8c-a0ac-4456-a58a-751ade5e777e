import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { ROLE, useGetUsersByOrg } from '@/config/queries/organizations/get-users.queries'
import { usePermissions } from '@/shared/guards/usePermissions'
import { LoadingScreen } from '@/shared/ui/suspense'
import { DeleteOutlined } from '@ant-design/icons'
import { Button, message, Popconfirm, Select, Table } from 'antd'
import { useState } from 'react'
import { useUserManagementStore } from '../utils/user-managament-store'
import {
	TRole,
	useRemoveUserFromOrganization
} from '@/config/queries/organizations/remove-user.queries'
import { useNavigate } from 'react-router-dom'

export const UserManagementTable = () => {
	const { hasPermission } = usePermissions()
	const { id: orgId, onClose } = useUserManagementStore()
	const { data: user, isLoading } = useAuthMe()
	const [role, setRole] = useState<ROLE>(ROLE.worker)
	const storedOrgId = localStorage.getItem('organizationId')
	const [api, contextHolder] = message.useMessage()
	const { mutateAsync: removeUser, isPending: isDeleting } = useRemoveUserFromOrganization()
	const storedOrgIdValue = storedOrgId ? JSON.parse(storedOrgId) : ''
	const effectiveOrgId = orgId || storedOrgIdValue || ''
	const {
		data: users,
		isLoading: isLoadingUsers,
		fetchNextPage,
		fetchPreviousPage
	} = useGetUsersByOrg(effectiveOrgId, role)

	const navigate = useNavigate()

	const handleDelete = (id: string) => {
		if (orgId && role) {
			removeUser({
				data: {
					[{
						[ROLE.worker]: 'workers',
						[ROLE.employee]: 'employees',
						[ROLE.responsible]: 'responsibles'
					}[role]]: [id]
				},
				orgId,
				who: {
					[ROLE.worker]: 'workers',
					[ROLE.employee]: 'employees',
					[ROLE.responsible]: 'responsibles'
				}[role] as TRole
			})
		}
	}

	if (isLoading || !user?.role) return <LoadingScreen />

	return (
		<>
			{contextHolder}
			<div>
				<Select
					className='w-40'
					defaultValue={ROLE.worker}
					value={role}
					onSelect={value => setRole(value)}
					options={[
						{ label: 'Ishchi', value: ROLE.worker },
						{ label: 'Xodim', value: ROLE.employee },
						{ label: "Ma'sul", value: ROLE.responsible }
					]}
				/>
			</div>
			<Table
				loading={isLoadingUsers}
				dataSource={users?.pages.flatMap(page => page.data)}
				pagination={{
					pageSize: 10,
					showSizeChanger: false,
					total: users?.pages[0]?.meta?.total,
					onChange: page => {
						if (page > 1) {
							fetchNextPage()
						} else {
							fetchPreviousPage()
						}
					}
				}}
				rowClassName='cursor-pointer'
				columns={[
					{ key: '#', title: '#', width: 40, render: (_, __, index) => ++index },
					{
						key: 'id',
						width: 40,
						title: 'id',
						render: rec => (
							<p className='text-ellipsis overflow-hidden whitespace-nowrap w-10 cursor-pointer text-blue-500'>
								{rec.id}
							</p>
						),
						onCell: data => {
							return {
								onClick: () => {
									if (navigator.clipboard) {
										navigator.clipboard.writeText(data.id).then(
											() => {
												api.info({
													content: 'ID clipboardga nusxalandi',
													duration: 1.5,
													key: 'copyId'
												})
											},
											() => {
												api.error({
													content: 'ID nusxalashda xatolik',
													duration: 1.5,
													key: 'copyId'
												})
											}
										)
									}
								}
							}
						}
					},

					{
						key: 'fullName',
						title: 'FIO',
						dataIndex: 'fullName',
						className: 'hover:text-blue-500',
						onCell: data => {
							return {
								onClick: () => {
									onClose()
									navigate(`/workspace/settings/employee/${data.id}`)
								}
							}
						}
					},
					{ key: 'position', title: 'Lavozim', dataIndex: ['position', 'name'] },
					hasPermission(user.role, ['delete:*', 'update:*'])
						? {
								key: 'actions',
								title: 'Amallar',
								className: 'w-[300px]',
								render: rec => (
									<div className='w-full flex items-center gap-x-3 justify-center'>
										<Popconfirm
											title='Haqiqatdan ham face id qurilmasini ochirib tashlamoqchimisiz?'
											okText='ha'
											cancelText="yo'q"
											onConfirm={() => handleDelete(rec.id)}
											okButtonProps={{ disabled: isDeleting }}>
											<Button
												danger
												loading={isDeleting}
												icon={<DeleteOutlined />}
											/>
										</Popconfirm>
									</div>
								)
							}
						: { key: 'actions' }
				]}
			/>
		</>
	)
}
