import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import { join } from 'path';

const prisma = new PrismaClient();

interface DistrictData {
  id: number;
  title: string;
  region_id: number;
}

async function main() {
  const districts: DistrictData[] = JSON.parse(
    fs.readFileSync(join(process.cwd(), 'json', 'district.json'), {
      encoding: 'utf-8',
    }),
  );

  for (const district of districts) {
    await prisma.district.upsert({
      where: { id: district.id.toString() }, // id должно быть строкой
      update: {}, // Здесь можно добавить логику обновления, если нужно
      create: {
        id: district.id.toString(), // Преобразуем id в строку
        name: district.title,
        regionId: district.region_id.toString(), // Преобразуем regionId в строку
      },
    });
    console.log(`District ${district.title} qo'shildi.`);
  }

  console.log("Barcha Tumanlar muvaffaqiyatli qo'shildi");
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
