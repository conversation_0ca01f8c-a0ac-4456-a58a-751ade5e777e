import { axiosPrivate } from '@/config/api'
import { userWorkingScheduleEndpoints } from '@/config/api/endpoints'
import { useMutation } from '@tanstack/react-query'
import { notification } from 'antd'

export type TAddWorkingScheduleToOrganization = {
	orgId: string
	days: string[]
}

export const useAddWorkingScheduleToOrganization = () => {
	return useMutation({
		mutationFn: async (data: TAddWorkingScheduleToOrganization) => {
			return await axiosPrivate.patch(userWorkingScheduleEndpoints.addToOrganization, data)
		},
		onSuccess: async () => {
			notification.success({
				message: 'Muvaffaqiyatli yangilandi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: 'Yangilashda xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
