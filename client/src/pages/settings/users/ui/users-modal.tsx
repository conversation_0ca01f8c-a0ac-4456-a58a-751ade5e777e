import { PlusOutlined } from '@ant-design/icons'
import { Button, Form, Modal } from 'antd'
import { useEffect } from 'react'
import OrganizationForm from './users-form'
import { getUserFormValidation } from '../config/validations'
import { useCreateUser } from '@/config/queries/users/create.queries'
import { useUpdateUser } from '@/config/queries/users/update.queries'
import { TUser } from '@/config/queries/users/get-all.queries'
import { useUsersModalStore } from '../utils/users-modal-store'
import { getFormChanges } from '@/shared/utils/getFormChanges'
import { isNoChanges } from '@/shared/utils/isNoChanges'
import { useFileUpload } from '@/shared/components/file-upload/file-upload'
import { format } from '@react-input/mask'

interface UsersModalProps {
	hideAddButton?: boolean
}

export const UsersModal = ({ hideAddButton = false }: UsersModalProps) => {
	const [form] = Form.useForm()
	const { mutateAsync: createUser, isPending: isCreating } = useCreateUser()
	const { mutateAsync: updateUser, isPending: isUpdating } = useUpdateUser()
	const { open, onClose, data } = useUsersModalStore()
	const userFormValidation = getUserFormValidation(!!data?.id)
	const { fileIds, clearFiles, render: Uploader } = useFileUpload()

	const close = () => {
		form.resetFields()
		onClose()
		clearFiles()
	}

	useEffect(() => {
		if (data) {
			const userData = { ...data } as { [key: string]: any }
			delete userData.password
			form.setFieldsValue(userData)
			if (data.phone) {
				form.setFieldValue(
					'phone',
					format(data.phone.startsWith('+998') ? data.phone.slice(4) : data.phone, {
						mask: '+998 (__) ___-__-__',
						replacement: { _: /\d/ }
					})
				)
			}
			if (data.username) {
				form.setFieldValue(
					'username',
					format(data.username, {
						mask: '+998 (__) ___-__-__',
						replacement: { _: /\d/ }
					})
				)
			}
		}
	}, [data, form])

	const onFinish = async (formData: TUser) => {
		try {
			const processedFormData = {
				...formData,
				phone: formData.phone?.replace('+998', '').replace(/[^\d]/g, ''),
				username: formData.username?.replace('+998', '').replace(/[^\d]/g, '')
			}

			if (!data) {
				await createUser({ ...processedFormData, avatarId: fileIds[0] })
			} else {
				if (data.id) {
					const updatedFields = getFormChanges(data, {
						...processedFormData,
						avatarId: fileIds[0] ?? data.avatarId
					})
					if (isNoChanges(updatedFields)) return close()

					await updateUser({
						...updatedFields,
						id: data.id
					})
				}
			}
			close()
		} catch (error) {
			console.error('Error saving user:', error)
		}
	}

	return (
		<div className='flex items-center justify-end'>
			{!hideAddButton && (
				<Button
					type='primary'
					icon={<PlusOutlined />}
					onClick={() => useUsersModalStore.getState().onOpen()}>
					Yangi Foydalanuvchi
				</Button>
			)}

			<Modal
				open={open}
				width={800}
				onCancel={close}
				destroyOnClose
				onOk={form.submit}
				title={data ? 'Foydalanuvchini tahrirlash' : "Yangi foydalanuvchi qo'shish"}
				okText={data ? 'Saqlash' : "Qo'shish"}
				cancelText='Bekor qilish'
				confirmLoading={isCreating || isUpdating}
				modalRender={node => (
					<Form
						layout='vertical'
						size='large'
						onFinish={onFinish}
						form={form}>
						{node}
					</Form>
				)}>
				<OrganizationForm
					validation={userFormValidation}
					Uploader={Uploader}
				/>
			</Modal>
		</div>
	)
}
