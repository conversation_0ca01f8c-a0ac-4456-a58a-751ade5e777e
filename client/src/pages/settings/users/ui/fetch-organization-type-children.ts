import { axiosPrivate } from '@/config/api'
import { organizationPositionTypeEndpoints } from '@/config/api/endpoints'
import { OrganizationPositionTypes } from '@/config/queries/organization-position-types/create.queries'
import { TPaginationWrapper } from '@/shared/types/pagination.type'

export async function fetchOrganizationTypeChildren(
	id: string
): Promise<TPaginationWrapper<OrganizationPositionTypes[]>> {
	const { data } = await axiosPrivate.get<TPaginationWrapper<OrganizationPositionTypes[]>>(
		organizationPositionTypeEndpoints.byTypeId(id),
		{ params: { limit: 50, page: 1 } }
	)
	return data || []
}
