import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { LoadingScreen } from '../ui/suspense'
import { ROLE } from '../types/role.type'
import { Navigate, Outlet } from 'react-router-dom'

export const RequireToBeAdmin = () => {
	const { data: user, isLoading } = useAuthMe()

	if (isLoading) return <LoadingScreen />

	if (user?.role !== ROLE.ADMIN) return <Navigate to='/personal' />

	return <Outlet />
}
