import { useEffect, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { useGetReferenceOrganizationPosition } from '@/config/queries/common/common-queries'
import { Select } from 'antd'

export default function AttendancePositionSelectTree() {
	const [searchParams, setSearchParams] = useSearchParams()
	const [selectedPositions, setSelectedPositions] = useState<string[]>([])

	const organizationPositions = useGetReferenceOrganizationPosition()

	useEffect(() => {
		const positionId = searchParams.get('positionId')
		if (positionId && positionId.trim() !== '') {
			const positions = positionId.split(',').filter(id => id.trim() !== '')
			setSelectedPositions(positions)
		} else {
			setSelectedPositions([])
		}
	}, [searchParams])

	const handleChange = (value: string | string[]) => {
		const valueArray = Array.isArray(value) ? value : value ? [value] : []
		setSelectedPositions(valueArray)

		setSearchParams(prev => {
			const newParams = { ...Object.fromEntries(prev) }
			if (!valueArray || valueArray.length === 0) {
				delete newParams.positionId
			} else {
				newParams.positionId = valueArray[0]
			}
			newParams.grade = '30'
			return newParams
		})
	}

	return (
		<div>
			<Select
				allowClear
				placeholder='Lavozimlarni tanlang'
				value={selectedPositions}
				onChange={handleChange}
				loading={organizationPositions.isLoading}
				className='w-full'
				showSearch
				filterOption={(input, option) =>
					(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
				}
				maxTagCount='responsive'
				// Change to single select for now since API handles single position
				mode={undefined}
				options={
					organizationPositions.data?.map(position => ({
						label: position.name,
						value: position.id
					})) || []
				}
			/>
		</div>
	)
}
