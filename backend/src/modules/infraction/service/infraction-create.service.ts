import { Injectable, NotFoundException } from '@nestjs/common';
import { InfractionService } from '../infraction.service';
import { PrismaService } from '../../../common';
import { CreateInfractionDto } from '../dto/create-infraction.dto';

@Injectable()
export class InfractionCreateService {
  constructor(
    private readonly infractionService: InfractionService,
    private readonly prismaService: PrismaService,
  ) {}

  async autoCreateInfractionToUser(createInfractionDto: CreateInfractionDto) {
    const { userId, ...infractionData } = createInfractionDto;

    const user = await this.prismaService.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} does not exist`);
    }

    return this.infractionService.create({
      ...infractionData,
      userId,
    });
  }

  async manualCreateInfractionToUser(creatorUserId: string, createInfractionDto: CreateInfractionDto) {
    const { userId, ...infractionData } = createInfractionDto;

    const creatorUser = await this.prismaService.user.findUnique({
      where: { id: creatorUserId },
    });

    if (!creatorUser) {
      throw new NotFoundException(`Creator user with ID ${creatorUserId} does not exist`);
    }

    const targetUser = await this.prismaService.user.findUnique({
      where: { id: userId },
    });

    if (!targetUser) {
      throw new NotFoundException(`Target user with ID ${userId} does not exist`);
    }

    const targetUserOrganizations = await this.prismaService.organization.findMany({
      where: {
        OR: [
          {
            Employee: {
              some: {
                id: userId,
              },
            },
          },
          {
            Worker: {
              some: {
                id: userId,
              },
            },
          },
        ],
      },
    });
    if (targetUserOrganizations.length === 0) {
      throw new NotFoundException(`Target user with ID ${userId} does not belong to any organization`);
    }

    const creatorUserOrganizations = await this.prismaService.organization.findMany({
      where: {
        AND: [
          {
            Responsible: {
              some: {
                id: creatorUserId,
              },
            },
          },
          {
            OR: [
              {
                Children: {
                  some: {
                    id: {
                      in: targetUserOrganizations.map((org) => org.id),
                    },
                  },
                },
              },
              {
                UnderControl: {
                  some: {
                    id: {
                      in: targetUserOrganizations.map((org) => org.id),
                    },
                  },
                },
              },
            ],
          },
        ],
      },
    });

    if (creatorUserOrganizations.length === 0) {
      throw new NotFoundException(`Creator user with ID ${creatorUserId} does not belong to any organization`);
    }
    return this.infractionService.create({
      ...infractionData,
      userId,
    });
  }
}
