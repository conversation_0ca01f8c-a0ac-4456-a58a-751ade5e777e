import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from '@nestjs/common';
import { UserWorkingScheduleService } from './user-working-schedule.service';
import { CreateUserWorkingScheduleDto, UpdateUserWorkingScheduleDto } from './dto/user-working-schedule.dto';
import { PaginationParams, ProtectedRoute } from '../../common';
import { Status } from '@prisma/client';
import { ApiResponse } from 'src/helpers/apiRespons';
import { BaseQueryParams } from 'src/helpers/types';
import { OrganizationUpdateDto } from './dto/organization-working.dto';

@ProtectedRoute({
  isPublic: false,
})
@Controller('user-working-schedule')
export class UserWorkingScheduleController {
  constructor(private readonly userWorkingScheduleService: UserWorkingScheduleService) {}

  @Post()
  async create(@Body() createUserWorkingScheduleDto: CreateUserWorkingScheduleDto) {
    return await this.userWorkingScheduleService.create(createUserWorkingScheduleDto);
  }

  @Get()
  async findAll(
    @Query() paginationParams: PaginationParams,
    @Query('userId') userId?: string,
    @Query('search') search?: string,
  ) {
    return await this.userWorkingScheduleService.findAll(paginationParams, userId, search);
  }

  @Get(':id')
  async findOne(@Param('id') id: string) {
    return await this.userWorkingScheduleService.findOne(id);
  }

  @Patch('organization')
  @ProtectedRoute({ context: 'WORKSPACE' })
  async updateByOrganization(@Body() query: OrganizationUpdateDto) {
    return this.userWorkingScheduleService.updateByOrganization(query);
  }

  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateUserWorkingScheduleDto: UpdateUserWorkingScheduleDto) {
    return await this.userWorkingScheduleService.update(id, updateUserWorkingScheduleDto);
  }

  @Patch(':id/status')
  async changeStatus(@Param('id') id: string, @Body('status') status: Status) {
    return await this.userWorkingScheduleService.changeStatus(id, status);
  }

  @Delete(':id')
  async remove(@Param('id') id: string) {
    return await this.userWorkingScheduleService.remove(id);
  }

  @Post(':userId/disconnect-schedule')
  @ProtectedRoute({ context: 'WORKSPACE' })
  async disconnectSchedule(@Param('userId') userId: string, @Body() { schedules }: { schedules: string[] }) {
    await this.userWorkingScheduleService.disconnectSchedule(userId, schedules);
    return new ApiResponse('Schedule disconnected');
  }

  @Get('user/:userId')
  @ProtectedRoute({ context: 'WORKSPACE' })
  async findByUser(@Param('userId') userId: string, @Query() query: BaseQueryParams) {
    return this.userWorkingScheduleService.findByUser(userId, query);
  }
}
