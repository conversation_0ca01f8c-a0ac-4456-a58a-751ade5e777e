model Task {
  id          String @id @default(cuid())
  name        String
  description String

  CreatedBy   User   @relation(fields: [createdById], references: [id])
  createdById String

  CreatedByOrganization   Organization @relation(fields: [createdByOrganizationId], references: [id])
  createdByOrganizationId String

  TaskState   TaskState @relation(fields: [taskStateId], references: [id])
  taskStateId String

  TaskType   TaskType? @relation(fields: [taskTypeId], references: [id])
  taskTypeId String?

  files  File[] @relation(name: "files")
  status Status @default(ACTIVE)

  dueDate DateTime?

  isCompleted    Boolean   @default(false)
  completedAt    DateTime?
  completeDesc   String?
  CompleteFile   File?     @relation(name: "CompleteFile", fields: [completeFileId], references: [id])
  completeFileId String?

  Recipients Recipient[]

  SubTasks Task[]  @relation("ParentTask")
  Parent   Task?   @relation("ParentTask", fields: [parentId], references: [id])
  parentId String?

  controller User? @relation("ContollerUser", fields: [userId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  userId    String?

  @@index([createdById])
  @@index([createdByOrganizationId])
  @@index([taskStateId])
  @@index([taskTypeId])
  @@index([parentId])
  @@index([dueDate])
}

model TaskState {
  id    String @id @default(cuid())
  name  String @unique
  key   String @unique
  order Int

  status Status @default(ACTIVE)

  Task Task[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([key])
}

model TaskType {
  id     String @id @default(cuid())
  name   String
  status Status @default(ACTIVE)

  Task Task[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([name])
}
