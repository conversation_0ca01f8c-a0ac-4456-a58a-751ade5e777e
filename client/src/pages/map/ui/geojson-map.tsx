import { useGetCoordinates } from '@/config/queries/map/map.queries'
import { useGeoJsonData } from '../hooks/useGeoJsonData'
import { useAreaTypes } from '../hooks/useAreaTypes'
import { useLayerVisibility } from '../hooks/useLayerVisibility'
import { useMapState } from '../hooks/useMapState'
import { useMapInteraction } from '../hooks/useMapInteraction'
import { InteractiveGeoJSON } from '../components/InteractiveGeoJSON'
import { LoadingState } from '../components/LoadingState'
import { ErrorState } from '../components/ErrorState'
import { NavigationPanel } from '../components/NavigationPanel'
import { HoverTooltip } from '../components/HoverTooltip'
import 'leaflet/dist/leaflet.css'
import LocationMarkers from './location-markers'
import { useMapHover } from '../hooks/useMapHover'

export default function GeojsonMap() {
	// Получение данных с API с учетом параметров URL
	const { data, isLoading, error } = useGetCoordinates()

	// Преобразование данных в GeoJSON формат
	const geoJsonData = useGeoJsonData(data)

	// Получение уникальных типов областей
	const areaTypes = useAreaTypes(geoJsonData)

	// Управление состоянием карты
	const { handleFeatureClick } = useMapState() // Управление навигацией по карте и уровнями масштабирования
	const {
		regionId,
		districtId,
		navigateBack,
		resetNavigation,
		getCurrentLevel,
		getCurrentLevelTitle,
		canNavigateBack,
		handleAreaClick,
		isAreaClickable
	} = useMapInteraction()
	const { hoverInfo, handleMouseEnter, handleMouseLeave, handleMouseMove } = useMapHover(
		regionId,
		districtId
	)

	// Управление видимостью слоев
	const { filteredGeoJsonData } = useLayerVisibility(areaTypes, geoJsonData)

	if (isLoading) {
		return <LoadingState />
	}

	if (error) {
		return <ErrorState />
	}

	const currentLevel = getCurrentLevel()
	const shouldFitBounds = currentLevel !== 'overview'

	return (
		<div className='h-full w-full relative'>
			{hoverInfo && <HoverTooltip hoverInfo={hoverInfo} />}
			<NavigationPanel
				currentLevel={currentLevel}
				levelTitle={getCurrentLevelTitle()}
				canNavigateBack={canNavigateBack()}
				onNavigateBack={navigateBack}
				onReset={resetNavigation}
			/>
			{geoJsonData && geoJsonData.features.length > 0 && (
				<InteractiveGeoJSON
					geoJsonData={filteredGeoJsonData || geoJsonData}
					onFeatureClick={handleFeatureClick}
					onAreaClick={handleAreaClick}
					isAreaClickable={isAreaClickable}
					fitBounds={shouldFitBounds}
					onMouseEnter={handleMouseEnter}
					onMouseLeave={handleMouseLeave}
					onMouseMove={handleMouseMove}
				/>
			)}
			<LocationMarkers />
		</div>
	)
}
