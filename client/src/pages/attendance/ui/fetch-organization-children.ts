import { axiosPrivate } from '@/config/api'
import { organizationEndpoints } from '@/config/api/endpoints'
import { TOrganization } from '@/config/queries/organizations/get-all.queries'

export async function fetchOrganizationChildren(id: string): Promise<TOrganization[]> {
	const { data } = await axiosPrivate.get<{ Children: TOrganization[] }>(
		organizationEndpoints.one(id)
	)
	return data.Children || []
}
