import { axiosPrivate } from '@/config/api'
import { vacancyEndpoints } from '@/config/api/endpoints'
import { TPaginationWrapper } from '@/shared/types/pagination.type'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'
import { TVacancy } from './create.vacancy'

export const useGetAllVacancies = () => {
	const [searchParams] = useSearchParams()
	const page = searchParams.get('page')
	const limit = searchParams.get('limit')
	const search = searchParams.get('search')

	return useQuery({
		queryKey: [vacancyEndpoints.all, page, limit, search],
		queryFn: async () => {
			return (
				await axiosPrivate.get<TPaginationWrapper<TVacancy[]>>(vacancyEndpoints.all, {
					params: {
						page,
						limit,
						search
					}
				})
			).data
		}
	})
}
