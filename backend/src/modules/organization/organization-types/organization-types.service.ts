import { Injectable, NotFoundException } from '@nestjs/common';
import { PaginationUtil, PrismaService, QueryUtil } from 'src/common';
import { CreateOrganizationTypeDto } from './dto/create-organization-type.dto';
import { UpdateOrganizationTypeDto } from './dto/update-organization-type.dto';
import { BaseQueryParams } from 'src/helpers/types';

@Injectable()
export class OrganizationTypesService {
  constructor(
    private prismaService: PrismaService,
    private pagination: PaginationUtil,
    private queryUtil: QueryUtil,
  ) {}

  async getAll(query: BaseQueryParams) {
    const model = this.prismaService.organizationType;

    const paginate = {
      limit: query.limit ? parseInt(query.limit) : 10,
      page: query.page ? parseInt(query.page) : 1,
    };

    const where: any = {
      ...this.queryUtil.createSearchQuery(query.search, ['name', 'id', 'description']),
      status: 'ACTIVE',
    };

    return this.pagination.paginate(model, paginate, where);
  }

  async getById(id: string) {
    const type = await this.prismaService.organizationType.findUnique({
      where: { id },
    });

    if (!type) {
      throw new NotFoundException('Organization type not found');
    }

    return type;
  }

  async getByName(name: string) {
    const type = await this.prismaService.organizationType.findUnique({
      where: { name },
    });

    if (!type) {
      throw new NotFoundException('Organization type not found');
    }

    return type;
  }

  async create(data: CreateOrganizationTypeDto) {
    return this.prismaService.organizationType.create({
      data,
    });
  }

  async update(id: string, data: UpdateOrganizationTypeDto) {
    return this.prismaService.organizationType.update({
      where: { id },
      data,
    });
  }
}
