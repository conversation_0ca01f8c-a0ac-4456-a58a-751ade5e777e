import { axiosPrivate } from '@/config/api'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { notification } from 'antd'
import { UserWorkingScheduleTypes } from './create.queries'
import { userWorkingScheduleDayEndpoints } from '@/config/api/endpoints'

export const useUpdateUserWorkingScheduleDay = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async ({ id, ...data }: { id: string }) =>
			(
				await axiosPrivate.patch<ApiResponse<UserWorkingScheduleTypes>>(
					userWorkingScheduleDayEndpoints.one(id),
					data
				)
			).data,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [userWorkingScheduleDayEndpoints.all] })
			notification.success({
				message: '<PERSON><PERSON><PERSON>aq<PERSON><PERSON>i yangilandi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: '<PERSON><PERSON>sh<PERSON> xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
