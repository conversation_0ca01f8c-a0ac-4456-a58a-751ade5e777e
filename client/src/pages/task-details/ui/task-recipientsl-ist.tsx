import { Ava<PERSON>, Badge, Card } from 'antd'
import { BankOutlined, UserOutlined, MessageOutlined } from '@ant-design/icons'
import { clsx } from 'clsx'

interface RecipientsListProps {
	filteredRecipients: any[]
	readStatusFilter: 'all' | 'read' | 'unread'
	isOwnTask: boolean
	navigate: (path: string) => void
	currentRecipient: any
	task: any
}

export const RecipientsList = ({
	filteredRecipients,
	readStatusFilter,
	isOwnTask,
	navigate,
	currentRecipient,
	task
}: RecipientsListProps) => {
	const getPendingAnswersCount = (recipient: any) => {
		if (!recipient.Answer || !Array.isArray(recipient.Answer)) return 0
		return recipient.Answer.filter((answer: any) => answer.state === 'PENDING').length
	}

	return (
		<ul className='w-full grid grid-cols-1 lg:grid-cols-2 gap-4'>
			{!filteredRecipients.length && (
				<p className='text-gray-500'>
					{readStatusFilter === 'all'
						? 'Bajaruvchilar mavjud emas'
						: readStatusFilter === 'read'
							? "O'qigan bajaruvchilar mavjud emas"
							: "O'qimagan bajaruvchilar mavjud emas"}
				</p>
			)}
			{filteredRecipients.map(recipient => {
				const pendingAnswersCount = getPendingAnswersCount(recipient)

				return recipient.User?.id ? (
					<Badge.Ribbon
						key={recipient.id}
						text={`${recipient.isRead || recipient.id === currentRecipient?.id ? 'Jarayonda' : "Ko'rilmagan"}`}
						style={{
							backgroundColor:
								recipient.isRead || recipient.id === currentRecipient?.id ? 'green' : 'red'
						}}>
						<Card
							onClick={() => isOwnTask && navigate(`recipients/${recipient.id}`)}
							className={clsx(
								'w-full shadow-lg rounded-2xl p-4',
								isOwnTask ? 'cursor-pointer' : 'cursor-default'
							)}>
							<div className='w-full flex items-center gap-x-5'>
								<Avatar
									size={64}
									icon={<UserOutlined />}
									src={recipient.User.name}
									className='mr-4 border border-gray-300'
								/>
								<div className='flex-1'>
									<h2 className='text-lg font-semibold'>{recipient.User.fullName}</h2>
									{task.CreatedByOrganization?.phone && (
										<p className='truncate'>Telefon raqami: {recipient.User?.phone}</p>
									)}
									<div className='flex items-center gap-2 mt-2'>
										<MessageOutlined className='text-blue-500' />
										<span className='text-lg'>
											Yangi javoblar soni{' '}
											<span className='font-semibold text-orange-600'>{pendingAnswersCount}</span>
										</span>
									</div>
								</div>
							</div>
						</Card>
					</Badge.Ribbon>
				) : (
					<Badge.Ribbon
						key={recipient.id}
						text={`${recipient.isRead || recipient.id === currentRecipient?.id ? 'Jarayonda' : "Ko'rilmagan"}`}
						style={{
							backgroundColor:
								recipient.isRead || recipient.id === currentRecipient?.id ? 'green' : 'red'
						}}>
						<Card
							className={clsx(
								'w-full shadow-lg rounded-2xl p-4',
								isOwnTask ? 'cursor-pointer' : 'cursor-default'
							)}
							onClick={() => isOwnTask && navigate(`recipients/${recipient.id}`)}>
							<div className='w-full flex items-center gap-x-5'>
								<Avatar
									size={64}
									icon={<BankOutlined />}
									src={recipient.Organization.name}
									className='mr-4 border border-gray-300'
								/>

								<div className='flex-1 min-w-0'>
									<h2 className='text-lg font-semibold truncate'>{recipient.Organization.name}</h2>
									{recipient.Organization?.grade?.name && (
										<p className='text-gray-500 truncate'>
											Lavozimi: {recipient.Organization.grade.name}
										</p>
									)}
									{task.CreatedByOrganization?.phone && (
										<p className='truncate'>Telefon raqami: {recipient.Organization?.phone}</p>
									)}
									{/* Pending Answers Count */}
									<div className='flex items-center gap-2 '>
										<MessageOutlined className='text-blue-500' />
										<span className='text-lg '>
											Yangi javoblar soni :{' '}
											<span className='font-semibold text-orange-600'>{pendingAnswersCount}</span>{' '}
											ta
										</span>
									</div>
								</div>
							</div>
						</Card>
					</Badge.Ribbon>
				)
			})}
		</ul>
	)
}
