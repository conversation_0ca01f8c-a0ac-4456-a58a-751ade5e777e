import { Form, Modal } from 'antd'
import { useFaceIDAddStore } from '../utils/face-id-add-modal-store'
import { FaceIdAddForm } from './face-id-add.form'
import { getfaceIdFormValidation } from '../config/validations'
import { TFaceIdRequest, useCreateFaceIdDevice } from '@/config/queries/face-id/create'

export const FaceIdAddModal = () => {
	const [form] = Form.useForm()
	const { open, id: orgId, onClose } = useFaceIDAddStore()
	const faceIdDeviceValidation = getfaceIdFormValidation()
	const { mutateAsync: createFaceIdDevice, isPending: isCreating } = useCreateFaceIdDevice()

	const close = () => {
		form.resetFields()
		onClose()
	}

	const onFinish = (data: TFaceIdRequest) => {
		if (orgId) createFaceIdDevice({ ...data, orgId }).then(close)
	}

	return (
		<Modal
			open={open}
			onClose={close}
			onCancel={close}
			okText='Yuborish'
			onOk={() => form.submit()}
			okButtonProps={{ disabled: isCreating }}
			cancelButtonProps={{ disabled: isCreating }}>
			<Form
				form={form}
				layout='vertical'
				onFinish={onFinish}>
				<FaceIdAddForm validation={faceIdDeviceValidation} />
			</Form>
		</Modal>
	)
}
