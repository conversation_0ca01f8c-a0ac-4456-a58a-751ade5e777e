import { EditOutlined } from '@ant-design/icons'
import { Button, Space, Table } from 'antd'
import { useState } from 'react'
import { useTaskStatesModalStore } from './utils/task-states-store'
import { useGetTaskStates } from '@/config/queries/task-states/get-queries'
import TaskStatesModal from './ui/task-states-modal'

import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { Search } from '@/shared/components/search/search'

export const TaskStatestable = () => {
	const { onOpen } = useTaskStatesModalStore()

	const [search] = useState('')
	const [currentPage, setCurrentPage] = useState(1)
	const [pageSize, setPageSize] = useState(10)
	const taskTypes = useGetTaskStates()

	const filteredData =
		taskTypes.data?.pages
			?.flatMap(page => page.data)
			.filter(
				item =>
					item.name.toLowerCase().includes(search.toLowerCase()) ||
					item.key.toLowerCase().includes(search.toLowerCase())
			) || []

	return (
		<>
			<Table
				pagination={{
					total: filteredData.length,
					pageSize: pageSize,
					current: currentPage,
					showSizeChanger: true,
					pageSizeOptions: [5, 10, 20, 50, 100],
					onChange: (page, size) => {
						setCurrentPage(page)
						setPageSize(size)
						if (
							Math.ceil((page * size) / size) > (taskTypes.data?.pages?.length || 0) &&
							taskTypes.hasNextPage
						) {
							taskTypes.fetchNextPage()
						}
					}
				}}
				title={() => (
					<div className='flex items-center justify-between'>
						<Search />
						<TaskStatesModal />
					</div>
				)}
				size='small'
				scroll={{ x: 'max-content' }}
				dataSource={filteredData}
				rowKey={(rec: { id?: string }) => rec.id || ''}
				columns={[
					{
						key: '#',
						title: '#',
						render: (_, _rec, index) => formatTableIndex(currentPage, pageSize, index)
					},
					{
						key: 'name',
						title: 'Nomi',
						dataIndex: 'name'
					},
					{
						key: 'key',
						title: 'Kaliti',
						dataIndex: 'key'
					},
					{
						key: 'order',
						title: 'Tartib raqami',
						dataIndex: 'order'
					},
					{
						key: 'actions',
						title: 'Amallar',
						render: record => (
							<Space>
								<Button
									type='primary'
									icon={<EditOutlined />}
									onClick={() => onOpen(record)}
								/>
							</Space>
						)
					}
				]}
			/>
		</>
	)
}
