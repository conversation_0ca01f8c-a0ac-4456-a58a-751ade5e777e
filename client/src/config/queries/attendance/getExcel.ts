import { axiosPrivate } from '@/config/api'
import { attendanceUserEndpoints } from '@/config/api/endpoints'
import { downloadBlob } from '@/shared/utils/downloadBlob'
import { useMutation } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'

export const useGetAttendanceExcel = () => {
	const [searchParams] = useSearchParams()

	const from = searchParams.get('from')
	const to = searchParams.get('to')
	const organizationId = searchParams.get('organizationId')

	return useMutation({
		mutationFn: async () => {
			return await axiosPrivate.get(attendanceUserEndpoints.attendanceExcel, {
				params: { startDate: from, endDate: to, organizationId },
				responseType: 'blob'
			})
		}
	})
}

export const useGetAttendanceReportExcel = () => {
	const [searchParams] = useSearchParams()

	const from = searchParams.get('startDate')
	const to = searchParams.get('endDate')
	const organizationId = searchParams.get('organizationId')

	return useMutation({
		mutationFn: async () => {
			return await axiosPrivate.get(attendanceUserEndpoints.attendanceReportExcel, {
				params: { startDate: from, endDate: to, organizationId },
				responseType: 'blob'
			})
		},
		onSuccess: data => {
			console.log(data)
			downloadBlob(data.data, data.headers['content-disposition'] || 'report.xlsx')
		}
	})
}
