FROM node:20-alpine as builder

WORKDIR /app

# Устанавливаем зависимости для сборки
RUN apk add --no-cache python3 make g++

# Копируем файлы зависимостей
COPY package*.json yarn.lock* ./

RUN mkdir -p static
RUN mkdir -p static/temp
RUN mkdir -p static/saved
RUN mkdir -p uploads
# Устанавливаем зависимости
RUN yarn config set registry https://registry.yarnpkg.com/
RUN yarn cache clean
RUN yarn config set network-timeout 300000
RUN yarn install --frozen-lockfile

# Копируем исходный код
COPY . .

RUN mkdir -p /app/uploads
RUN mkdir -p /app/static
RUN mkdir -p /app/static/temp
RUN mkdir -p /app/static/temp/saved

# Генерируем клиент Prisma и собираем проект
RUN NODE_OPTIONS="--stack-size=8192"
RUN yarn prisma generate
RUN yarn build

# Убедимся, что файл main.js существует
RUN ls -la dist/ && \
    [ -f dist/main.js ] || echo "ОШИБКА: файл dist/main.js не создан при сборке!"

FROM node:20-alpine

WORKDIR /app

# Копируем файлы зависимостей
COPY package*.json yarn.lock* ./

# Устанавливаем только production-зависимости
RUN yarn config set registry https://registry.yarnpkg.com/
RUN yarn cache clean
RUN yarn config set network-timeout 300000
RUN yarn install --frozen-lockfile

# Копируем собранные файлы и настройки Prisma
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder /app/.env ./.env
COPY --from=builder /app/seed ./seed
COPY --from=builder /app/json ./json
COPY --from=builder /app/static ./static
COPY --from=builder /app/uploads ./uploads
COPY --from=builder /app/tsconfig.dev.json ./tsconfig.dev.json
# Убедимся, что файл main.js существует после копирования
RUN ls -la dist/ && \
    [ -f dist/main.js ] || echo "ОШИБКА: файл dist/main.js не скопирован!"

# Запускаем приложение
CMD ["node", "dist/main.js"]