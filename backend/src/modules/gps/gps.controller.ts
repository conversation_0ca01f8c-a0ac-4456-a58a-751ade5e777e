import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  Logger,
  Param,
  Post,
  Query,
  Req,
  Res,
  UnauthorizedException,
} from '@nestjs/common';
import { GpsService } from './gps.service';
import { ProtectedRoute } from 'src/common';
import { InjectQueue } from '@nestjs/bullmq';
import { GPS } from './interfaces/gps';
import { Queue } from 'bullmq';
import { Request, Response } from 'express';
import { AuthDto } from './dto/auth.dto';
import { ChangePassword } from './dto/change-password';
import { Context } from 'src/common/decorators/context.decorator';

@Controller('gps')
export class GpsController {
  logger = new Logger(GpsController.name);

  constructor(
    private readonly gpsService: GpsService,
    @InjectQueue(GPS) private gpsQueue: Queue,
  ) {}

  @ProtectedRoute({ isPublic: true })
  @Post('auth')
  @HttpCode(200)
  async create(@Body() body: AuthDto) {
    return await this.gpsService.auth(body);
  }

  @ProtectedRoute({ isPublic: true })
  @Get('user/me')
  async me(@Req() req) {
    const header = req.headers.authorization;
    const [bearer, token] = header.split(' ');
    if (bearer !== 'Bearer') {
      throw new UnauthorizedException('Неверный токен');
    }

    return await this.gpsService.me(token);
  }

  @Get('user/coords')
  @ProtectedRoute({ isPublic: true })
  async getUserCoords(@Req() req) {
    const header = req.headers.authorization;
    const [bearer, token] = header.split(' ');
    if (bearer !== 'Bearer') {
      throw new UnauthorizedException('Неверный токен');
    }

    return this.gpsService.getCoords(token);
  }

  @ProtectedRoute({ isPublic: true })
  @Post('/user/setlocation')
  async setLocation(@Req() req: Request, @Res() res: Response) {
    await this.gpsQueue.add('gps.setLocation', {
      authorization: req.headers.authorization,
      body: req.body,
    });

    res.json({ success: true }).status(200);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE' })
  @Get('/user/getHistory')
  async getHistory(
    @Context('organizationId') organizationId: string,
    @Query('regionId') regionId?: string,
    @Query('districtId') districtId?: string,
    @Query('sectionId') sectionId?: string,
    @Query('isInArea') isInArea?: string,
    @Query('positionId') positionId?: string,
    @Query('isDisabled') isDisabled?: string,
  ) {
    const start = performance.now();

    if (!regionId || !districtId) {
      throw new BadRequestException('Регион и район обязательны для запроса истории');
    }

    let isInAreaBoolean: boolean | undefined;
    let isDisabledBoolean: boolean | undefined;
    if (isDisabled !== undefined) {
      if (isDisabled === 'true') {
        isDisabledBoolean = true;
      } else if (isDisabled === 'false') {
        isDisabledBoolean = false;
      } else {
        isDisabledBoolean = undefined;
      }
    }
    if (isInArea !== undefined) {
      if (isInArea === 'true') {
        isInAreaBoolean = true;
      } else if (isInArea === 'false') {
        isInAreaBoolean = false;
      } else {
        isInAreaBoolean = undefined;
      }
    }

    const result = await this.gpsService.getHistory({
      organizationId,
      regionId,
      districtId,
      sectionId,
      isInArea: isInAreaBoolean,
      positionId,
      isDisabled: isDisabledBoolean,
    });

    const end = performance.now();

    this.logger.debug(
      `GPS history fetched in ${end - start}ms for organizationId: ${organizationId}, regionId: ${regionId}, districtId: ${districtId}, sectionId: ${sectionId}, isInArea: ${isInAreaBoolean}, positionId: ${positionId}, isDisabled: ${isDisabledBoolean}`,
    );

    return result;
  }

  @ProtectedRoute({ isPublic: true })
  @Get('user/daily/:userId')
  async getUserDailyHistory(
    @Param('userId') userId: string,
    @Query('day') day?: string,
    @Query('from') from?: string,
    @Query('to') to?: string,
  ) {
    return await this.gpsService.getUserDailyHistory(userId, day, from, to);
  }

  @Post('user/change-password')
  @ProtectedRoute({ isPublic: true })
  async changePassword(@Req() req: Request, @Body() body: ChangePassword, @Res() res: Response) {
    const header = req.headers.authorization;
    const [bearer, token] = header.split(' ');
    if (bearer !== 'Bearer') {
      throw new UnauthorizedException('Неверный токен');
    }
    const { accessToken, refreshToken } = await this.gpsService.changePassword(token, body);
    res
      .cookie('refreshToken', refreshToken, {
        httpOnly: true,
        secure: true,
      })
      .send({ token: accessToken });
  }

  @Get('status/user/:userId')
  @ProtectedRoute({ isPublic: false })
  async getUserGpsStatus(@Param('userId') userId: string) {
    return this.gpsService.getUserGpsStatus(userId);
  }

  @Get('status/organization/:organizationId')
  @ProtectedRoute({ isPublic: false })
  async getOrganizationGpsStatus(@Param('organizationId') organizationId: string) {
    return this.gpsService.getAllUsersGpsStatus(organizationId);
  }

  @Get('status/all')
  @ProtectedRoute({ isPublic: false })
  async getAllGpsStatus() {
    return this.gpsService.getAllUsersGpsStatus();
  }

  @Get('all-workers-with-last-gps/:orgId')
  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  async getAllWorkersWithLastGps(@Param('orgId') orgId: string) {
    return this.gpsService.getAllUserWithLastLocation(orgId);
  }
}
