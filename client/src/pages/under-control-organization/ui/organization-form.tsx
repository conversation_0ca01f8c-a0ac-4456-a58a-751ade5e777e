import { useGetLowGradeOrganizations } from '@/config/queries/organizations/get-low-grads.queries'
import { fetchOrganizationChildren } from '@/pages/attendance/ui/fetch-organization-children'
import { LoadingScreen } from '@/shared/ui/suspense'
import { Form, TreeSelect } from 'antd'
import { debounce } from 'lodash'
import { useCallback, useEffect, useState } from 'react'

const { Item } = Form

interface TreeNode {
	title: string
	value: string
	key: string
	isLeaf?: boolean
	pId?: string
}

interface Organization {
	id: string
	name: string
	status?: string
	description?: string
	Children?: Organization[]
}

type TOrganizationForm = {
	orgIds: string[]
	setOrgIds: (ids: string[]) => void
	orgId: string
}

export default function OrganizationForm({ orgId, orgIds, setOrgIds }: TOrganizationForm) {
	const [searchValue, setSearchValue] = useState('')
	const [isSearching, setIsSearching] = useState(false)
	const { data: organizations, isLoading } = useGetLowGradeOrganizations(searchValue, orgId)
	const [treeData, setTreeData] = useState<TreeNode[]>([])
	const [loadingKeys, setLoadingKeys] = useState<string[]>([])

	const debouncedSearch = useCallback(
		debounce((value: string) => {
			setSearchValue(value)
			setIsSearching(false)
		}, 100),
		[setSearchValue, setIsSearching]
	)

	const handleSearch = (value: string) => {
		if (value.trim()) {
			setIsSearching(true)
			debouncedSearch(value)
		} else {
			setSearchValue('')
			setIsSearching(false)
		}
	}

	useEffect(() => {
		if (organizations && organizations.length > 0) {
			console.log('low grade organizations', organizations)
			const newData = organizations.map(item => ({
				title: item.name,
				value: item.id,
				key: item.id,
				isLeaf: false,
				pId: undefined
			}))

			if (searchValue.trim()) {
				setTreeData(newData)
			} else {
				setTreeData(prev => {
					const keys = new Set(prev.map(node => node.key))
					const merged = [...prev, ...newData.filter(node => !keys.has(node.key))]
					return merged
				})
			}
		}
	}, [organizations, searchValue])

	const updateTreeWithChildren = useCallback((nodeId: string, children: Organization[]) => {
		setTreeData(prev => {
			const hasExistingChildren = prev.some(node => node.pId === nodeId)
			if (hasExistingChildren) {
				return prev.map(node =>
					node.key === nodeId ? { ...node, isLeaf: children.length === 0 } : node
				)
			}

			const childNodes: TreeNode[] = children.map(child => ({
				title: child.name,
				value: child.id,
				key: child.id,
				isLeaf: false,
				pId: nodeId
			}))

			return [
				...prev.map(node =>
					node.key === nodeId ? { ...node, isLeaf: children.length === 0 } : node
				),
				...childNodes
			]
		})
	}, [])

	const loadData = useCallback(
		async (treeNode: any) => {
			const { key: id } = treeNode
			const hasChildren = treeData.some(node => node.pId === id)
			if (hasChildren) return
			if (loadingKeys.includes(id)) return
			setLoadingKeys(keys => [...keys, id])
			try {
				const children = await fetchOrganizationChildren(id)
				if (children && children.length > 0) {
					updateTreeWithChildren(id, children)
				} else {
					setTreeData(prev =>
						prev.map(node => (node.key === id ? { ...node, isLeaf: true } : node))
					)
				}
			} catch (e) {
				console.error('Error loading organization children:', e)
				setTreeData(prev => prev.map(node => (node.key === id ? { ...node, isLeaf: true } : node)))
			} finally {
				setLoadingKeys(keys => keys.filter(k => k !== id))
			}
		},
		[treeData, loadingKeys, updateTreeWithChildren]
	)

	const handleChange = useCallback(
		(value: string) => {
			// Handle both string and array cases
			const selectedValue = Array.isArray(value) ? value[0] : value
			setOrgIds(selectedValue ? [selectedValue] : [])
		},
		[setOrgIds]
	)

	// Handle dropdown visibility to prevent auto-closing
	const handleDropdownVisibleChange = useCallback(
		(open: boolean) => {
			// Prevent dropdown from closing when searching
			if (!open && isSearching) {
				return false
			}
		},
		[isSearching]
	)

	if (isLoading && !searchValue) return <LoadingScreen />

	return (
		<div>
			<Item
				name='organizations'
				label='Tashkilotlar'
				rules={[{ required: true, message: 'Tashkilotni tanlang' }]}>
				<TreeSelect
					treeDataSimpleMode={{
						id: 'value',
						pId: 'pId'
					}}
					allowClear
					loadData={loadData}
					treeCheckable
					multiple={false}
					value={orgIds[0] || undefined}
					onChange={handleChange}
					onDropdownVisibleChange={handleDropdownVisibleChange}
					showCheckedStrategy={TreeSelect.SHOW_ALL}
					treeData={treeData}
					className='w-full'
					placeholder='Tashkilotlarni tanlang'
					showSearch
					onSearch={handleSearch}
					loading={isSearching || isLoading}
					filterTreeNode={false}
					searchValue={searchValue}
					dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
				/>
			</Item>
		</div>
	)
}
