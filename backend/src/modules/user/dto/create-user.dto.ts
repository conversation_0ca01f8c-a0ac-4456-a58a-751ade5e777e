import { IsOptional, IsString } from 'class-validator';

export class CreateUserDto {
  @IsString()
  fullName: string;

  @IsString()
  username: string;

  @IsString()
  password: string;

  @IsString()
  @IsOptional()
  phone?: string;

  @IsString()
  positionId: string;

  @IsString()
  @IsOptional()
  avatarId?: string;

  @IsString()
  @IsOptional()
  faceIdImageId?: string;

  @IsString()
  @IsOptional()
  mainOrganizationId?: string;

  @IsString()
  @IsOptional()
  imei?: string;

  @IsString()
  @IsOptional()
  telegramId?: string;
}
