import { PlusOutlined } from '@ant-design/icons'
import { Button, Form, Modal } from 'antd'
import { useOrganizationsModalStore } from '../utils/organizations-modal-store'
import { getFormChanges } from '@/shared/utils/getFormChanges'
import {
	TOrganizationRequest,
	useCreateOrganization
} from '@/config/queries/organizations/create.queries'
import { useUpdateOrganization } from '@/config/queries/organizations/update.queries'
import { isNoChanges } from '@/shared/utils/isNoChanges'
import OrganizationForm from './organization-form'
import { getOrganizationFormValidation } from '../config/validations'
import { usePermissions } from '@/shared/guards/usePermissions'
import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { LoadingScreen } from '@/shared/ui/suspense'
import { useEffect } from 'react'

export const OrganizationModal = () => {
	const [form] = Form.useForm()
	const { mutateAsync: createOrganization, isPending: isCreating } = useCreateOrganization()
	const { mutateAsync: updateOrganization, isPending: isUpdating } = useUpdateOrganization()
	const { open, onOpen, onClose, data } = useOrganizationsModalStore()
	const organizationFormValidation = getOrganizationFormValidation(!!data?.id)
	const { data: user, isLoading } = useAuthMe()
	const { hasPermission } = usePermissions()

	useEffect(() => {
		if (data) {
			form.setFieldsValue(data)
		}
	}, [data, form])

	const close = () => {
		form.resetFields()
		onClose()
	}

	const onFinish = (dataValues: TOrganizationRequest) => {
		if (data?.id) {
			const updatedFields = getFormChanges(data, dataValues)
			if (isNoChanges(updatedFields)) return close()
			updateOrganization({
				id: data.id,
				data: updatedFields
			}).then(close)
		} else {
			createOrganization(dataValues).then(close)
		}
	}

	if (isLoading || !user?.role) return <LoadingScreen />

	return (
		hasPermission(user.role, ['create:*']) && (
			<div className='flex items-center justify-end'>
				<Button
					type='primary'
					icon={<PlusOutlined />}
					onClick={() => onOpen()}>
					Yangi Tashkilot
				</Button>

				<Modal
					title={data ? 'Tashkilotni tahrirlash' : "Yangi Tashkilot qo'shish"}
					open={open}
					onCancel={close}
					onClose={close}
					destroyOnClose
					width={800}
					okText={data ? 'Saqlash' : "Qo'shish"}
					cancelText='Bekor qilish'
					confirmLoading={isCreating || isUpdating}
					onOk={() => form.submit()}>
					{open && (
						<Form
							form={form}
							layout='vertical'
							onFinish={onFinish}>
							<OrganizationForm validation={organizationFormValidation} />
						</Form>
					)}
				</Modal>
			</div>
		)
	)
}
