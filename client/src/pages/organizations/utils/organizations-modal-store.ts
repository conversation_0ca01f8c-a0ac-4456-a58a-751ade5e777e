import { TOrganization } from '@/config/queries/organizations/get-all.queries'
import { create } from 'zustand'

interface OrganizationsModalStore {
	open: boolean
	data: TOrganization | null
	onOpen: (data?: TOrganization) => void
	onClose: () => void
}

export const useOrganizationsModalStore = create<OrganizationsModalStore>(set => ({
	open: false,
	data: null,
	onOpen: data => set({ open: true, data: data || null }),
	onClose: () => set({ open: false, data: null })
}))
