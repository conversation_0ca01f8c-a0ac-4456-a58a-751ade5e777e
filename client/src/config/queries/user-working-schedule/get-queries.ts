import { axiosPrivate } from '@/config/api'
import { useQuery } from '@tanstack/react-query'
// import { ApiResponse } from '../types'
import { userWorkingScheduleEndpoints } from '@/config/api/endpoints'
import { UserWorkingScheduleTypes } from './create.queries'
import { ApiResponse } from '../types'
import { useParams } from 'react-router-dom'

export const useGetUserWorkingSchedule = (userId?: string) => {
	const { id } = useParams()

	return useQuery({
		queryKey: [userWorkingScheduleEndpoints.byUserId(userId ?? id!), userId, id],
		queryFn: async () => {
			const { data } = await axiosPrivate.get<ApiResponse<UserWorkingScheduleTypes[]>>(
				userWorkingScheduleEndpoints.byUserId(userId ?? id!)
			)
			return data
		},
		enabled: !!userId || !!id
	})
}
