import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { TaskService } from './task.service';
import { TaskController } from './task.controller';
import { PrismaService } from '../../common';
import { FileService } from '../file/file.service';
import { TaskExcelService } from './task-excel.service';
import { TaskHelperService } from './services/task-helper.service';
import { TaskCreatingService } from './services/task-creating.service';
import { InspectorService } from './inspector.service';

@Module({
  controllers: [TaskController],
  providers: [TaskService, FileService, TaskExcelService, TaskHelperService, TaskCreatingService, InspectorService],
})
export class TaskModule {}
