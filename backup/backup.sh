#!/bin/bash

TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="/backups/backup_${TIMESTAMP}.sql"

# Создание резервной копии
echo "Создание резервной копии базы данных..."
PGPASSWORD=${DB_PASSWORD} pg_dump -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d ${DB_NAME} -f ${BACKUP_FILE}

# Загрузка в S3
echo "Загрузка в S3..."
mc cp ${BACKUP_FILE} s3/${S3_BUCKET}/${S3_FOLDER}/backup_${TIMESTAMP}.sql

echo "Резервное копирование завершено"