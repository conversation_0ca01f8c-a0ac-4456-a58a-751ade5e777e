import { Injectable, NotFoundException, OnModuleInit } from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { PaginationUtil, PrismaService } from 'src/common';
import { hash } from 'bcrypt';
import { FileService } from '../file/file.service';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';

@Injectable()
export class UserService implements OnModuleInit {
  constructor(
    private prisma: PrismaService,
    private pagination: PaginationUtil,
    private FileService: FileService,
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
  ) {}

  async onModuleInit() {
    await this.formatPhoneNumber();
    await this.findDuplicatePhones();
    await this.changeUserNameToPhone();
  }

  private async findDuplicatePhones() {
    const users = await this.prisma.user.findMany({
      where: { status: 'ACTIVE' },
      select: { id: true, phone: true, fullName: true, username: true },
    });

    const phoneMap = new Map<string, { id: string; fullName: string; username: string }[]>();

    users.forEach((user) => {
      if (!user.phone) return; // Пропускаем пользователей без номера телефона

      if (phoneMap.has(user.phone)) {
        phoneMap.get(user.phone).push({ id: user.id, fullName: user.fullName, username: user.username });
      } else {
        phoneMap.set(user.phone, [{ id: user.id, fullName: user.fullName, username: user.username }]);
      }
    });

    const duplicates = Array.from(phoneMap.entries())
      .filter(([_, users]) => users.length > 1)
      .map(([phone, users]) => ({ phone, users }));

    // Для каждого дубликата оставляем первого пользователя, остальных деактивируем
    const results = await Promise.all(
      duplicates.map(async (dup) => {
        const keepUser = dup.users[0];
        const usersToDeactivate = dup.users.slice(1);

        const deactivatePromises = usersToDeactivate.map((user) =>
          this.prisma.user.update({
            where: { id: user.id },
            data: { status: 'INACTIVE' },
          }),
        );

        await Promise.all(deactivatePromises);
        return dup.phone;
      }),
    );

    return results;
  }

  private async formatPhoneNumber() {
    const users = await this.prisma.user.findMany({
      where: { status: 'ACTIVE' },
      select: { id: true, phone: true },
    });

    let updatedCount = 0;
    const updates = [];

    for (const user of users) {
      // Пропускаем если нет телефона
      if (!user.phone) continue;

      // Форматируем номер телефона - удаляем все нецифровые символы
      let formattedPhone = user.phone.replace(/\D/g, '');

      // Если длина номера больше 9 цифр, берем последние 9
      if (formattedPhone.length > 9) {
        formattedPhone = formattedPhone.slice(-9);
      } else if (formattedPhone.length < 9) {
        // Если меньше 9 цифр, пропускаем (номер неполный)
        continue;
      }

      // Обновляем только если номер изменился
      if (formattedPhone !== user.phone) {
        updates.push(
          this.prisma.user.update({
            where: { id: user.id },
            data: { phone: formattedPhone },
          }),
        );
        updatedCount++;
      }
    }

    if (updates.length > 0) {
      await Promise.all(updates);
    }
  }

  private async changeUserNameToPhone() {
    const users = await this.prisma.user.findMany({
      where: { status: 'ACTIVE' },
      select: { id: true, phone: true, username: true },
    });
    const updates = [];
    for (const user of users) {
      if (user.phone && user.phone.length === 9 && user.username !== user.phone) {
        // Проверяем, что username (phone) уникален
        const existing = await this.prisma.user.findFirst({
          where: {
            username: user.phone,
            id: { not: user.id },
          },
        });
        if (!existing) {
          updates.push(
            this.prisma.user.update({
              where: { id: user.id },
              data: { username: user.phone },
            }),
          );
        }
      }
    }
    if (updates.length > 0) {
      await Promise.all(updates);
    }
  }

  async create(createUserDto: CreateUserDto) {
    const position = await this.prisma.organizationTypePosition.findUnique({ where: { id: createUserDto.positionId } });

    if (!position) throw new NotFoundException('Position not found');

    if (createUserDto?.avatarId) {
      const avatar = await this.prisma.file.findUnique({ where: { id: createUserDto.avatarId } });

      if (!avatar) throw new NotFoundException('Avatar not found');

      this.FileService.moveFileFromTempToSaved([avatar.id]);
    }

    if (createUserDto?.faceIdImageId) {
      const faceIdImage = await this.prisma.file.findUnique({ where: { id: createUserDto.faceIdImageId } });

      if (!faceIdImage) throw new NotFoundException('FaceIdImage not found');
    }

    if (createUserDto?.mainOrganizationId) {
      const mainOrganization = await this.prisma.organization.findUnique({
        where: { id: createUserDto.mainOrganizationId },
      });

      if (!mainOrganization) throw new NotFoundException('MainOrganization not found');
    }

    await this.prisma.user.create({ data: { ...createUserDto, password: await hash(createUserDto.password, 3) } });
    return 'created user';
  }

  async findAll(page: string, limit: string, orgId?: string, search?: string, phone?: string) {
    const $page = Number(page) || 1;
    const $limit = Number(limit) || 10;

    if (phone) {
      if (phone.length !== 9) {
        throw new NotFoundException('Phone number must be exactly 9 digits');
      }

      if (!/^\d+$/.test(phone)) {
        throw new NotFoundException('Phone number must contain only digits');
      }
    }

    const whereFilter: any = {
      status: 'ACTIVE',
    };

    if (orgId) {
      whereFilter.Organization = { some: { id: orgId } };
    }

    if (phone) {
      whereFilter.username = phone;
    } else if (search) {
      whereFilter.OR = [
        { phone: { mode: 'insensitive', contains: search } },
        { username: { mode: 'insensitive', contains: search } },
        { fullName: { mode: 'insensitive', contains: search } },
        { id: { mode: 'insensitive', contains: search } },
      ];
    }

    const users = await this.pagination.paginate(this.prisma.user, { page: $page, limit: $limit }, whereFilter, {
      MainOrganization: true,
      position: true,
      avatar: true,
      ResponsibleFor: {
        select: {
          id: true,
          name: true,
        },
      },
    });

    if (phone && (!users?.data || users.data.length === 0)) {
      throw new NotFoundException('Bunday telefon raqamga ega foydalanuvchi topilmadi');
    }

    return users;
  }

  async findOne(id: string) {
    const user = await this.prisma.user.findFirst({
      where: {
        id,
        status: 'ACTIVE',
      },
      include: {
        MainOrganization: true,
        position: true,
        avatar: true,
        ResponsibleFor: true,
        Attendance: true,
        Organization: true,
        GPSLocationReport: {
          orderBy: {
            createdAt: 'desc',
          },
        },
      },
    });

    if (!user) throw new NotFoundException('User not found');

    const vacation = await this.prisma.vacation.findFirst({
      where: {
        user: { id: user.id },
        status: 'ACTIVE',
      },
    });

    const completedTaskCount = await this.prisma.task.count({
      where: {
        status: 'ACTIVE',
        Recipients: { some: { User: { id: user.id }, isCompleted: true } },
      },
    });

    const inCompletedTaskCount = await this.prisma.task.count({
      where: {
        status: 'ACTIVE',
        Recipients: { some: { User: { id: user.id }, isCompleted: false } },
      },
    });

    return {
      ...user,
      isinVocation: vacation?.state || false,
      type: vacation?.state ? vacation.type : null,
      gpsStatus: user.GPSLocationReport[0]?.type,
      GPSLocationReport: undefined,
      completedTaskCount,
      inCompletedTaskCount,
    };
  }

  async update(id: string, updateUserDto: UpdateUserDto) {
    const user = await this.prisma.user.findUnique({ where: { id } });

    if (!user) throw new NotFoundException('User not found');

    const password = updateUserDto?.password ? await hash(updateUserDto.password, 3) : user.password;

    await this.prisma.user.update({ where: { id }, data: { ...updateUserDto, password } });

    return 'updated user';
  }

  async remove(id: string) {
    const user = await this.prisma.user.findUnique({ where: { id } });

    if (!user) throw new NotFoundException('User not found');

    const microDate = Date.now();
    const newUsername = `${user.username}_${microDate}`;
    const deleted = await this.prisma.user.update({
      where: { id },
      data: { status: 'INACTIVE', username: newUsername },
    });

    return deleted;
  }

  async addDateUsername() {
    const users = await this.prisma.user.findMany({
      where: { status: 'INACTIVE' },
      select: { id: true, username: true },
    });

    const updates = users.map((user) => {
      const microDate = Date.now();
      const newUsername = `${user.username}_${microDate}`;
      return this.prisma.user.update({
        where: { id: user.id },
        data: { username: newUsername },
      });
    });

    const updated = await Promise.all(updates);
    return updated;
  }
}
