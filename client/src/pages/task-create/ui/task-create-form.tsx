import { Form, Card } from 'antd'
import { useGetTaskTypes } from '@/config/queries/task-types/get-all.queries'
import { useState, useEffect } from 'react'
import {
	TOrganization,
	useGetAllChildrenOrganizations
} from '@/config/queries/organizations/get-all.queries'
import { useGetAllUnderControlOrganizations } from '@/config/queries/under-control-organizations/get-queries'
import { useGetOrganizationWorkers } from '@/config/queries/organizations/get-workers.queries'
import { TUser } from '@/config/queries/users/get-all.queries'
import { TRecipent } from '../task-create'
import { TTaskRequest, useCreateTask } from '@/config/queries/task/create'
import { useGetOneOrganization } from '@/config/queries/organizations/getOne'
import { removeDuplicates } from '@/shared/utils/removeDuplicatedData'
import { useFileUpload } from '@/shared/components/file-upload/file-upload'
import { RecipientRow } from './task-reciepent-form'
import { RecipientTypeSelector } from './task-reciepent-select'
import { TaskTypeAndDate } from './task-type-form'
import { TaskNameForm, TaskDescriptionForm } from './task-detail-form'
import { SubmitButton } from './task-submit'
import { AddRecipientButton } from './task-add-reciepent'
import { TaskControllerSelect } from './task-controller-select'

type TUsersState = {
	[key: string]: TUser[]
}

type TTaskCreateForm = {
	recipents: TRecipent[]
	setRecipents: React.Dispatch<React.SetStateAction<TRecipent[]>>
}

export default function TaskCreateForm({ recipents, setRecipents }: TTaskCreateForm) {
	const taskType = useGetTaskTypes()
	const orgId = localStorage.getItem('organizationId')
	const parsedOrgId = JSON.parse(orgId!)

	const { data: selfOrganization } = useGetOneOrganization(parsedOrgId)

	const childrenQuery = useGetAllChildrenOrganizations()
	const underControlsQuery = useGetAllUnderControlOrganizations()

	const [users, setUsers] = useState<TUsersState>({})
	const [selectedOrgForWorkers, setSelectedOrgForWorkers] = useState<string>('')
	const workersQuery = useGetOrganizationWorkers(selectedOrgForWorkers)
	const [recipientType, setRecipientType] = useState('organizations')

	const { mutateAsync: createTask } = useCreateTask()
	const [form] = Form.useForm()
	const { fileIds, render: FileUpload, clearFiles, isUploading } = useFileUpload()

	const isSelfOrganization = selfOrganization?.id === parsedOrgId

	useEffect(() => {
		if (workersQuery.data && selectedOrgForWorkers) {
			const allWorkers = workersQuery.data.pages.flatMap(page => page.data)
			setUsers(prev => ({
				...prev,
				[selectedOrgForWorkers]: removeDuplicates(allWorkers)
			}))
		}
	}, [workersQuery.data, selectedOrgForWorkers])

	const childrenOrgs = childrenQuery.data ?? []
	const underControlOrgs = underControlsQuery.data ?? []

	// Remove duplicates while preserving order (children first)
	const uniqueOrgs = [...childrenOrgs]
	underControlOrgs.forEach(underControlOrg => {
		if (!uniqueOrgs.some(org => org.id === underControlOrg.id)) {
			uniqueOrgs.push(underControlOrg)
		}
	})

	const allOrganizations = [
		...(recipientType === 'employees'
			? [
					{
						...selfOrganization,
						id: parsedOrgId,
						name: 'Mazkur tashkilotning o`zi'
					}
				]
			: []),
		...uniqueOrgs
	]

	const handleRecipientTypeChange = (e: any) => {
		const newType = e.target.value
		setRecipientType(newType)

		setRecipents([
			{
				organizationId: '',
				userId: [],
				...(newType === 'positions' && { positionId: '' })
			}
		])
		setUsers({})
		setSelectedOrgForWorkers('')
	}

	const handleOrganizationSelect = async (organizationId: string, recipentIndex: number) => {
		if (recipientType === 'organizations') {
			if (organizationId === 'all') {
				setRecipents([
					{
						organizationId: 'all',
						userId: []
					}
				])
			} else {
				const currentRecipent = recipents[recipentIndex]
				const currentSelected = Array.isArray(currentRecipent?.selectedOrganizations)
					? currentRecipent.selectedOrganizations
					: []

				const newSelected = currentSelected.includes(organizationId)
					? currentSelected.filter(id => id !== organizationId)
					: [...currentSelected, organizationId]

				setRecipents(prev =>
					prev.map((item, index) =>
						index === recipentIndex
							? {
									...item,
									organizationId: newSelected.length > 0 ? 'selected' : '',
									selectedOrganizations: newSelected
								}
							: item
					)
				)
			}
		} else {
			setSelectedOrgForWorkers(organizationId)

			setRecipents(prev =>
				prev.map((item, index) =>
					index === recipentIndex ? { ...item, organizationId, userId: [] } : item
				)
			)
		}
	}

	const handleOrganizationDeselect = (organizationId: string, recipentIndex: number) => {
		if (recipientType === 'organizations') {
			if (organizationId === 'all') {
				setRecipents(prev =>
					prev.map((item, index) =>
						index === recipentIndex
							? {
									...item,
									organizationId: '',
									selectedOrganizations: []
								}
							: item
					)
				)
			} else {
				const currentRecipent = recipents[recipentIndex]
				const currentSelected = currentRecipent?.selectedOrganizations || []
				const newSelected = currentSelected.filter(id => id !== organizationId)

				setRecipents(prev =>
					prev.map((item, index) =>
						index === recipentIndex
							? {
									...item,
									organizationId: newSelected.length > 0 ? 'selected' : '',
									selectedOrganizations: newSelected
								}
							: item
					)
				)
			}
		}
	}

	const handlePositionSelect = (positionId: string, recipentIndex: number) => {
		setRecipents(prev =>
			prev.map((item, index) => (index === recipentIndex ? { ...item, positionId } : item))
		)
	}

	return (
		<Card className='p-2'>
			<Form
				form={form}
				onFinish={async (values: Omit<TTaskRequest, 'recipients'>) => {
					const taskData: TTaskRequest = {
						name: values.name,
						description: values.description,
						taskTypeId: values.taskTypeId,
						dueDate: new Date(values.dueDate).toISOString(),
						files: fileIds,
						controllerId: values.controllerId,
						recipients: recipents
							.filter(item => {
								if (recipientType === 'positions') {
									return item.positionId
								}
								return item.organizationId
							})
							.map(item => {
								if (recipientType === 'positions') {
									return { positionId: item.positionId }
								}
								if (recipientType === 'organizations') {
									if (item.organizationId === 'all') {
										return { organizationId: 'all' }
									}

									if (item.selectedOrganizations?.length) {
										return item.selectedOrganizations.map(orgId => ({ organizationId: orgId }))
									}
									return { organizationId: item.organizationId }
								}
								return item.userId.length
									? { organizationId: item.organizationId, userId: item.userId }
									: { organizationId: item.organizationId }
							})
							.flat()
					}
					await createTask(taskData).then(() => {
						form.resetFields()
						setRecipents([
							{
								organizationId: '',
								userId: [],
								...(recipientType === 'positions' && { positionId: '' })
							}
						])
						clearFiles()
						setSelectedOrgForWorkers('')
					})
				}}>
				<TaskTypeAndDate taskTypeData={taskType.data} />

				<RecipientTypeSelector
					recipientType={recipientType}
					handleRecipientTypeChange={handleRecipientTypeChange}
					isSelfOrganization={isSelfOrganization}
				/>

				{recipents.map((recipent, recipentIndex) => (
					<RecipientRow
						key={recipentIndex}
						recipent={recipent}
						recipentIndex={recipentIndex}
						recipents={recipents}
						setRecipents={setRecipents}
						recipientType={recipientType}
						allOrganizations={allOrganizations as TOrganization[]}
						users={users}
						isPending={workersQuery.isFetching}
						hasNextPage={workersQuery.hasNextPage}
						fetchNextPage={workersQuery.fetchNextPage}
						organizationHasNextPage={false}
						organizationFetchNextPage={() => {}}
						organizationIsPending={childrenQuery.isFetching || underControlsQuery.isFetching}
						handleOrganizationSelect={handleOrganizationSelect}
						handleOrganizationDeselect={handleOrganizationDeselect}
						handlePositionSelect={handlePositionSelect}
					/>
				))}

				{recipientType === 'employees' && (
					<AddRecipientButton
						recipents={recipents}
						setRecipents={setRecipents}
						recipientType={recipientType}
					/>
				)}

				<TaskNameForm />

				<TaskControllerSelect organizationId={parsedOrgId} />

				<TaskDescriptionForm />

				<FileUpload />

				<SubmitButton
					recipents={recipents}
					recipientType={recipientType}
					isUploading={isUploading}
				/>
			</Form>
		</Card>
	)
}
