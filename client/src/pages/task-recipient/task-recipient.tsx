import { useGetOneRecipient } from '@/config/queries/recipient/getOne'
import { useGetOneTask } from '@/config/queries/task/getOne'
import { useUpdateRecipientAnswerState } from '@/config/queries/task/update-recipient-state'
import { STATE } from '@/shared/types/state.type'
import { LoadingScreen } from '@/shared/ui/suspense'
import { getFileUrl } from '@/shared/utils/getFileUrl'
import { Button, Card, Input, Modal, Popconfirm, Badge } from 'antd'
import { useEffect, useState } from 'react'
import { Link, useParams } from 'react-router-dom'
import { InfoItem } from '../task-details/ui/info-item'
import dayjs from 'dayjs'

const TaskRecipient = () => {
	const { recipientId } = useParams()

	const { data: recipient, isLoading, refetch } = useGetOneRecipient(recipientId!)
	const [open, setOpen] = useState(false)
	const { data: task, isLoading: isLoadingTask } = useGetOneTask()
	const [recipientAnswerId, setRecipientAnswerId] = useState<string | null>(null)
	const [reason, setReason] = useState('')
	const { mutateAsync: updateTaskAnswerState, isPending } = useUpdateRecipientAnswerState(
		recipientId!
	)

	useEffect(() => {
		refetch()
	}, [refetch])

	const close = () => {
		setRecipientAnswerId(null)
		setOpen(false)
		setReason('')
	}

	const confirmTaskAnswer = (answerId: string) => {
		updateTaskAnswerState({ recipientAnswerId: answerId, data: { state: STATE.CONFIRMED } }).then(
			close
		)
	}

	const rejectTaskAnswer = () => {
		if (recipientAnswerId && reason) {
			updateTaskAnswerState({
				recipientAnswerId,
				data: { state: STATE.REJECTED, rejectReason: reason }
			}).then(close)
		}
	}

	if (isLoading || !recipient || isLoadingTask || !task) return <LoadingScreen />

	return (
		<Card className='mt-3'>
			<Card className='mb-4'>
				<div className='flex items-center gap-4 mb-4'>
					<Badge
						status='processing'
						text={task.TaskState?.name ?? 'Holat mavjud emas'}
						style={{
							fontSize: 16
						}}
					/>
				</div>
				<h1 className='text-2xl font-bold mb-4'>{task.name}</h1>
				<p className='font-bold mb-4'>{task.description}</p>
				<div className='grid grid-cols-2 gap-4'>
					<InfoItem
						title='Topshiriq beruvchi'
						value={task.CreatedByOrganization?.name + ': ' + task.CreatedBy?.fullName}
					/>
					<InfoItem
						title='Topshiriq kategoriyasi'
						value={task.TaskType?.name}
					/>
					<InfoItem
						title='Topshiriq holati'
						value={task.TaskState?.name}
					/>
					<InfoItem
						title='Muddati'
						value={task.dueDate ? dayjs(task.dueDate).format('DD.MM.YYYY') : 'Belgilanmagan'}
					/>
				</div>
				{!!task.files.length && (
					<div className='mt-4'>
						<h4 className='font-bold mb-2'>Fayllar:</h4>
						<div className='flex flex-wrap gap-3'>
							{task.files.map(file => (
								<Link
									key={file.path}
									className='px-3 py-2 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors'
									to={getFileUrl(file.path)}
									target='_blank'>
									{file.slug}
								</Link>
							))}
						</div>
					</div>
				)}
			</Card>

			<Card
				title={`Topshiriq bajaruvchisi: ${recipient.Organization?.name}`}
				className='p-4 shadow-lg'>
				<h1 className='font-bold text-lg mb-4'>Javoblar: </h1>
				{recipient.Answer.length === 0 && <p className='text-lg'>Javoblar mavjud emas</p>}
				<div className='space-y-4'>
					{recipient.Answer.map(answer => (
						<div
							key={answer.id}
							className='p-3 border rounded-lg shadow-sm'>
							<div className='flex justify-between items-center'>
								<div className='flex flex-col'>
									<span className={`text-lg ${answer.state !== STATE.PENDING ? 'opacity-50' : ''}`}>
										Izoh: {answer.description}
									</span>
									<span className='text-base'>Natija turi : {answer.type?.name}</span>
									<span className='text-sm text-gray-500'>
										{dayjs(answer.createdAt).format('DD.MM.YYYY HH:mm')}
									</span>
								</div>
								<div className='flex items-center gap-x-4'>
									{answer.state === STATE.PENDING && (
										<>
											<Button
												danger
												type='primary'
												onClick={() => {
													setRecipientAnswerId(answer.id)
													setOpen(true)
												}}>
												Rad qilish
											</Button>
											<Popconfirm
												title='Haqiqatdan ham bu javobni tasdiqlamoqchimisiz?'
												okText='ha'
												cancelText="yo'q"
												onConfirm={() => confirmTaskAnswer(answer.id)}>
												<Button type='primary'>Tasdiqlash</Button>
											</Popconfirm>
										</>
									)}
									{answer.state !== STATE.PENDING && (
										<>
											<Button
												danger
												type='primary'
												disabled
												onClick={() => {
													setRecipientAnswerId(answer.id)
													setOpen(true)
												}}>
												Rad qilish
											</Button>
											<Button
												type='primary'
												disabled>
												Tasdiqlash
											</Button>
										</>
									)}
								</div>
							</div>
							{!!answer.files.length && (
								<div
									className={`flex flex-col gap-y-2 mt-5 ${answer.state !== STATE.PENDING ? 'opacity-50' : ''}`}>
									<h4>Fayllar:</h4>
									<ul className='flex flex-col gap-y-2'>
										{answer.files.map(file => (
											<Link
												key={file.path}
												className='w-fit'
												to={getFileUrl(file.path)}
												target='_blank'>
												{file.slug}
											</Link>
										))}
									</ul>
								</div>
							)}
							{answer.state === STATE.REJECTED && (
								<p className='mt-2 text-red-500'>Rad etish sababi: {answer.rejectReason}</p>
							)}
						</div>
					))}
				</div>
			</Card>

			<Modal
				title='Nima sabab dan bu javobni rad qilmoqchisiz?'
				centered
				open={open && !!recipientAnswerId}
				onOk={rejectTaskAnswer}
				onCancel={() => setOpen(false)}
				okText='Rad qilish'
				okType='danger'
				loading={isPending}
				okButtonProps={{ disabled: !reason.length || !recipientAnswerId || isPending }}>
				<Input.TextArea
					rows={4}
					value={reason}
					required
					onChange={e => setReason(e.target.value)}
					placeholder='Sabab'
					className='w-full'
				/>
			</Modal>
		</Card>
	)
}
export default TaskRecipient
