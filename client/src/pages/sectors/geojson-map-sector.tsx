/// @ts-ignore
import { useGetCoordinates } from '@/config/queries/map/map.queries'
import {
	ErrorState,
	HoverTooltip,
	InteractiveGeoJSON,
	LoadingState,
	NavigationPanel,
	useAreaTypes,
	useGeoJsonData,
	useLayerVisibility,
	useMapHover,
	useMapInteraction
} from '../map'
import LocationMarkers from '../map/ui/location-markers'

export default function GeojsonMapSector() {
	// Получение данных с API с учетом параметров URL
	const { data, isLoading, error } = useGetCoordinates()

	// Преобразование данных в GeoJSON формат
	const geoJsonData = useGeoJsonData(data)

	// Получение уникальных типов областей
	const areaTypes = useAreaTypes(geoJsonData)

	// Управление состоянием карты
	const {
		regionId,
		districtId,
		navigateBack,
		resetNavigation,
		getCurrentLevel,
		getCurrentLevelTitle,
		canNavigateBack
	} = useMapInteraction()
	const { hoverInfo, handleMouseEnter, handleMouseLeave, handleMouseMove } = useMapHover(
		regionId,
		districtId
	)

	// Управление видимостью слоев
	const { filteredGeoJsonData } = useLayerVisibility(areaTypes, geoJsonData)

	if (isLoading) {
		return <LoadingState />
	}

	if (error) {
		return <ErrorState />
	}

	const currentLevel = getCurrentLevel()
	const shouldFitBounds = currentLevel !== 'overview'

	return (
		<div className='h-full w-full relative'>
			{hoverInfo && <HoverTooltip hoverInfo={hoverInfo} />}
			<NavigationPanel
				currentLevel={currentLevel}
				levelTitle={getCurrentLevelTitle()}
				canNavigateBack={canNavigateBack()}
				onNavigateBack={navigateBack}
				onReset={resetNavigation}
			/>
			{geoJsonData && geoJsonData.features.length > 0 && (
				<InteractiveGeoJSON
					geoJsonData={filteredGeoJsonData || geoJsonData}
					// @ts-ignore
					onFeatureClick={null}
					handleAreaClick={null}
					// @ts-ignore
					isAreaClickable={false}
					fitBounds={shouldFitBounds}
					onMouseEnter={handleMouseEnter}
					onMouseLeave={handleMouseLeave}
					onMouseMove={handleMouseMove}
				/>
			)}
			<LocationMarkers />
		</div>
	)
}
