{"name": "client", "private": true, "version": "0.116.21", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "format": "npx prettier ./ -w", "preview": "vite preview", "cy:open": "cypress open"}, "dependencies": {"@ant-design/icons": "^5.6.1", "@ant-design/plots": "^2.3.3", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@react-input/mask": "^2.0.4", "@tailwindcss/vite": "^4.0.5", "@tanstack/query-sync-storage-persister": "^5.66.0", "@tanstack/react-query": "^5.66.0", "@tanstack/react-query-devtools": "^5.66.0", "@tanstack/react-query-persist-client": "^5.66.0", "@types/react-input-mask": "^3.0.6", "antd": "^5.23.4", "axios": "^1.7.9", "chart.js": "^4.4.8", "date-fns": "^4.1.0", "framer-motion": "^12.9.4", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "lodash": "^4.17.21", "lucide-react": "^0.475.0", "rc-virtual-list": "^3.19.1", "react": "^19.0.0-rc.1", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0-rc.1", "react-input-mask": "^2.0.4", "react-leaflet": "^5.0.0-rc.2", "react-router-dom": "^7.1.5", "recharts": "^2.15.1", "zustand": "^5.0.3"}, "devDependencies": {"@commitlint/cli": "19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/js": "^9.19.0", "@types/leaflet": "^1.9.16", "@types/lodash": "^4.17.16", "@types/node": "^22.13.1", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react-swc": "^3.5.0", "commitlint": "^19.8.1", "cypress": "^14.0.2", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "prettier": "^3.4.2", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "buildInfo": {"buildDate": "2025-07-02 20:00:31", "buildAuthor": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}