import { axiosPrivate } from '@/config/api'
import { faceIdEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { FaceId } from './getAllByOrg'
import { AxiosError, AxiosResponse } from 'axios'
import { notification } from 'antd'
import { TApiErrorResponse } from '@/shared/types/api-error.type'

export type TFaceIdRequest = Omit<FaceId, 'id' | 'organizationId'> & { orgId: string }
export type TFAceIdResponse = FaceId

export const useCreateFaceIdDevice = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async (data: TFaceIdRequest) => {
			return await axiosPrivate.post(faceIdEndpoints.all, data)
		},
		onSuccess: async (response: AxiosResponse<TFAceIdResponse>) => {
			console.log(response)
			notification.success({ message: 'Face ID muaffaqiyatli yaratildi!' })
			queryClient.invalidateQueries({
				queryKey: [faceIdEndpoints.allByOrg(response.data.organizationId)]
			})
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response?.data)

			if (
				response?.data.message === '[P2002]: Noyob cheklov buzildi' &&
				response.data.statusCode === 409
			) {
				notification.error({ message: 'MAC address yoki IP address allaqachon mavjud' })
			} else {
				notification.error({ message: 'Face ID yaratishda xatolik yuz berdi!' })
			}
		}
	})
}
