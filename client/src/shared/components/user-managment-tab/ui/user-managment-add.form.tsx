import { TValidationFormProps } from '@/shared/types/validation-form.type'
import { Col, Form, Select } from 'antd'
import { useFaceIDAddStore } from '../utils/face-id-add-modal-store'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import debounce from 'lodash/debounce'
import { useSearchParams } from 'react-router-dom'
import { InputMask } from '@react-input/mask'
import { useUsersSearchByPhones } from '@/config/queries/users/get-all.queries'
import { useGetAllUsers } from '@/config/queries/users/get-all.queries'

const { Item } = Form

type TProps = TValidationFormProps & {
	userIds: string[]
	setUserIds: React.Dispatch<React.SetStateAction<string[]>>
	onUserFound?: (userData: any) => void
}

export const FaceIdAddForm = ({ validation, setUserIds, onUserFound }: TProps) => {
	const { who, open } = useFaceIDAddStore()
	const [searchParams, setSearchParams] = useSearchParams()
	const [phoneNumber, setPhoneNumber] = useState<string>('+998 ')
	const [searchPhone, setSearchPhone] = useState<{
		phone: string | null
	}>({
		phone: null
	})
	const [foundUser, setFoundUser] = useState<any>(null)
	const [hasTriggeredUserFound, setHasTriggeredUserFound] = useState<boolean>(false)
	const lastProcessedPhone = useRef<string | null>(null)

	const usersData = useGetAllUsers()

	const {
		data: searchResult,
		isLoading,
		refetch
	} = useUsersSearchByPhones(searchPhone.phone ? Number(searchPhone.phone) : 0)

	useEffect(() => {
		if (!open) {
			setPhoneNumber('+998 ')
			setSearchPhone({ phone: null })
			setFoundUser(null)
			setHasTriggeredUserFound(false)
			lastProcessedPhone.current = null
			setUserIds([])
		}
	}, [open, setUserIds])

	useEffect(() => {
		searchParams.set('role', who)
		setSearchParams(searchParams, { replace: true })
	}, [who, searchParams, setSearchParams])

	const handlePhoneSearch = useCallback(
		(phone: string) => {
			const cleanPhone = phone.replace(/^\+998\s*/, '').replace(/[^\d]/g, '')
			if (cleanPhone.length >= 9) {
				setSearchPhone({ phone: cleanPhone })
				setHasTriggeredUserFound(false)
				lastProcessedPhone.current = cleanPhone
			} else {
				setSearchPhone({ phone: null })
				setFoundUser(null)
				setHasTriggeredUserFound(false)
				lastProcessedPhone.current = null
				setUserIds([])
			}
		},
		[setUserIds]
	)

	const debouncedPhoneSearch = useMemo(() => debounce(handlePhoneSearch, 500), [handlePhoneSearch])

	const handleSelectSearch = useCallback(
		(value: string) => {
			searchParams.set('search', value.trim())
			setSearchParams(searchParams.toString())
		},
		[searchParams, setSearchParams]
	)

	const handleSelectClear = useCallback(() => {
		searchParams.delete('search')
		setSearchParams(searchParams.toString())
	}, [searchParams, setSearchParams])

	const debouncedSelectSearch = useMemo(
		() => debounce(handleSelectSearch, 300),
		[handleSelectSearch]
	)

	const handlePopupScroll = useCallback(
		(e: React.UIEvent<HTMLDivElement>) => {
			const target = e.target as HTMLDivElement
			const isNearBottom = target.scrollTop + target.offsetHeight >= target.scrollHeight - 10
			if (isNearBottom) {
				usersData.fetchNextPage()
			}
		},
		[usersData]
	)

	useEffect(() => {
		if (lastProcessedPhone.current !== searchPhone.phone) {
			return
		}

		if (searchResult?.data && !hasTriggeredUserFound) {
			const userData = Array.isArray(searchResult.data) ? searchResult.data[0] : searchResult.data

			if (userData) {
				setFoundUser(userData)
				setHasTriggeredUserFound(true)

				if (onUserFound) {
					onUserFound(userData)
				}
			}
		} else if (searchResult && !searchResult.data && searchPhone.phone && !hasTriggeredUserFound) {
			setFoundUser(null)
			setUserIds([])
			setHasTriggeredUserFound(true)
		}
	}, [searchResult, searchPhone.phone, hasTriggeredUserFound, setUserIds, onUserFound])

	useEffect(() => {
		if (searchPhone.phone && searchPhone.phone.length >= 9) {
			refetch()
		}
	}, [searchPhone.phone, refetch])

	const handlePhoneChange = (value: string) => {
		setPhoneNumber(value)
		debouncedPhoneSearch(value)
	}

	if (who === 'responsibles') {
		return (
			<div className='face-id-add-form'>
				<Item
					name='users'
					label="Mas'ullarni tanlang"
					rules={validation.type}>
					<Select
						mode='multiple'
						allowClear
						options={
							usersData?.data?.pages.flatMap(data => {
								return data?.data
									?.filter(user => user.roleTypes.includes('worker'))
									.map(d => ({
										value: d.id,
										label: d.fullName
									}))
							}) || []
						}
						onSelect={id => {
							setUserIds(prev => [...prev, id])
						}}
						onDeselect={id => {
							setUserIds(prev => prev.filter(f => f !== id))
						}}
						showSearch
						filterOption={false}
						onSearch={debouncedSelectSearch}
						onClear={handleSelectClear}
						placeholder='Qidirish uchun yozing...'
						onPopupScroll={handlePopupScroll}
						listHeight={300}
					/>
				</Item>
			</div>
		)
	}
	return (
		<div className='face-id-add-form'>
			<Item
				name='users'
				label={who === 'workers' ? 'Ishchilarni tanlang' : 'Xodimlarni tanlang'}
				rules={validation.type}>
				<Col span={24}>
					<Item
						name='username'
						required>
						<InputMask
							style={{
								width: '100%',
								padding: '8px 11px',
								fontSize: '16px',
								lineHeight: '1.8',
								borderRadius: '6px',
								border: '1px solid gray'
							}}
							value={phoneNumber}
							mask='+998 (__) ___-__-__'
							replacement={{ _: /\d/ }}
							placeholder='+998 (__) ___-__-__'
							onChange={e => handlePhoneChange(e.target.value)}
						/>
					</Item>

					{isLoading && <div className='text-blue-500 mt-2'>Qidirilmoqda...</div>}

					{searchPhone.phone && !searchResult?.data && !isLoading && (
						<div className='text-red-500 mt-2'>Foydalanuvchi topilmadi</div>
					)}

					{foundUser && (
						<div className='text-green-500 mt-2'>Foydalanuvchi topildi: {foundUser.fullName}</div>
					)}
				</Col>
			</Item>
		</div>
	)
}
