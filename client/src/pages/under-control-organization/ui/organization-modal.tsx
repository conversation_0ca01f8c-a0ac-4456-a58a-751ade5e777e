import { Button, Form, Modal } from 'antd'
import { useOrganizationsModalStore } from '../utils/organizations-modal-store'
import OrganizationForm from './organization-form'
import { usePermissions } from '@/shared/guards/usePermissions'
import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { LoadingScreen } from '@/shared/ui/suspense'
import { useEffect, useState } from 'react'
import { useCreateUnderControlOrganization } from '@/config/queries/organizations/create-under-control.queries'
import { LinkIcon } from 'lucide-react'

type TOrganizationModal = {
	orgId: string
}

export const UnderControlManagmentModal = ({ orgId }: TOrganizationModal) => {
	const [form] = Form.useForm()
	const { mutateAsync: createOrganization, isPending: isCreating } =
		useCreateUnderControlOrganization()
	const { open, onOpen, onClose, data } = useOrganizationsModalStore()
	const { data: user, isLoading } = useAuthMe()
	const { hasPermission } = usePermissions()
	const [orgIds, setOrgIds] = useState<string[]>([])

	useEffect(() => {
		if (data) {
			form.setFieldsValue(data)
		}
	}, [data, form])

	const close = () => {
		setOrgIds([])
		form.resetFields()
		onClose()
	}

	const onFinish = () => {
		createOrganization({
			id: orgId,
			data: {
				organizations: orgIds
			}
		}).then(close)
	}

	if (isLoading || !user?.role) return <LoadingScreen />

	return (
		hasPermission(user.role, ['create:*']) && (
			<>
				<Button
					type='primary'
					icon={<LinkIcon className='w-4 h-4' />}
					onClick={() => onOpen()}>
					Tashkilot biriktirish
				</Button>

				<Modal
					title={'Tashkilot biriktirish'}
					open={open}
					onCancel={close}
					onClose={close}
					destroyOnClose
					width={800}
					okText={data ? 'Saqlash' : "Qo'shish"}
					cancelText='Bekor qilish'
					confirmLoading={isCreating}
					onOk={() => form.submit()}>
					{open && (
						<Form
							form={form}
							layout='vertical'
							onFinish={onFinish}>
							<OrganizationForm
								orgId={orgId}
								orgIds={orgIds}
								setOrgIds={setOrgIds}
							/>
						</Form>
					)}
				</Modal>
			</>
		)
	)
}
