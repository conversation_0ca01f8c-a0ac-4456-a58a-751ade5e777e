import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
import { Button, Modal, Space, Table } from 'antd'
import { useUserWorkingScheduleDayModalStore } from './utils/user-working-schedule-day-store'
import { useGetUserWorkingScheduleDay } from '@/config/queries/user-working-schedule-day/get.queries'
import UserWorkingScheduleDayModal from './ui/user-working-schedule-day-modal'
import { useDeleteUserWorkingScheduleDay } from '@/config/queries/user-working-schedule-day/delete.queries'
import { usePagination } from '@/shared/hooks/usePagination'
import { useEffect } from 'react'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { Search } from '@/shared/components/search/search'
const UserWorkingScheduleDayTable = () => {
	const { onOpen } = useUserWorkingScheduleDayModalStore()
	const { setTotal, pagination, handlePaginationChange } = usePagination()
	const { data: organizationPositionTypes } = useGetUserWorkingScheduleDay()
	const deleteUserWorkingScheduleDay = useDeleteUserWorkingScheduleDay()

	useEffect(() => {
		if (organizationPositionTypes?.pages[0]?.meta) {
			setTotal(organizationPositionTypes.pages[0].meta.total)
		}
	}, [organizationPositionTypes])

	const handleDelete = (id: string) => {
		Modal.confirm({
			title: 'Ishonchingiz komilmi?',
			content: "Ushbu ish vaqtlarini o'chirishni xoxlaysizmi?",
			okText: 'Ha',
			cancelText: "Yo'q",
			onOk: () => {
				deleteUserWorkingScheduleDay.mutateAsync(id)
			}
		})
	}
	return (
		<Table
			pagination={pagination}
			onChange={handlePaginationChange}
			title={() => (
				<div className='flex items-center justify-between'>
					<Search />
					<UserWorkingScheduleDayModal />
				</div>
			)}
			size='small'
			scroll={{ x: 'max-content' }}
			dataSource={organizationPositionTypes?.pages[0]?.data || []}
			rowKey={rec => rec.id || ''}
			columns={[
				{
					key: '#',
					title: '#',
					render: (_, _rec, index) =>
						formatTableIndex(pagination.current, pagination.pageSize, index)
				},
				{
					key: 'name',
					title: 'Nomi',
					dataIndex: 'name'
				},
				{
					key: 'day',
					title: 'Kun',
					dataIndex: 'day'
				},
				{
					key: 'startTime',
					title: 'Boshlanish vaqti',
					dataIndex: 'startTime',
					render: time => {
						const date = new Date(time)
						return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
					}
				},
				{
					key: 'endTime',
					title: 'Tugash vaqti',
					dataIndex: 'endTime',
					render: time => {
						const date = new Date(time)
						return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
					}
				},
				{
					key: 'actions',
					title: 'Amallar',
					render: record => (
						<Space>
							<Button
								type='primary'
								icon={<EditOutlined />}
								onClick={() => onOpen(record)}
							/>
							<Button
								danger
								icon={<DeleteOutlined />}
								onClick={() => handleDelete(record.id)}
							/>
						</Space>
					)
				}
			]}
		/>
	)
}
export default UserWorkingScheduleDayTable
