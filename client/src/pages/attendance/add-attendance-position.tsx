import { Typography } from 'antd'
import { useState } from 'react'
import AddAttendancePositionFilter from './add-attendance-position/add-attendance-position-filter'
import AddAttendancePositionTable from './add-attendance-position/add-attendance-position-table'
import dayjs from 'dayjs'

const { Title } = Typography

const AddAttendancePosition = () => {
	const [selectedPositionId, setSelectedPositionId] = useState<string>()
	const [selectedDate, setSelectedDate] = useState<string>()

	const handleFormChange = (_changedFields: any, allFields: any) => {
		// Update selected position and date when form changes
		const positionField = allFields.find((field: any) => field.name[0] === 'positionId')
		const dateField = allFields.find((field: any) => field.name[0] === 'date')

		if (positionField?.value) {
			setSelectedPositionId(positionField.value)
		} else {
			setSelectedPositionId(undefined)
		}

		if (dateField?.value) {
			setSelectedDate(dayjs(dateField.value).format('DD.MM.YYYY'))
		} else {
			setSelectedDate(undefined)
		}
	}

	return (
		<div>
			<Title
				level={4}
				className='dark:text-gray-200'
				style={{ marginBottom: '20px' }}>
				Lavozim bo'yicha davomat qo'shish
			</Title>

			<AddAttendancePositionFilter onFormChange={handleFormChange} />

			<AddAttendancePositionTable
				selectedPositionId={selectedPositionId}
				selectedDate={selectedDate}
			/>
		</div>
	)
}

export default AddAttendancePosition
