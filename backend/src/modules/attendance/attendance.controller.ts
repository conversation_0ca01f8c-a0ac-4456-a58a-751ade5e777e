import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpStatus,
  Inject,
  Logger,
  NotFoundException,
  Param,
  Post,
  Query,
  Req,
  Res,
  UseInterceptors,
} from '@nestjs/common';
import { AttendanceService } from './attendance.service';
import { Request, Response } from 'express';
import { PrismaService, ProtectedRoute } from 'src/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { MulterService } from 'src/common/multer/multer.service';
import { InjectQueue } from '@nestjs/bullmq';
import { ATTENDANCE, AttendanceData } from './interface/attendance';
import { Queue } from 'bullmq';
import { Context } from 'src/common/decorators/context.decorator';
import { CreateAttendanceDto, FindAttendanceDto } from './dto/create-attendance.dto';
import xlsx from 'node-xlsx';
import { AttendanceExcelService } from './attendace-excel.service';
import { unlink } from 'fs';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';

@Controller('attendance')
export class AttendanceController {
  logger = new Logger(AttendanceController.name);

  constructor(
    private readonly attendanceService: AttendanceService,
    private readonly attendanceExcelService: AttendanceExcelService,
    private readonly prisma: PrismaService,
    @InjectQueue(ATTENDANCE) private attendanceQueue: Queue,
    @Inject(CACHE_MANAGER) private readonly cacheService: Cache,
  ) {}

  @ProtectedRoute({ isPublic: true })
  @UseInterceptors(FileInterceptor('Picture', new MulterService().createMemoryImageOptions()))
  @Post()
  async attendance(@Req() req: Request, @Res() res: Response) {
    const event = JSON.parse(req.body.AccessControllerEvent) as AttendanceData;
    if (event.eventType === 'heartBeat') {
      this.cacheService.set(
        `device-heartbeat-${event.macAddress}`,
        {
          macAddress: event.macAddress,
          dateTime: event.dateTime,
        },
        600_000, // 10 minutes
      );
      res.sendStatus(HttpStatus.OK);
      return;
    }
    if (event.AccessControllerEvent?.employeeNoString) {
      const job = await this.attendanceQueue.add('attendance', {
        ipAddress: event.ipAddress,
        macAddress: event.macAddress,
        dateTime: event.dateTime,
        AccessControllerEvent: {
          employeeNoString: event.AccessControllerEvent.employeeNoString,
        },
      });
    }
    res.sendStatus(HttpStatus.OK);
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
    onlyAdmin: false,
  })
  @Post('add-attendance')
  async addAttendance(@Body() body: CreateAttendanceDto) {
    const { userId, positionId, time } = body;

    if (positionId) {
      const users = await this.prisma.user.findMany({
        where: { positionId },
        select: {
          id: true,
          Organization: { select: { id: true } },
        },
      });

      if (users.length === 0) {
        throw new NotFoundException(`Berilgan positionId bo‘yicha foydalanuvchilar topilmadi`);
      }

      const jobs = [];

      for (const user of users) {
        const attendance = await this.attendanceService.createAttendance({ userId: user.id });

        if (!attendance?.organization?.FaceIDDevice?.length) {
          this.logger.warn(`User ${user.id} uchun FaceID qurilma topilmadi. Skipped.`);
          continue;
        }

        const job = await this.attendanceQueue.add('attendance', {
          ipAddress: attendance.organization.FaceIDDevice[0].IP,
          macAddress: attendance.organization.FaceIDDevice[0].MAC,
          dateTime: time,
          AccessControllerEvent: {
            employeeNoString: attendance.user.id,
          },
        });

        jobs.push(job.asJSON());
      }

      return {
        successCount: jobs.length,
        jobs,
      };
    }

    if (userId) {
      const attendance = await this.attendanceService.createAttendance({ userId });

      if (!attendance) {
        throw new BadRequestException('Failed to create attendance record');
      }

      const device = attendance.organization.FaceIDDevice[0];

      if (!device) {
        throw new BadRequestException('FaceID qurilma topilmadi');
      }

      const job = await this.attendanceQueue.add('attendance', {
        ipAddress: device.IP,
        macAddress: device.MAC,
        dateTime: time,
        AccessControllerEvent: {
          employeeNoString: attendance.user.id,
        },
      });

      return job.asJSON();
    }

    throw new BadRequestException('userId yoki positionId berilishi shart');
  }

  @ProtectedRoute({
    context: 'WORKSPACE',
    isPublic: false,
  })
  @Get('user/:userId')
  async getUserAttendance(@Param('userId') userId: string, @Query() { page, limit }: { page: string; limit: string }) {
    return await this.attendanceService.getUserAttendance(userId, page, limit);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Get('personal/:userId')
  async getUserAttendanced(@Param('userId') userId: string, @Query() { page, limit }: { page: string; limit: string }) {
    return await this.attendanceService.getUserAttendance(userId, page, limit);
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
  })
  @Get()
  async getAttendance(@Context('organizationId') id: string, @Query() query: FindAttendanceDto) {
    return this.attendanceService.getAttendance(id, query);
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
  })
  @Get('/excel')
  async downloadAttendanceTable(
    @Context('organizationId') id: string,
    @Query() query: FindAttendanceDto,
    @Res() res: Response,
  ) {
    const { data, sheetOptions } = await this.attendanceService.getAttendanceTable(id, query);
    const buffer = xlsx.build([{ name: 'user-attendance', data, options: sheetOptions }]);

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', 'attachment; filename=user-attendance.xlsx');

    res.send(buffer);
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
  })
  @Get('/report-excel')
  async downloadAttendanceReport(
    @Context('organizationId') id: string,
    @Res() res: Response,
    @Query('organizationId') organizationId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const filePath = await this.attendanceExcelService.generateAttendanceReportExcel(
      organizationId?.split(',') ?? [id],
      startDate,
      endDate,
    );

    res.download(filePath, `attendance_report_${new Date().toISOString().split('T')[0]}.xlsx`);

    res.on('finish', () => {
      unlink(filePath, () => {});
    });
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
  })
  @Get('statistics')
  async getAttendanceStatistics(
    @Context('organizationId') id: string,
    @Query('organizationId') organizationId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.attendanceService.getAttendanceReport(organizationId?.split(',') ?? [id], startDate, endDate);
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
  })
  @Get('statistics/organization-type-position/:positionId')
  async getAttendanceReportByOrganizationTypePosition(
    @Context('organizationId') id: string,
    @Param('positionId') positionId: string,
    @Query('limit') limit?: number,
    @Query('page') page?: number,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.attendanceService.getOptimizedAttendanceReport({
      organizationId: id,
      positionId,
      limit: limit ? Number(limit) : undefined,
      page: page ? Number(page) : undefined,
      startDate,
      endDate,
    });
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
  })
  @Get('statistics/organization-type-position/:positionId/excel')
  async downloadAttendanceReportByOrganizationTypePosition(
    @Context('organizationId') id: string,
    @Param('positionId') positionId: string,
    @Res() res: Response,
    @Query('limit') limit?: string,
    @Query('page') page?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const filePath = await this.attendanceExcelService.generateAttendanceReportByOrganizationTypePositionExcel(
      id,
      positionId,
      startDate,
      endDate,
      page ? Number(page) : 1,
      limit ? Number(limit) : 20,
    );

    res.download(filePath, `lavozim_davomat_hisoboti_${new Date().toISOString().split('T')[0]}.xlsx`);

    res.on('finish', () => {
      unlink(filePath, () => {});
    });
  }
}
