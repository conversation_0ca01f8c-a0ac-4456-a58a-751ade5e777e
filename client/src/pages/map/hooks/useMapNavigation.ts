import { useSearchParams } from 'react-router-dom'
import { useCallback, useMemo } from 'react'

/**
 * Хук для управления навигацией по уровням карты (регион -> район -> секция)
 */
export const useMapNavigation = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const organization = useMemo(() => {
		try {
			return JSON.parse(localStorage.getItem('organization') || '{}')
		} catch (error) {
			console.error('Ошибка при парсинге организации из localStorage:', error)
			return {}
		}
	}, [])

	const regionId = searchParams.get('regionId')
	const districtId = searchParams.get('districtId')
	const sectionId = searchParams.get('sectionId')

	const navigateToRegion = useCallback(
		(regionId: string) => {
			const newParams = new URLSearchParams()
			newParams.set('regionId', regionId)
			setSearchParams(newParams)
		},
		[setSearchParams]
	)

	const navigateToDistrict = useCallback(
		(regionId: string, districtId: string) => {
			const newParams = new URLSearchParams()
			newParams.set('regionId', regionId)
			newParams.set('districtId', districtId)
			setSearchParams(newParams)
		},
		[setSearchParams]
	)

	const navigateToSection = useCallback(
		(regionId: string, districtId: string, sectionId: string) => {
			const newParams = new URLSearchParams()
			newParams.set('regionId', regionId)
			newParams.set('districtId', districtId)
			newParams.set('sectionId', sectionId)
			setSearchParams(newParams)
		},
		[setSearchParams]
	)

	const navigateBack = useCallback(() => {
		const newParams = new URLSearchParams()

		if (sectionId) {
			// Если на уровне секции - вернуться к району
			newParams.set('regionId', regionId!)
			newParams.set('districtId', districtId!)
		} else if (districtId) {
			// Если на уровне района - вернуться к региону
			newParams.set('regionId', regionId!)
		} else if (regionId) {
			// Если на уровне региона - вернуться к общему виду
			// Очищаем все параметры
		}

		setSearchParams(newParams)
	}, [regionId, districtId, sectionId, setSearchParams])

	const resetNavigation = useCallback(() => {
		setSearchParams(new URLSearchParams())
	}, [setSearchParams])

	const getCurrentLevel = useCallback(() => {
		if (sectionId) return 'section'
		if (districtId) return 'district'
		if (regionId) return 'region'
		return 'overview'
	}, [regionId, districtId, sectionId])

	const getCurrentLevelTitle = useCallback(() => {
		const level = getCurrentLevel()
		switch (level) {
			case 'section':
				return 'Mahalla'
			case 'district':
				return 'Tuman / Shahar'
			case 'region':
				return 'Viloyat'
			default:
				return 'Respublika'
		}
	}, [getCurrentLevel])

	const canNavigateBack = useCallback(() => {
		if (organization?.grade?.level === 10) {
			return districtId !== null
		}

		if (organization?.grade?.level === 20) {
			return sectionId !== null
		}

		if (organization?.grade?.level === 30) {
			return false // На уровне 30 нет навигации назад
		}

		return regionId !== null || districtId !== null || sectionId !== null
	}, [regionId, districtId, sectionId])

	return {
		regionId,
		districtId,
		sectionId,
		navigateToRegion,
		navigateToDistrict,
		navigateToSection,
		navigateBack,
		resetNavigation,
		getCurrentLevel,
		getCurrentLevelTitle,
		canNavigateBack
	}
}
