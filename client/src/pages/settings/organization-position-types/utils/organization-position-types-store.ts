import { OrganizationPositionTypes } from '@/config/queries/organization-position-types/create.queries'
import { create } from 'zustand'

interface OrganizationPositionTypesModalStore {
	open: boolean
	data: OrganizationPositionTypes | null
	onOpen: (data?: OrganizationPositionTypes) => void
	onClose: () => void
}

export const useOrganizationPositionTypesModalStore = create<OrganizationPositionTypesModalStore>(
	set => ({
		open: false,
		data: null,
		onOpen: data => set({ open: true, data: data || null }),
		onClose: () => set({ open: false, data: null })
	})
)
