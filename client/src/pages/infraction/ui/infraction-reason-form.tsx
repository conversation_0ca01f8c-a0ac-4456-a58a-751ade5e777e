import { Form, Input } from 'antd'
import { useFileUpload } from '@/shared/components/file-upload/file-upload'
import { useEffect } from 'react'

const { TextArea } = Input

interface InfractionReasonFormProps {
	loading?: boolean
	onFileIdsChange?: (fileIds: string[]) => void
}

export const InfractionReasonForm = ({ loading, onFileIdsChange }: InfractionReasonFormProps) => {
	const { fileIds, render: FileUpload } = useFileUpload()

	// Notify parent component when file IDs change
	useEffect(() => {
		if (onFileIdsChange) {
			onFileIdsChange(fileIds)
		}
	}, [fileIds, onFileIdsChange])

	return (
		<div className="space-y-6">
			<Form.Item
				label={
					<span className="text-sm font-medium">
						Sabab nomi <span className="text-red-500">*</span>
					</span>
				}
				name="name"
				rules={[
					{ required: true, message: 'Sabab nomini kiriting!' }
				]}
			>
				<Input
					placeholder="Sabab nomini kiriting"
					disabled={loading}
					size="large"
					className="rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500"
				/>
			</Form.Item>

			<Form.Item
				label={
					<span className="text-sm font-medium">
						Izoh <span className="text-red-500">*</span>
					</span>
				}
				name="description"
				rules={[
					{ required: true, message: 'Izohni kiriting!' }
				]}
			>
				<TextArea
					rows={4}
					placeholder="Qoidabuzarlik sababini batafsil yozing"
					disabled={loading}
					className="rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500"
					style={{ resize: 'none' }}
				/>
			</Form.Item>

			<div className="border-t pt-4">
				<div className="mb-3">
					<span className="text-sm font-medium">
						Fayl yuklash
					</span>
				</div>
				<FileUpload />
			</div>
		</div>
	)
}
