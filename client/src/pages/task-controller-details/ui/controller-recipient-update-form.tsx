import { Form, Select, <PERSON>, Col, Button, Modal, Radio } from 'antd'
import { useState, useEffect } from 'react'
import { useGetInspectorOrganizations } from '@/config/queries/organization/get-inspector-organizations'
import { LoadingScreen } from '@/shared/ui/suspense'
import { useGetOrganizationWorkers } from '@/config/queries/organizations/get-workers.queries'
import { TUser } from '@/config/queries/users/get-all.queries'
import { useGetOneOrganization } from '@/config/queries/organizations/getOne'
import { removeDuplicates } from '@/shared/utils/removeDuplicatedData'
import { useParams } from 'react-router-dom'
import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { Plus, XCircleIcon } from 'lucide-react'
import { useUpdateRecipientInspector } from '@/config/queries/task/update-recipient-inspector'
import TaskPositionSelect from '@/pages/task-create/ui/task-position-select'

export type TRecipent = {
	organizationId: string
	userId: string[]
	positionId?: string
}

type TTaskCreateForm = {
	recipents: TRecipent[]
	setRecipents: React.Dispatch<React.SetStateAction<TRecipent[]>>
	task?: any // Add task prop to get createdByOrganizationId
}

type TEmployeeSelector = {
	recipientType: string
	recipent: TRecipent
	recipentIndex: number
	users: Record<string, TUser[]>
	isPending: boolean
	setRecipents: React.Dispatch<React.SetStateAction<TRecipent[]>>
}

const EmployeeSelector = ({
	recipientType,
	recipent,
	recipentIndex,
	users,
	isPending,
	setRecipents
}: TEmployeeSelector) => {
	if (recipientType === 'organizations' || recipientType === 'positions') return null

	return (
		<Col
			xs={24}
			sm={24}
			md={11}>
			<Form.Item
				label='Ishchisi'
				labelCol={{ span: 24 }}
				rules={[{ required: true }]}>
				<Select
					mode='multiple'
					loading={isPending}
					className='rounded-md'
					value={recipent?.userId || []}
					onDeselect={userId =>
						setRecipents(prev =>
							prev.map((item, index) =>
								index === recipentIndex
									? { ...item, userId: item.userId.filter(id => id !== userId) }
									: item
							)
						)
					}
					onSelect={userId =>
						setRecipents(prev =>
							prev.map((item, index) =>
								index === recipentIndex
									? { ...item, userId: [...(item?.userId ?? []), userId] }
									: item
							)
						)
					}
					options={users?.[recipent.organizationId!]?.map(user => ({
						label: user.fullName,
						value: user.id
					}))}
					showSearch
				/>
			</Form.Item>
		</Col>
	)
}

export default function ControllerRecipientUpdateForm({ recipents, setRecipents, task }: TTaskCreateForm) {
	const { id: taskId } = useParams()

	// Use task's createdByOrganizationId instead of localStorage
	const createdByOrganizationId = task?.createdByOrganizationId

	const { data: selfOrganization, isLoading: isLoadingSelfOrganization } =
		useGetOneOrganization(createdByOrganizationId)

	// Use inspector organizations query for controller context with task's organization ID
	const { data: inspectorOrganizations, isLoading: isLoadingInspectorOrganizations } =
		useGetInspectorOrganizations(createdByOrganizationId, { page: 1, limit: 100 })
	
	const { isLoading: isLoadingUser } = useAuthMe()

	const [recipientType, setRecipientType] = useState<'organizations' | 'employees' | 'positions'>(
		'organizations'
	)
	const [selectedOrgForWorkers, setSelectedOrgForWorkers] = useState<string>('')
	const [users, setUsers] = useState<Record<string, TUser[]>>({})

	const workersQuery = useGetOrganizationWorkers(selectedOrgForWorkers)

	useEffect(() => {
		if (workersQuery.data && selectedOrgForWorkers) {
			const workersData = workersQuery.data.pages?.flatMap(page => page.data) || []
			setUsers(prev => ({
				...prev,
				[selectedOrgForWorkers]: removeDuplicates(workersData)
			}))
		}
	}, [workersQuery.data, selectedOrgForWorkers])

	const { mutateAsync: updateRecipient } = useUpdateRecipientInspector()
	const [form] = Form.useForm()
	const [recipientOpen, setRecipientOpen] = useState(false)

	if (isLoadingInspectorOrganizations || isLoadingSelfOrganization || isLoadingUser)
		return <LoadingScreen />

	// Use inspector organizations for controller context
	const allOrganizations = [
		...(recipientType === 'employees'
			? [
					{
						...selfOrganization,
						id: createdByOrganizationId,
						name: 'Mazkur tashkilotning o`zi'
					}
				]
			: []),
		// Extract Children and UnderControl organizations from inspector data
		...(inspectorOrganizations?.Children || []),
		...(inspectorOrganizations?.UnderControl || [])
	]

	const handleRecipientTypeChange = (e: any) => {
		const newType = e.target.value
		setRecipientType(newType)
		setRecipents([
			{
				organizationId: '',
				userId: [],
				...(newType === 'positions' && { positionId: '' })
			}
		])
	}

	const handleOrganizationSelect = async (organizationId: string, recipentIndex: number) => {
		if (recipientType === 'organizations') {
			setRecipents(
				recipents.map((item, index) =>
					index === recipentIndex ? { ...item, organizationId, userId: [] } : item
				)
			)
		} else {
			setSelectedOrgForWorkers(organizationId)
			setRecipents(
				recipents.map((item, index) =>
					index === recipentIndex ? { ...item, organizationId, userId: [] } : item
				)
			)
		}
	}

	const addNewRecipient = () => {
		const hasIncompleteRecipient = recipents.some(item =>
			recipientType === 'positions' ? !item.positionId : !item.organizationId
		)

		if (!hasIncompleteRecipient) {
			setRecipents([
				...recipents,
				{
					organizationId: '',
					userId: [],
					...(recipientType === 'positions' && { positionId: '' })
				}
			])
		}
	}

	const removeRecipient = (recipentIndex: number) => {
		setRecipents(recipents.filter((_, index) => index !== recipentIndex))
	}

	const handlePositionSelect = (positionId: string, recipentIndex: number) => {
		setRecipents(prev =>
			prev.map((item, index) => (index === recipentIndex ? { ...item, positionId } : item))
		)
	}

	const resetForm = () => {
		form.resetFields()
		setRecipents([
			{
				organizationId: '',
				userId: [],
				...(recipientType === 'positions' && { positionId: '' })
			}
		])
		setUsers({})
	}

	return (
		<>
			<Button
				onClick={() => setRecipientOpen(true)}
				type='primary'>
				Yangi bajaruvchi qo'shish
			</Button>

			<Modal
				title='Yangi bajaruvchi qo`shish'
				centered
				open={recipientOpen}
				onCancel={() => setRecipientOpen(false)}
				footer={null}
				width={800}>
				<Form
					form={form}
					layout='vertical'
					onFinish={async (_values) => {
						await updateRecipient({
							taskId: taskId!,
							data: {
								recipients: recipents.map(item => ({
									organizationId: item.organizationId === 'all' ? undefined : item.organizationId,
									userId: item.userId.length ? item.userId : undefined,
									positionId: item.positionId || undefined
								}))
							}
						})
						setRecipientOpen(false)
						resetForm()
					}}>
					<Radio.Group
						value={recipientType}
						onChange={handleRecipientTypeChange}>
						<Radio value='organizations'>Tashkilotlarga</Radio>
						<Radio value='employees'>Tashkilot hodimlariga</Radio>
						<Radio value='positions'>Lavozim</Radio>
					</Radio.Group>

					{recipents.map((recipent, recipentIndex) => (
						<Row
							key={recipentIndex}
							gutter={[14, 14]}
							className='flex items-center'>
							{recipientType !== 'positions' && (
								<Col
									xs={24}
									sm={24}
									md={recipientType === 'organizations' ? 24 : 11}>
									<Form.Item
										label='Tashkilot'
										labelCol={{ span: 24 }}
										rules={[{ required: true }]}>
										<Select
											value={recipent?.organizationId || undefined}
											placeholder='Tashkilotni tanlang'
											className='rounded-md'
											allowClear
											showSearch
											onSelect={(organizationId: string) =>
												handleOrganizationSelect(organizationId, recipentIndex)
											}
											onClear={() => {
												setRecipents(prev =>
													prev.map((item, index) =>
														index === recipentIndex
															? { ...item, organizationId: '', userId: [] }
															: item
													)
												)
											}}
											options={allOrganizations.map(item => ({
												...item,
												disabled: recipents.some(
													(each, index) =>
														index !== recipentIndex && each.organizationId === item.id
												)
											}))}
											fieldNames={{ label: 'name', value: 'id' }}
											filterOption={(input, option) =>
												(option?.name || '').toLowerCase().includes(input.toLowerCase())
											}
										/>
									</Form.Item>
								</Col>
							)}

							<EmployeeSelector
								recipientType={recipientType}
								recipent={recipent}
								recipentIndex={recipentIndex}
								users={users}
								isPending={workersQuery.isFetching}
								setRecipents={setRecipents}
							/>

							{recipientType === 'positions' && (
								<Col md={recipents.length > 1 ? 21 : 24}>
									<Form.Item
										label='Lavozim'
										labelCol={{ span: 24 }}
										rules={[{ required: true, message: 'Lavozimni tanlang' }]}>
										<TaskPositionSelect
											value={recipent.positionId}
											onChange={(positionId: string) =>
												handlePositionSelect(positionId, recipentIndex)
											}
										/>
									</Form.Item>
								</Col>
							)}

							{recipientType === 'employees' && (
								<Col
									xs={24}
									sm={24}
									md={2}
									className='mt-6'>
									<Button
										disabled={recipents.length < 2}
										onClick={() => removeRecipient(recipentIndex)}>
										<XCircleIcon />
									</Button>
								</Col>
							)}
						</Row>
					))}

					<Row
						gutter={[14, 14]}
						className='mb-5'>
						{recipientType === 'employees' && (
							<Button
								disabled={recipents.some(item => !item.organizationId)}
								onClick={addNewRecipient}
								className='mx-2 w-full'
								type='dashed'>
								<Plus />
								<span className='text-md'>Qabul qiluvchini qo'shish</span>
							</Button>
						)}
					</Row>

					<Row className='flex items-center justify-end'>
						<Button
							disabled={
								!recipents.filter(item =>
									recipientType === 'positions' ? !!item.positionId : !!item.organizationId
								).length
							}
							type='primary'
							htmlType='submit'>
							Yuborish
						</Button>
					</Row>
				</Form>
			</Modal>
		</>
	)
}
