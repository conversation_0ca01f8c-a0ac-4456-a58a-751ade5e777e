import { FaceId } from '@/shared/components/face-id/face-id'
import { useGetOneOrganization } from '@/config/queries/organizations/getOne'
import { LoadingScreen } from '@/shared/ui/suspense'
import { WorkingScheduleManagment } from '@/shared/components/working-schedule-managment/working-schedule-managment'

export const OrganizationDetails = () => {
	const organizationId = localStorage.getItem('organizationId')
	const { data: organization, isLoading } = useGetOneOrganization(JSON.parse(organizationId!))

	if (isLoading) return <LoadingScreen />

	return (
		<div className='flex items-start justify-between py-5 px-3'>
			<div className='flex flex-col gap-y-2'>
				<h1 className='text-3xl font-bold'>{organization?.name}</h1>
				{organization?.address && <p className='text-md'>Manzili: {organization?.address}</p>}
			</div>
			<div className='flex flex-col items-end gap-y-2'>
				<div className='flex items-center gap-x-3'>
					{organization?.grade?.level === -1 && (
						<div>
							<FaceId orgId={JSON.parse(organizationId!)} />
						</div>
					)}
					<div>
						<WorkingScheduleManagment currentId={JSON.parse(organizationId!)} />
					</div>
				</div>
			</div>
		</div>
	)
}
