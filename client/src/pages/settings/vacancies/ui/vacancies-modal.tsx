import { Button, Form, Modal } from 'antd'
import { useVacanciesModalStore } from '../utils/vacancies-modal-store'
import { TVacancy, useCreateVacancy } from '@/config/queries/vacancy/create.vacancy'
import { PlusOutlined } from '@ant-design/icons'
import VacanciesForm from './vacancies-form'
import { useUpdateVacancy } from '@/config/queries/vacancy/update.vacancy'
import { getFormChanges } from '@/shared/utils/getFormChanges'

const VacanciesModal = () => {
	const { data, open, onClose } = useVacanciesModalStore()
	const [form] = Form.useForm()
	const create = useCreateVacancy()
	const update = useUpdateVacancy()

	const close = () => {
		form.resetFields()
		onClose()
	}

	// useEffect(() => {
	// 	if (data) {
	// 		form.setFieldsValue(data)
	// 	}
	// }, [data, form])

	const onFinish = (formData: TVacancy) => {
		if (!data) {
			create.mutateAsync({ ...formData }).then(close)
		} else {
			const { id, ...rest } = data
			const updatedFields = getFormChanges(rest, formData)
			update.mutateAsync({ id, ...updatedFields } as { id: string }).then(close)
		}
	}

	return (
		<div className='flex items-center justify-end'>
			<Button
				type='primary'
				icon={<PlusOutlined />}
				onClick={() => useVacanciesModalStore.setState({ open: true })}>
				Vakansiya qo'shish
			</Button>
			<Modal
				open={open}
				onCancel={close}
				destroyOnClose
				onOk={form.submit}
				title={`Vakansiyani ${data ? 'ni tahrirlash' : "ni qo'shish"}`}
				okText={data ? 'Tahrirlash' : "Qo'shish"}
				cancelText='Bekor qilish'
				modalRender={node => (
					<Form
						layout='vertical'
						size='large'
						onFinish={onFinish}
						form={form}>
						{node}
					</Form>
				)}>
				<VacanciesForm />
			</Modal>
		</div>
	)
}

export default VacanciesModal
