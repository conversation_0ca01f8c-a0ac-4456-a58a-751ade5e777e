import { axiosPrivate } from '@/config/api'
import { dashboardEndpoints } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'
import { ApiResponse } from '../types'

type PositionCount = {
	id?: string
	name: string
	userCount: number
}

type AttendanceRecord = {
	id: string
	name: string
	count: number
}

type AttendanceData = {
	total: number
	list: AttendanceRecord[]
}

export type TDashboardSectionStats = {
	patientCount: number
	vacancyCount: number
	vacationCount: number
	employeeCount: number
	shortVacationCount: number
	positionsCount: PositionCount[]
	sectionCount: number
	lateAttendance: AttendanceData
	noAttendance: AttendanceData
}

// New type for GPS area data
export type TDashboardSectionStatsArea = {
	inArea: AttendanceData
	outArea: AttendanceData
	disconnected: AttendanceData
}

export const useGetDashboardSectionStats = () => {
	const [searchParams] = useSearchParams()
	const orgId = searchParams.get('orgId')

	return useQuery({
		queryKey: ['dashboardSectionStats', { orgId }],
		queryFn: async () => {
			return (
				await axiosPrivate.get<ApiResponse<TDashboardSectionStats>>(
					dashboardEndpoints.dashboardSectionStats(orgId ?? '')
				)
			).data
		},
		enabled: !!orgId
	})
}

export const useGetDashboardSectionStatsAttendance = () => {
	const [searchParams] = useSearchParams()
	const orgId = searchParams.get('orgId')

	return useQuery({
		queryKey: ['dashboardSectionStatsAttendance', { orgId }],
		queryFn: async () => {
			return (
				await axiosPrivate.get<TDashboardSectionStats>(
					dashboardEndpoints.dashboardSectionStatsAttendance(orgId ?? '')
				)
			).data
		},
		enabled: !!orgId
	})
}

export const useGetDashboardSectionStatsArea = () => {
	const [searchParams] = useSearchParams()
	const orgId = searchParams.get('orgId')

	return useQuery({
		queryKey: ['dashboardSectionStatsArea', { orgId }],
		queryFn: async () => {
			return (
				await axiosPrivate.get<TDashboardSectionStatsArea>(
					dashboardEndpoints.dashboardSectionStatsArea(orgId ?? '')
				)
			).data
		},
		enabled: !!orgId
	})
}
