import React from 'react'
import { HoverInfo } from '../hooks/useMapHover'

interface HoverTooltipProps {
	hoverInfo: HoverInfo
}

/**
 * Компонент для отображения всплывающей подсказки при наведении на область карты
 * Tooltip следует за курсором мыши для лучшего пользовательского опыта
 */
export const HoverTooltip: React.FC<HoverTooltipProps> = ({ hoverInfo }) => {
	const { areaName, position } = hoverInfo

	// Tooltip следует за курсором мыши
	const tooltipStyle = {
		left: position.x + 15, // Небольшой отступ от курсора
		top: position.y - 10, // Показываем немного выше курсора
		boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
		backgroundColor: 'rgba(255, 255, 255, 0.95)',
		backdropFilter: 'blur(4px)',
		transition: 'none' // Убираем анимацию для мгновенного следования за мышью
	}

	// Проверяем границы экрана и корректируем позицию
	const tooltipWidth = 200 // примерная ширина tooltip
	const tooltipHeight = 80 // примерная высота tooltip

	// Корректировка по горизонтали
	if (position.x + tooltipWidth + 15 > window.innerWidth) {
		tooltipStyle.left = position.x - tooltipWidth - 15 // Показываем слева от курсора
	}

	// Корректировка по вертикали
	if (position.y - tooltipHeight - 10 < 0) {
		tooltipStyle.top = position.y + 20 // Показываем ниже курсора
	}

	return (
		<div
			className='fixed z-[9999] bg-white border border-gray-300 rounded-lg shadow-lg p-3 pointer-events-none max-w-xs'
			style={tooltipStyle}>
			<div className='font-semibold text-gray-800 mb-1 text-center'>{areaName}</div>
		</div>
	)
}
