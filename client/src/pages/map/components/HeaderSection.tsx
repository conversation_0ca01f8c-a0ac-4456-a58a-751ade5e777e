import { Typo<PERSON>, Button } from 'antd'
import { MapPin, RefreshCw } from 'lucide-react'
import StatsCard from './StatsCard'
import { LocationStats } from '../types/location.types'

const { Title } = Typography

interface HeaderSectionProps {
	stats: LocationStats
	onRefresh: () => void
}

export default function HeaderSection({ stats, onRefresh }: HeaderSectionProps) {
	return (
		<div className='space-y-2'>
			<div className='flex items-center justify-between'>
				<Title
					level={4}
					className='!mb-0 text-gray-800 dark:text-gray-200 flex items-center'>
					<MapPin
						className='inline mr-2'
						size={20}
					/>
					GPS Joylashuv
				</Title>
				<Button
					type='text'
					icon={<RefreshCw size={16} />}
					onClick={onRefresh}
					className='text-gray-600 dark:text-gray-400'
				/>
			</div>
			<div className='grid grid-cols-3 gap-2'>
				<StatsCard
					title='Jami'
					value={stats.total}
					color='blue'
				/>
				<StatsCard
					title='Hududda'
					value={stats.active}
					color='green'
				/>
				<StatsCard
					title='Hududdan tashqarida'
					value={stats.inactive}
					color='red'
				/>
			</div>
		</div>
	)
}
