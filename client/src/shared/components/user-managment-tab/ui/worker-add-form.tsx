import { TValidationFormProps } from '@/shared/types/validation-form.type'
import { Col, Form, Input, Row, Select, TreeSelect, FormInstance } from 'antd'
import { useGetAllOrganizations } from '@/config/queries/organizations/get-all.queries'
import { useSearchWorkerByPhone } from '@/config/queries/users/get-all.queries'
import { JSX, useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { InputMask } from '@react-input/mask'
import { useGetOrganizationTypes } from '@/config/queries/organization-types/get.queries'
import debounce from 'lodash/debounce'
import { useUsersModalStore } from '@/pages/settings/users/utils/users-modal-store'
import { fetchOrganizationTypeChildren } from '@/pages/settings/users/ui/fetch-organization-type-children'

interface TreeNode {
	title: string
	value: string
	key: string
	isLeaf?: boolean
	pId?: string
}

interface WorkerAddFormProps extends TValidationFormProps {
	form: FormInstance
	Uploader: ({ file }: { file?: string }) => JSX.Element
	onUserFound?: (userData: any, formData: any) => void
}

export const WorkerAddForm = ({ validation, form, Uploader, onUserFound }: WorkerAddFormProps) => {
	const { data: organization } = useGetAllOrganizations()
	const { data: organizationTypes, fetchNextPage, hasNextPage } = useGetOrganizationTypes()
	const { data: user } = useUsersModalStore()
	const [treeData, setTreeData] = useState<TreeNode[]>([])
	const [loadingKeys, setLoadingKeys] = useState<string[]>([])
	const [phoneNumber, setPhoneNumber] = useState<string>('+998 ')
	const [searchParams, setSearchParams] = useState<{
		phone: string | null
	}>({
		phone: null
	})
	const [foundUser, setFoundUser] = useState<any>(null)
	const [hasTriggeredUserFound, setHasTriggeredUserFound] = useState<boolean>(false)
	const lastProcessedPhone = useRef<string | null>(null)

	const {
		data: searchResult,
		isLoading,
		refetch
	} = useSearchWorkerByPhone(searchParams.phone ? Number(searchParams.phone) : 0)

	useEffect(() => {
		if (organizationTypes && organizationTypes.pages.flatMap(page => page.data).length > 0) {
			const newData = organizationTypes?.pages
				.flatMap(page => page.data)
				.map(item => ({
					title: item.name,
					value: item.id,
					key: item.id,
					isLeaf: false,
					pId: undefined,
					selectable: false
				}))
			setTreeData(prev => {
				const keys = new Set(prev.map(node => node.key))
				const merged = [...prev, ...newData.filter(node => !keys.has(node.key as string))]
				return merged as TreeNode[]
			})
		}
	}, [organizationTypes])

	const updateTreeWithChildren = (nodeId: string, children: any[]) => {
		setTreeData(prev => {
			const hasExistingChildren = prev.some(node => node.pId === nodeId)
			if (hasExistingChildren) {
				return prev.map(node =>
					node.key === nodeId ? { ...node, isLeaf: children.length === 0 } : node
				)
			}
			const childNodes = children.map(child => ({
				title: child.name,
				value: child.id,
				key: child.id,
				isLeaf: false,
				pId: nodeId
			}))

			return [
				...prev.map(node =>
					node.key === nodeId ? { ...node, isLeaf: children.length === 0 } : node
				),
				...childNodes
			] as TreeNode[]
		})
	}

	const loadData = async (treeNode: any) => {
		const { key: id } = treeNode
		const hasChildren = treeData.some(node => node.pId === id)
		if (hasChildren) return
		if (loadingKeys.includes(id)) return

		setLoadingKeys(keys => [...keys, id])
		try {
			const { data: children } = await fetchOrganizationTypeChildren(id)
			if (children && children.length > 0) {
				updateTreeWithChildren(id, children)
			} else {
				setTreeData(prev => prev.map(node => (node.key === id ? { ...node, isLeaf: true } : node)))
			}
		} catch (e) {
			setTreeData(prev => prev.map(node => (node.key === id ? { ...node, isLeaf: true } : node)))
		} finally {
			setLoadingKeys(keys => keys.filter(k => k !== id))
		}
	}

	const handlePhoneSearch = useCallback(
		(phone: string) => {
			const cleanPhone = phone.replace(/^\+998\s*/, '').replace(/[^\d]/g, '')
			if (cleanPhone.length >= 9) {
				setSearchParams({ phone: cleanPhone })
				setHasTriggeredUserFound(false) // Reset the flag when searching
				lastProcessedPhone.current = cleanPhone
			} else {
				setSearchParams({ phone: null })
				setFoundUser(null)
				setHasTriggeredUserFound(false)
				lastProcessedPhone.current = null
				// Remove username from the form reset to prevent clearing it
				form.setFieldsValue({
					fullName: '',
					phone: '',
					imei: '',
					positionId: undefined,
					userId: '',
					mainOrganizationId: undefined
				})
			}
		},
		[form]
	)

	const debouncedPhoneSearch = useMemo(() => debounce(handlePhoneSearch, 500), [handlePhoneSearch])

	useEffect(() => {
		// Prevent processing the same phone number multiple times
		if (lastProcessedPhone.current !== searchParams.phone) {
			return
		}

		if (searchResult?.data && !hasTriggeredUserFound) {
			const userData = searchResult.data
			setFoundUser(userData)

			const formData = {
				userId: userData.id,
				fullName: userData.fullName,
				phone: userData.phone,
				imei: userData.imei || '',
				positionId: userData.position?.name,
				username: userData.username,
				mainOrganizationId: userData.Organization?.[0]?.name || null
			}
			form.setFieldsValue(formData)

			if (onUserFound) {
				onUserFound(userData, formData)
				setHasTriggeredUserFound(true)
			}
		} else if (searchResult && !searchResult.data && searchParams.phone && !hasTriggeredUserFound) {
			setFoundUser(null)

			// Keep the existing username value instead of overwriting it
			const currentUsername = form.getFieldValue('username')
			const formData = {
				userId: '',
				fullName: '',
				phone: searchParams.phone,
				imei: '',
				positionId: undefined,
				username: currentUsername || searchParams.phone,
				mainOrganizationId: undefined
			}
			form.setFieldsValue(formData)
			setHasTriggeredUserFound(true)
		}
	}, [searchResult, searchParams.phone, hasTriggeredUserFound, onUserFound, form])

	useEffect(() => {
		if (searchParams.phone && searchParams.phone.length >= 9) {
			refetch()
		}
	}, [searchParams.phone, refetch])

	const handlePhoneChange = (value: string) => {
		setPhoneNumber(value)
		debouncedPhoneSearch(value)
	}

	return (
		<Row gutter={[16, 16]}>
			<Col span={12}>
				<Form.Item
					name='username'
					label='Telefon raqami '
					required
					preserve={true}>
					<InputMask
						style={{
							width: '100%',
							padding: '8px 11px',
							fontSize: '16px',
							lineHeight: '1.8',
							borderRadius: '6px',
							border: '1px solid gray'
						}}
						value={phoneNumber}
						mask='+998 (__) ___-__-__'
						replacement={{ _: /\d/ }}
						placeholder='+998 (__) ___-__-__'
						onChange={e => handlePhoneChange(e.target.value)}
					/>
				</Form.Item>
				{isLoading && <div className='text-blue-500'>Qidirilmoqda...</div>}
				{searchParams.phone && !searchResult?.data && !isLoading && (
					<div className='text-red-500'>
						Foydalanuvchi topilmadi - yangi foydalanuvchi yaratiladi
					</div>
				)}
				{foundUser && <div className='text-green-500'>Foydalanuvchi topildi!</div>}
			</Col>
			<Form.Item
				name='userId'
				hidden>
				<Input />
			</Form.Item>

			<Col span={12}>
				<Form.Item
					name='fullName'
					label='F.I.O'
					rules={!foundUser ? validation.name : undefined}>
					<Input
						disabled={!!foundUser}
						placeholder={foundUser ? "Foydalanuvchi topilganda ko'rsatiladi" : 'F.I.O ni kiriting'}
					/>
				</Form.Item>
			</Col>

			<Col span={12}>
				<Form.Item
					name='phone'
					label="Qo'shimcha telefon raqami"
					rules={
						user
							? undefined
							: [
									{
										required: true,
										message: 'Telefon raqami, minimal 2 ta belgidan iborat',
										min: 2
									}
								]
					}>
					<InputMask
						style={{
							width: '100%',
							padding: '8px 11px',
							fontSize: '16px',
							lineHeight: '1.8',
							borderRadius: '6px',
							border: '1px solid gray'
						}}
						defaultValue={'+998'}
						mask='+998 (__) ___-__-__'
						replacement={{ _: /\d/ }}
						placeholder='Telefon raqamini kiriting'
					/>
				</Form.Item>
			</Col>
			{!foundUser && (
				<Col span={12}>
					<Form.Item
						name='password'
						label='Parol'
						rules={[{ required: true, message: 'Parol majburiy' }]}>
						<Input.Password placeholder='Parolni kiriting' />
					</Form.Item>
				</Col>
			)}

			<Col span={12}>
				<Form.Item
					name='imei'
					label='IMEI'>
					<Input
						disabled={!!foundUser}
						placeholder='Qurilma IMEI raqami'
					/>
				</Form.Item>
			</Col>

			<Col span={12}>
				<Form.Item
					name='positionId'
					label='Lavozim'
					rules={!foundUser ? validation.organizationType : undefined}>
					<TreeSelect
						allowClear
						disabled={!!foundUser}
						treeDataSimpleMode={{
							id: 'value',
							pId: 'pId'
						}}
						onPopupScroll={e => {
							const target = e.target as HTMLDivElement
							if (
								target.scrollTop + target.clientHeight >= target.scrollHeight - 5 &&
								hasNextPage
							) {
								fetchNextPage()
							}
						}}
						loadData={loadData}
						treeData={treeData}
						showCheckedStrategy={TreeSelect.SHOW_ALL}
					/>
				</Form.Item>
			</Col>

			<Col span={12}>
				<Form.Item
					name='mainOrganizationId'
					label='Asosiy tashkiloti'>
					<Select
						allowClear
						disabled={!!foundUser}
						onPopupScroll={e => {
							const target = e.target as HTMLDivElement
							if (
								target.scrollTop + target.clientHeight >= target.scrollHeight - 5 &&
								hasNextPage
							) {
								fetchNextPage()
							}
						}}
						options={organization?.pages?.flatMap(org => {
							return org?.data.map((item: any) => ({
								value: item?.id,
								label: item?.name
							}))
						})}
					/>
				</Form.Item>
			</Col>

			<Col span={24}>
				<Form.Item label='Rasm'>
					<Uploader file={user?.avatar?.path} />
				</Form.Item>
			</Col>
		</Row>
	)
}
