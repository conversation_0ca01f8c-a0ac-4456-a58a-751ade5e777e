import { axiosPrivate } from '@/config/api'
import { useQuery } from '@tanstack/react-query'
import { useParams, useSearchParams } from 'react-router-dom'

export interface Response {
	organization: Organization
	history: History[]
}

export interface Organization {
	id: string
	name: string
	regionId: string
	districtId: string
	sectionId: string
}

export interface History {
	id: string
	userId: string
	fullName: string
	position: Position
	organization: Organization2[]
	lat: number
	lng: number
	type: 'IN' | 'OUT' | 'DISABLED'
	createdAt: string
	isInArea: boolean
}

export interface Position {
	id: string
	name: string
	status: string
	typeId: string
	createdAt: string
	updatedAt: string
	description: string
}

export interface Organization2 {
	id: string
	name: string
	regionId: string
	districtId?: string
	sectionId?: string
}

export interface User {
	id: string
	fullName: string
	position: string
	phone: string
	organization: Organization[]
}

export interface Organization {
	name: string
}

export const useGetLocationsBySection = () => {
	const [search] = useSearchParams()

	const regionId = search.get('regionId') || ''
	const districtId = search.get('districtId') || ''
	const sectionId = search.get('sectionId') || ''

	const positionId = search.get('positionId')
	const isInArea = search.get('isInArea')
	const isDisabled = search.get('isDisabled')

	const key = [
		'locationsBySection',
		regionId,
		districtId,
		sectionId,
		isInArea,
		positionId,
		isDisabled
	].filter(Boolean)

	return useQuery({
		queryKey: key,
		queryFn: async () =>
			(
				await axiosPrivate.get<Response>(`gps/user/getHistory`, {
					params: {
						regionId,
						districtId:
							districtId?.length > 3 ? districtId.substring(districtId.length - 3) : districtId,
						sectionId:
							sectionId?.length > 6 ? sectionId.substring(sectionId.length - 6) : sectionId,
						isInArea: isInArea ? isInArea === 'true' : undefined,
						isDisabled: isDisabled ? isDisabled === 'true' : undefined,
						positionId: positionId && positionId !== 'all' ? positionId : undefined
					}
				})
			).data,
		enabled: !!regionId && !!districtId,
		gcTime: 0,
		staleTime: 0,
		refetchInterval: 30000
	})
}

export interface DailyLocation {
	id: string
	userId: string
	lat: number
	lng: number
	status: string
	isInArea: boolean
	createdAt: string
	type: 'IN' | 'OUT' | 'DISABLED' | 'ENABLED' | 'NOT_WORKING_TIME'
	updatedAt: string
	User: User
}

export interface Position {
	id: string
	name: string
	status: string
	description: string
	typeId: string
	createdAt: string
	updatedAt: string
}

export const useGetUserDailyLocation = () => {
	const { id: userId } = useParams()
	const [search] = useSearchParams()
	const day = search.get('day')
	const from = search.get('from') ?? '00:00'
	const to = search.get('to') ?? '23:59'
	return useQuery({
		queryKey: ['userDailyLocation', userId, day, from, to],
		queryFn: async () =>
			(
				await axiosPrivate.get<{
					history: DailyLocation[]
					historyReport: DailyLocation[]
					user: User
				}>(`gps/user/daily/${userId}`, {
					params: {
						day: day ? day : undefined,
						from: from.split(' ').at(-1),
						to: to.split(' ').at(-1)
					}
				})
			).data,
		enabled: !!userId,
		gcTime: 0,
		staleTime: 0,
		refetchInterval: 30000
	})
}
