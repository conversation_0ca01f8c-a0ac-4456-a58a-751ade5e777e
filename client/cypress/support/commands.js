/// <reference types="cypress" />
// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })
//
// declare global {
//   namespace Cypress {
//     interface Chainable {
//       login(email: string, password: string): Chainable<void>
//       drag(subject: string, options?: Partial<TypeOptions>): Chainable<Element>
//       dismiss(subject: string, options?: Partial<TypeOptions>): Chainable<Element>
//       visit(originalFn: CommandOriginalFn, url: string, options: Partial<VisitOptions>): Chainable<Element>
//     }
//   }
// }

Cypress.Commands.add('getById', id => cy.get(`[data-testid="${id}"]`))
Cypress.Commands.add('login', () => {
	cy.request({
		method: 'POST',
		url: 'http://localhost:3000/api/v1/auth/login',
		body: {
			username: 'admin',
			password: '1234'
		}
	}).then(resp => {
		window.localStorage.setItem('accessToken', resp.body.accessToken)
	})
})
Cypress.Commands.add('logout', () => {
	cy.getById('logout-button').click()
	cy.get('.ant-popconfirm-buttons > .ant-btn-primary').click()
})
Cypress.Commands.add('isPathname', pathname => {
	cy.location().then(location => {
		expect(location.pathname).to.eq(pathname)
	})
})
