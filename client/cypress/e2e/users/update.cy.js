describe('update user spec', () => {
	beforeEach(() => {
		cy.login()
		cy.visit('/users', {
			timeout: 500000
		})
	})

	it('should update user', () => {
		cy.get('.users-table tr:last [data-testid="user-edit-button"]').click()
		cy.getById('user-form-modal').should('be.visible')
		cy.getById('user-form-username-input').type(' update')
		cy.getById('user-form-fullname-input').type(' update')
		cy.getById('user-form-role-select').click()
		cy.get('[title="Admin"]').click()
		cy.getById('user-form-password-input').type('new 1234')
		cy.get('.ant-modal-footer > .ant-btn-primary').click()
		cy.get('.ant-notification-notice-success').should('be.visible')
	})
})
