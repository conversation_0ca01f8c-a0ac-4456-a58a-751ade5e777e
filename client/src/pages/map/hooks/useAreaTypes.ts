import { useMemo } from 'react'
import { FeatureCollection } from 'geojson'

/**
 * Хук для извлечения уникальных типов областей
 */
export const useAreaTypes = (geoJsonData: FeatureCollection | null) => {
	return useMemo(() => {
		if (!geoJsonData) return []
		const types = new Set<string>()
		geoJsonData.features.forEach(feature => {
			if (feature.properties?.type) {
				types.add(feature.properties.type)
			}
		})
		return Array.from(types)
	}, [geoJsonData])
}
