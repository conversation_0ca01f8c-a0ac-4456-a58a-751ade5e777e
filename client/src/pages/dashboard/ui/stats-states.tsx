import { Card, Typography, Progress } from 'antd'

const { Title } = Typography

interface StatSectionProps {
	title: string
	stats: {
		label: string
		value: number
		count: number
		color: string
		percent: number
	}[]
}

export function StatStates({ title, stats = [] }: StatSectionProps) {
	return (
		<Card
			bordered={false}
			className='stats-states-card'>
			<Title
				level={4}
				className='mb-6 text-2xl'>
				{title}
			</Title>

			{stats.length > 0 ? (
				<div className='space-y-6'>
					{stats.map((stat, index) => (
						<div
							key={index}
							className='stat-item  p-3 rounded-lg transition-colors'>
							<div className='flex items-center gap-4'>
								<div className='flex-shrink-0 w-28'>
									<span className='text-base font-medium'>{stat.label}</span>
								</div>

								<div className='flex-grow'>
									<Progress
										percent={stat.value}
										status='active'
										strokeColor={stat.color}
										strokeLinecap='round'
										strokeWidth={12}
										showInfo={false}
									/>
								</div>

								<div className='flex-shrink-0'>
									<p
										color={getTagColor(index)}
										className='text-xl px-3 py-2'>
										{stat.count} ta / {stat.percent}%
									</p>
								</div>
							</div>
						</div>
					))}
				</div>
			) : (
				<div className='flex items-center justify-center h-40'>
					<p className='text-xl text-gray-500'>Ma'lumot topilmadi</p>
				</div>
			)}
		</Card>
	)
}

function getTagColor(index: number): string {
	const colors = ['purple']
	return colors[index % colors.length]
}
