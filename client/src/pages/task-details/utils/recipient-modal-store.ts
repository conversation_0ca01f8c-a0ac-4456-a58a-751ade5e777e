import { TRecipient } from '@/config/queries/task/getOne'
import { create } from 'zustand'

interface RecipientModalStore {
	open: boolean
	data: TRecipient | null
	onOpen: (data?: TRecipient) => void
	onClose: () => void
}

export const useRecipientModalStore = create<RecipientModalStore>(set => ({
	open: false,
	data: null,
	onOpen: data => set({ open: true, data: data || null }),
	onClose: () => set({ open: false, data: null })
}))
