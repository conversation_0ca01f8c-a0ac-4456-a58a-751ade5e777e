import { Body, Controller, Get, Param, Patch, Post, Query } from '@nestjs/common';
import { TaskTypeService } from './task-type.service';
import { CreateTaskTypeDto, UpdateTaskTypeDto } from './dto/task-type.dto';
import { Status } from '@prisma/client';
import { ApiResponse } from '../../helpers/apiRespons';
import { ProtectedRoute } from '../../common';

@Controller('task-type')
export class TaskTypeController {
  constructor(private readonly taskTypeService: TaskTypeService) {}

  @ProtectedRoute({
    isPublic: false,
  })
  @Post()
  async create(@Body() createTaskTypeDto: CreateTaskTypeDto) {
    return await this.taskTypeService.create(createTaskTypeDto);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Get()
  async findAll(@Query('page') page?: string, @Query('limit') limit?: string, @Query('search') search?: string) {
    return await this.taskTypeService.findAll(page, limit, search);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Get('search')
  async findByName(@Query('name') name: string) {
    return await this.taskTypeService.findByName(name);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Get(':id')
  async findOne(@Param('id') id: string) {
    return await this.taskTypeService.findOne(id);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateTaskTypeDto: UpdateTaskTypeDto) {
    return await this.taskTypeService.update(id, updateTaskTypeDto);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Patch(':id/status')
  async changeStatus(@Param('id') id: string, @Body('status') status: Status) {
    return await this.taskTypeService.changeStatus(id, status);
  }
}
