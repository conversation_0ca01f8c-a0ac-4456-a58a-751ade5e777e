import { ArgumentsHost, Catch, HttpException, HttpServer, HttpStatus } from '@nestjs/common';
import { APP_FILTER, BaseExceptionFilter, HttpAdapterHost } from '@nestjs/core';
import { Prisma } from '@prisma/client';

export declare type GqlContextType = 'graphql';

export type ErrorCodesStatusMapping = {
  [key: string]:
    | number
    | {
        statusCode?: number;
        message?: string;
      };
};

/**
 * {@link PrismaClientExceptionFilter} catches {@link Prisma.PrismaClientKnownRequestError} exceptions.
 */
@Catch(Prisma?.PrismaClientKnownRequestError)
export class PrismaClientExceptionFilter extends BaseExceptionFilter {
  /**
   * default error codes mapping
   *
   * Error codes definition for Prisma Client (Query Engine)
   * @see https://www.prisma.io/docs/reference/api-reference/error-reference#prisma-client-query-engine
   */
  private readonly defaultMapping = {
    // Query Engine Errors - Request related
    P2000: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: "Ma'lumot juda uzun",
    },
    P2001: {
      statusCode: HttpStatus.NOT_FOUND,
      error: 'Not Found',
      message: 'Qidirish shartlari bilan yozuv topilmadi',
    },
    P2002: {
      statusCode: HttpStatus.CONFLICT,
      error: 'Conflict',
      message: 'Noyob cheklov buzildi',
    },
    P2003: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: 'Tashqi kalit cheklovi buzildi',
    },
    P2004: {
      statusCode: HttpStatus.CONFLICT,
      error: 'Conflict',
      message: 'Cheklov buzildi',
    },
    P2005: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: "Qiymat turi ma'lumotlar bazasi turidan farq qiladi",
    },
    P2006: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: "Ma'lumot turini o'zgartirish jarayonida xatolik yuz berdi",
    },
    P2007: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: "Ma'lumotlarni validatsiya qilishda xatolik",
    },
    P2008: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: "So'rovda xatolik mavjud",
    },
    P2009: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: "So'rovda xatolik. Ma'lumot validatsiyasi muvaffaqiyatsiz",
    },
    P2010: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: "So'rovda xatolik. Raw query bajarilishida xatolik",
    },
    P2011: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: 'Null qiymat kiritilishi mumkin emas',
    },
    P2012: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: "Zaruriy maydonlarni to'ldirish kerak",
    },
    P2013: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: "Maydon birgalikda noyob bo'lishi kerak",
    },
    P2014: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: 'Munosabatlar orasida ziddiyat mavjud',
    },
    P2015: {
      statusCode: HttpStatus.NOT_FOUND,
      error: 'Not Found',
      message: 'Munosabat yozuvi topilmadi',
    },
    P2016: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: "So'rov interpretatsiyasida xatolik",
    },
    P2017: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: "Munosabatlar o'rtasida aloqa yo'q",
    },
    P2018: {
      statusCode: HttpStatus.NOT_FOUND,
      error: 'Not Found',
      message: 'Munosabat topilmadi',
    },
    P2019: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: "Kirish ma'lumoti raqam bo'lishi kerak",
    },
    P2020: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: "Yozuvni yangilash yoki o'chirish uchun yozuv topilmadi",
    },
    P2021: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: 'Jadval mavjud emas',
    },
    P2022: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: 'Ustun mavjud emas',
    },
    P2023: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: 'Qiymatlarni taqqoslashda xatolik',
    },
    P2024: {
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      error: 'Internal Server Error',
      message: 'Baza bilan ulanish vaqti tugadi',
    },
    P2025: {
      statusCode: HttpStatus.NOT_FOUND,
      error: 'Not Found',
      message: 'Yozuv topilmadi',
    },
    P2026: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: "So'rovda xatolik mavjud",
    },
    P2027: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: 'Multiple errors occurred',
    },
    P2028: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: 'Transaction commit failed',
    },
    P2030: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: "Transaction qo'llab-quvvatlanmaydi",
    },
    P2033: {
      statusCode: HttpStatus.BAD_REQUEST,
      error: 'Bad Request',
      message: "Ma'lumot qayta o'zgartirishda xatolik",
    },
    P2034: {
      statusCode: HttpStatus.GATEWAY_TIMEOUT,
      error: 'Gateway Timeout',
      message: 'Transaction vaqti tugadi',
    },

    // Migration Engine errors
    P3000: {
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      error: 'Internal Server Error',
      message: 'Migration engine xatoligi',
    },
    P3001: {
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      error: 'Internal Server Error',

      message: "Migration bo'lmagan ma'lumotlar bazasiga ulanish mumkin emas",
    },
    P3002: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.CONFLICT,
      message: "Migration allaqachon qo'llanilgan",
    },
    P3003: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.CONFLICT,
      message: "Migration formati noto'g'ri",
    },
    P3004: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: "Migration faylini yaratib bo'lmadi",
    },
    P3005: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: "Ma'lumotlar yo'qolishi mumkin",
    },
    P3006: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: "Migration shadow ma'lumotlar bazasini yaratishda xatolik",
    },
    P3007: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: 'Ketma-ketlik buzilgan',
    },
    P3008: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: "Migration faylini o'chirishda xatolik",
    },
    P3009: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: 'Migration generate qilishda xatolik',
    },
    P3010: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: 'Migration nomini aniqlashda xatolik',
    },
    P3011: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: 'Migration engine ishga tushirishda xatolik',
    },
    P3012: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: "Ma'lumotlar bazasi sxemasi boshqa manbaga bog'liq",
    },
    P3013: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: "Ma'lumotlar bazasi ulanishi sozlanmagan",
    },
    P3014: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: "Prisma schema faylini o'qib bo'lmadi",
    },
    P3015: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: "Ma'lumotlar bazasi provayderini aniqlashda xatolik",
    },

    // Query Engine errors - Common
    P4000: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: 'Introspection engine xatoligi',
    },
    P4001: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: "Ma'lumotlar bazasi versiyasi qo'llab-quvvatlanmaydi",
    },
    P4002: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: 'Sxema tahlil qilishda xatolik',
    },

    // Database connector errors
    P5000: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: "Ma'lumotlar bazasi ulanishida xatolik",
    },
    P5001: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: "Ma'lumotlar bazasi topilmadi",
    },
    P5002: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: 'Kirish huquqi rad etildi',
    },
    P5003: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: "Ma'lumotlar bazasi allaqachon mavjud",
    },
    P5004: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: "Ma'lumotlar bazasi bog'lash ulanishi uzildi",
    },
    P5010: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: "Ma'lumotlar bazasi serverini ulashda xatolik",
    },
    P5011: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: "Ma'lumotlar bazasi faylini ochishda xatolik",
    },
    P5013: {
      error: 'Internal Server Error',
      statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      message: "Ma'lumotlar bazasi uchun hisob ma'lumotlari noto'g'ri",
    },
  };

  private readonly userDefinedMapping?: ErrorCodesStatusMapping;

  /**
   * @param applicationRef
   * @param errorCodesStatusMapping
   */
  constructor(applicationRef?: HttpServer, errorCodesStatusMapping?: ErrorCodesStatusMapping) {
    super(applicationRef);

    // use custom error codes mapping (overwrite)
    //
    // @example:
    //
    //   const { httpAdapter } = app.get(HttpAdapterHost);
    //   app.useGlobalFilters(new PrismaClientExceptionFilter(httpAdapter, {
    //     P2022: HttpStatus.BAD_REQUEST,
    //   }));
    //
    //   or
    //
    //   const { httpAdapter } = app.get(HttpAdapterHost);
    //   app.useGlobalFilters(new PrismaClientExceptionFilter(httpAdapter, {
    //     // You can omit either statusCode or errorMessage so that the default one is used.
    //     P2022: { statusCode: HttpStatus.BAD_REQUEST, errorMessage: "bad request" },
    //   }));
    //
    this.userDefinedMapping = errorCodesStatusMapping;
  }

  /**
   * @param exception
   * @param host
   * @returns
   */
  catch(exception: Prisma.PrismaClientKnownRequestError, host: ArgumentsHost) {
    return this.catchClientKnownRequestError(exception, host);
  }

  private catchClientKnownRequestError(exception: Prisma.PrismaClientKnownRequestError, host: ArgumentsHost) {
    const statusCode = this.userDefinedStatusCode(exception) || this.getDefaultStatusCode(exception);

    const message = this.userDefinedExceptionMessage(exception) || this.getDefaultExceptionMessage(exception);

    if (host.getType() === 'http') {
      if (statusCode === undefined) {
        return super.catch(exception, host);
      }

      return super.catch(new HttpException({ statusCode, message }, statusCode), host);
    } else if (host.getType<GqlContextType>() === 'graphql') {
      // for graphql requests
      if (statusCode === undefined) {
        return exception;
      }

      return new HttpException({ statusCode, message }, statusCode);
    }
  }

  private userDefinedStatusCode(exception: Prisma.PrismaClientKnownRequestError): number | undefined {
    const userDefinedValue = this.userDefinedMapping?.[exception.code];
    return typeof userDefinedValue === 'number' ? userDefinedValue : userDefinedValue?.statusCode;
  }

  private getDefaultStatusCode(exception: Prisma.PrismaClientKnownRequestError): number | undefined {
    const mapping = this.defaultMapping[exception.code];
    return typeof mapping === 'number' ? mapping : mapping?.statusCode;
  }

  private userDefinedExceptionMessage(exception: Prisma.PrismaClientKnownRequestError): string | undefined {
    const userDefinedValue = this.userDefinedMapping?.[exception.code];
    return typeof userDefinedValue === 'number' ? undefined : userDefinedValue?.message;
  }

  private getDefaultExceptionMessage(exception: Prisma.PrismaClientKnownRequestError): string {
    const defaultMessage =
      typeof this.defaultMapping[exception.code] === 'object' ? this.defaultMapping[exception.code].message : undefined;

    if (defaultMessage) {
      return `[${exception.code}]: ${defaultMessage}`;
    }

    const shortMessage = exception.message.substring(exception.message.indexOf('→'));
    return `[${exception.code}]: ` + shortMessage.substring(shortMessage.indexOf('\n')).replace(/\n/g, '').trim();
  }
}

export function providePrismaClientExceptionFilter(errorCodesStatusMapping?: ErrorCodesStatusMapping) {
  return {
    provide: APP_FILTER,
    useFactory: ({ httpAdapter }: HttpAdapterHost) => {
      return new PrismaClientExceptionFilter(httpAdapter, errorCodesStatusMapping);
    },
    inject: [HttpAdapterHost],
  };
}
