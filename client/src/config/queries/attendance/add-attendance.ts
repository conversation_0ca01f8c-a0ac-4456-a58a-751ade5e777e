import { axiosPrivate } from '@/config/api'
import { attendanceUserEndpoints } from '@/config/api/endpoints'
import { useMutation } from '@tanstack/react-query'
import { notification } from 'antd'
import type { Dayjs } from 'dayjs'

type TAddAttendance = {
	userId: string
	time: Dayjs
}

export const useAddAttendance = () => {
	return useMutation({
		mutationFn: async (data: TAddAttendance) => {
			return await axiosPrivate.post(attendanceUserEndpoints.addAttendance, data)
		},
		onSuccess: () => {
			notification.success({ message: 'Davomat muaffaqiyatli yaratildi!' })
		},
		onError: () => {
			notification.error({ message: 'Davomat yaratishda xatolik yuz berdi!' })
		}
	})
}
