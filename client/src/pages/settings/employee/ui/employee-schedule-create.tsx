import { Form, Button, Select, Modal } from 'antd'
import { useCreateUserWorkingSchedule } from '@/config/queries/user-working-schedule/create.queries'
import { useGetUserWorkingScheduleDay } from '@/config/queries/user-working-schedule-day/get.queries'
import { useGetUsersByOrgId } from '@/config/queries/organizations/get-all.queries'
import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { useCallback, useEffect, useState } from 'react'
import { UserWorkingScheduleTypes } from '@/config/queries/user-working-schedule-day/create.queries'
import { useGetUserWorkingScheduleById } from '@/config/queries/user-working-schedule/getById.queries'
import { useUpdateUserWorkingSchedule } from '@/config/queries/user-working-schedule/update.queries'
import { debounce } from 'lodash'

interface EmployeeScheduleCreateProps {
	open: boolean
	onClose: () => void
	employeeId: string | null
	record?: {
		id: string
		userId: string
	} | null
}

const EmployeeScheduleCreate = ({
	open,
	onClose,
	employeeId,
	record
}: EmployeeScheduleCreateProps) => {
	const [form] = Form.useForm()
	const { data: me } = useAuthMe()
	const organizationId = me?.Organization?.id || me?.MainOrganization?.id
	const { data: employees } = useGetUsersByOrgId(organizationId || '')
	const { data: days, fetchNextPage } = useGetUserWorkingScheduleDay()
	const assignScheduleMutation = useCreateUserWorkingSchedule()
	const updateScheduleMutation = useUpdateUserWorkingSchedule()
	const { mutateAsync: getSchedules } = useGetUserWorkingScheduleById()
	const [scheduleDays, setScheduleDays] = useState<string[]>([])

	useEffect(() => {
		if (open && employeeId) {
			form.setFieldsValue({
				userId: employeeId
			})
		}
	}, [open, employeeId, form])

	useEffect(() => {
		if (record) {
			getSchedules(record.id).then(res => {
				setScheduleDays(res.days.map((item: any) => item.id))
			})
		}
	}, [record, getSchedules])

	useEffect(() => {
		form.setFieldValue('days', scheduleDays)
	}, [scheduleDays, form])

	const handleSubmit = (values: UserWorkingScheduleTypes) => {
		const payload: UserWorkingScheduleTypes = {
			userId: values.userId,
			days: values.days,
			name: values.name,
			day: values.day,
			startTime: values.startTime,
			endTime: values.endTime
		}

		if (record) {
			updateScheduleMutation.mutate(
				{ data: payload, scheduleId: record.id },
				{
					onSuccess: () => {
						onClose()
						form.resetFields()
					}
				}
			)
		} else {
			assignScheduleMutation.mutate(payload, {
				onSuccess: () => {
					onClose()
					form.resetFields()
				}
			})
		}
	}

	const employeeOptions =
		employees?.pages?.reduce((acc: any[], page: any) => {
			if (Array.isArray(page)) {
				return [
					...acc,
					...page.map((employee: { fullName: any; id: any }) => ({
						label: employee.fullName,
						value: employee.id
					}))
				]
			}
			return acc
		}, []) || []

	const handlePopupScroll = useCallback(
		debounce((e: React.UIEvent<HTMLDivElement>) => {
			const target = e.target as HTMLDivElement
			const isNearBottom = target.scrollTop + target.offsetHeight >= target.scrollHeight - 10
			if (isNearBottom) {
				fetchNextPage()
			}
		}, 100),
		[]
	)

	return (
		<Modal
			title='Ishchi jadvalini yaratish'
			open={open}
			onCancel={onClose}
			footer={null}
			width={600}>
			<Form
				form={form}
				layout='vertical'
				onFinish={handleSubmit}>
				<Form.Item
					name='userId'
					label='Xodimlar'
					rules={[{ required: true, message: 'Iltimos, xodimni tanlang' }]}>
					<Select
						placeholder='Xodimni tanlang'
						style={{ width: '100%' }}
						options={employeeOptions}
					/>
				</Form.Item>
				<Form.Item
					name='days'
					label='Ish kunlari'
					rules={[{ required: true, message: 'Iltimos, kamida bir ish kunini tanlang' }]}>
					<Select
						mode='multiple'
						placeholder='Ish kunlarini tanlang'
						style={{ width: '100%' }}
						options={days?.pages?.[0]?.data?.sort((a, b) => a.day - b.day)}
						fieldNames={{ label: 'name', value: 'id' }}
						onPopupScroll={handlePopupScroll}
					/>
				</Form.Item>

				<Form.Item>
					<Button
						type='primary'
						htmlType='submit'
						loading={assignScheduleMutation.isPending}>
						Saqlash
					</Button>
				</Form.Item>
			</Form>
		</Modal>
	)
}
export default EmployeeScheduleCreate
