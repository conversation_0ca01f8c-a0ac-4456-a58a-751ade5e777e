import { TaskTypes, useCreateTaskType } from '@/config/queries/task-types/create.queries'
import { useUpdateTaskType } from '@/config/queries/task-types/update.queries'
import { Button, Form, Modal } from 'antd'
import { useParams } from 'react-router-dom'
import { useTaskTypesModalStore } from '../utils/task-types-store'
import { useEffect } from 'react'
import TaskTypesForm from './task-types-form'
import { PlusOutlined } from '@ant-design/icons'

const TaskTypesModal = () => {
	const { data, open, onClose } = useTaskTypesModalStore()
	const [form] = Form.useForm()
	const create = useCreateTaskType()
	const update = useUpdateTaskType()
	const { id } = useParams()

	const close = () => {
		form.resetFields()
		onClose()
	}

	useEffect(() => {
		if (data) {
			form.setFieldsValue(data)
		}
	}, [data, form])

	const onFinish = (formData: TaskTypes) => {
		if (!data) {
			create.mutateAsync({ id: id!, ...formData }).then(close)
		} else {
			if (data.id) {
				update.mutateAsync({ id: data.id, ...formData }).then(close)
			}
		}
	}

	return (
		<div className='flex items-center justify-end'>
			<Button
				type='primary'
				icon={<PlusOutlined />}
				onClick={() => useTaskTypesModalStore.setState({ open: true })}>
				Topshiriq turlari qo'shish
			</Button>
			<Modal
				open={open}
				onCancel={close}
				destroyOnClose
				onOk={form.submit}
				title={`Topshiriq turi${data ? 'ni tahrirlash' : "ni qo'shish"}`}
				okText={data ? 'Tahrirlash' : "Qo'shish"}
				cancelText='Bekor qilish'
				modalRender={node => (
					<Form
						layout={'vertical'}
						size='large'
						onFinish={onFinish}
						form={form}>
						{node}
					</Form>
				)}>
				<TaskTypesForm />
			</Modal>
		</div>
	)
}
export default TaskTypesModal
