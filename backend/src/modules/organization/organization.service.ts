import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/common';
import { PaginationUtil } from 'src/common/utils/pagination.util';
import { QueryUtil } from 'src/common/utils/query.util';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';
import { Prisma, Status } from '@prisma/client';

interface GetAllParams {
  organizationId?: string;
  search?: string;
  page?: string;
  limit?: string;
}

interface GetAllParam {
  organizationId?: string;
  search?: string;
  page?: string;
  limit?: string;
}
@Injectable()
export class OrganizationService {
  constructor(
    private prismaService: PrismaService,
    private pagination: PaginationUtil,
    private queryUtil: QueryUtil,
  ) {}

  async getAll(params: GetAllParams) {
    const { search, page, limit, organizationId } = params;

    const $page = page ? parseInt(page) : 1;
    const $limit = limit ? parseInt(limit) : 10;
    const where = this.queryUtil.createSearchQuery(search, ['name', 'description', 'address', 'phone', 'id']);

    const organization = await this.prismaService.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization topilmadi');
    }

    const result = await this.pagination.paginate<any>(
      this.prismaService.organization,
      { page: $page, limit: $limit },
      { parentId: organizationId, ...where, status: Status.ACTIVE },
      { Worker: true, Employee: true, Responsible: true },
    );

    const cleaned = result.data.map((org) => {
      const allUsers = [...org.Worker, ...org.Employee, ...org.Responsible];

      const uniqueUsers = allUsers.filter((user, index, self) => index === self.findIndex((u) => u.id === user.id));

      return {
        ...org,
        userCount: uniqueUsers.length,
        Worker: undefined,
        Employee: undefined,
        Responsible: undefined,
      };
    });
    return {
      ...result,
      data: cleaned,
    };
  }

  async getControlledBy(parentId: string) {
    return this.prismaService.organization.findMany({
      where: {
        controlledById: parentId,
      },
    });
  }

  async getChildren(parentId: string) {
    const organizations = await this.prismaService.organization.findMany({
      where: { parentId },
    });

    if (!organizations) {
      throw new NotFoundException('Organization not found');
    }

    return organizations;
  }

  async getUnderControl(params: GetAllParams) {
    const { search, page, limit, organizationId } = params;

    const $page = page ? parseInt(page) : 1;
    const $limit = limit ? parseInt(limit) : 10;

    const where = this.queryUtil.createSearchQuery(search, ['name', 'description', 'address', 'phone', 'id']);

    const model = this.prismaService.organization;

    const withPagination = this.pagination.paginate(
      model,
      { page: $page, limit: $limit },
      { controlledById: organizationId, ...where },
      {
        UnderControl: true,
        Parent: true,
        Children: true,
        type: true,
        ControlledBy: true,
        region: true,
        district: true,
        section: true,
      },
    );

    return withPagination;
  }

  async getByWorker(id: string) {
    const workers = await this.prismaService.organization.findFirst({ where: { id }, select: { Worker: true } });

    if (!workers) throw new NotFoundException('Worker not found');

    return workers;
  }

  // async getOne(id: string) {
  //   const organization = await this.prismaService.organization.findUnique({
  //     where: { id, status: 'ACTIVE' },
  //   });

  //   if (!organization) {
  //     throw new NotFoundException('Organization not found');
  //   }

  //   const data = await this.prismaService.organization.findUnique({
  //     where: { id, status: Status.ACTIVE },
  //     select: {
  //       Children: {
  //         where: {
  //           status: 'ACTIVE',
  //         },
  //         select: {
  //           id: true,
  //           name: true,
  //           phone: true,
  //           address: true,
  //           status: true,
  //           grade: {
  //             select: {
  //               id: true,
  //               name: true,
  //             },
  //           },
  //           Worker: {
  //             select: {
  //               id: true,
  //             },
  //           },
  //         },
  //       },
  //       UnderControl: {
  //         where: {
  //           status: 'ACTIVE',
  //         },
  //         select: {
  //           id: true,
  //           name: true,
  //           phone: true,
  //           address: true,
  //           grade: {
  //             select: {
  //               id: true,
  //               name: true,
  //             },
  //           },
  //           Worker: {
  //             select: {
  //               id: true,
  //             },
  //           },
  //         },
  //       },
  //       Parent: true,
  //       region: {
  //         select: {
  //           id: true,
  //           name: true,
  //         },
  //       },
  //       district: {
  //         select: {
  //           id: true,
  //           name: true,
  //         },
  //       },
  //       section: {
  //         select: {
  //           id: true,
  //           name: true,
  //         },
  //       },
  //       FaceIDDevice: true,
  //       grade: true,
  //       type: {
  //         select: {
  //           id: true,
  //           name: true,
  //         },
  //       },
  //     },
  //   });

  //   return {
  //     ...data,
  //     Children: data.Children.map((d) => {
  //       const { Worker, ...rest } = d;
  //       return {
  //         ...rest,
  //         workerCount: d.Worker.length,
  //       };
  //     }),
  //     UnderControl: data.UnderControl.map((d) => {
  //       const { Worker, ...rest } = d;
  //       return {
  //         ...rest,
  //         workerCount: d.Worker.length,
  //       };
  //     }),
  //   };
  // }

  // async getOne(id: string) {
  //   const organization = await this.prismaService.organization.findUnique({
  //     where: { id, status: 'ACTIVE' },
  //   });

  //   if (!organization) {
  //     throw new NotFoundException('Organization not found');
  //   }

  //   const data = await this.prismaService.organization.findUnique({
  //     where: { id, status: Status.ACTIVE },
  //     select: {
  //       name: true,
  //       address: true,
  //       Children: {
  //         where: {
  //           status: 'ACTIVE',
  //         },
  //         select: {
  //           id: true,
  //           name: true,
  //           phone: true,
  //           address: true,
  //           status: true,
  //           typeId: true,
  //           description: true,
  //           sector: true,
  //           sectorResponsible: true,
  //           regionId: true,
  //           districtId: true,
  //           sectionId: true,
  //           gradeId: true,
  //           grade: {
  //             select: {
  //               id: true,
  //               name: true,
  //             },
  //           },
  //           Worker: {
  //             select: {
  //               id: true,
  //             },
  //           },
  //         },
  //       },
  //       UnderControl: {
  //         where: {
  //           status: 'ACTIVE',
  //         },
  //         select: {
  //           id: true,
  //           name: true,
  //           phone: true,
  //           address: true,
  //           grade: {
  //             select: {
  //               id: true,
  //               name: true,
  //             },
  //           },
  //           Worker: {
  //             select: {
  //               id: true,
  //             },
  //           },
  //         },
  //       },
  //       Parent: true,
  //       region: {
  //         select: {
  //           id: true,
  //           name: true,
  //         },
  //       },
  //       district: {
  //         select: {
  //           id: true,
  //           name: true,
  //         },
  //       },
  //       section: {
  //         select: {
  //           id: true,
  //           name: true,
  //         },
  //       },
  //       FaceIDDevice: true,
  //       grade: true,
  //       type: {
  //         select: {
  //           id: true,
  //           name: true,
  //           OrganizationTypePosition: {
  //             select: {
  //               id: true,
  //               name: true,
  //               description: true,
  //               status: true,
  //             },
  //           },
  //         },
  //       },
  //     },
  //   });

  //   return {
  //     ...data,
  //     Children: data.Children.map((d) => {
  //       const { Worker, ...rest } = d;
  //       return {
  //         ...rest,
  //         workerCount: d.Worker.length,
  //       };
  //     }),
  //     UnderControl: data.UnderControl.map((d) => {
  //       const { Worker, ...rest } = d;
  //       return {
  //         ...rest,
  //         workerCount: d.Worker.length,
  //       };
  //     }),
  //   };
  // }

  async getOne(id: string) {
    const organization = await this.prismaService.organization.findUnique({
      where: { id, status: 'ACTIVE' },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const baseData = await this.prismaService.organization.findUnique({
      where: { id },
      select: {
        name: true,
        address: true,
        Children: {
          where: {
            status: 'ACTIVE',
          },
          select: {
            id: true,
            name: true,
            phone: true,
            address: true,
            status: true,
            typeId: true,
            description: true,
            sector: true,
            sectorResponsible: true,
            regionId: true,
            districtId: true,
            sectionId: true,
            gradeId: true,
            grade: {
              select: {
                id: true,
                name: true,
              },
            },
            Worker: {
              select: {
                id: true,
              },
            },
          },
        },
        Parent: true,
        region: {
          select: {
            id: true,
            name: true,
          },
        },
        district: {
          select: {
            id: true,
            name: true,
          },
        },
        section: {
          select: {
            id: true,
            name: true,
          },
        },
        FaceIDDevice: true,
        grade: true,
        type: {
          select: {
            id: true,
            name: true,
            OrganizationTypePosition: {
              select: {
                id: true,
                name: true,
                description: true,
                status: true,
              },
            },
          },
        },
      },
    });

    const underControls = await this.prismaService.organization.findMany({
      where: {
        controlledById: id,
        status: 'ACTIVE',
      },
      select: {
        id: true,
        name: true,
        phone: true,
        address: true,
        grade: {
          select: {
            id: true,
            name: true,
          },
        },
        Worker: {
          select: {
            id: true,
          },
        },
      },
    });

    return {
      ...baseData,
      Children: baseData.Children.map((child) => ({
        ...child,
        workerCount: child.Worker.length,
      })),
      UnderControl: underControls.map((org) => ({
        ...org,
        workerCount: org.Worker.length,
      })),
    };
  }

  async create(data: CreateOrganizationDto) {
    return this.prismaService.organization.create({
      data: {
        name: data.name,
        description: data.description,
        regionId: data.regionId,
        districtId: data.districtId,
        sectionId: data.sectionId,
        address: data.address,
        phone: data.phone,
        sector: data.sector,
        sectorResponsible: data.sectorResponsible,
        typeId: data.typeId,
        gradeId: data.gradeId,
      },
    });
  }

  async update(id: string, data: UpdateOrganizationDto) {
    const organization = await this.prismaService.organization.findUnique({
      where: { id },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    return this.prismaService.organization.update({
      where: { id },
      data: {
        name: data.name,
        description: data.description,
        regionId: data.regionId,
        districtId: data.districtId,
        sectionId: data.sectionId,
        address: data.address,
        phone: data.phone,
        sector: data.sector,
        sectorResponsible: data.sectorResponsible,
        typeId: data.typeId,
        gradeId: data.gradeId,
      },
    });
  }

  async addWorkers(organizationId: string, usersId: string[]) {
    const organization = await this.prismaService.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const isExistsUsers = await this.prismaService.user.findMany({
      where: {
        id: {
          in: usersId,
        },
      },
      select: {
        id: true,
      },
    });

    if (isExistsUsers.length !== usersId.length) {
      throw new NotFoundException('Some users were not found');
    }

    await this.prismaService.organization.update({
      where: { id: organizationId },
      data: {
        Worker: {
          connect: usersId.map((id) => ({ id })),
        },
      },
    });

    return {
      message: 'Workers added successfully',
    };
  }

  async removeWorkers(organizationId: string, usersId: string[]) {
    const organization = await this.prismaService.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const isExistsUsers = await this.prismaService.user.findMany({
      where: {
        id: {
          in: usersId,
        },
      },
      select: {
        id: true,
      },
    });

    if (isExistsUsers.length !== usersId.length) {
      throw new NotFoundException('Some users were not found');
    }

    await this.prismaService.organization.update({
      where: { id: organizationId },
      data: {
        Worker: {
          disconnect: usersId.map((id) => ({ id })),
        },
        Responsible: {
          disconnect: usersId.map((id) => ({ id })),
        },
      },
    });

    return {
      message: 'Workers removed successfully',
    };
  }

  async addEmployees(organizationId: string, usersId: string[]) {
    const organization = await this.prismaService.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const isExistsUsers = await this.prismaService.user.findMany({
      where: {
        id: {
          in: usersId,
        },
      },
      select: {
        id: true,
      },
    });

    if (isExistsUsers.length !== usersId.length) {
      throw new NotFoundException('Some users were not found');
    }

    await this.prismaService.organization.update({
      where: { id: organizationId },
      data: {
        Employee: {
          connect: usersId.map((id) => ({ id })),
        },
      },
    });

    return {
      message: 'Employees added successfully',
    };
  }

  async removeEmployees(organizationId: string, usersId: string[]) {
    const organization = await this.prismaService.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const isExistsUsers = await this.prismaService.user.findMany({
      where: {
        id: {
          in: usersId,
        },
      },
      select: {
        id: true,
      },
    });

    if (isExistsUsers.length !== usersId.length) {
      throw new NotFoundException('Some users were not found');
    }

    await this.prismaService.organization.update({
      where: { id: organizationId },
      data: {
        Employee: {
          disconnect: usersId.map((id) => ({ id })),
        },
      },
    });

    return {
      message: 'Employees removed successfully',
    };
  }

  async addResponsibles(organizationId: string, usersId: string[]) {
    const organization = await this.prismaService.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const isExistsUsers = await this.prismaService.user.findMany({
      where: {
        id: {
          in: usersId,
        },
      },
      select: {
        id: true,
      },
    });

    if (isExistsUsers.length !== usersId.length) {
      throw new NotFoundException('Some users were not found');
    }

    await this.prismaService.organization.update({
      where: { id: organizationId },
      data: {
        Responsible: {
          connect: usersId.map((id) => ({ id })),
        },
      },
    });

    return {
      message: 'Responsibles added successfully',
    };
  }

  async removeResponsibles(organizationId: string, usersId: string[]) {
    const organization = await this.prismaService.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const isExistsUsers = await this.prismaService.user.findMany({
      where: {
        id: {
          in: usersId,
        },
      },
      select: {
        id: true,
      },
    });

    if (isExistsUsers.length !== usersId.length) {
      throw new NotFoundException('Some users were not found');
    }

    await this.prismaService.organization.update({
      where: { id: organizationId },
      data: {
        Responsible: {
          disconnect: usersId.map((id) => ({ id })),
        },
      },
    });

    return {
      message: 'Responsibles removed successfully',
    };
  }

  async createChildren(parentId: string, data: CreateOrganizationDto) {
    const parent = await this.prismaService.organization.findUnique({
      where: { id: parentId },
    });

    if (!parent) {
      throw new NotFoundException('Parent organization not found');
    }

    return this.prismaService.organization.create({
      data: {
        name: data.name,
        description: data.description,
        regionId: data.regionId,
        districtId: data.districtId,
        sectionId: data.sectionId,
        address: data.address,
        phone: data.phone,
        sector: data.sector,
        sectorResponsible: data.sectorResponsible,
        typeId: data.typeId,
        gradeId: data.gradeId,
        parentId,
      },
    });
  }

  async addUnderControls(parentId: string, organizationsId: string[]) {
    const parent = await this.prismaService.organization.findUnique({
      where: { id: parentId },
    });

    if (!parent) {
      throw new NotFoundException('Parent organization not found');
    }

    const isExistsOrganizations = await this.prismaService.organization.findMany({
      where: {
        id: {
          in: organizationsId,
        },
      },
    });

    if (isExistsOrganizations.length !== organizationsId.length) {
      throw new NotFoundException('Some organizations were not found');
    }

    await this.prismaService.organization.update({
      where: { id: parentId },
      data: {
        UnderControl: {
          connect: organizationsId.map((id) => ({ id })),
        },
      },
    });

    return {
      message: 'Organizations added successfully',
    };
  }

  // async removeUnderControls(parentId: string, organizationsId: string[]) {
  //   const parent = await this.prismaService.organization.findUnique({
  //     where: { id: parentId },
  //   });

  //   if (!parent) {
  //     throw new NotFoundException('Parent organization not found');
  //   }

  //   const isExistsOrganizations = await this.prismaService.organization.findMany({
  //     where: {
  //       id: {
  //         in: organizationsId,
  //       },
  //     },
  //   });

  //   if (isExistsOrganizations.length !== organizationsId.length) {
  //     throw new NotFoundException('Some organizations were not found');
  //   }

  //   await this.prismaService.organization.update({
  //     where: { id: parentId },
  //     data: {
  //       UnderControl: {
  //         disconnect: organizationsId.map((id) => ({ id })),
  //       },
  //     },
  //   });

  //   return {
  //     message: 'Organizations removed successfully',
  //   };
  // }

  async removeUnderControls(parentId: string, organizationsId: string[]) {
    const parent = await this.prismaService.organization.findUnique({
      where: { id: parentId },
    });

    if (!parent) {
      throw new NotFoundException('Parent organization not found');
    }

    const isExistsOrganizations = await this.prismaService.organization.findMany({
      where: {
        id: {
          in: organizationsId,
        },
      },
    });

    if (isExistsOrganizations.length !== organizationsId.length) {
      throw new NotFoundException('Some organizations were not found');
    }

    const disconnectResult = await this.prismaService.organization.update({
      where: { id: parentId },
      data: {
        UnderControl: {
          disconnect: organizationsId.map((id) => ({ id })),
        },
      },
    });

    const updateManyResult = await this.prismaService.organization.updateMany({
      where: {
        id: { in: organizationsId },
        controlledById: parentId,
      },
      data: {
        controlledById: null,
      },
    });

    return {
      message: 'Organizations removed from under control successfully',
    };
  }

  async getUsersByOrganization(
    orgId: string,
    role?: ('worker' | 'employee' | 'responsible')[],
    page?: string,
    limit?: string,
    search?: string,
  ) {
    const where: Prisma.UserWhereInput = {};

    // Поиск
    if (search) {
      where.AND = [
        {
          OR: [
            { phone: { mode: 'insensitive', contains: search } },
            { username: { mode: 'insensitive', contains: search } },
            { fullName: { mode: 'insensitive', contains: search } },
            { id: { mode: 'insensitive', contains: search } },
          ],
        },
      ];
    }

    // Фильтрация по ролям
    const roleFilters: Prisma.UserWhereInput[] = [];

    if (role?.includes('responsible')) {
      roleFilters.push({ ResponsibleFor: { some: { id: orgId } } });
    }

    if (role?.includes('employee')) {
      roleFilters.push({ mainOrganizationId: orgId });
    }

    if (role?.includes('worker')) {
      roleFilters.push({ Organization: { some: { id: orgId } } });
    }

    // Если роли не указаны — искать по всем
    if (!role || role.length === 0) {
      roleFilters.push(
        { ResponsibleFor: { some: { id: orgId } } },
        { mainOrganizationId: orgId },
        { Organization: { some: { id: orgId } } },
      );
    }

    if (roleFilters.length > 0) {
      where.AND = [...(Array.isArray(where.AND) ? where.AND : where.AND ? [where.AND] : []), { OR: roleFilters }];
    }

    const $page = Number(page) || 1;
    const $limit = Number(limit) || 10;

    const users = await this.prismaService.user.findMany({
      where,
      skip: ($page - 1) * $limit,
      take: $limit,
      // include: {
      //   Organization: true,
      //   ResponsibleFor: true,
      //   MainOrganization: true,
      // },
      select: {
        id: true,
        fullName: true,
        username: true,
        phone: true,
        status: true,
        avatarId: true,
        faceIdImageId: true,
        imei: true,
        createdAt: true,
        updatedAt: true,
        role: true,
        mainOrganizationId: true,
        userData: true,
        position: {
          select: {
            id: true,
            name: true,
          },
        },
        GPSLocationReport: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
          select: {
            id: true,
            createdAt: true,
            type: true,
          },
        },
        Attendance: {
          orderBy: {
            createdAt: 'desc',
          },
          take: 1,
          select: {
            id: true,
            createdAt: true,
            type: true,
          },
        },
        ResponsibleFor: {
          where: { id: orgId },
          select: { id: true },
        },
        Organization: {
          where: { id: orgId },
          select: {
            id: true,
            name: true,
            status: true,
            description: true,
            address: true,
            phone: true,
            typeId: true,
            controlledById: true,
            parentId: true,
            sector: true,
            sectorResponsible: true,
            gradeId: true,
            regionId: true,
            districtId: true,
            sectionId: true,
            createdAt: true,
            updatedAt: true,
          },
        },
      },
    });

    const total = await this.prismaService.user.count({ where });

    // Добавляем roleTypes каждому пользователю
    const usersWithRoles = users.map((user) => {
      const roleTypes: ('worker' | 'employee' | 'responsible')[] = [];

      if (user.ResponsibleFor?.some((org) => org.id === orgId)) {
        roleTypes.push('responsible');
      }
      if (user.mainOrganizationId === orgId) {
        roleTypes.push('employee');
      }
      if (user.Organization?.some((org) => org.id === orgId)) {
        roleTypes.push('worker');
      }

      return {
        ...user,
        roleTypes,
        ResponsibleFor: undefined,
        MainOrganization: undefined,
      };
    });

    return {
      data: usersWithRoles,
      meta: {
        page: $page,
        limit: $limit,
        total,
        totalPages: Math.ceil(total / $limit),
        hasMore: $page < Math.ceil(total / $limit),
      },
    };
  }

  async getLowGradeOrganizations(organizationId: string, search?: string) {
    const organization = await this.prismaService.organization.findFirst({
      where: {
        id: organizationId,
      },
      include: {
        grade: true,
      },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const where: Prisma.OrganizationWhereInput = {
      grade: {
        level: {
          gte: organization.grade.level,
        },
      },
      id: {
        not: organizationId,
      },
    };

    if (search) {
      where.name = {
        contains: search,
        mode: 'insensitive',
      };
    }

    const lowGradeOrganizations = await this.prismaService.organization.findMany({
      where,
    });

    return lowGradeOrganizations;
  }

  async deactivateOrganizationById(organizationId: string) {
    const organization = await this.prismaService.organization.findUnique({
      where: {
        id: organizationId,
        status: 'ACTIVE',
      },
    });

    if (!organization) {
      throw new NotFoundException('Organization topilmadi');
    }

    const deactive = await this.prismaService.organization.update({
      where: { id: organizationId },
      data: { status: 'INACTIVE' },
    });

    return {
      message: 'Organization deactivated successfully',
    };
  }

  //
  async getAllForInspector({ query, organizationId }: { query: GetAllParam; organizationId: string }) {
    const { search, page, limit } = query;

    const $page = page ? parseInt(page) : 1;
    const $limit = limit ? parseInt(limit) : 10;
    const where = this.queryUtil.createSearchQuery(search, ['name', 'description', 'address', 'phone', 'id']);

    const organization = await this.prismaService.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization topilmadi');
    }

    const result = await this.pagination.paginate<any>(
      this.prismaService.organization,
      { page: $page, limit: $limit },
      { parentId: organizationId, ...where, status: Status.ACTIVE },
      { Worker: true, Employee: true, Responsible: true },
    );

    const cleaned = result.data.map((org) => {
      const allUsers = [...org.Worker, ...org.Employee, ...org.Responsible];

      const uniqueUsers = allUsers.filter((user, index, self) => index === self.findIndex((u) => u.id === user.id));

      return {
        ...org,
        userCount: uniqueUsers.length,
        Worker: undefined,
        Employee: undefined,
        Responsible: undefined,
      };
    });

    return {
      ...result,
      data: cleaned,
    };
  }

  // async getAllForInspector({ query, organizationId }: { query: GetAllParam; organizationId: string }) {
  //   const { search, page, limit } = query;

  //   const $page = page ? parseInt(page) : 1;
  //   const $limit = limit ? parseInt(limit) : 10;
  //   const where = this.queryUtil.createSearchQuery(search, ['name', 'description', 'address', 'phone', 'id']);

  //   const organization = await this.prismaService.organization.findUnique({
  //     where: { id: organizationId },
  //   });

  //   if (!organization) {
  //     throw new NotFoundException('Tashkilot topilmadi');
  //   }

  //   const result = await this.pagination.paginate<any>(
  //     this.prismaService.organization,
  //     { page: $page, limit: $limit },
  //     {
  //       OR: [{ parentId: organizationId }, { controlledById: organizationId }],
  //       ...where,
  //       status: Status.ACTIVE,
  //     },
  //     {
  //       Worker: true,
  //       Employee: true,
  //       Responsible: true,
  //       Parent: true,
  //       ControlledBy: true,
  //       type: true,
  //       grade: true,
  //     },
  //   );

  //   const cleaned = result.data.map((org) => {
  //     const allUsers = [...org.Worker, ...org.Employee, ...org.Responsible];
  //     const uniqueUsers = allUsers.filter((user, index, self) => index === self.findIndex((u) => u.id === user.id));

  //     return {
  //       ...org,
  //       userCount: uniqueUsers.length,
  //       relationshipType: org.parentId === organizationId ? 'CHILD' : 'UNDER_CONTROL',
  //       Worker: undefined,
  //       Employee: undefined,
  //       Responsible: undefined,
  //     };
  //   });

  //   return {
  //     ...result,
  //     data: cleaned,
  //   };
  // }
}
