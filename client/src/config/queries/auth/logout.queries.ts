import { axiosPrivate } from '@/config/api'
import { authEndpoints } from '@/config/api/endpoints'
import { TApiErrorResponse } from '@/shared/types/api-error.type'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { notification } from 'antd'
import { AxiosError } from 'axios'
import { useNavigate } from 'react-router-dom'

export const useLogout = () => {
	const navigate = useNavigate()
	const queryClient = useQueryClient()

	return useMutation({
		mutationKey: [authEndpoints.logout],
		mutationFn: async () => {
			return await axiosPrivate.get(authEndpoints.logout)
		},
		onSuccess: async () => {
			localStorage.clear()
			notification.success({
				message: 'Tizimdan muaffaqiyatli chiqdingiz'
			})
			queryClient.clear()
			navigate('/auth/login')
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			localStorage.clear()
			queryClient.clear()
			navigate('/auth/login')
			notification.error({ message: response?.data.message })
		}
	})
}
