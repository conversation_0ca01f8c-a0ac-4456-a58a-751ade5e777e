import { Button, Layout } from 'antd'
import { ReactNode } from 'react'

import { MenuIcon } from 'lucide-react'
import { useSidebar } from './utils/use-sidebar'
import { HeaderTitle } from './ui/header-title'
import Logout from '@/pages/auth/ui/logout'
import { ThemeButton } from '@/shared/ui/theme-button'
import Notification from './ui/notification'

interface HeaderProps {
	children?: ReactNode
}

export const Header: React.FC<HeaderProps> = ({ children }) => {
	const { toggleSidebar } = useSidebar()

	return (
		<Layout.Header className='sticky top-0 z-[1] px-0 w-full'>
			<div className='grid grid-cols-[auto_1fr_auto] justify-items-center justify-center w-full absolute inset-0 px-2 lg:px-4'>
				<div className='flex w-full items-center lg:hidden min-w-[40px]'>
					<Button
						onClick={() => toggleSidebar()}
						type={'default'}
						icon={<MenuIcon className='w-4 h-4' />}
					/>
				</div>
				<div className='flex items-center justify-between w-full lg:justify-start ml-6 lg:ml-12'>
					<HeaderTitle />
				</div>
				<div className='flex justify-end w-full'>
					<div className='flex justify-end items-center space-x-2'>
						<Notification />
						<ThemeButton />
						<Logout />
					</div>
					<div className='inline-flex items-center gap-2'>{children}</div>
				</div>
			</div>
		</Layout.Header>
	)
}
