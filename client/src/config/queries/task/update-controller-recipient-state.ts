import { axiosPrivate } from '@/config/api'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { notification } from 'antd'

interface UpdateControllerRecipientStateRequest {
	taskId: string
	recipientId: string
	recipientAnswerId: string
	data: {
		state: string
		rejectReason?: string
	}
}

export const useUpdateControllerRecipientAnswerState = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({  recipientAnswerId, data }: UpdateControllerRecipientStateRequest) => {
			return (
				await axiosPrivate.patch(
					`/task/recipient-answer/${recipientAnswerId}`,
					data
				)
			).data
		},
		onSuccess: (_data, variables) => {
			notification.success({
				message: 'Javob holati muvaffaqiyatli yangilandi',
				description: variables.data.state === 'CONFIRMED' ? 'Javob tasdiqlandi' : 'Javob rad etildi'
			})

			// Invalidate related queries
			queryClient.invalidateQueries({ 
				queryKey: ['controller-recipient', variables.taskId, variables.recipientId] 
			})
		},
		onError: (error: any) => {
			const message = error?.response?.data?.message || 'Javob holatini yangilashda xatolik yuz berdi'
			notification.error({
				message: 'Xatolik',
				description: Array.isArray(message) ? message.join(', ') : message
			})
		}
	})
}
