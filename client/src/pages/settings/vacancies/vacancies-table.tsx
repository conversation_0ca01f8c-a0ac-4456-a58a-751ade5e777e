import { Button, Modal, Space, Table } from 'antd'
import { useVacanciesModalStore } from './utils/vacancies-modal-store'
import { usePagination } from '@/shared/hooks/usePagination'
import VacanciesMmodal from './ui/vacancies-modal'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
import { useGetAllVacancies } from '@/config/queries/vacancy/get-all.queries'
import { useDeleteVacancy } from '@/config/queries/vacancy/delete.vacancy'
import { Search } from '@/shared/components/search/search'

const VacanciesTable = () => {
	const { onOpen } = useVacanciesModalStore()
	const { pagination, handlePaginationChange } = usePagination()

	const { data: vacancies, isLoading: isVacanciesLoading } = useGetAllVacancies()

	const deleteVacancy = useDeleteVacancy()

	const handleDelete = (vacancyId: string) => {
		Modal.confirm({
			title: '<PERSON>honchingiz komilmi?',
			content: "Ushbu vakansiyani o'chirishni xoxlaysizmi?",
			okText: 'Ha',
			cancelText: "Yo'q",
			onOk: () => {
				deleteVacancy.mutateAsync(vacancyId)
			}
		})
	}

	return (
		<Table
			pagination={pagination}
			onChange={handlePaginationChange}
			title={() => (
				<div className='flex items-center justify-between'>
					<Search />
					<VacanciesMmodal />
				</div>
			)}
			size='small'
			loading={isVacanciesLoading}
			scroll={{ x: 'max-content' }}
			dataSource={vacancies?.data}
			rowKey={rec => rec.id || ''}
			columns={[
				{
					key: '#',
					title: '#',
					render: (_, _rec, index) =>
						formatTableIndex(pagination.current, pagination.pageSize, index)
				},

				{
					key: 'name',
					title: 'Lavozim',
					dataIndex: ['OrganizationTypePosition', 'name']
				},
				{
					key: 'organization',
					title: 'Tashkilot',
					dataIndex: ['Organization', 'name']
				},

				{
					key: 'actions',
					title: 'Amallar',
					render: record => (
						<Space>
							<Button
								type='primary'
								icon={<EditOutlined />}
								onClick={e => {
									e.stopPropagation()
									onOpen(record)
								}}
							/>

							<Button
								danger
								icon={<DeleteOutlined />}
								onClick={e => {
									e.stopPropagation()
									handleDelete(record.id)
								}}
							/>
						</Space>
					)
				}
			]}
		/>
	)
}
export default VacanciesTable
