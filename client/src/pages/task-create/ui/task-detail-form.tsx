import { Form, Input } from 'antd'

export const TaskNameForm = () => {
	return (
		<Form.Item
			name='name'
			label='Topshiriq nomi'
			labelCol={{ span: 24 }}
			rules={[{ required: true }]}>
			<Input className='rounded-md' />
		</Form.Item>
	)
}

export const TaskDescriptionForm = () => {
	return (
		<Form.Item
			name='description'
			label='Izoh'
			labelCol={{ span: 24 }}
			rules={[{ required: true }]}>
			<Input.TextArea className='rounded-md !h-48' />
		</Form.Item>
	)
}

// Keep the original for backward compatibility
export const TaskDetailsForm = () => {
	return (
		<>
			<TaskNameForm />
			<TaskDescriptionForm />
		</>
	)
}
