import { Card, Badge, Typography, Tag, Tooltip, Space } from 'antd'
import { Users, Building, Clock, LogOut, LogIn, LocateOffIcon, Locate } from 'lucide-react'
import { LocationItem as LocationItemType } from '../types/location.types'

const { Text } = Typography

interface LocationItemProps {
	location: LocationItemType
	formatDate: (dateString: string) => string
	onUserClick?: (user: LocationItemType) => void
}

export default function LocationItem({ location, formatDate, onUserClick }: LocationItemProps) {
	const handleClick = () => {
		onUserClick?.(location)
	}

	return (
		<div className='w-full'>
			<Card
				className={`
					group hover:shadow-lg transition-all duration-300 border-l-4
					${
						location.isInArea
							? 'border-l-green-400 bg-gradient-to-r from-green-50/50 to-white dark:from-green-900/10 dark:to-gray-800'
							: 'border-l-red-400 bg-gradient-to-r from-red-50/50 to-white dark:from-red-900/10 dark:to-gray-800'
					}
					cursor-pointer hover:scale-[1.02]
				`}
				size='small'
				onClick={handleClick}>
				<div className='space-y-3'>
					{/* Заголовок карточки */}
					<div className='flex items-start justify-between'>
						<div className='flex-1'>
							<div className='flex items-center gap-3 mb-2'>
								<Text
									strong
									className='text-gray-800 dark:text-gray-200 text-base'>
									{location.fullName}
								</Text>
								<Badge
									status={location.isInArea ? 'success' : 'error'}
									text={
										<Text
											className={`text-sm font-medium ${
												location.isInArea
													? 'text-green-600 dark:text-green-400'
													: 'text-orange-600 dark:text-orange-400'
											}`}>
											{location.isInArea ? 'Hududda' : 'Hududdan tashqarida'}
										</Text>
									}
								/>
							</div>

							<div className='flex items-center gap-2 text-gray-600 dark:text-gray-400'>
								<Users size={14} />
								<Text className='text-sm'>{location.position.name}</Text>
							</div>
						</div>
						<Space>
							<Tooltip title={location.isInArea ? 'Hududda' : 'Hududdan tashqarida'}>
								{location.isInArea ? (
									<LogIn
										className='text-green-500'
										size={20}
									/>
								) : (
									<LogOut
										className='text-orange-500'
										size={20}
									/>
								)}
							</Tooltip>
							<Tooltip title={location.type === 'DISABLED' ? "GPS o'chgan" : 'GPS faol'}>
								{location.type === 'DISABLED' ? (
									<LocateOffIcon
										size={20}
										className='text-red-500'
									/>
								) : (
									<Locate
										size={20}
										className='text-green-500'
									/>
								)}
							</Tooltip>
						</Space>
					</div>

					{/* Организации */}
					<div className='space-y-2'>
						<div className='flex items-center gap-2 text-gray-600 dark:text-gray-400'>
							<Building size={14} />
							<Text className='text-sm font-medium'>Tashkilotlar:</Text>
						</div>
						<div className='flex flex-wrap gap-2'>
							{location.organization.map(org => (
								<Tag
									key={org.id}
									className='bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-600'>
									{org.name}
								</Tag>
							))}
						</div>
					</div>

					{/* Время */}
					<div className='flex items-center gap-2 text-gray-500 dark:text-gray-400 pt-2 border-t border-gray-100 dark:border-gray-700'>
						<Clock size={14} />
						<Text className='text-sm'>{formatDate(location.createdAt)}</Text>
					</div>
				</div>
			</Card>
		</div>
	)
}
