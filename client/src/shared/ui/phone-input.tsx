import { Input } from 'antd'
import { ComponentPropsWithoutRef } from 'react'
import InputMask from 'react-input-mask'

export const PhoneInput = ({ value, onChange }: ComponentPropsWithoutRef<'input'>) => {
	return (
		<InputMask
			style={{
				width: '100%',
				padding: '8px 11px',
				fontSize: '16px',
				lineHeight: '1.8',
				borderRadius: '6px',
				border: '1px solid gray'
			}}
			mask='+998 99 999-99-99'
			value={value}
			onChange={onChange}
			placeholder='+998 99 999-99-99'>
			{inputProps => <Input {...inputProps} />}
		</InputMask>
	)
}
