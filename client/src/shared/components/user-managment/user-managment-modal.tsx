import { Button, Modal } from 'antd'
import { useUserManagementStore } from './utils/user-managament-store'
import { LoadingScreen } from '@/shared/ui/suspense'
import { UserManagementTable } from './ui/user-managment.table'
import { useFaceIDAddStore } from './utils/face-id-add-modal-store'
import { FaceIdAddModal } from './ui/user-managment-add.modal'
import { usePermissions } from '@/shared/guards/usePermissions'
import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { PlusIcon } from 'lucide-react'

export const UserManagmentModal = () => {
	const { open, id, onClose } = useUserManagementStore()
	const { onOpen: addOnOpen } = useFaceIDAddStore()
	const { hasPermission } = usePermissions()
	const { data: user, isLoading: isLoadingUser } = useAuthMe()

	if (isLoadingUser || !user?.role) return <LoadingScreen />

	return (
		<Modal
			title='Foydalanuvchilar'
			width='1200px'
			footer={false}
			open={open}
			onClose={onClose}
			onCancel={onClose}
			destroyOnClose>
			<div className='w-full flex items-center justify-end gap-x-3'>
				{hasPermission(user.role, ['create:*']) && (
					<>
						<Button
							type='primary'
							onClick={() => addOnOpen(id!, 'workers')}>
							<PlusIcon />
							Ishchi
						</Button>
						<Button
							type='primary'
							onClick={() => addOnOpen(id!, 'employees')}>
							<PlusIcon />
							Xodim
						</Button>
						<Button
							type='primary'
							onClick={() => addOnOpen(id!, 'responsibles')}>
							<PlusIcon />
							Ma`sul
						</Button>
					</>
				)}
			</div>
			<UserManagementTable />
			<FaceIdAddModal />
		</Modal>
	)
}
