import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateVacancyDto } from './dto/create-vacancy.dto';
import { UpdateVacancyDto } from './dto/update-vacancy.dto';
import { PaginationParams, PaginationUtil, PrismaService, QueryUtil } from 'src/common';
import { Status } from '@prisma/client';

@Injectable()
export class VacancyService {
  constructor(
    private readonly prismaService: PrismaService,
    private paginationUtil: PaginationUtil,
    private queryUtil: QueryUtil,
  ) {}

  async create(createVacancyDto: CreateVacancyDto) {
    const organization = await this.prismaService.organization.findUnique({
      where: {
        id: createVacancyDto.organizationId,
      },
      select: {
        id: true,
        name: true,
      },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const position = await this.prismaService.organizationTypePosition.findFirst({
      where: {
        id: createVacancyDto.positionId,
        type: {
          Organization: {
            some: { id: organization.id },
          },
        },
      },
      select: {
        id: true,
        name: true,
      },
    });

    if (!position) {
      throw new NotFoundException('OrganizationTypePosition not found');
    }

    const vacancy = await this.prismaService.vacancy.create({
      data: {
        Organization: {
          connect: { id: organization.id },
        },
        OrganizationTypePosition: {
          connect: { id: position.id },
        },
      },
      select: {
        id: true,
        status: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    return {
      data: {
        ...vacancy,
        organization,
        organizationTypePosition: position,
      },
    };
  }

  async findAll({ page, limit, search }: PaginationParams, orgId: string) {
    const organization = await this.prismaService.organization.findUnique({
      where: { id: orgId, status: Status.ACTIVE },
      include: { Children: true },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const organizationIds = [organization.id, ...organization.Children.map((child) => child.id)];

    const where: any = {
      ...this.queryUtil.createSearchQuery(search, ['id']),
      organizationId: { in: organizationIds },
      status: Status.ACTIVE,
    };

    return await this.paginationUtil.paginate(
      this.prismaService.vacancy,
      { page, limit },
      where,
      undefined,
      { createdAt: 'desc' },
      {
        id: true,
        status: true,
        createdAt: true,
        updatedAt: true,
        Organization: {
          select: {
            id: true,
            name: true,
          },
        },
        OrganizationTypePosition: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    );
  }

  async findOne(id: string) {
    const vacancy = await this.prismaService.vacancy.findUnique({
      where: { id, status: 'ACTIVE' },
      include: { Organization: true, OrganizationTypePosition: true },
    });

    if (!vacancy) {
      throw new NotFoundException('Vacancy not found');
    }

    return vacancy;
  }

  async update(id: string, updateVacancyDto: UpdateVacancyDto) {
    const vacancy = await this.findOne(id);

    if (updateVacancyDto.organizationId) {
      const organization = await this.prismaService.organization.findUnique({
        where: {
          id: updateVacancyDto.organizationId,
        },
      });

      if (!organization) {
        throw new NotFoundException('Organization not found');
      }

      vacancy.organizationId = organization.id;
    }

    if (updateVacancyDto.positionId) {
      const position = await this.prismaService.organizationTypePosition.findFirst({
        where: {
          id: updateVacancyDto.positionId,
          type: {
            Organization: {
              some: { id: vacancy.organizationId },
            },
          },
        },
      });

      if (!position) {
        throw new NotFoundException('OrganizationTypePosition not found');
      }

      vacancy.organizationTypePositionId = position.id;
    }

    return await this.prismaService.vacancy.update({
      data: {
        Organization: {
          connect: { id: vacancy.organizationId },
        },
        OrganizationTypePosition: {
          connect: { id: vacancy.organizationTypePositionId },
        },
      },
      where: { id: vacancy.id },
    });
  }

  async remove(id: string) {
    await this.findOne(id);
    return await this.prismaService.vacancy.update({
      data: {
        status: Status.INACTIVE,
      },
      where: { id },
    });
  }
}
