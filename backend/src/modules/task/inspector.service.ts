import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/common';
import { CompleteTaskDto } from './dto/complete-task.dto';
import { FileService } from '../file/file.service';

@Injectable()
export class InspectorService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly fileService: FileService,
  ) {}

  //   async completeTask(taskId: string, userId: string, completeTaskDto: CompleteTaskDto) {
  //     const task = await this.prisma.task.findFirst({ where: { id: taskId, createdById: userId } });

  //     if (!task) throw new NotFoundException('task not found');

  //     const taskState = await this.prisma.taskState.findUnique({
  //       where: {
  //         id: completeTaskDto.stateId,
  //       },
  //     });

  //     if (completeTaskDto.file) {
  //       completeTaskDto.file = (await this.fileService.moveFileFromTempToSaved([completeTaskDto.file]))[0].id;
  //     }

  //     await this.prisma.task.update({
  //       where: { id: taskId },
  //       data: {
  //         isCompleted: true,
  //         completedAt: new Date(),
  //         TaskState: {
  //           connect: { id: taskState.id },
  //         },
  //         completeDesc: completeTaskDto.description,
  //         ...(completeTaskDto.file
  //           ? {
  //               CompleteFile: {
  //                 connect: {
  //                   id: completeTaskDto.file,
  //                 },
  //               },
  //             }
  //           : {}),
  //       },
  //     });
  //     return 'task completed successfully';
  //   }

  async completeTask(taskId: string, userId: string, completeTaskDto: CompleteTaskDto) {
    const task = await this.prisma.task.findFirst({
      where: { id: taskId, createdById: userId },
    });

    if (!task) {
      throw new NotFoundException('Task not found');
    }

    const taskState = await this.prisma.taskState.findUnique({
      where: { id: completeTaskDto.stateId },
    });

    if (!taskState) {
      throw new NotFoundException(`Task state with ID ${completeTaskDto.stateId} not found`);
    }

    if (completeTaskDto.file) {
      const savedFile = await this.fileService.moveFileFromTempToSaved([completeTaskDto.file]);
      completeTaskDto.file = savedFile[0].id;
    }

    await this.prisma.task.update({
      where: { id: taskId },
      data: {
        isCompleted: true,
        completedAt: new Date(),
        TaskState: {
          connect: { id: taskState.id },
        },
        completeDesc: completeTaskDto.description,
        ...(completeTaskDto.file
          ? {
              CompleteFile: {
                connect: {
                  id: completeTaskDto.file,
                },
              },
            }
          : {}),
      },
    });

    return 'Task completed successfully';
  }
}
