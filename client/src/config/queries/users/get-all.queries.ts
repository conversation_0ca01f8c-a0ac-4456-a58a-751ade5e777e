import { axiosPrivate } from '@/config/api'
import {
	usersEndpoints,
	userConnectOrganizationEndpoints,
	organizationEndpoints
} from '@/config/api/endpoints'
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import { TOrganization } from '../organizations/get-all.queries'
import { STATUS } from '@/shared/types/status.type'
import { TPaginationWrapper } from '@/shared/types/pagination.type'
import { useParams, useSearchParams } from 'react-router-dom'
import { useState } from 'react'
import { ApiResponse } from '../types'

type TPosition = {
	createdAt: string
	description: string | null
	id: string
	name: string
	status: STATUS
	typeId: string
	updatedAt: string
}

export type TUser = {
	id: string
	fullName: string
	username: string
	grade?: {
		name: string
	}
	password: string
	phone: string
	avatar: {
		path: string
	}
	avatarId: string
	faceIdImage: string
	imei: string
	telegramId: string
	organization: TOrganization
	Organization: TOrganization[]
	MainOrganization: TOrganization
	position: TPosition
	name?: string
	ResponsibleFor?: {
		id: string
		name: string
	}
	UserWorkingSchedule:
		| {
				id: string
				userId: string
		  }[]
		| null
	isinVocation: boolean
	type: string | null
	completedTaskCount: number
	inCompletedTaskCount: number
	gpsStatus: 'IN' | 'OUT' | 'OFFLINE'
	roleTypes: string[]
}

export const useGetAllUser = () => {
	const [searchParams] = useSearchParams()
	const { id: orgId } = useParams()
	const xOrgId = localStorage.getItem('organizationId')
	const role = searchParams.get('role')

	const search = searchParams.get('search')
	const [limit, setLimit] = useState(10)
	const [page, setPage] = useState(1)

	const query = useQuery({
		queryKey: [usersEndpoints.all, 'paginated', page, limit, search, role, orgId, xOrgId],
		queryFn: async () => {
			return (
				await axiosPrivate.get<TPaginationWrapper<TUser[]>>(
					search ? usersEndpoints.search(search) : usersEndpoints.all,
					{
						params: {
							page,
							limit,
							orgId: role === 'responsibles' ? (orgId ?? JSON.parse(xOrgId ?? '')) : undefined
						}
					}
				)
			).data
		}
	})

	return {
		...query,
		setPage,
		setLimit
	}
}
export const useGetAllUsers = () => {
	const [searchParams] = useSearchParams()
	const { id: orgId } = useParams()
	const xOrgId = localStorage.getItem('organizationId')
	const role = searchParams.get('role')

	const search = searchParams.get('search')
	const limit = 10
	const [page, setPage] = useState(1)

	const query = useInfiniteQuery({
		queryKey: [usersEndpoints.all, 'infinite', page, limit, search, role, orgId, xOrgId],
		queryFn: async ({ pageParam }) => {
			return (
				await axiosPrivate.get<TPaginationWrapper<TUser[]>>(
					search
						? usersEndpoints.search(search)
						: organizationEndpoints.usersByOrg(orgId ?? JSON.parse(xOrgId ?? '')),
					{
						params: {
							page: pageParam,
							limit,
							orgId: role === 'responsibles' ? (orgId ?? JSON.parse(xOrgId ?? '')) : undefined,
							roleType:
								role === 'workers' ? 'worker' : role === 'employees' ? 'employee' : undefined
						}
					}
				)
			).data
		},
		initialPageParam: page,
		getNextPageParam: lastPage => {
			if (lastPage?.meta?.page === lastPage?.meta?.totalPages) return undefined
			return lastPage?.meta?.page + 1
		},
		getPreviousPageParam: lastPage => {
			if (lastPage?.meta?.page === 1) return undefined
			return lastPage?.meta?.page - 1
		}
	})

	return {
		...query,
		setPage
	}
}

export const useGetUserOne = (id: string) => {
	return useQuery({
		queryKey: [usersEndpoints.one(id), id],
		queryFn: async () => (await axiosPrivate.get<TUser>(usersEndpoints.one(id))).data,
		enabled: !!id
	})
}

export const useSearchUsers = (query: string) => {
	return useQuery({
		queryKey: ['searchUsers', query],
		queryFn: async () => {
			return (await axiosPrivate.get<TPaginationWrapper<TUser[]>>(usersEndpoints.search(query)))
				.data
		},
		enabled: !!query && query.length > 2
	})
}

export const useGetOrganizationWorkers = (organizationId: string) => {
	return useQuery({
		queryKey: ['organizationWorkers', organizationId],
		queryFn: async () => {
			return (await axiosPrivate.get<TUser[]>(userConnectOrganizationEndpoints.all(organizationId)))
				.data
		},
		enabled: !!organizationId
	})
}

export const useSearchWorkerByPhone = (phone: number) => {
	return useQuery({
		queryKey: [userConnectOrganizationEndpoints.workerSearch, phone],
		queryFn: async () => {
			return (
				await axiosPrivate.get<ApiResponse<TUser>>(
					userConnectOrganizationEndpoints.workerSearch(phone)
				)
			).data
		},
		enabled: !!phone
	})
}

export const useUsersSearchByPhones = (phone: number) => {
	return useQuery({
		queryKey: [usersEndpoints.searchWorker, phone],
		queryFn: async () => {
			return (
				await axiosPrivate.get<ApiResponse<TUser>>(usersEndpoints.searchWorker(phone.toString()))
			).data
		},
		enabled: !!phone
	})
}
