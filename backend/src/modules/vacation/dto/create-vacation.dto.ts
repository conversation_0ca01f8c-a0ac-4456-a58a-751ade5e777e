import { IVacation } from '@prisma/client';
import { Type } from 'class-transformer';
import { IsBoolean, IsDate, IsEnum, IsOptional, IsString } from 'class-validator';

// enums/vacation-type.enum.ts
export enum VacationType {
  PATIONS = 'PATIONS',
  VACATION = 'VACATION',
  SHORT_VACATION = 'SHORT_VACATION',
}

export class CreateVacationDto {
  @IsString()
  userId: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsEnum(VacationType)
  type: IVacation;

  @Type(() => Date)
  @IsDate()
  begin: Date;

  @Type(() => Date)
  @IsDate()
  end: Date;
}
