import { useState } from 'react'

/**
 * Хук для управления состоянием карты
 */
export const useMapState = () => {
	const [showControls, setShowControls] = useState(true)
	const [selectedFeature, setSelectedFeature] = useState<any>(null)

	const handleFeatureClick = (feature: any) => {
		setSelectedFeature(feature)
	}

	const closeInfoPanel = () => {
		setSelectedFeature(null)
	}

	return {
		showControls,
		setShowControls,
		selectedFeature,
		handleFeatureClick,
		closeInfoPanel
	}
}
