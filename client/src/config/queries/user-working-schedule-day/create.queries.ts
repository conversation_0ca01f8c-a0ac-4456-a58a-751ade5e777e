import { axiosPrivate } from '@/config/api'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { userWorkingScheduleDayEndpoints } from '@/config/api/endpoints'
import { notification } from 'antd'

export interface UserWorkingScheduleTypes {
	id?: string
	name: string
	day: number
	startTime: string
	userId?: string
	days?: string[]
	endTime: string
}

export const useCreateUserWorkingScheduleDay = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async (data: UserWorkingScheduleTypes) =>
			(
				await axiosPrivate.post<ApiResponse<UserWorkingScheduleTypes>>(
					userWorkingScheduleDayEndpoints.all,
					data
				)
			).data,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [userWorkingScheduleDayEndpoints.all] })
			notification.success({
				message: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yaratil<PERSON>',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: 'Yaratishda xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
