import { useLocation, useNavigate } from 'react-router-dom'
import { Badge, Card, Table } from 'antd'
import { CheckCircleOutlined, ClockCircleOutlined, CloseCircleOutlined } from '@ant-design/icons'
import { TTask } from '@/config/queries/task/getOne'
import { useGetTasks } from '@/config/queries/task/getAllByOrgId'
import SearchForExcel from './ui/search-for-excel'
import { Search } from '@/shared/components/search/search'
import { DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import SegmentedStatusTask from '@/pages/tasks-outgoing/ui/segmented-status-task.tsx'
import SegmentedCompletedStatus from '@/pages/tasks-outgoing/ui/segmented-completed-status.tsx'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex.ts'

dayjs.extend(timezone)

const getDueDateColor = (dueDate: string) => {
	const now = new Date()
	const due = new Date(dueDate)
	const diffDays = Math.ceil((due.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

	if (diffDays < 3) return '#ff4d4f' // red
	if (diffDays < 7) return '#faad14' // yellow
	return '#52c41a' // green
}

const Tasks = () => {
	const navigate = useNavigate()
	const location = useLocation()
	const workspace = location.pathname.startsWith('/personal') ? 'personal' : 'organization'
	const { data: tasks, setPage, isLoading } = useGetTasks(workspace, 'outgoing')

	const handleRowClick = (record: { id: string | number }) => {
		const orgId = localStorage.getItem('organizationId')
		if (orgId) {
			navigate(`/workspace/tasks/${record.id}`)
		} else {
			navigate(`/personal/tasks/${record.id}`)
		}
	}

	const currentPage = tasks?.pages?.at(-1)?.meta?.page || 1
	const pageLimit = tasks?.pages?.at(-1)?.meta?.limit || 10
	const totalItems = tasks?.pages?.at(-1)?.meta?.total || 0

	const columns = [
		{
			title: 'ID',
			dataIndex: 'id',
			key: 'id',
			width: 10,
			render: (_: string, _record: TTask, index: number) => (
				<span className='text-gray-500'>{formatTableIndex(currentPage, pageLimit, index)}</span>
			)
		},
		{
			title: 'Sarlavhasi',
			dataIndex: 'name',
			key: 'name',
			width: 150,
			className: 'text-start truncate max-w-24'
		},
		{
			title: 'Qabul qiluvchilar soni',
			dataIndex: 'RecipientsCount',
			key: 'description',
			width: 200,
			className: 'text-start truncate max-w-24'
		},
		{
			title: 'Tashkilot',
			dataIndex: ['CreatedByOrganization', 'name'],
			key: 'createdByOrg',
			width: 150,
			className: 'text-start truncate max-w-24'
		},
		{
			title: 'Turi',
			dataIndex: ['TaskType', 'name'],
			key: 'taskType',
			width: 150,
			className: 'text-start truncate max-w-24'
		},
		{
			title: 'Holati',
			key: 'state',
			width: 150,
			render: (rec: TTask) =>
				rec.TaskState ? (
					<Badge
						style={{
							backgroundColor: '#6366F1',
							borderRadius: '4px',
							display: 'inline-block',
							textAlign: 'center',
							fontSize: '14px',
							padding: '1px 8px'
						}}
						count={!rec.isCompleted ? 'jarayonda' : rec.TaskState.name}
					/>
				) : null
		},
		{
			title: 'Javoblar soni',
			key: 'totalAnswers',
			width: 150,
			render: (rec: TTask) => (
				<div className='flex items-center gap-2'>
					{rec.totalAnswers?.PENDING > 0 && (
						<Badge
							style={{
								padding: '1px 8px'
							}}
							className='bg-yellow-500 rounded-md flex items-center justify-center'
							count={
								<div className='flex items-center text-base text-white'>
									<ClockCircleOutlined className='text-base mr-1.5' />
									{rec.totalAnswers.PENDING}
								</div>
							}
						/>
					)}
					{rec.totalAnswers?.REJECTED > 0 && (
						<Badge
							style={{
								padding: '1px 8px'
							}}
							className='bg-red-500 rounded-md px-3 py-1.5 flex items-center justify-center'
							count={
								<div className='flex items-center text-base text-white'>
									<CloseCircleOutlined className='text-base mr-1.5' />
									{rec.totalAnswers.REJECTED}
								</div>
							}
						/>
					)}
					{rec.totalAnswers?.CONFIRMED > 0 && (
						<Badge
							style={{
								padding: '1px 8px'
							}}
							className='bg-green-500 rounded-md px-3 py-1.5 flex items-center justify-center'
							count={
								<div className='flex items-center text-base text-white'>
									<CheckCircleOutlined className='text-base mr-1.5 tex' />
									{rec.totalAnswers.CONFIRMED}
								</div>
							}
						/>
					)}
				</div>
			)
		},
		{
			title: 'Muddati',
			dataIndex: 'dueDate',
			key: 'dueDate',
			width: 150,
			render: (dueDate: string) => (
				<span style={{ color: getDueDateColor(dueDate) }}>
					{dayjs(dueDate).tz(DEFAULT_TIMEZONE).format('DD.MM.YYYY HH:mm')}
				</span>
			)
		}
	]

	return (
		<Card className=' p-4 '>
			<div className='p-3 rounded-lg shadow-sm dark:bg-primary'>
				<div className='mb-4 flex justify-between items-center'>
					<SegmentedStatusTask />
				</div>
				<SegmentedCompletedStatus />
				<Table
					title={() => (
						<div className='flex justify-between items-center'>
							<Search />
							<SearchForExcel />
						</div>
					)}
					loading={isLoading}
					columns={columns}
					scroll={{ x: 'max-content' }}
					dataSource={tasks?.pages?.flatMap(page => page.data)}
					rowKey='id'
					pagination={{
						current: currentPage,
						pageSize: pageLimit,
						total: totalItems,
						showSizeChanger: false,
						showTotal: total => `Jami ${total} ta`,
						onChange: async page => {
							setPage(page)
						}
					}}
					onRow={(record: TTask) => ({
						onClick: () => handleRowClick(record),
						style: { cursor: 'pointer' }
					})}
				/>
			</div>
		</Card>
	)
}
export default Tasks
