import { Pagination } from './pagination';

export class ApiResponse {
  data: any;
  status: number;
  pagination?: Pagination;
  date: Date;
  message?: string;

  constructor(data: any, status?: number, pagination?: Pagination, message?: string) {
    this.data = data;
    this.status = status || 200;
    this.pagination = pagination || null;
    this.date = new Date();
    this.message = message || 'Muvaffaqqiyatli bajarildi';
  }
}
