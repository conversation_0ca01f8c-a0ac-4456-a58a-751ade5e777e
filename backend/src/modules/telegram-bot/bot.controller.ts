import { <PERSON>, HttpStatus, Post, Req, Res } from '@nestjs/common';
import { BotService } from './bot.service';
import { Request, Response } from 'express';
import { ProtectedRoute } from '../../common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { TelegramEventKeys } from './interfaces/bot.interfaces';

@Controller('telegram-bot')
export class BotController {
  constructor(
    private readonly botService: BotService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  @ProtectedRoute({
    isPublic: true,
  })
  @Post('webhook')
  async setWebhook(@Res() res: Response, @Req() req: Request) {
    const body = req.body;
    this.eventEmitter.emit(TelegramEventKeys.TELEGRAM_HEAR, body);
    res.status(HttpStatus.OK).json({});
  }
}
