import { axiosPrivate } from '@/config/api'
import { organizationEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { TOrganization } from './get-all.queries'
import { AxiosError, AxiosResponse } from 'axios'
import { TApiErrorResponse } from '@/shared/types/api-error.type'
import { notification } from 'antd'

export type TOrganizationResponse = TOrganization

export const useDeleteOrganization = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async (id: string) => {
			return await axiosPrivate.delete(organizationEndpoints.one(id))
		},
		onSuccess: async (response: AxiosResponse<TOrganizationResponse>) => {
			console.log(response)
			queryClient.invalidateQueries({ queryKey: [organizationEndpoints.all] })
			notification.success({ message: "Tashkilot muaffaqiyatli o'chirildi!" })
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response?.data.message)
			notification.error({ message: "Tashkilot o'chirishda xatolik yuz berdi!" })
		}
	})
}
