import { useGetUserDailyLocation } from '@/config/queries/gps/gps.queries.ts'
import { Col, Row, Card, Statistic } from 'antd'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet'
import L from 'leaflet'
import { useMemo } from 'react'

// Custom marker iconlari
const startIcon = new L.Icon({
	iconUrl: '/icons/in.png',
	iconSize: [40, 50],
	iconAnchor: [16, 16],
	popupAnchor: [0, -16]
})

const endIcon = new L.Icon({
	iconUrl: '/icons/off.png',
	iconSize: [40, 50],
	iconAnchor: [16, 16],
	popupAnchor: [0, -16]
})

// Ikki nuqta orasidagi masofani hisoblash (Haversine formula)
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
	const R = 6371 // Yer radiusi km da
	const dLat = (lat2 - lat1) * (Math.PI / 180)
	const dLon = (lon2 - lon1) * (Math.PI / 180)
	const a =
		Math.sin(dLat / 2) * Math.sin(dLat / 2) +
		Math.cos(lat1 * (Math.PI / 180)) *
			Math.cos(lat2 * (Math.PI / 180)) *
			Math.sin(dLon / 2) *
			Math.sin(dLon / 2)
	const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
	return R * c
}

export default function MapLocationUserMap() {
	const location = useGetUserDailyLocation()
	const { history } = location?.data || { history: [] }
	// GPS ma'lumotlarini qayta ishlash
	const trackingData = useMemo(() => {
		if (!location.data || history.length === 0) {
			return {
				polylineCoords: [],
				startPoint: null,
				endPoint: null,
				totalDistance: 0,
				totalTime: '0ч 0мин',
				averageSpeed: 0
			}
		}

		// Vaqt bo'yicha tartiblash
		const sortedData = [...history].sort(
			(a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
		)

		// Polyline uchun koordinatalar
		const polylineCoords: [number, number][] = sortedData.map(point => [point.lat, point.lng])

		// Boshlangich va tugash nuqtalari
		const startPoint = sortedData[0]
		const endPoint = sortedData[sortedData.length - 1]

		// Umumiy masofani hisoblash
		let totalDistance = 0
		for (let i = 1; i < sortedData.length; i++) {
			const prev = sortedData[i - 1]
			const curr = sortedData[i]
			totalDistance += calculateDistance(prev.lat, prev.lng, curr.lat, curr.lng)
		}

		// Umumiy vaqtni hisoblash
		let totalTime = '0ч 0мин'
		let averageSpeed = 0

		if (sortedData.length >= 2) {
			const startTime = new Date(startPoint.createdAt)
			const endTime = new Date(endPoint.createdAt)
			const timeDiffMs = endTime.getTime() - startTime.getTime()
			const hours = Math.floor(timeDiffMs / (1000 * 60 * 60))
			const minutes = Math.floor((timeDiffMs % (1000 * 60 * 60)) / (1000 * 60))

			totalTime = `${hours}ч ${minutes}мин`

			// O'rtacha tezlik (km/h)
			const totalHours = timeDiffMs / (1000 * 60 * 60)
			averageSpeed = totalHours > 0 ? totalDistance / totalHours : 0
		}

		return {
			polylineCoords,
			startPoint,
			endPoint,
			totalDistance: Math.round(totalDistance * 100) / 100, // 2 raqamgacha yaxlitlash
			totalTime,
			averageSpeed: Math.round(averageSpeed * 100) / 100
		}
	}, [location.data, history])
	// Markaz nuqtasini aniqlash (barcha GPS nuqtalarining o'rtasi)
	const mapCenter: [number, number] = useMemo(() => {
		if (trackingData.polylineCoords.length > 0) {
			// Barcha nuqtalarning o'rtacha koordinatasini hisoblash
			const totalLat = trackingData.polylineCoords.reduce((sum, coord) => sum + coord[0], 0)
			const totalLng = trackingData.polylineCoords.reduce((sum, coord) => sum + coord[1], 0)
			const avgLat = totalLat / trackingData.polylineCoords.length
			const avgLng = totalLng / trackingData.polylineCoords.length
			return [avgLat, avgLng]
		}
		return [41.311081, 69.240562] // Default Toshkent koordinatalari
	}, [trackingData.polylineCoords])
	return (
		<Row
			className='h-full'
			gutter={[16, 16]}>
			{/* Statistika panel */}
			<Col
				xs={24}
				className='mb-2'>
				<Row gutter={[16, 16]}>
					<Col
						xs={24}
						sm={8}>
						<Card>
							<Statistic
								title='Umumiy masofa'
								value={trackingData.totalDistance}
								suffix='km'
								precision={2}
							/>
						</Card>
					</Col>
					<Col
						xs={24}
						sm={8}>
						<Card>
							<Statistic
								title='Umumiy vaqt'
								value={trackingData.totalTime}
							/>
						</Card>
					</Col>
					<Col
						xs={24}
						sm={8}>
						<Card>
							<Statistic
								title="O'rtacha tezlik"
								value={trackingData.averageSpeed}
								suffix='km/h'
								precision={2}
							/>
						</Card>
					</Col>
				</Row>
			</Col>

			{/* Xarita */}
			<Col
				xs={24}
				className='relative rounded-2xl overflow-hidden'
				style={{ height: 'calc(100vh - 250px)' }}>
				<MapContainer
					key={mapCenter.join(',')} // Xarita markazini o'zgartirish uchun
					center={mapCenter}
					zoom={13}
					style={{ height: '100%', width: '100%' }}
					zoomControl={false}
					attributionControl={false}>
					<TileLayer
						attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
						url='https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
					/>

					{/* GPS tracking yo'li */}
					{trackingData.polylineCoords.length > 1 && (
						<Polyline
							positions={trackingData.polylineCoords}
							color='#1890ff'
							weight={4}
							opacity={0.8}
						/>
					)}

					{/* Boshlangich nuqta */}
					{trackingData.startPoint && (
						<Marker
							position={[trackingData.startPoint.lat, trackingData.startPoint.lng]}
							icon={startIcon}>
							<Popup>
								<div className='text-center'>
									<h4 className='font-semibold text-green-600'>Boshlangich nuqta</h4>
									<p className='text-sm'>
										Vaqt: {new Date(trackingData.startPoint.createdAt).toLocaleString('uz-UZ')}
									</p>{' '}
									<p className='text-sm'>
										Koordinata: {trackingData.startPoint.lat.toFixed(6)},{' '}
										{trackingData.startPoint.lng.toFixed(6)}
									</p>
									<p className='text-sm'>
										Status: {trackingData.startPoint.isInArea ? 'Hududda' : 'Hududdan tashqarida'}
									</p>
								</div>
							</Popup>
						</Marker>
					)}

					{/* Tugash nuqtasi */}
					{trackingData.endPoint && trackingData.startPoint?.id !== trackingData.endPoint?.id && (
						<Marker
							position={[trackingData.endPoint.lat, trackingData.endPoint.lng]}
							icon={endIcon}>
							<Popup>
								<div className='text-center'>
									<h4 className='font-semibold text-red-600'>Tugash nuqtasi</h4>
									<p className='text-sm'>
										Vaqt: {new Date(trackingData.endPoint.createdAt).toLocaleString('uz-UZ')}
									</p>{' '}
									<p className='text-sm'>
										Koordinata: {trackingData.endPoint.lat.toFixed(6)},{' '}
										{trackingData.endPoint.lng.toFixed(6)}
									</p>
									<p className='text-sm'>
										Status: {trackingData.endPoint.isInArea ? 'Hududda' : 'Hududdan tashqarida'}
									</p>
								</div>
							</Popup>
						</Marker>
					)}
				</MapContainer>
			</Col>
		</Row>
	)
}
