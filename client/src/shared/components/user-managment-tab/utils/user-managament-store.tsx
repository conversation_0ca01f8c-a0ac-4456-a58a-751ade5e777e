import { create } from 'zustand'

interface FaceIDStore {
	open: boolean
	id: string | null
	onOpen: (id?: string) => void
	onClose: () => void
	setId: (id: string) => void
}

export const useUserManagementStore = create<FaceIDStore>(set => ({
	open: false,
	id: null,
	onOpen: id => set({ open: true, id: id || null }),
	onClose: () => set({ open: false, id: null }),
	setId: (id: string) => set({ id })
}))
