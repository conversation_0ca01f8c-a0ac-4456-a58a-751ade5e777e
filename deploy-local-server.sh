#!/bin/bash

# SSH ulanish va kerakli direktoriyaga o'tish
PLINK_PATH="\"C:/Program Files/PuTTY/plink.exe\""
HOST="*************"
USER="user"
PASSWORD="12345678"

# SSH orqali ulanish va kerakli komandalarni bajarish
$PLINK_PATH -ssh $USER@$HOST -pw $PASSWORD << EOF

# kerakli direktoriyaga o'tish
cd ~/apps/energy24-node

# git o'zgarishlarini qo'shish va commit qilish
git add .
git commit -m "fix: saved local changes"

# Docker buyruqlarini bajarish
docker compose -f docker-compose.dev.local down backend
docker compose -f docker-compose.dev.local build backend
docker compose -f docker-compose.dev.local up backend -d

EOF
