import { axiosPrivate } from '@/config/api'
import { organizationEndpoints } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'
import { TOrganization } from './get-all.queries'
import { useParams, useSearchParams } from 'react-router-dom'

export const useGetOneOrganization = (paramId?: string) => {
	const { id } = useParams()
	const [searchParams] = useSearchParams()
	const search = searchParams.get('search')

	const organizationId = paramId || id

	return useQuery({
		queryKey: [organizationEndpoints.one(organizationId!), search],
		queryFn: async () => {
			return (
				await axiosPrivate.get<TOrganization>(organizationEndpoints.one(organizationId!), {
					params: { search }
				})
			).data
		},
		enabled: !!organizationId
	})
}
