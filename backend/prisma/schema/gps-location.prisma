model GPSLocation {
  id     String @id @default(cuid())
  User   User   @relation(fields: [userId], references: [id])
  userId String

  lat Float
  lng Float

  status Status @default(ACTIVE)

  isInArea Boolean

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model GPSLocationReport {
  id String @id @default(cuid())

  User   User?   @relation(fields: [userId], references: [id])
  userId String?

  status Status @default(ACTIVE)

  type     GPSLocationType
  isInArea Boolean?

  lat Float?
  lng Float?

  writedInfraction Boolean?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum GPSLocationType {
  IN
  OUT
  ENABLED
  DISABLED
  NOT_WORKING_TIME
}
