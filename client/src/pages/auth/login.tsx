import { Typography, Layout } from 'antd'
import LoginForm from '@/pages/auth/ui/login-form'
import { TLoginRequest, useLogin } from '@/config/queries/auth/login.queries'
import { Navigate, NavLink } from 'react-router-dom'
import { ThemeButton } from '@/shared/ui/theme-button'
import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { motion } from 'framer-motion'
import { ChevronRight } from 'lucide-react'
const { Content } = Layout

export default function LoginPage() {
	const { data: user } = useAuthMe()
	const { mutateAsync: login, isPending: isPendingLogin } = useLogin()

	function onFinish(data: TLoginRequest) {
		login(data)
	}

	if (user?.id) return <Navigate to='/' />

	const partners = [
		{
			img: '/ishkiishlar.png',
			title: 'Ichki ishlar vazirligi',
			borderColor: 'slate'
		},
		{
			img: '/gerbuzb.png',
			title: 'Ma<PERSON><PERSON><PERSON> davlat hokim<PERSON>',
			borderColor: 'slate'
		},
		{
			img: '/uzuyushma.png',
			title: "O'zbekiston mahallalari uyushmasi",
			borderColor: 'slate'
		},
		{
			img: '/gerbuzb.png',
			title: "O'zbekiston Respublikasi Yoshlar ishlari agentligi",
			borderColor: 'slate'
		},
		{
			img: '/gerbuzb.png',
			title: 'O‘zbekiston Respublikasi Oila va xotin-qizlar qo‘mitasi',
			borderColor: 'slate'
		},
		{
			img: {
				light: '/inson2.svg',
				dark: '/inson.svg'
			},
			title: 'O‘zbekiston Respublikasi Prezidenti huzuridagi Ijtimoiy himoya milliy agentligi',
			borderColor: 'slate'
		},
		{
			img: '/soliq.svg',
			title: "Davlat soliq Qo'mitasi",
			borderColor: 'slate'
		}
	]

	return (
		<Layout className='h-screen bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-950 dark:to-slate-900 overflow-hidden'>
			<motion.div
				initial={{ opacity: 0, y: -50 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.8 }}
				className='w-full py-8 flex justify-center'>
				<div className='relative w-full overflow-hidden'>
					<motion.div
						className='flex whitespace-nowrap'
						animate={{
							x: ['100%', '-100%']
						}}
						transition={{
							duration: 25,
							repeat: Infinity,
							ease: 'linear',
							repeatType: 'loop'
						}}>
						<span className='inline-block px-4 py-2 text-yellow-700 dark:text-yellow-400 bg-yellow-200/50 dark:bg-yellow-900/20 rounded-lg'>
							Tizimga faqatgina oldindan ruhsat berilgan foydalanuvchilar kira oladi shu sababdan
							tizimda ro'yhatdan o'tish imkoniyati mavjud emas !
						</span>
					</motion.div>
				</div>
			</motion.div>{' '}
			<Content className='grid grid-cols-1 md:grid-cols-5 gap-6 flex-1 px-6 pb-10 '>
				<motion.div
					initial={{ opacity: 0, x: -50 }}
					animate={{ opacity: 1, x: 0 }}
					transition={{ duration: 0.8, delay: 0.3 }}
					className='md:col-span-3 hidden md:flex items-center justify-center relative overflow-hidden rounded-3xl backdrop-blur-sm border border-slate-200 dark:border-white/10'>
					<div className='absolute inset-0 '>
						<div className='absolute top-0 left-0 w-96 h-96 bg-slate-500/30 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-blob'></div>
						<div className='absolute bottom-0 right-0 w-96 h-96 bg-slate-500/30 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-blob animation-delay-2000'></div>
						<div className='absolute -bottom-8 left-20 w-96 h-96 bg-slate-500/30 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-blob animation-delay-4000'></div>
					</div>
					<div className='w-full max-w-3xl space-y-4 p-8 backdrop-blur-md bg-white/80 dark:bg-white/5 rounded-2xl border border-slate-200 dark:border-white/10 shadow-2xl z-10'>
						<div className='relative flex items-center justify-start'>
							<Typography.Title
								level={1}
								className='relative pt-2 text-primary font-black text-[clamp(2rem,8vw,7rem)] tracking-tighter drop-shadow-lg bg-clip-text text-transparent'>
								MNAZORAT
							</Typography.Title>
						</div>

						<div className='space-y-6 text-slate-700 dark:text-white/90 font-medium backdrop-blur-sm bg-white/50 dark:bg-black/30 p-6 rounded-xl text-[clamp(1rem,2vw,1.125rem)] max-h-[500px] overflow-y-auto'>
							<div>
								<div className='flex flex-col space-y-4'>
									<div className='p-4 rounded-xl backdrop-blur-md '>
										<p className='mb-2 text-justify'>
											Mahalla yettiligini ish faoliyatini nazorat qilishda ularning ishga vaqtida
											kelishlarini ta'minlash va davomatlarini yuritish, ish vaqtida o'zi mahallasi
											hududida ekanligini xaritada ko'rib nazorat qilish, mahalla yettiligi
											xodimlariga berilgan topshiriqlar berish va o'z vaqtida bajarishlarini nazorat
											qilish.
										</p>
										<h5 className='font-bold mb-2'>Modullar:</h5>
										<ul className='space-y-2 list-disc list-inside'>
											<li>
												Xodimlarning yurgan joylarini xaritadan ko'rish va xodim o'z hududidan
												chiqqanda mas'ullarga xabar yuborish
											</li>
											<li>
												Mahalla binosiga o'rnatilgan FaceID biometrik qurilmasini integratsiya
												qilish orqali xodimlarning davomatini yuritish va ta'tilga chiqqan,
												topshiriq bilan boshqa joyda yurgan hamda bemor xodimlarning ro'yhatini
												yuritish
											</li>
											<li>
												Xodimlarga topshiriq berish va topshiriqlarni vaqtida bajarilishini nazorat
												qilish
											</li>
										</ul>
									</div>
									<h1 className='flex items-center justify-center text-4xl'>Hamkor tashkilotlar</h1>

									<div className='relative w-full overflow-hidden py-2'>
										<motion.div
											className='flex'
											animate={{
												x: ['0%', '-50%']
											}}
											transition={{
												duration: 40,
												repeat: Infinity,
												ease: 'linear',
												repeatType: 'loop'
											}}
											style={{
												width: `${partners.length * 100}%`
											}}>
											{[...partners, ...partners].map((partner, index) => (
												<div
													key={index}
													className='flex-shrink-0'
													style={{
														width: `${100 / (partners.length * 2)}%`
													}}>
													<div className='p-4 m-2 rounded-xl backdrop-blur-md border border-slate-200 hover:border-slate-300  h-full'>
														<img
															src={
																typeof partner.img === 'string' ? partner.img : partner.img.light
															}
															alt={partner.title}
															className='w-full h-32 object-contain dark:hidden'
														/>
														<img
															src={typeof partner.img === 'string' ? partner.img : partner.img.dark}
															alt={partner.title}
															className='w-full h-32 object-contain hidden dark:block'
														/>
														<p className='font-semibold text-center mt-2'>{partner.title}</p>
													</div>
												</div>
											))}
										</motion.div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</motion.div>

				<motion.div
					initial={{ opacity: 0, x: 50 }}
					animate={{ opacity: 1, x: 0 }}
					transition={{ duration: 0.8, delay: 0.5 }}
					className='md:col-span-2 flex items-center justify-center backdrop-blur-sm border border-slate-200 dark:border-white/10 rounded-2xl'>
					<div className='w-[600px] relative'>
						<div className='absolute -inset-0.5  opacity-75'></div>
						<div className='relative w-full rounded-2xl shadow-lg overflow-hidden'>
							<div className='bg-white dark:bg-gradient-to-br dark:from-slate-900 dark:to-slate-950 p-8'>
								<Typography.Title
									level={2}
									className='mb-8 font-bold flex items-center justify-start space-x-2'>
									<span className='text-[clamp(1.5rem,3vw,2rem)]'>Tizimga kirish</span>
									<ChevronRight className='h-5 w-5 text-slate-600 dark:text-slate-300' />
								</Typography.Title>

								<LoginForm
									onFinish={onFinish}
									isLoading={isPendingLogin}
								/>
							</div>
						</div>
					</div>
				</motion.div>
			</Content>
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				transition={{ duration: 0.5, delay: 1 }}
				className='fixed bottom-10 left-6'>
				<ThemeButton />
			</motion.div>
			<div className='fixed bottom-6 right-6 mb-4 md:mb-6 flex items-center justify-end mt-2'>
				<NavLink
					to='https://www.mbos.uz/'
					target='_blank'
					rel='noopener noreferrer'
					className='text-gray-400 text-sm hover:text-gray-600 transition-colors'>
					© {new Date().getFullYear()} Mbos. All rights reserved.
				</NavLink>
			</div>{' '}
		</Layout>
	)
}
