import { useGetOrganizationTypes } from '@/config/queries/organization-types/get.queries'
import { Form, Input, Select } from 'antd'
import { debounce } from 'lodash'
import { useCallback } from 'react'

const OrganizationPositionTypesForm = () => {
	const { data: organizationTypes, fetchNextPage } = useGetOrganizationTypes()

	const handlePopupScroll = useCallback(
		debounce((e: React.UIEvent<HTMLDivElement>) => {
			const target = e.target as HTMLDivElement
			const isNearBottom = target.scrollTop + target.offsetHeight >= target.scrollHeight - 10
			if (isNearBottom) {
				fetchNextPage()
			}
		}, 100),
		[]
	)

	return (
		<>
			<Form.Item
				name='name'
				label='Nomi'
				rules={[
					{ required: true, message: 'Nomini kiriting minimal 3 ta belgidan iborat', min: 3 }
				]}>
				<Input
					placeholder='Nomini kiriting'
					size='middle'
				/>
			</Form.Item>
			<Form.Item
				name='description'
				label='Izoh'
				rules={[{ required: true, message: 'Izoh kiriting minimal 3 ta belgidan iborat', min: 3 }]}>
				<Input
					placeholder='Izoh kiriting'
					size='middle'
				/>
			</Form.Item>
			<Form.Item
				name='typeId'
				label='Tashkilot turi'
				rules={[{ required: true, message: 'Tashkilot turini kiriting' }]}>
				<Select
					placeholder='Tashkilot turini tanlang'
					size='middle'
					options={organizationTypes?.pages.flatMap(page => page.data)}
					fieldNames={{ label: 'name', value: 'id' }}
					onPopupScroll={handlePopupScroll}
				/>
			</Form.Item>
		</>
	)
}
export default OrganizationPositionTypesForm
