import { TVacancy } from '@/config/queries/vacancy/create.vacancy'
import { create } from 'zustand'

interface VacanciesModalStore {
	open: boolean
	data: TVacancy | null
	onOpen: (data?: TVacancy) => void
	onClose: () => void
}

export const useVacanciesModalStore = create<VacanciesModalStore>(set => ({
	open: false,
	data: null,
	onOpen: data => set({ open: true, data: data || null }),
	onClose: () => set({ open: false, data: null })
}))
