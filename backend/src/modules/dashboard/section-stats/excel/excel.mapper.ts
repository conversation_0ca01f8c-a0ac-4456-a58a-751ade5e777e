import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import * as timezone from 'dayjs/plugin/timezone';
import { User } from '@prisma/client';
import { format } from 'date-fns';
export class LateAttendanceDto {
  fullName: string;
  username: string;
  phone: string;
  position: string;
  organization: string;
  date: string;
  enter: string;
  exit: string;
  minutes: number;
  description: string;
}

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.locale('uz');
dayjs.tz.setDefault('Asia/Tashkent');

export const sectionMapper = (item: any, index: number) => ({
  index: index + 1,
  name: item.name || '',
  address: item.address || '',
  description: item.description || '',
  region: item.region?.name || '',
  district: item.district?.name || '',
  section: item.section?.name || '',
  type: item.type?.name || '',
  phone: item.phone || '',
  createdAt: item.createdAt ? new Date(item.createdAt).toLocaleDateString('uz-UZ') : '',
});

export const positionExcelMapper = (item: any, idx: number) => ({
  index: idx + 1,
  fullName: item.fullName || '',
  phone: item.phone || '',
  organization: item.Organization?.[0]?.name || '',
  position: item.position?.name?.trim() || '',
});

export const noAttendanceMapper = (user: any) => ({
  fullName: user.fullName,
  phone: user.phone,
  position: user.position?.name || '',
  organization: user.Organization?.[0]?.name || '', // Organization arraydan birinchi nomi
});

export const employeeMapper = (item: any) => ({
  fullName: item.fullName || '',
  phone: item.phone || '',
  position: item.position?.name || '',
  organization: item.Organization?.map((org: any) => org.name).join(', ') || '',
});

export const vacationMapper = (item: any) => ({
  fullName: item.user?.fullName || '',
  position: item.user?.position?.name || '',
  organization: item.user?.Organization?.[0]?.name || '',
  phone: item.user?.phone || '',
  begin: item.begin ? new Date(item.begin).toLocaleDateString('uz-UZ') : '',
  end: item.end ? new Date(item.end).toLocaleDateString('uz-UZ') : '',
  description: item.description || '',
});

export const shortVacationMapper = (item: any) => ({
  fullName: item.user?.fullName || '',
  position: item.user?.position?.name || '',
  organization: item.user?.Organization?.[0]?.name || '',
  phone: item.user?.phone || '',
  begin: item.begin ? new Date(item.begin).toLocaleDateString('uz-UZ') : '',
  end: item.end ? new Date(item.end).toLocaleDateString('uz-UZ') : '',
  description: item.description || '',
});

export const patientMapper = (item: any) => ({
  fullName: item.fullName || '',
  position: item.position?.name || '',
  organization: item.Organization?.[0]?.name || '',
  phone: item.phone || '',
  begin: item.begin ? new Date(item.begin).toLocaleDateString('uz-UZ') : '',
  end: item.end ? new Date(item.end).toLocaleDateString('uz-UZ') : '',
  description: item.description || '',
});

export const vacancyMapper = (item: any) => ({
  position: item.OrganizationTypePosition?.name || '',
  organization: item.Organization?.name || '',
  region: item.Organization?.region?.name || '',
  district: item.Organization?.district?.name || '',
  section: item.Organization?.section?.name || '',
  address: item.Organization?.address || '',
  description: item.Organization?.description || '',
  createdAt: format(new Date(item.createdAt), 'yyyy-MM-dd HH:mm'),
});

export const inAreaMapper = (item: any) => ({
  fullName: item.fullName?.trim() || '',
  username: item.username || '',
  phone: item.phone || '',
  position: item.position?.name?.trim() || '',
  organization: item.Organization?.[0]?.name?.trim() || '',
  lastInTime: item.GPSLocationReport?.[0]
    ? format(new Date(item.GPSLocationReport[0].createdAt), 'yyyy-MM-dd HH:mm')
    : '',
});

export const outAreaMapper = (item: any) => ({
  fullName: item.fullName?.trim() || '',
  username: item.username || '',
  phone: item.phone || '',
  position: item.position?.name?.trim() || '',
  organization: item.Organization?.[0]?.name?.trim() || '',
  lastOutTime: item.GPSLocationReport?.[0]
    ? format(new Date(item.GPSLocationReport[0].createdAt), 'yyyy-MM-dd HH:mm')
    : '',
});

export const disabledAreaMapper = (item: any) => ({
  fullName: item.fullName?.trim() || '',
  username: item.username || '',
  phone: item.phone || '',
  position: item.position?.name?.trim() || '',
  organization: item.Organization?.[0]?.name?.trim() || '',
  lastDisabledTime: item.GPSLocationReport?.[0]
    ? format(new Date(item.GPSLocationReport[0].createdAt), 'yyyy-MM-dd HH:mm')
    : '',
});
