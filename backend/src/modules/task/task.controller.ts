import { Body, Controller, Get, Param, Patch, Post, Put, Query, Req, Res } from '@nestjs/common';
import { TaskService } from './task.service';
import { CreateTaskDto } from './dto/task.dto';
import { Me } from '../../common/decorators/me.decorator';
import { ProtectedRoute } from '../../common';
import { UpdateTaskDto } from './dto/update-task.dto';
import { Context } from 'src/common/decorators/context.decorator';
import { AnswerDto, AnswerStateDto } from './dto/answer.dto';
import { GetAllTasksByOrgId } from './dto/get-all-by-orgId.dto';
import { ReadTaskDto } from './dto/read-task.dto';
import { Response } from 'express';
import { unlinkSync } from 'fs';
import { CompleteTaskDto } from './dto/complete-task.dto';
import { UpdateTaskRecipientsBodyDto } from './dto/update-task-recipients.dto';
import { TaskCreatingService } from './services/task-creating.service';
import { InspectorService } from './inspector.service';

@Controller('task')
export class TaskController {
  constructor(
    private readonly taskService: TaskService,
    private readonly taskCreatingService: TaskCreatingService,
    private readonly inspectorService: InspectorService,
  ) {}

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
  })
  @Put('recipient/:taskId')
  async updateRecipients(@Param('taskId') taskId: string, @Body() body: UpdateTaskRecipientsBodyDto) {
    const { recipients } = body;

    return await this.taskService.updateTaskRecipients(taskId, recipients);
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'PERSONAL',
  })
  @Get('controller/:userId')
  async getTasksControlledByUser(@Param('userId') userId: string) {
    return this.taskService.getControlledTasksOnly(userId);
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
  })
  @Get('statistics')
  async getTaskStatistics(
    @Context('organizationId') orgId: string,
    @Res() res: Response,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const filePath = await this.taskService.getTaskStatistics(orgId, startDate, endDate);

    res.sendFile(filePath, (err) => {
      if (err) {
        res.status(500).send('Error sending file');
      } else {
        unlinkSync(filePath); // Delete the file after sending
      }
    });
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
  })
  @Post()
  async create(
    @Body() createTaskDto: CreateTaskDto,
    @Me('id') userId: string,
    @Context('organizationId') organizationId: string,
  ) {
    return await this.taskCreatingService.create(createTaskDto, userId, organizationId);
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
  })
  @Get('available-controllers')
  async getAvailableControllers(@Context('organizationId') organizationId: string) {
    return await this.taskService.getAvailableControllers(organizationId);
  }

  @ProtectedRoute({ isPublic: false })
  @Post('recipient-answer')
  async createByAnswer(@Body() createAnswerDto: AnswerDto) {
    return await this.taskService.createByAnswer(createAnswerDto);
  }

  @ProtectedRoute({ isPublic: false })
  @Patch('recipient-answer/:id')
  async updateByAnswerState(@Body() state: AnswerStateDto, @Param('id') id: string, @Me('id') userId: string) {
    return await this.taskService.updateByAnswerState(state, id, userId);
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
  })
  @Get('organization')
  async findAllByOrg(
    @Context('organizationId') orgId: string,
    @Me('id') userId: string,
    @Query() query: GetAllTasksByOrgId,
  ) {
    return await this.taskService.findAllByOrg(orgId, query, userId);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE' })
  @Patch(':taskId/complete')
  async completeTask(
    @Param('taskId') taskId: string,
    @Me('id') userId: string,
    @Body() completeTaskDto: CompleteTaskDto,
  ) {
    return await this.taskService.completeTask(taskId, userId, completeTaskDto);
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'PERSONAL',
  })
  @Get('user')
  async findAllByUser(@Me('id') userId: string, @Query() query: GetAllTasksByOrgId) {
    return await this.taskService.findAllByUser(userId, query);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Get(':id')
  async findOne(@Param('id') id: string, @Me('id') userId: string, @Context('organizationId') orgId?: string) {
    return await this.taskService.findOne({
      id,
      userId,
      orgId,
    });
  }

  @Patch(':id')
  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
  })
  async update(
    @Param('id') id: string,
    @Body() updateTaskDto: UpdateTaskDto,
    @Context('organizationId') orgId: string,
  ) {
    return this.taskService.update(id, updateTaskDto, orgId);
  }

  @ProtectedRoute({
    isPublic: true,
  })
  @Post('read-task')
  async readTask(@Body() readTaskDto: ReadTaskDto) {
    await this.taskService.readTask(readTaskDto);
    return 'Task isRead changed successfully';
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
  })
  @Post(':id')
  async createSubTask(
    @Param('id') taskId: string,
    @Me('id') userId: string,
    @Body() createTaskDto: CreateTaskDto,
    @Context('organizationId') organizationId: string,
  ) {
    return await this.taskService.createSubtask(createTaskDto, taskId, userId, organizationId);
  }

  // inspector service endpoint
  @ProtectedRoute({ isPublic: false, context: 'PERSONAL' })
  @Patch(':taskId/complete-inspector')
  async completeTaskInspector(
    @Param('taskId') taskId: string,
    @Me('id') userId: string,
    @Body() completeTaskDto: CompleteTaskDto,
  ) {
    return await this.inspectorService.completeTask(taskId, userId, completeTaskDto);
  }

  @ProtectedRoute({ isPublic: false, context: 'PERSONAL' })
  @Patch('recipient-answer-inspector/:id')
  async updateByAnswerStateInspector(@Body() state: AnswerStateDto, @Param('id') id: string, @Me('id') userId: string) {
    return await this.taskService.updateByAnswerState(state, id, userId);
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'PERSONAL',
  })
  @Put('recipient-inspector/:taskId')
  async updateRecipientsForInspector(@Param('taskId') taskId: string, @Body() body: UpdateTaskRecipientsBodyDto) {
    const { recipients } = body;

    return await this.taskService.updateTaskRecipients(taskId, recipients);
  }
}
