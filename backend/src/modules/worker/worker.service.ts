import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../common';
import { CreateUserDto } from '../user/dto/create-user.dto';
import { hash } from 'bcrypt';

@Injectable()
export class WorkerService {
  constructor(private prisma: PrismaService) {}

  async create(createUserDto: CreateUserDto, organizationId: string) {
    const { username, positionId, avatarId, faceIdImageId, ...rest } = createUserDto;

    const organization = await this.prisma.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    let user = await this.prisma.user.findUnique({
      where: { username },
      include: { Organization: true },
    });

    if (user) {
      const isAlreadyConnected = user.Organization.some((org) => org.id === organizationId);

      if (!isAlreadyConnected) {
        await this.prisma.user.update({
          where: { id: user.id },
          data: {
            Organization: {
              connect: { id: organizationId },
            },
            userData: {
              push: 'WORKER',
            },
          },
        });
      }

      return { message: 'Existing user connected to organization' };
    }

    if (avatarId) {
      const avatar = await this.prisma.file.findUnique({ where: { id: avatarId } });
      if (!avatar) throw new NotFoundException('Avatar not found');
    }

    if (faceIdImageId) {
      const faceIdImage = await this.prisma.file.findUnique({ where: { id: faceIdImageId } });
      if (!faceIdImage) throw new NotFoundException('FaceIdImage not found');
    }

    const position = await this.prisma.organizationTypePosition.findUnique({
      where: { id: positionId },
    });
    if (!position) throw new NotFoundException('Position not found');

    const hashedPassword = await hash(rest.password, 3);

    user = await this.prisma.user.create({
      data: {
        ...rest,
        username,
        password: hashedPassword,
        positionId,
        avatarId,
        faceIdImageId,
        userData: ['WORKER'],
        Organization: {
          connect: { id: organizationId },
        },
      },
      include: {
        Organization: true,
      },
    });

    const sanitizedUser = {
      id: user.id,
      fullName: user.fullName,
      username: user.username,
      status: user.status,
      phone: user.phone,
      avatarId: user.avatarId,
      faceIdImageId: user.faceIdImageId,
      role: user.role,
      Organization: user.Organization.map((org) => ({
        id: org.id,
        name: org.name,
        status: org.status,
        description: org.description,
        address: org.address,
        phone: org.phone,
        typeId: org.typeId,
        controlledById: org.controlledById,
        parentId: org.parentId,
        sector: org.sector,
        sectorResponsible: org.sectorResponsible,
      })),
    };

    return { data: sanitizedUser };
  }

  async findByPhone(phone: string) {
    if (phone.length !== 9) {
      throw new NotFoundException('Phone number must be exactly 9 digits');
    }

    if (!/^\d+$/.test(phone)) {
      throw new NotFoundException('Phone number must contain only digits');
    }

    const user = await this.prisma.user.findUnique({
      where: { username: phone, status: 'ACTIVE' },
      select: {
        id: true,
        fullName: true,
        username: true,
        status: true,
        phone: true,
        position: {
          select: {
            id: true,
            name: true,
          },
        },
        avatarId: true,
        userData: true,
        faceIdImageId: true,
        imei: true,
        role: true,
        Organization: {
          select: {
            id: true,
            name: true,
            status: true,
            description: true,
            address: true,
            phone: true,
          },
        },
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return { data: user };
  }

  async connectUserToOrganization(userId: string, organizationId: string) {
    const [user, organization] = await Promise.all([
      this.prisma.user.findUnique({ where: { id: userId }, include: { Organization: true } }),
      this.prisma.organization.findUnique({ where: { id: organizationId } }),
    ]);

    if (!user) throw new NotFoundException('Foydalanuvchi topilmadi');
    if (!organization) throw new NotFoundException('Tashkilot topilmadi');

    const isAlreadyConnected = user.Organization.some((org) => org.id === organizationId);
    if (isAlreadyConnected) {
      return { message: 'Foydalanuvchi allaqachon ushbu tashkilotga ulangan' };
    }

    const worker = await this.prisma.user.update({
      where: { id: userId },
      data: {
        Organization: {
          connect: { id: organizationId },
        },
        userData: {
          push: 'WORKER',
        },
      },
    });

    return { data: worker };
  }
}
