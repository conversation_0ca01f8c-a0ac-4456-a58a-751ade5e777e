import { Button, Select } from 'antd'
import { PlusIcon, CheckIcon } from 'lucide-react'
import { CloseCircleOutlined } from '@ant-design/icons'
import TaskConfirmForm from '@/pages/task-details/ui/task-confirm-form.tsx'
import { useState } from 'react'

interface TaskActionsProps {
	isOwnTask: boolean
	orgId: string | null
	navigate: (path: string) => void
	taskCompleted: boolean
	isUpdating: boolean
	updateTaskAnswerState: (params: { taskStateId: string; taskId: string }) => void
	task: any
	taskStates: any
	currentRecipient?: any
	onMarkAsRead?: () => Promise<void>
}

export const TaskActions = ({
	isOwnTask,
	orgId,
	navigate,
	taskCompleted,
	isUpdating,
	updateTaskAnswerState,
	task,
	taskStates,
	currentRecipient,
	onMarkAsRead
}: TaskActionsProps) => {
	const [openConfirmModal, setConfirmModal] = useState(false)
	const [isMarkingAsRead, setIsMarkingAsRead] = useState(false)

	const handleMarkAsRead = async () => {
		if (!onMarkAsRead) return

		setIsMarkingAsRead(true)
		try {
			await onMarkAsRead()
		} finally {
			setIsMarkingAsRead(false)
		}
	}

	return (
		<div className='flex flex-wrap items-start gap-3 justify-end'>
			{!isOwnTask && orgId && (
				<Button onClick={() => navigate('create-subtask')}>
					<PlusIcon />
					Quyi topshiriq yaratish
				</Button>
			)}
			{!isOwnTask && currentRecipient && !currentRecipient.isRead && (
				<Button
					onClick={handleMarkAsRead}
					loading={isMarkingAsRead}
					type='default'>
					<CheckIcon />
					Ishni boshlash
				</Button>
			)}
			{isOwnTask && (
				<>
					<Button
						onClick={() => setConfirmModal(true)}
						disabled={taskCompleted}>
						<CloseCircleOutlined />
						Topshiriqni yakunlash
					</Button>
					<TaskConfirmForm
						open={openConfirmModal}
						onCancel={() => setConfirmModal(false)}
					/>
				</>
			)}
			{isOwnTask && (
				<Select
					loading={isUpdating}
					onSelect={id => task?.id && updateTaskAnswerState({ taskStateId: id, taskId: task.id })}
					className='w-48'
					defaultValue={task.TaskState?.id}
					options={taskStates?.pages?.[0].data}
					fieldNames={{ label: 'name', value: 'id' }}
				/>
			)}
		</div>
	)
}
