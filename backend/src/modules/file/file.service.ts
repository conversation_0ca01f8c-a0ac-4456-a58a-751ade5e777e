import { Injectable, OnModuleInit } from '@nestjs/common';
import { PrismaService } from 'src/common';
import * as fs from 'fs';
import { Cron, CronExpression } from '@nestjs/schedule';

@Injectable()
export class FileService implements OnModuleInit {
  private tempDir = 'static/temp';
  private savedDir = 'static/saved';

  constructor(private prismaService: PrismaService) {}

  onModuleInit() {
    // Создаем директории, если они не существуют
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }
    if (!fs.existsSync(this.savedDir)) {
      fs.mkdirSync(this.savedDir, { recursive: true });
    }
  }

  async saveFileAsTemp(files: Express.Multer.File[]) {
    const fileIds = [];

    await this.prismaService.$transaction(async (prisma) => {
      for (const file of files) {
        // Получаем расширение из originalname
        const ext = file.originalname.split('.').pop();
        const filename = `${file.filename}.${ext}`; // Добавляем расширение

        const savedFile = await prisma.file.create({
          data: {
            path: this.makePath(filename), // Используем новое имя с расширением
            slug: file.originalname.toLowerCase().replace(/ /g, '_'),
            mimeType: file.mimetype,
            size: file.size,
            status: 'INACTIVE',
          },
          select: {
            id: true,
            path: true,
          },
        });

        fileIds.push({ id: savedFile.id, path: `${savedFile.path}` });

        // Переименовываем файл в папке static/temp
        const oldPath = `static/temp/${file.filename}`;
        const newPath = `static/temp/${filename}`;
        fs.renameSync(oldPath, newPath);
      }
    });

    return fileIds;
  }

  async moveFileFromTempToSaved(filesId: string[]) {
    const files = await this.prismaService.file.findMany({
      where: {
        id: {
          in: filesId,
        },
      },
    });

    const updates = [];

    for (const file of files) {
      try {
        const oldPath = file.path;
        const newPath = oldPath.replace('static/temp', 'static/saved');

        // Создаем директорию, если она не существует
        const dir = newPath.substring(0, newPath.lastIndexOf('/'));
        if (!fs.existsSync(dir)) {
          fs.mkdirSync(dir, { recursive: true });
        }

        fs.renameSync(oldPath, newPath);

        updates.push(
          this.prismaService.file.update({
            where: { id: file.id },
            data: {
              status: 'ACTIVE',
              path: newPath,
            },
            select: {
              id: true,
            },
          }),
        );
      } catch (e) {
        throw new Error(`Error moving file from temp to saved: ${e.message}`);
      }
    }

    return Promise.all(updates);
  }

  @Cron(CronExpression.EVERY_10_MINUTES)
  async clearTempFiles() {
    const files = fs.readdirSync(this.tempDir);
    await this.prismaService.file.deleteMany({
      where: {
        status: 'INACTIVE',
      },
    });
    for (const file of files) {
      fs.unlinkSync(`${this.tempDir}/${file}`);
    }
  }

  private makePath(path: string, isTemp: boolean = true) {
    return isTemp ? `${this.tempDir}/${path}` : `${this.savedDir}/${path}`;
  }
}
