import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { PaginationUtil, PrismaService, QueryUtil } from '../../common';
import { CreateUserWorkingScheduleDayDto, UpdateUserWorkingScheduleDayDto } from './dto/user-working-schedule-day.dto';

@Injectable()
export class UserWorkingScheduleDayService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly queryUtil: QueryUtil,
    private readonly pagination: PaginationUtil,
  ) {}

  async create(createUserWorkingScheduleDayDto: CreateUserWorkingScheduleDayDto) {
    const existingScheduleDay = await this.prisma.userWorkingScheduleDay.findUnique({
      where: { name: createUserWorkingScheduleDayDto.name },
    });

    if (existingScheduleDay) {
      throw new BadRequestException(`Schedule day with name "${createUserWorkingScheduleDayDto.name}" already exists`);
    }

    return this.prisma.userWorkingScheduleDay.create({ data: createUserWorkingScheduleDayDto });
  }

  async findAll(search?: string) {
    const model = this.prisma.userWorkingScheduleDay;

    const where: any = {
      ...this.queryUtil.createSearchQuery(search, ['name', 'id']),
      status: 'ACTIVE',
    };

    const withPagination = this.pagination.paginate(model, {}, where, {}, { day: 'asc' });

    return withPagination;
  }

  async findOne(id: string) {
    const scheduleDay = await this.prisma.userWorkingScheduleDay.findUnique({ where: { id } });
    if (!scheduleDay) {
      throw new NotFoundException(`Schedule day with ID "${id}" not found`);
    }
    return scheduleDay;
  }

  async findOneByUser(id: string) {
    const scheduleDay = await this.prisma.userWorkingScheduleDay.findMany({
      where: {
        UserWorkingSchedule: { every: { userId: id } },
      },
    });

    return scheduleDay;
  }

  async update(id: string, updateUserWorkingScheduleDayDto: Partial<UpdateUserWorkingScheduleDayDto>) {
    const scheduleDay = await this.prisma.userWorkingScheduleDay.findUnique({
      where: { id },
    });

    if (!scheduleDay) {
      throw new NotFoundException(`Schedule day with ID "${id}" not found`);
    }

    if (updateUserWorkingScheduleDayDto.name) {
      const existingScheduleDayWithName = await this.prisma.userWorkingScheduleDay.findUnique({
        where: { name: updateUserWorkingScheduleDayDto.name },
      });

      if (existingScheduleDayWithName && existingScheduleDayWithName.id !== id) {
        throw new BadRequestException(
          `Schedule day with name "${updateUserWorkingScheduleDayDto.name}" already exists`,
        );
      }
    }

    return this.prisma.userWorkingScheduleDay.update({
      where: { id },
      data: updateUserWorkingScheduleDayDto,
    });
  }

  async remove(id: string) {
    const scheduleDay = await this.prisma.userWorkingScheduleDay.findUnique({ where: { id } });

    if (!scheduleDay) {
      throw new NotFoundException(`Schedule day with ID "${id}" not found`);
    }

    return this.prisma.userWorkingScheduleDay.delete({ where: { id } });
  }
}
