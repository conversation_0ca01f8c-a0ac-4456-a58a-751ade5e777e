import { createRoot } from 'react-dom/client'
import { ThemeProvider } from './providers/theme-providers'
import { QueryProvider } from './providers/query-providers'
import { AntProvider } from './providers/ant-providers'
import { RouterProviders } from './providers/route-providers'
import '@ant-design/v5-patch-for-react-19'
import './index.css'

createRoot(document.getElementById('root')!).render(
	<ThemeProvider>
		<QueryProvider>
			<AntProvider>
				<RouterProviders />
			</AntProvider>
		</QueryProvider>
	</ThemeProvider>
)
