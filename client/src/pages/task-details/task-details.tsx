// React и React Router
import { useCallback, useMemo, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'

// Библиотеки
import { Badge, Col, Row } from 'antd'
import { useForm } from 'antd/es/form/Form'
import dayjs from 'dayjs'

// Компоненты и хуки проекта
import { LoadingScreen } from '@/shared/ui/suspense'
import { useFileUpload } from '@/shared/components/file-upload/file-upload'
import { DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'

// Запросы API
import { useGetOneTask } from '@/config/queries/task/getOne'
import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { useReadTask } from '@/config/queries/task/read'
import { useTaskStates } from '@/shared/hooks/useTaskStates'
import { useUpdateTaskAnswerState } from '@/config/queries/task/update-answer-state'
import { useRecipientAnswerTypes } from '@/config/queries/recipient-answer-type/get.queries'
import { useAnswerTask } from '@/config/queries/task/answer'
import {
	useGetNotifications,
	useReadNotification
} from '@/config/queries/notification/notification.queries'

// Компоненты страницы
import { TaskSubTasks } from './ui/task-subtask-table'
import { TaskAnswerForm } from './ui/task-answer-form'
import { TaskRecipients } from './ui/task-reciepent'
import { TaskDescription } from './ui/task-description'
import { TaskFiles } from './ui/task-files'
import { TaskHeader } from './ui/task-header'
import { TaskInfo } from './ui/task-info'
import { TaskResults } from './ui/task-results'
import { TaskActions } from './ui/task-action'

dayjs.tz.setDefault(DEFAULT_TIMEZONE)

const TaskDetails = () => {
	const { id } = useParams<{ id: string }>()
	const { data: notifications } = useGetNotifications()
	const { mutate: readNotification } = useReadNotification()
	const { data: task, isLoading } = useGetOneTask()
	const navigate = useNavigate()
	const orgId = localStorage.getItem('organizationId')
	const { data: user, isLoading: isLoadingUser } = useAuthMe()
	const { mutateAsync: readTask } = useReadTask()
	const { mutateAsync: answerTask, isPending } = useAnswerTask()
	const { taskStates } = useTaskStates()
	const { mutateAsync: updateTaskAnswerState, isPending: isUpdating } = useUpdateTaskAnswerState(
		id ?? ''
	)
	const { fileIds, render: FileUpload, clearFiles } = useFileUpload()
	const { data: recipientAnswerTypes, isLoading: isLoadingRecipientAnswerTypes } =
		useRecipientAnswerTypes()
	const [form] = useForm()

	// Состояние для отображения и фильтрации получателей
	const [showAllRecipients, setShowAllRecipients] = useState(false)
	const [readStatusFilter, setReadStatusFilter] = useState<'all' | 'read' | 'unread'>('all')

	// Безопасное получение ID организации
	const parsedOrgId = useMemo(() => {
		try {
			return orgId ? JSON.parse(orgId) : null
		} catch (e) {
			console.error('Ошибка при парсинге ID организации:', e)
			return null
		}
	}, [orgId])

	// Проверка на собственную задачу
	const isOwnTask = useCallback(() => {
		if (!task || !parsedOrgId) return false
		return task.CreatedByOrganization.id === parsedOrgId
	}, [task, parsedOrgId])

	// Текущий получатель
	const currentRecipient = useMemo(() => {
		if (!task || !user) return undefined

		return task.Recipients.find(recipient => {
			if (recipient.User?.id) {
				return recipient.User.id === user.id
			}
			return recipient.Organization?.id === parsedOrgId
		})
	}, [task, parsedOrgId, user])

	// Функция для ручной отметки задачи как прочитанной
	const handleMarkAsRead = useCallback(async () => {
		if (!currentRecipient) return

		try {
			// Отмечаем задачу как прочитанную
			await readTask(currentRecipient.id)

			// Отмечаем уведомление как прочитанное
			const currentNotification = notifications?.find(
				notification => notification?.Content?.id === id
			)

			if (currentNotification) {
				await readNotification([currentNotification.id])
			}
		} catch (error) {
			console.error('Ошибка при отметке задачи как прочитанной:', error)
		}
	}, [currentRecipient, readTask, notifications, id, readNotification])

	// Фильтрация получателей
	const filteredRecipients = useMemo(() => {
		if (!task) return []

		const isOwn = isOwnTask()

		let recipients = task.Recipients.filter(
			recipient => isOwn || recipient.organizationId === parsedOrgId
		)

		// Применяем фильтр по статусу прочтения
		if (readStatusFilter === 'read') {
			recipients = recipients.filter(
				recipient => recipient.isRead || recipient.id === currentRecipient?.id
			)
		} else if (readStatusFilter === 'unread') {
			recipients = recipients.filter(
				recipient => !recipient.isRead && recipient.id !== currentRecipient?.id
			)
		}

		// Ограничиваем количество отображаемых получателей
		if (!showAllRecipients && recipients.length > 4) {
			return recipients.slice(0, 4)
		}

		return recipients
	}, [task, readStatusFilter, showAllRecipients, isOwnTask, parsedOrgId, currentRecipient])

	// Подсчёт получателей
	const recipientCounts = useMemo(() => {
		if (!task) return { total: 0, read: 0, unread: 0 }

		const isOwn = isOwnTask()
		const allRecipients = task.Recipients.filter(
			recipient => isOwn || recipient.organizationId === parsedOrgId
		)

		const readCount = allRecipients.filter(
			recipient => recipient.isRead || recipient.id === currentRecipient?.id
		).length

		return {
			total: allRecipients.length,
			read: readCount,
			unread: allRecipients.length - readCount
		}
	}, [task, isOwnTask, parsedOrgId, currentRecipient])

	// Обработчик отправки формы
	const handleSubmit = ({
		answer,
		taskAnswerState
	}: {
		answer: string
		taskAnswerState: string
	}) => {
		if (answer.length && currentRecipient) {
			answerTask({
				description: answer,
				recipientId: currentRecipient.id,
				files: fileIds,
				typeId: taskAnswerState
			})
				.then(() => {
					form.resetFields()
					clearFiles()
				})
				.catch(error => {
					console.error('Ошибка при отправке ответа:', error)
				})
		}
	}

	// Показываем экран загрузки, если данные еще не получены
	if (isLoading || !task || isLoadingUser) return <LoadingScreen />

	// Значение для проверки принадлежности задачи
	const isOwn = isOwnTask()

	return (
		<main className='w-full h-full'>
			<Badge.Ribbon
				placement='start'
				text={
					task.isCompleted ? 'Topshiriq yakunlangan' : (task.TaskState?.name ?? 'Holat mavjud emas')
				}
				style={{
					fontSize: 20,
					height: 34,
					top: -2
				}}>
				<div className='border border-[#363850] rounded-lg h-full w-full pb-5'>
					<Row
						gutter={16}
						className='w-full py-10 px-3'>
						<Col
							xs={8}
							className='flex flex-col gap-y-4 space-y-4'>
							<TaskHeader title={task.name} />
							<TaskInfo task={task} />
						</Col>

						<Col
							xs={16}
							className='flex flex-col gap-y-3 items-end w-fit flex-1'>
							<TaskActions
								isOwnTask={isOwn}
								orgId={orgId}
								navigate={navigate}
								taskCompleted={task.isCompleted}
								isUpdating={isUpdating}
								updateTaskAnswerState={updateTaskAnswerState}
								task={task}
								taskStates={taskStates}
								currentRecipient={currentRecipient}
								onMarkAsRead={handleMarkAsRead}
							/>
							{!isOwn && (
								<TaskResults
									task={task}
									user={user}
									orgId={orgId}
								/>
							)}
						</Col>
					</Row>

					<TaskDescription description={task.description} />

					{!!task.files.length && <TaskFiles files={task.files} />}

					<TaskRecipients
						filteredRecipients={filteredRecipients}
						recipientCounts={recipientCounts}
						readStatusFilter={readStatusFilter}
						setReadStatusFilter={setReadStatusFilter}
						showAllRecipients={showAllRecipients}
						setShowAllRecipients={setShowAllRecipients}
						isOwnTask={isOwn}
						navigate={navigate}
						currentRecipient={currentRecipient}
						task={task}
					/>

					{!isOwn && !task.isCompleted && (
						<TaskAnswerForm
							form={form}
							handleSubmit={handleSubmit}
							recipientAnswerTypes={recipientAnswerTypes}
							isLoadingRecipientAnswerTypes={isLoadingRecipientAnswerTypes}
							FileUpload={FileUpload}
							isPending={isPending}
						/>
					)}

					{!isOwn && parsedOrgId && (
						<TaskSubTasks subTasks={task.SubTasks.filter(task => task.createdById === user?.id)} />
					)}
				</div>
			</Badge.Ribbon>
		</main>
	)
}

export default TaskDetails
