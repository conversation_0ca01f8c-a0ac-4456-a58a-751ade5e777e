import React, { useEffect } from 'react'
import { GeoJSON, useMap } from 'react-leaflet'
import { FeatureCollection } from 'geojson'
import { createFeatureStyle } from '../utils/mapHelpers'

interface InteractiveGeoJSONProps {
	geoJsonData: FeatureCollection
	onFeatureClick: (feature: any) => void
	onAreaClick?: (feature: any) => void
	isAreaClickable?: (feature: any) => boolean
	fitBounds?: boolean
	onMouseEnter?: (feature: any, event: MouseEvent, map?: any) => void
	onMouseLeave?: () => void
	onMouseMove?: (event: MouseEvent) => void
}

export const InteractiveGeoJSON: React.FC<InteractiveGeoJSONProps> = ({
	geoJsonData,
	onFeatureClick,
	onAreaClick,
	isAreaClickable,
	fitBounds = false,
	onMouseEnter,
	onMouseLeave,
	onMouseMove
}) => {
	const map = useMap()

	// Автоматически подгоняем карту под границы данных
	useEffect(() => {
		if (fitBounds && geoJsonData?.features?.length > 0) {
			try {
				// @ts-ignore - метод у L.geoJSON есть, но типы не определены корректно
				const bounds = L.geoJSON(geoJsonData).getBounds()
				map.fitBounds(bounds, { padding: [20, 20] })
			} catch (error) {
				console.error('Error fitting bounds:', error)
			}
		}
	}, [geoJsonData, fitBounds, map])

	// Обрабатываем события пользовательского интерфейса
	useEffect(() => {
		// Обработчик события для кнопки "Подробнее" в popup
		const handleSelectFeature = (e: any) => {
			if (e.detail?.properties) {
				onFeatureClick(e.detail)
			}
		}

		// Обработчик события для кнопки "Перейти" в popup
		const handleNavigateToArea = (e: any) => {
			if (e.detail?.properties && onAreaClick) {
				onAreaClick(e.detail)
				if (e.detail._popup) {
					e.detail._popup.close()
				}
			}
		}

		window.addEventListener('selectFeature', handleSelectFeature)
		window.addEventListener('navigateToArea', handleNavigateToArea)

		return () => {
			window.removeEventListener('selectFeature', handleSelectFeature)
			window.removeEventListener('navigateToArea', handleNavigateToArea)
		}
	}, [onFeatureClick, onAreaClick])

	return (
		<GeoJSON
			key={JSON.stringify(geoJsonData)}
			data={geoJsonData}
			style={feature => {
				const colorIndex = feature?.properties?.colorIndex ?? 0
				const clickable = isAreaClickable ? isAreaClickable(feature) : false
				return createFeatureStyle(colorIndex, false, clickable)
			}}
			onEachFeature={(feature, layer) => {
				const clickable = isAreaClickable ? isAreaClickable(feature) : false // Добавляем интерактивность
				layer.on({
					mouseover: e => {
						const targetLayer = e.target
						const colorIndex = feature?.properties?.colorIndex ?? 0
						const hoverStyle = createFeatureStyle(colorIndex, true, clickable)
						targetLayer.setStyle(hoverStyle)
						targetLayer.bringToFront() // Меняем курсор для кликабельных областей
						if (clickable) {
							const element = targetLayer.getElement()
							if (element) {
								element.style.cursor = 'pointer'
							}
						}

						// Вызываем обработчик hover для отображения названия
						if (onMouseEnter) {
							onMouseEnter(feature, e.originalEvent, map)
						}
					},
					mouseout: e => {
						const targetLayer = e.target
						const colorIndex = feature?.properties?.colorIndex ?? 0
						const normalStyle = createFeatureStyle(colorIndex, false, clickable)
						targetLayer.setStyle(normalStyle)

						// Возвращаем обычный курсор
						const element = targetLayer.getElement()
						if (element) {
							element.style.cursor = 'grab'
						}

						// Вызываем обработчик для скрытия tooltip
						if (onMouseLeave) {
							onMouseLeave()
						}
					},
					mousemove: e => {
						// Обрабатываем движение мыши для обновления позиции tooltip
						if (onMouseMove) {
							onMouseMove(e.originalEvent)
						}
					},
					click: e => {
						// Предотвращаем немедленное закрытие popup
						e.originalEvent?.stopPropagation()

						// Сначала показываем информационную панель
						onFeatureClick(feature)

						// Для кликабельных областей добавляем небольшую задержку перед навигацией
						if (isAreaClickable && isAreaClickable(feature) && onAreaClick) {
							setTimeout(() => {
								// Закрываем popup перед навигацией
								if (layer.getPopup()) {
									layer.closePopup()
								}
								onAreaClick(feature)
							}, 500) // Задержка 1.5 секунды для просмотра popup
						}
					}
				})
			}}
		/>
	)
}
