import { useContext } from 'react'
import { Button, Tooltip } from 'antd'
import { MoonFilled, SunFilled } from '@ant-design/icons'
import { ThemeContext } from '../../providers/theme-providers'

export const ThemeButton = () => {
	const { theme, toggleMode } = useContext(ThemeContext)
	return (
		<Tooltip
			title="Mav<PERSON><PERSON> o'zgaritirish"
			className='p-4 bg-gray-200 dark:bg-primary'>
			<Button
				shape='circle'
				type='text'
				size='large'
				onClick={() => toggleMode()}
				icon={theme === 'dark' ? <SunFilled /> : <MoonFilled />}
			/>
		</Tooltip>
	)
}
