import { axiosPrivate } from '@/config/api'
import { taskEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { TTaskRequest } from './create'
import { AxiosError, AxiosResponse } from 'axios'
import { TTask } from './getOne'
import { notification } from 'antd'
import { TApiErrorResponse } from '@/shared/types/api-error.type'

type TCreateSubTask = {
	id: string
	data: TTaskRequest
}

export const useCreateSubTask = (id: string) => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({ id, data }: TCreateSubTask) => {
			return await axiosPrivate.post(taskEndpoints.one(id), data)
		},
		onSuccess: async (response: AxiosResponse<TTask>) => {
			console.log(response)
			notification.success({ message: 'Topshiriq muaffaqiyatli yaratil<PERSON>!' })
			queryClient.invalidateQueries({ queryKey: [taskEndpoints.one(id)] })
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response)
			notification.error({ message: 'Topshiriq yaratishda xatolik yuz berdi!' })
		}
	})
}
