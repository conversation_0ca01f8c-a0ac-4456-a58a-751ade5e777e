import { Button } from 'antd'
import { ScanFaceIcon } from 'lucide-react'
import { useFaceIDStore } from './utils/face-id-modal-store'
import { useParams } from 'react-router-dom'

type Props = {
	orgId?: string
}

export const FaceId = ({ orgId }: Props) => {
	const { onOpen } = useFaceIDStore()
	const { id } = useParams()
	return (
		<>
			<Button onClick={() => onOpen(orgId ?? id)}>
				<ScanFaceIcon />
			</Button>
		</>
	)
}
