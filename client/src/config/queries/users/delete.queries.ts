import { axiosPrivate } from '@/config/api'
import { usersEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { TUser } from './get-all.queries'
import { AxiosError } from 'axios'
import { TApiErrorResponse } from '@/shared/types/api-error.type'
import { notification } from 'antd'

export type TUserResponse = TUser

export const useDeleteUser = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async (id: string) => {
			return await axiosPrivate.delete(usersEndpoints.one(id))
		},
		onSuccess: async () => {
			queryClient.invalidateQueries({ queryKey: [usersEndpoints.all] })
			notification.success({
				message: 'Muvaffaqiyatli o`chirildi',
				placement: 'bottomRight'
			})
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response?.data.message)
		}
	})
}
