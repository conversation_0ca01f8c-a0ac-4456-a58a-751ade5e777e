import { Injectable } from '@nestjs/common';
import { join } from 'path';

@Injectable()
export class CoordsService {
  private geoPath = join(process.cwd(), 'json', 'geo');

  private regionPath() {
    return join(this.geoPath, 'coords.json');
  }

  private regionDistrictsPath(regionId: string) {
    return join(this.geoPath, `${regionId}`, 'coords.json');
  }

  private districtSectionsPath(regionId: string, districtId: string) {
    let distId = districtId;

    if (districtId.length === 7) {
      distId = districtId.substring(districtId.length - 3);
    }
    return join(this.geoPath, `${regionId}`, `${distId}.json`);
  }

  private async sectionCoordsPath(regionId: string, districtId: string, sectionId: string) {
    const distId = districtId.substring(districtId.length - 3);

    const district = join(this.geoPath, `${regionId}`, `${distId}.json`);

    const districtJson = await import(district);
    let sectId = sectionId;

    if (sectionId.length === 13) {
      sectId = sectionId.substring(sectionId.length - 6);
    }

    return districtJson.filter((section) => {
      return section.section_id == sectId;
    });
  }

  async getCoords(regionId?: string, districtId?: string, sectionId?: string) {
    if (!regionId) {
      return await import(this.regionPath());
    }

    if (!districtId) {
      return await import(this.regionDistrictsPath(regionId));
    }

    if (!sectionId) {
      return await import(this.districtSectionsPath(regionId, districtId));
    }
    return await this.sectionCoordsPath(regionId, districtId, sectionId);
  }
}
