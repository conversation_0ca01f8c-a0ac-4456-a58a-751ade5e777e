import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import * as timezone from 'dayjs/plugin/timezone';
import 'dayjs/locale/uz-latn';
import * as isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import * as isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
// Dayjs pluginlarini o'rnatish

dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.locale('uz-latn');

export const DEFAULT_TIMEZONE = 'Asia/Tashkent';

/**
 * Timezone bilan ishlash uchun yordamchi funksiyalar
 */
export class TimezoneHelper {
  static oneDayUTC(date?: Date | string | dayjs.Dayjs) {
    return {
      startOf: dayjs.utc().startOf('day').toDate(),
      endOf: dayjs.utc().endOf('day').toDate(),
    };
  }

  /**
   * UTC vaqtni mahalliy vaqtga o'tkazish
   */
  static toLocalTime(utcDate: Date | string): dayjs.Dayjs {
    return dayjs.utc(utcDate).tz(DEFAULT_TIMEZONE);
  }

  /**
   * Mahalliy vaqtni UTC ga o'tkazish
   */
  static toUTC(localDate: Date | string | dayjs.Dayjs): dayjs.Dayjs {
    if (dayjs.isDayjs(localDate)) {
      return localDate.utc();
    }
    return dayjs.tz(localDate, DEFAULT_TIMEZONE).utc();
  }

  /**
   * Kunning boshi (00:00:00) - mahalliy vaqtda
   */
  static getStartOfDay(date?: Date | string | dayjs.Dayjs): dayjs.Dayjs {
    const dayObj = date ? dayjs(date) : dayjs();
    return dayObj.tz(DEFAULT_TIMEZONE).startOf('day');
  }

  /**
   * Kunning oxiri (23:59:59.999) - mahalliy vaqtda
   */
  static getEndOfDay(date?: Date | string | dayjs.Dayjs): dayjs.Dayjs {
    const dayObj = date ? dayjs(date) : dayjs();
    return dayObj.tz(DEFAULT_TIMEZONE).endOf('day');
  }

  /**
   * UTC sanani olish, lekin mahalliy vaqt zonasida hisoblash
   */
  static getUTCRangeForLocalDay(date?: Date | string | dayjs.Dayjs): {
    start: Date;
    end: Date;
  } {
    const localStart = this.getStartOfDay(date);
    const localEnd = this.getEndOfDay(date);
    return {
      start: localStart.utc().toDate(),
      end: localEnd.utc().toDate(),
    };
  }

  /**
   * Joriy mahalliy vaqtni olish
   */
  static now(): dayjs.Dayjs {
    return dayjs().tz(DEFAULT_TIMEZONE);
  }

  /**
   * Vaqtni mahalliy formatda ko'rsatish
   */
  static formatLocal(date: Date | string, format: string = 'YYYY-MM-DD HH:mm:ss'): string {
    return this.toLocalTime(date).format(format);
  }

  /**
   * Faqat vaqtni ko'rsatish (HH:mm)
   */
  static formatTime(date: Date | string): string {
    return this.toLocalTime(date).format('HH:mm');
  }

  /**
   * Frontend uchun format
   */
  static formatForDisplay(date: Date | string): string {
    return this.toLocalTime(date).format('DD.MM.YYYY HH:mm');
  }

  /**
   * Debug uchun vaqt zonalari bilan format
   */
  static debugFormat(date: Date | string): string {
    const utc = dayjs.utc(date);
    const local = this.toLocalTime(date);
    return `UTC: ${utc.format('YYYY-MM-DD HH:mm:ss')} | Local: ${local.format('YYYY-MM-DD HH:mm:ss')} (${DEFAULT_TIMEZONE})`;
  }

  /**
   * Ikki vaqt o'rtasidagi farqni daqiqalarda hisoblash
   */
  static diffInMinutes(start: Date | string, end: Date | string): number {
    const startTime = dayjs(start);
    const endTime = dayjs(end);
    return endTime.diff(startTime, 'minute');
  }

  /**
   * Vaqtni to'g'ri parse qilish
   */
  static parseDateTime(dateTimeValue: string | Date): dayjs.Dayjs | null {
    try {
      if (!dateTimeValue) {
        return null;
      }
      const parsedDate = dayjs(dateTimeValue);
      if (!parsedDate.isValid()) {
        return null;
      }
      return parsedDate.utc();
    } catch (error) {
      return null;
    }
  }
}
