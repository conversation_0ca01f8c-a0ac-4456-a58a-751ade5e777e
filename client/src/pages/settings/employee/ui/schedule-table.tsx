import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { UserWorkingScheduleTypes } from '@/config/queries/user-working-schedule-day/create.queries'
import { useGetUserWorkingSchedule } from '@/config/queries/user-working-schedule/get-queries'
import { useUpdateUserWorkingSchedule } from '@/config/queries/user-working-schedule/update.queries'
import { usePermissions } from '@/shared/guards/usePermissions'
import { LoadingScreen } from '@/shared/ui/suspense'
import { DisconnectOutlined } from '@ant-design/icons'
import { Button, Modal, Table } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'
import { useParams } from 'react-router-dom'

type Props = {
	workspace?: 'organization' | 'personal'
}

const ScheduleTable = ({ workspace = 'organization' }: Props) => {
	const { id } = useParams()
	const { data: user, isLoading } = useAuthMe()
	const { data: scheduleData } = useGetUserWorkingSchedule(
		workspace === 'personal' ? user?.id : undefined
	)
	const { hasPermission } = usePermissions()
	const updateScheduleMutation = useUpdateUserWorkingSchedule()

	const handleDelete = (dayId: string) => {
		Modal.confirm({
			title: 'Haqiqatan ham bu Ish vaqtini ajratmoqchimisiz?',
			okText: 'Ajratish',
			cancelText: 'Bekor qilish',
			onOk: () => {
				if (id && scheduleData?.data) {
					const currentSchedule = scheduleData.data.flat()?.[0]

					const payload: UserWorkingScheduleTypes = {
						userId: id,
						// @ts-ignore
						days: currentSchedule.days.map(item => item.id).filter(item => item !== dayId),
						name: currentSchedule.name,
						day: currentSchedule.day,
						startTime: currentSchedule.startTime,
						endTime: currentSchedule.endTime
					}

					updateScheduleMutation.mutate({
						data: payload,
						scheduleId: currentSchedule.id ?? ''
					})
				}
			}
		})
	}

	if (!user || isLoading) return <LoadingScreen />

	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	const columns: ColumnsType<any> = [
		{
			title: 'Nomi',
			dataIndex: 'name',
			key: 'name'
		},
		{
			title: 'Kuni',
			dataIndex: 'day',
			key: 'day'
		},
		{
			title: 'Boshlanish vaqti',
			dataIndex: 'startTime',
			key: 'startTime',
			render: text => dayjs(text).format('HH:mm')
		},
		{
			title: 'Tugash vaqti',
			dataIndex: 'endTime',
			key: 'endTime',
			render: text => dayjs(text).format('HH:mm')
		},
		hasPermission(user.role, ['delete:*']) && workspace === 'organization'
			? {
					title: 'Amallar',
					key: 'actions',
					render: rec => (
						<div>
							<Button
								danger
								onClick={() => handleDelete(rec.id)}
								icon={<DisconnectOutlined />}
							/>
						</div>
					)
				}
			: { key: 'actions' }
	]

	return (
		<Table
			columns={columns}
			dataSource={
				scheduleData?.data
					?.sort((a, b) => a.day - b.day)
					.flatMap(
						(schedule: UserWorkingScheduleTypes & { days?: string[] }) => schedule.days || []
					) || []
			}
			rowKey='id'
		/>
	)
}
export default ScheduleTable
