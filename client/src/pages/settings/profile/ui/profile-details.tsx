import { useParams } from 'react-router-dom'
import { Card, Spin, Table } from 'antd'
import { useGetUserAttendance } from '@/config/queries/user-attendance/get.queries'
import { formatDate } from '@/shared/utils/formatDate'

const ProfileDetails = () => {
	const { id } = useParams()
	const { data: attendance, isLoading } = useGetUserAttendance(id || '')

	if (isLoading) return <Spin />

	const columns = [
		{
			title: 'Tashkilot nomi',
			dataIndex: ['Organization', 'name'],
			key: 'orgName'
		},
		{
			title: 'FIO',
			dataIndex: ['User', 'fullName'],
			key: 'userName'
		},
		{
			title: 'Vaqti',
			dataIndex: 'time',
			key: 'time',
			render: (time: string) => formatDate(time)
		},
		{
			title: 'Turi',
			dataIndex: 'type',
			key: 'type'
		}
	]

	return (
		<Card title='Profile Details'>
			<Table
				loading={isLoading}
				scroll={{ x: 'max-content' }}
				dataSource={attendance?.data}
				columns={columns}
				rowKey='id'
				pagination={false}
			/>
		</Card>
	)
}
export default ProfileDetails
