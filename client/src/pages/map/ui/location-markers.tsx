import { useGetLocationsBySection } from '@/config/queries/gps/gps.queries.ts'
import { Marker, Popup, useMap } from 'react-leaflet'
import L from 'leaflet'
import { useId } from 'react'
import { Link, useSearchParams } from 'react-router-dom'
import { Building, ChevronRight } from 'lucide-react'
import { Avatar, Button, Card, Tag, Space, Typography } from 'antd'
import {
	ClockCircleOutlined,
	BankOutlined,
	EnvironmentOutlined,
	EyeOutlined,
	CheckCircleOutlined,
	CloseCircleOutlined
} from '@ant-design/icons'

const { Text, Title } = Typography

const formatTimeAgo = (dateString: string): string => {
	const now = new Date()
	const date = new Date(dateString)
	const diffMs = now.getTime() - date.getTime()
	const diffMins = Math.floor(diffMs / (1000 * 60))
	const diffHours = Math.floor(diffMins / 60)
	const diffDays = Math.floor(diffHours / 24)

	if (diffMins < 1) return 'hozir'
	if (diffMins < 60) return `${diffMins} min oldin`
	if (diffHours < 24) return `${diffHours} soat oldin`
	return `${diffDays} kun oldin`
}

export default function LocationMarkers() {
	const { data } = useGetLocationsBySection()
	const [search] = useSearchParams()
	const id = useId()
	const districtId = search.get('districtId')

	const map = useMap()

	// Создаем кастомные иконки маркеров
	const createCustomMarkerIcon = (isInArea: boolean, userInitials: string, type: string = 'IN') => {
		// Определяем цвета в зависимости от состояния и местоположения пользователя:
		// 1. Зеленый - для пользователей в зоне (isInArea = true) и онлайн (type не 'DISABLED')
		// 2. Желтый - для пользователей вне зоны (isInArea = false) и онлайн (type не 'DISABLED')
		// 3. Красный - для пользователей оффлайн (type = 'DISABLED') независимо от местоположения

		const isOffline = type === 'DISABLED'

		// Цвета для маркера
		const backgroundColor = isOffline ? '#ff4d4f' : isInArea ? '#52c41a' : '#faad14' // зеленый, желтый или красный

		const pulseColor = isOffline ? '#ff7875' : isInArea ? '#95de64' : '#ffd666' // оттенок для пульсации

		const shadowColor = isOffline
			? 'rgba(255, 77, 79, 0.4)'
			: isInArea
				? 'rgba(82, 196, 26, 0.4)'
				: 'rgba(250, 173, 20, 0.4)'

		// Определяем иконку должности
		const getPositionIcon = () => {
			return '👤'
		}

		return L.divIcon({
			html: `
			<div style="position: relative; width: 48px; height: 48px;">
				<!-- Анимированный пульс -->
				<div style="
					position: absolute;
					top: 50%;
					left: 50%;
					width: 48px;
					height: 48px;
					background: ${pulseColor};
					border-radius: 50%;
					transform: translate(-50%, -50%);
					animation: pulse 2s infinite;
					opacity: 0.6;
				"></div>
				
				<!-- Основной маркер -->
				<div style="
					position: absolute;
					top: 50%;
					left: 50%;
					width: 36px;
					height: 36px;
					background: linear-gradient(135deg, ${backgroundColor} 0%, ${backgroundColor}dd 100%);
					border: 3px solid white;
					border-radius: 50%;
					transform: translate(-50%, -50%);
					box-shadow: 0 4px 12px ${shadowColor};
					display: flex;
					align-items: center;
					justify-content: center;
					font-weight: bold;
					font-size: 12px;
					color: white;
					text-shadow: 0 1px 2px rgba(0,0,0,0.3);
				">
					${userInitials}
				</div>
				
				<!-- Статус индикатор -->
				<div style="
					position: absolute;
					top: 2px;
					right: 2px;
					width: 12px;
					height: 12px;
					background: ${isOffline ? '#ef4444' : isInArea ? '#22c55e' : '#faad14'};
					border: 2px solid white;
					border-radius: 50%;
					box-shadow: 0 2px 4px rgba(0,0,0,0.2);
				"></div>
				
				<!-- Иконка должности -->
				<div style="
					position: absolute;
					bottom: -2px;
					left: 50%;
					transform: translateX(-50%);
					width: 20px;
					height: 20px;
					background: white;
					border-radius: 50%;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 12px;
					box-shadow: 0 1px 3px rgba(0,0,0,0.2);
				">
					${getPositionIcon()}
				</div>
			</div>
			
			<style>				@keyframes pulse {
					0% {
						transform: translate(-50%, -50%) scale(1);
						opacity: 0.6;
					}
					50% {
						transform: translate(-50%, -50%) scale(1.3);
						opacity: 0.2;
					}
					100% {
						transform: translate(-50%, -50%) scale(1);
						opacity: 0.6;
					}
				}.custom-location-popup .leaflet-popup-content-wrapper {
					background: transparent !important;
					border-radius: 16px !important;
					box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25) !important;
					backdrop-filter: blur(10px) !important;
				}
				
				.custom-location-popup .leaflet-popup-content {
					margin: 0 !important;
					border-radius: 16px;
					overflow: hidden;
				}				.custom-location-popup .leaflet-popup-tip {
					background: white !important;
					border: none !important;
					box-shadow: 0 4px 6px rgba(0,0,0,0.1) !important;
				}
						@media (max-width: 480px) {
					.custom-location-popup .leaflet-popup-content-wrapper {
						min-width: 260px !important;
						max-width: 85vw !important;
					}
				}
			</style>
		`,
			className: 'custom-marker-icon',
			iconSize: [48, 48],
			iconAnchor: [24, 24],
			popupAnchor: [0, -24]
		})
	}

	if (!data?.history || data.history.length === 0) return null

	const handleMarkerClick = (_: string, lat: number, lng: number) => {
		map.setView([lat, lng], Math.max(map.getZoom(), 14), { animate: true })
	}

	if (!districtId) {
		return null
	}

	return (
		<>
			{data.history.map((item, index: number) => {
				// Создаем инициалы пользователя
				const fullName = item.fullName || 'Unknown User'
				const initials = fullName
					.split(' ')
					.slice(0, 2)
					.map((name: string) => name.charAt(0).toUpperCase())
					.join('')

				const position = item.position
				const organizations = item.organization || []
				const isInArea = item.isInArea ?? false
				const lastUpdate = formatTimeAgo(item.createdAt)

				return (
					<Marker
						key={item.id + id + index}
						position={[item.lat, item.lng]}
						title={fullName}
						icon={createCustomMarkerIcon(isInArea, initials, item.type)}
						eventHandlers={{
							click: () => handleMarkerClick(item.id || `${index}`, item.lat, item.lng)
						}}>
						<Popup
							className='custom-location-popup'
							maxWidth={300}
							minWidth={280}>
							<Card
								className='!p-0 !m-0 border-0 shadow-2xl overflow-hidden '
								style={{ margin: '-12px', minWidth: '280px' }}
								styles={{
									body: { padding: 0 }
								}}>
								<div
									className='px-3 py-3 relative overflow-hidden rounded-t-2xl'
									style={{
										background: isInArea
											? 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)'
											: 'linear-gradient(135deg, #ff4d4f 0%, #cf1322 100%)'
									}}>
									<div className='absolute inset-0 opacity-10'>
										<div
											className='absolute inset-0'
											style={{
												backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
												backgroundSize: '30px 30px'
											}}></div>
									</div>

									<div className='flex items-center justify-between relative z-10'>
										<div className='flex items-center space-x-2'>
											<div>
												<Title
													level={5}
													className='!text-white !mb-0 !text-sm font-semibold'>
													{fullName}
												</Title>
											</div>
										</div>
									</div>
									<div className='flex justify-between items-center mt-2'>
										<Text className='!text-white text-xs'>{formatTimeAgo(item.createdAt)}</Text>
										<Tag
											color={isInArea ? 'success' : 'error'}
											icon={isInArea ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
											className='border-0 font-medium text-xs'>
											{isInArea ? 'Hududda' : 'Tashqarida'}
										</Tag>
									</div>
									<div className='absolute -top-2 -right-2 w-16 h-16 opacity-10'>
										<EnvironmentOutlined className='text-8xl' />
									</div>
								</div>
								<div className='p-4 bg-gradient-to-br from-gray-50 via-white to-blue-50 dark:from-gray-800 dark:via-gray-900 dark:to-gray-700 rounded-b-xl'>
									<Space
										direction='vertical'
										size='small'
										className='w-full mt-2'>
										{position && (
											<div className='flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 hover:shadow-lg hover:border-blue-200 dark:hover:border-blue-800 transition-all duration-300 transform hover:-translate-y-1'>
												<Avatar
													size={36}
													className='bg-gradient-to-br from-blue-400 to-blue-600 shadow-md'
													icon={<BankOutlined className='w-4 h-4 text-white' />}
												/>
												<div className='flex-1 min-w-0'>
													<Text className='text-xs block font-medium uppercase tracking-wide'>
														Lavozim
													</Text>
													<Text className='font-semibold block text-sm leading-tight'>
														{position.name}
													</Text>
												</div>
											</div>
										)}
										{organizations.length > 0 && (
											<div className='flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 hover:shadow-lg hover:border-purple-200 transition-all duration-300 transform hover:-translate-y-1'>
												<Avatar
													size={36}
													className='bg-gradient-to-br from-purple-400 to-purple-600 shadow-md'
													icon={<Building className='w-4 h-4 text-white' />}
												/>
												<div className='flex-1 min-w-0'>
													<Text className='text-gray-500 text-xs block font-medium uppercase tracking-wide'>
														Tashkilot
													</Text>
													<Text className='font-semibold text-gray-900 block text-sm leading-tight'>
														{organizations[0]?.name}
													</Text>
													{organizations.length > 1 && (
														<Tag
															color='geekblue'
															className='text-xs mt-1 rounded-full'>
															+{organizations.length - 1} yana
														</Tag>
													)}
												</div>
											</div>
										)}
										<div className='flex items-center gap-3 p-3 bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 hover:shadow-lg hover:border-orange-200 transition-all duration-300 transform hover:-translate-y-1'>
											<Avatar
												size={36}
												className='bg-gradient-to-br from-orange-400 to-orange-600 shadow-md'
												icon={<ClockCircleOutlined className='text-white' />}
											/>
											<div className='flex-1 min-w-0'>
												<Text className='text-gray-500 text-xs block font-medium uppercase tracking-wide'>
													So'nggi yangilanish
												</Text>
												<Text className='font-semibold text-gray-900 block text-sm leading-tight'>
													{lastUpdate}
												</Text>
											</div>
										</div>
										<Link
											to={`/workspace/map/location-user/${item.id}`}
											className='block'>
											<Button
												type='primary'
												size='small'
												block
												icon={<EyeOutlined />}
												className='font-semibold shadow-lg mt-4'>
												<span className='flex items-center justify-center space-x-2'>
													<span className='text-base'>Harakat yo'li</span>
													<ChevronRight className='w-4 h-4 ml-1 transition-transform group-hover:translate-x-1' />
												</span>
											</Button>
										</Link>
									</Space>
								</div>
							</Card>
						</Popup>
					</Marker>
				)
			})}
		</>
	)
}
