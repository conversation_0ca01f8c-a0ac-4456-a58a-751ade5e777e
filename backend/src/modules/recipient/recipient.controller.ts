import { Controller, Get, Param } from '@nestjs/common';
import { RecipientService } from './recipient.service';
import { ProtectedRoute } from 'src/common';

@Controller('recipient')
export class RecipientController {
  constructor(private readonly recipientService: RecipientService) {}

  // @Post()
  // create(@Body() createRecipientDto: CreateRecipientDto) {
  //   return this.recipientService.create(createRecipientDto);
  // }

  // @Get()
  // findAll() {
  //   return this.recipientService.findAll();
  // }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
  })
  @Get(':id')
  async findOne(@Param('id') id: string) {
    return await this.recipientService.findOne(id);
  }

  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updateRecipientDto: UpdateRecipientDto) {
  //   return this.recipientService.update(+id, updateRecipientDto);
  // }

  // @Delete(':id')
  // remove(@Param('id') id: string) {
  //   return this.recipientService.remove(+id);
  // }

  @ProtectedRoute({
    isPublic: false,
    context: 'PERSONAL',
  })
  @Get('inspector/:id')
  async findOneRsipent(@Param('id') id: string) {
    return await this.recipientService.findOne(id);
  }
}
