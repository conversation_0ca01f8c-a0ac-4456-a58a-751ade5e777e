import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { Logger, ValidationPipe } from '@nestjs/common';
import * as cookieParser from 'cookie-parser';
import { PrismaClientExceptionFilter } from './common/exceptions/prisma-exception';
import * as cl from 'cluster';
import { cpus } from 'os';
import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
import * as express from 'express';
import * as process from 'node:process';

const cluster = cl as unknown as cl.Cluster;

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  app.setGlobalPrefix('api/v1');

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  app.use(express.json({ limit: '50mb' }));
  app.use(express.urlencoded({ limit: '50mb', extended: true }));

  app.enableCors({
    credentials: true,
    origin: (origin, callback) => {
      const allowedOrigins = [
        'https://new.mnazorat.uz',
        'http://new.mnazorat.uz',
        'http://localhost:5173',
        'http://localhost:8001',
      ];

      if (!origin || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error('CORS policy violation'));
      }
    },
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  });

  app.use('/api/v1/static', express.static(join(process.cwd(), 'static')));

  app.use(cookieParser());
  const httpAdapter = app.getHttpAdapter();
  app.useGlobalFilters(new PrismaClientExceptionFilter(httpAdapter));

  const port = process.env.PORT || 3000;
  await app.listen(port);
  return port;
}

let processCount = 0;
if (process.env.NODE_ENV === 'development') {
  bootstrap().then((port) => {
    Logger.log(`Backend is running on http://localhost:${port}/api/v1`, 'Bootstrap');
    Logger.log(`BullBoard is running on http://localhost:${port}/api/v1/queues`, 'Bootstrap');
  });
} else if (cluster.isPrimary) {
  const numCPUs = cpus().length;
  Logger.log(`Основной процесс запущен. Запускаем ${numCPUs} рабочих процессов...`);
  for (let i = 0; i < numCPUs; i++) {
    if (i === 0) {
      process.env['PROCESS_WORKER_ID'] = '0';
    }
    cluster.fork();
  }

  cluster.on('exit', (worker, code, signal) => {
    Logger.warn(`Рабочий процесс ${worker.process.pid} завершился. Перезапуск...`);
    cluster.fork();
  });
  processCount++;
} else {
  bootstrap().then((port) => {
    Logger.log(`Рабочий процесс ${process.pid} запущен`);
    Logger.log(`Backend is running on http://localhost:${port}/api/v1`, 'Bootstrap');
    Logger.log(`BullBoard is running on http://localhost:${port}/api/v1/queues`, 'Bootstrap');
  });
}
