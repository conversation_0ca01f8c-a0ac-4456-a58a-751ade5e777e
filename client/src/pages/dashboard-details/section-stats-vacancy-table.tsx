import { useGetDashboardDetailsVacancy } from '@/config/queries/dashboard-details/get-dashboard-details'
import { useGetVacancyExcel } from '@/config/queries/dashboard-details/get-dashboard-details-excel'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { Button, Table } from 'antd'
import { DownloadOutlined } from '@ant-design/icons'
import { useSearchParams } from 'react-router-dom'

const SectionStatsVacancyTable = () => {
	const { data: vacancy, isLoading } = useGetDashboardDetailsVacancy()
	const [searchParams, setSearchParams] = useSearchParams()
	const organizationId = searchParams.get('orgId')
	const vacancyExcel = useGetVacancyExcel()

	const currentPage = parseInt(searchParams.get('page') || '1')
	const pageSize = parseInt(searchParams.get('limit') || '10')

	const handleDownloadExcel = () => {
		vacancyExcel.mutate(organizationId || undefined)
	}

	const columns = [
		{
			key: '#',
			title: '#',
			render: (_: any, _rec: any, index: number) => formatTableIndex(currentPage, pageSize, index)
		},
		{
			title: 'Tashkilot',
			dataIndex: ['Organization', 'name'],
			key: 'organizationName'
		},
		{
			title: 'Lavozim',
			dataIndex: ['OrganizationTypePosition', 'name'],
			key: 'positionName'
		}
	]

	const handleTableChange = (pagination: any) => {
		const newPage = pagination.current
		const newPageSize = pagination.pageSize

		// Update URL parameters
		setSearchParams(prev => {
			const newParams = new URLSearchParams(prev)
			newParams.set('page', newPage.toString())
			newParams.set('limit', newPageSize.toString())
			return newParams
		})
	}

	return (
		<div className='p-4'>
			<div className='flex justify-between items-center mb-4'>
				<h1 className='text-2xl font-bold'>Vakansiyalar ro`yhati</h1>
				<Button
					type='primary'
					icon={<DownloadOutlined />}
					onClick={handleDownloadExcel}
					loading={vacancyExcel.isPending}>
					Excel yuklab olish
				</Button>
			</div>
			<Table
				columns={columns}
				dataSource={vacancy?.data || []}
				rowKey='id'
				pagination={{
					current: currentPage,
					total: vacancy?.meta?.total,
					showTotal: (total, range) => `${range[0]}-${range[1]} tadan ${total} ta`,
					pageSize: vacancy?.meta?.limit
				}}
				loading={isLoading}
				onChange={handleTableChange}
			/>
		</div>
	)
}

export default SectionStatsVacancyTable
