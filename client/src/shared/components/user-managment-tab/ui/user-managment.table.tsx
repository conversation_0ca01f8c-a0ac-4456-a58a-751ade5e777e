import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { ROL<PERSON>, useGetUsersByOrg } from '@/config/queries/organizations/get-users.queries'
import { LoadingScreen } from '@/shared/ui/suspense'
import { Button, Checkbox, message, Modal, Table, Tooltip } from 'antd'
import { useEffect, useState } from 'react'
import { useUserManagementStore } from '../utils/user-managament-store'
import { Link, useNavigate, useParams, useSearchParams } from 'react-router-dom'
import { BanIcon, Clock, CopyIcon, EditIcon, MapPin, PlusIcon, TimerIcon } from 'lucide-react'
import { useFaceIDAddStore } from '../utils/face-id-add-modal-store'
import { useWorkerAddModalStore } from '../utils/worker-add-store'
import { EmployeeVocationCreate } from '@/pages/settings/employee/ui/employee-vocation-create'
import {
	TRole,
	useRemoveUserFromOrganization
} from '@/config/queries/organizations/remove-user.queries'
import { AttendanceAddModal } from '../../attendance-add/attendance-add-modal'
import { Search } from '../../search/search'
import { UsersModal } from '@/pages/settings/users/ui/users-modal'
import { useUsersModalStore } from '@/pages/settings/users/utils/users-modal-store'
import { WorkerAddModal } from './worker-add-modal'

const types: Record<string, string> = {
	worker: 'Ishchi',
	employee: 'Xodim',
	responsible: "Mas'ul"
}

export const UserManagementTable = () => {
	const { id } = useParams()
	const [pageSize, setPageSize] = useState(10)
	const { id: orgId, onClose } = useUserManagementStore()
	const [searchParams, setSearchParams] = useSearchParams()
	const { data: user, isLoading } = useAuthMe()
	const [role, setRole] = useState<ROLE[]>([])
	const storedOrgId = localStorage.getItem('organizationId')
	const [api, contextHolder] = message.useMessage()
	const storedOrgIdValue = storedOrgId ? JSON.parse(storedOrgId) : ''
	const effectiveOrgId = orgId || storedOrgIdValue || ''
	const {
		data: users,
		isLoading: isLoadingUsers,
		fetchNextPage,
		fetchPreviousPage,
		refetch,
		setPage
	} = useGetUsersByOrg(effectiveOrgId, role[0])
	const [isVocationModalOpen, setIsVocationModalOpen] = useState(false)
	const [selectedEmployeeId, setSelectedEmployeeId] = useState<string | null>(null)
	const [isAttendanceModalOpen, setIsAttendanceModalOpen] = useState(false)
	const { mutateAsync: removeUser } = useRemoveUserFromOrganization()
	const { onOpen: openUsersModal } = useUsersModalStore()

	const navigate = useNavigate()

	useEffect(() => {
		if (role.length > 0) {
			searchParams.set('roles', JSON.stringify(role))
		} else {
			searchParams.delete('roles')
		}
		setSearchParams(searchParams, { replace: true })
	}, [role, searchParams, setSearchParams])

	const { onOpen: addOnOpen } = useFaceIDAddStore()
	const { onOpen: openWorkerModal } = useWorkerAddModalStore()

	const handleDisconnectUser = (userId: string, roleType: string) => {
		Modal.confirm({
			title: `Haqiqatan ham bu foydalanuvchidan ${types[roleType]} lavozimini ajratmoqchimisiz?`,
			okText: 'Ajratish',
			cancelText: 'Bekor qilish',
			onOk: () => {
				if (!id && !storedOrgId) return
				let who = 'workers'

				switch (roleType) {
					case 'worker':
						who = 'workers'
						break
					case 'employee':
						who = 'employees'
						break
					case 'responsible':
						who = 'responsibles'
						break
					default:
						who = 'workers'
				}

				removeUser({
					data: { [who]: [userId] },
					orgId: id ?? JSON.parse(storedOrgId ?? ''),
					who: who as TRole
				}).then()
			}
		})
	}

	const handleAddUser = async (type: 'workers' | 'employees' | 'responsibles') => {
		if (type === 'workers') {
			openWorkerModal()
		} else {
			await addOnOpen(orgId!, type)
		}
		refetch()
	}

	const handleEditUser = (userData: any) => {
		openUsersModal(userData)
	}

	if (isLoading || !user?.role) return <LoadingScreen />

	return (
		<>
			{contextHolder}
			<WorkerAddModal />

			<div className='flex items-center justify-between p-3'>
				<div className='flex flex-col gap-y-2'>
					<h1 className='font-bold'>Foydalanuvchilar</h1>
					<div className='w-full flex items-center gap-x-3'>
						<Button
							type='primary'
							onClick={() => handleAddUser('workers')}>
							<PlusIcon />
							Ishchi
						</Button>
						<Button
							type='primary'
							onClick={() => handleAddUser('employees')}>
							<PlusIcon />
							Xodim
						</Button>
						<Button
							type='primary'
							onClick={() => handleAddUser('responsibles')}>
							<PlusIcon />
							Ma`sul
						</Button>
					</div>
				</div>

				<div className='flex flex-col items-center gap-y-2 border border-dashed p-2 rounded-sm'>
					<h1 className='font-bold'>Lavozimi bo`yicha saralash</h1>
					<Checkbox.Group onChange={value => setRole(value)}>
						<Checkbox value={ROLE.worker}>Ishchi</Checkbox>
						<Checkbox value={ROLE.employee}>Hodim</Checkbox>
						<Checkbox value={ROLE.responsible}>Ma'sul</Checkbox>
					</Checkbox.Group>
				</div>
			</div>
			<EmployeeVocationCreate
				open={isVocationModalOpen}
				onClose={() => setIsVocationModalOpen(false)}
				employeeId={selectedEmployeeId}
			/>
			<AttendanceAddModal
				isOpen={isAttendanceModalOpen}
				closeFn={() => {
					searchParams.delete('userId')
					setSearchParams(searchParams)
					setIsAttendanceModalOpen(false)
				}}
			/>

			<Table
				title={() => (
					<div className='flex items-center justify-between'>
						<UsersModal hideAddButton />
						<Search />
					</div>
				)}
				size='small'
				loading={isLoadingUsers}
				dataSource={users?.pages.flatMap(page => page.data)}
				pagination={{
					pageSize: pageSize,
					showSizeChanger: false,
					total: users?.pages[0]?.meta?.total,

					onChange: (page, size) => {
						if (page > 1) {
							fetchNextPage()
							setPageSize(size)
							setPage(page)
						} else {
							fetchPreviousPage()
						}
					}
				}}
				rowClassName='cursor-pointer'
				columns={[
					{ key: '#', title: '#', width: 40, render: (_, __, index) => ++index },
					{
						key: 'id',
						width: 40,
						title: 'id',
						render: () => (
							<p className='text-ellipsis overflow-hidden whitespace-nowrap w-10 cursor-pointer text-blue-500'>
								<CopyIcon size={20} />
							</p>
						),
						onCell: data => {
							return {
								onClick: () => {
									if (navigator.clipboard) {
										navigator.clipboard.writeText(data.id).then(
											() => {
												api.info({
													content: 'ID clipboardga nusxalandi',
													duration: 1.5,
													key: 'copyId'
												})
											},
											() => {
												api.error({
													content: 'ID nusxalashda xatolik',
													duration: 1.5,
													key: 'copyId'
												})
											}
										)
									}
								}
							}
						}
					},

					{
						key: 'fullName',
						title: 'FIO',
						dataIndex: 'fullName',
						className: 'hover:text-blue-500',
						onCell: data => {
							return {
								onClick: () => {
									onClose()
									navigate(`/workspace/settings/employee/${data.id}`)
								}
							}
						}
					},
					{
						key: 'position',
						title: 'Lavozim',
						dataIndex: ['position', 'name']
					},
					{
						key: 'imei',
						title: 'Imei',
						dataIndex: 'imei'
					},
					{
						key: 'username',
						title: 'Telefon raqami',
						dataIndex: 'username'
					},
					{
						key: 'status',
						title: 'Holati',
						dataIndex: 'status',
						render: status => (
							<span className={`text-${status === 'ACTIVE' ? 'green' : 'red'}-500`}>
								{status === 'ACTIVE' ? 'Faol' : 'Nofaol'}
							</span>
						)
					},
					{
						width: 150,

						key: 'faceId',
						title: 'Ohirgi kirish',
						dataIndex: ['Attendance'],
						render: attendance => (
							<span className={attendance?.length ? 'text-blue-500' : 'text-red-500'}>
								<Clock className='inline-block mr-1 w-4 h-4 text-yellow-500' />
								{attendance?.length > 0
									? new Date(attendance[0].createdAt).toLocaleString()
									: 'Ma`lumot yo`q'}
							</span>
						)
					},
					{
						width: 150,
						key: 'GPS',
						title: 'Ohirgi joylashuv',
						dataIndex: ['GPSLocationReport'],
						render: gps => (
							<span className={gps?.length ? 'text-blue-500' : 'text-red-500'}>
								<MapPin className='inline-block mr-1 w-4 h-4 text-red-500' />
								{gps?.length > 0 ? new Date(gps[0].createdAt).toLocaleString() : 'Ma`lumot yo`q'}
							</span>
						)
					},
					{
						key: 'organizations',
						title: 'Tashkilotlar',
						render: (_, rec) => (
							<div>
								<Tooltip
									title={
										<div>
											<ul className='flex flex-col gap-y-2'>
												{rec.Organization.slice(0, 2)?.map(org => (
													<li
														key={org.id}
														className='line-clamp-1'>
														<Link
															to={`/workspace/organizations/${org.id}`}
															className='!text-white'>
															{org.name}
														</Link>
													</li>
												))}
											</ul>
										</div>
									}>
									<ul>
										{rec.Organization.slice(0, 2)?.map(org => (
											<li
												key={org.id}
												className='line-clamp-1'>
												{org.name},
											</li>
										))}
									</ul>
								</Tooltip>
							</div>
						)
					},
					{
						key: 'position',
						title: 'Role',
						width: 250,
						render: (_, rec) => (
							<ul className='flex gap-x-2'>
								{rec.roleTypes?.map(role => (
									<Tooltip
										key={role}
										title={
											<div>
												<span className='font-bold text-red-400'>{types[role]}</span> lavozimdan
												olib tashlash
											</div>
										}>
										<Button
											onClick={() => handleDisconnectUser(rec.id, role)}
											size='small'
											className='flex items-center gap-x-1'
											danger>
											<BanIcon size={14} /> {types[role]}
										</Button>
									</Tooltip>
								))}
							</ul>
						)
					},

					{
						key: 'actions',
						title: 'Amallar',
						width: 200,
						render: rec => (
							<div className='flex items-center gap-x-2'>
								<Button
									size='small'
									onClick={() => {
										searchParams.set('userId', rec.id)
										setSearchParams(searchParams)
										setIsAttendanceModalOpen(true)
									}}>
									<TimerIcon size={16} />
								</Button>

								<Button
									size='small'
									onClick={() => {
										setIsVocationModalOpen(rec.id)
										setSelectedEmployeeId(rec.id)
									}}
									type='primary'>
									Tatil berish
								</Button>

								<Tooltip title='Foydalanuvchini tahrirlash'>
									<Button
										size='small'
										onClick={() => handleEditUser(rec)}
										icon={<EditIcon size={16} />}
										type='default'
									/>
								</Tooltip>
							</div>
						)
					}
				]}
			/>
		</>
	)
}
