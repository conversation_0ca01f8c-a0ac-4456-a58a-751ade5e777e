import { But<PERSON>, <PERSON><PERSON>, Drawer, Form } from 'antd'
import { useWorkingScheduleManagementStore } from './utils/working-schedule-managament-store'
import { LoadingScreen } from '@/shared/ui/suspense'
import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { useGetUserWorkingScheduleDay } from '@/config/queries/user-working-schedule-day/get.queries'
import { useAddWorkingScheduleToOrganization } from '@/config/queries/user-working-schedule/addToOrganization.queries'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import { useUserWorkingScheduleDayModalStore } from '@/pages/settings/user-working-schedule-day/utils/user-working-schedule-day-store'
import { UserWorkingScheduleDayModalForm } from '@/pages/settings/user-working-schedule-day/ui/user-working-schedule-day-modal'
import { useState } from 'react'
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.locale('uz-latn')
dayjs.tz.setDefault('Asia/Tashkent')

export const WorkingScheduleManagmentModal = () => {
	const [form] = Form.useForm()
	const { open, onClose, id } = useWorkingScheduleManagementStore()
	const { data: user, isLoading: isLoadingUser } = useAuthMe()
	const { data: days } = useGetUserWorkingScheduleDay()
	const { mutateAsync: addWorkingSchedule } = useAddWorkingScheduleToOrganization()
	const [selectedDays, setSelectedDays] = useState<string[]>([])

	const onClear = () => {
		form.resetFields()
		setSelectedDays([])
		onClose()
	}

	const handleSubmit = () => {
		if (!id) return
		const uniqueDays = [...new Set(selectedDays)]
		addWorkingSchedule({ orgId: id, days: uniqueDays }).then(onClear)
	}

	if (isLoadingUser || !user?.role) return <LoadingScreen />

	return (
		<Drawer
			title={
				<div className='flex items-center justify-between'>
					<h1>Ish jadvali</h1>
					<Button
						type='primary'
						onClick={() => useUserWorkingScheduleDayModalStore.setState({ open: true })}>
						Ish jadvali yaratish
					</Button>
				</div>
			}
			open={open}
			onClose={onClose}
			destroyOnClose
			footer={
				<div className='flex items-center justify-end gap-x-3'>
					<Button
						className='btn btn-outline-danger'
						onClick={onClear}>
						Bekor qilish
					</Button>
					<Button
						type='primary'
						className='btn btn-primary'
						onClick={form.submit}>
						Saqlash
					</Button>
				</div>
			}>
			<Form
				form={form}
				layout='vertical'
				onFinish={handleSubmit}>
				<Form.Item
					name='days'
					label='Ish kunlari'
					rules={[{ required: true, message: 'Iltimos, kamida bir ish kunini tanlang' }]}>
					<div className='flex flex-col gap-y-3'>
						{days?.pages?.[0]?.data
							.sort((a, b) => a.day - b.day)
							.map(day => (
								<Checkbox
									onChange={e =>
										e.target.checked
											? setSelectedDays([...selectedDays, e.target.value])
											: setSelectedDays(selectedDays.filter(id => id !== e.target.value))
									}
									value={day.id}
									key={day.id}
									className=''>
									{day.name} ({dayjs(day.startTime).format('HH:mm')} -{' '}
									{dayjs(day.endTime).format('HH:mm')})
								</Checkbox>
							))}
					</div>
				</Form.Item>
			</Form>

			<UserWorkingScheduleDayModalForm />
		</Drawer>
	)
}
