import { Alert } from 'antd'
import dayjs from 'dayjs'

import utc from 'dayjs/plugin/utc'
import 'dayjs/locale/uz-latn'

dayjs.extend(utc)

interface AttendancePositionReportInfoProps {
	reportPeriod: {
		startDate: string
		endDate: string
	} | null
}

export const AttendancePositionReportInfo = ({
	reportPeriod
}: AttendancePositionReportInfoProps) => {
	if (!reportPeriod) return null

	const { startDate, endDate } = reportPeriod

	return (
		<Alert
			type='info'
			showIcon
			className='mb-4'
			message={
				<div>
					<strong>Hisobot davri: </strong>
					{dayjs(startDate)?.format('DD.MM.YYYY')} - {dayjs(endDate)?.format('DD.MM.YYYY')}
				</div>
			}
		/>
	)
}
