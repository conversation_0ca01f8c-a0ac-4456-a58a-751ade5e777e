import { Type } from 'class-transformer';
import { IsISO8601, <PERSON><PERSON>ptional, IsString, MinLength, IsArray, ValidateNested, ValidateIf } from 'class-validator';

export class AttendanceRecipientDto {
  @IsArray()
  @IsOptional()
  @Type(() => String)
  userIds?: string[];

  @IsString()
  @IsOptional()
  positionId?: string;
}

export class CreateAttendanceDto {
  @IsString()
  @IsISO8601()
  time: string;

  @IsString()
  @IsOptional()
  @MinLength(3)
  description?: string;

  @ValidateIf((o) => !o.positionId)
  @IsString()
  @IsOptional()
  userId?: string;

  @ValidateIf((o) => !o.userId)
  @IsString()
  @IsOptional()
  positionId?: string;
}

export class FindAttendanceDto {
  @IsString()
  @IsOptional()
  page?: string;

  @IsString()
  @IsOptional()
  limit?: string;

  @IsString()
  @IsOptional()
  organizationId?: string;

  @IsOptional()
  @Type(() => Date)
  startDate?: Date;

  @Type(() => Date)
  @IsOptional()
  endDate?: Date;
}
