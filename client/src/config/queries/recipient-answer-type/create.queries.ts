import { axiosPrivate } from '@/config/api'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { recipientAnswerTypeEndpoints } from '@/config/api/endpoints'
import { notification } from 'antd'

export interface RecipientAnswerType {
	id?: string
	name: string
}

export const useCreateRecipientAnswerType = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async (data: RecipientAnswerType) =>
			(
				await axiosPrivate.post<ApiResponse<RecipientAnswerType>>(
					recipientAnswerTypeEndpoints.all,
					data
				)
			).data,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [recipientAnswerTypeEndpoints.all] })
			notification.success({
				message: 'Muvaffaqiyatli yaratildi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: '<PERSON><PERSON><PERSON><PERSON> xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
