export const formatPhoneInput = (value: string) => {
	// Remove all non-digit characters
	const digits = value.replace(/\D/g, '')

	// Ensure the number starts with 998
	if (!digits.startsWith('998')) {
		return '+998 '
	}

	// Extract relevant parts
	const countryCode = '+998 '
	const part1 = digits.slice(3, 5) // 99
	const part2 = digits.slice(5, 8) // 999
	const part3 = digits.slice(8, 10) // 99
	const part4 = digits.slice(10, 12) // 99

	let formatted = countryCode
	if (part1) formatted += `${part1} `
	if (part2) formatted += `${part2}-`
	if (part3) formatted += `${part3}-`
	if (part4) formatted += `${part4}`

	return formatted.trim()
}
