import { Card } from 'antd'
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Tooltip } from 'recharts'

type TSatsByOrganizationProps = {
	title: string
	value: number
}
export const StatsByOrganization = ({ title, value }: TSatsByOrganizationProps) => {
	const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d']

	return (
		<Card
			className='flex flex-col gap-y-7 p-4 '
			title={title}
			headStyle={{ fontSize: '1.5rem' }}>
			<div className='flex items-center justify-end'>
				<p className='text-4xl  font-bold'>{value}</p>
			</div>
			<div className='h-[300px]'>
				<ResponsiveContainer
					width='100%'
					height='100%'>
					<PieChart>
						<Pie
							data={[{ title, value }]}
							dataKey='value'
							nameKey='title'
							cx='50%'
							cy='50%'
							outerRadius={100}
							fill='#8884d8'
							label>
							<Cell
								key='cell-0'
								fill={COLORS[0]}
							/>
						</Pie>
						<Tooltip />
					</PieChart>
				</ResponsiveContainer>
			</div>
		</Card>
	)
}
