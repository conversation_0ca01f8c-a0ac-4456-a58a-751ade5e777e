import { axiosPrivate } from '@/config/api'
import { archiveEndpoints } from '@/config/api/endpoints'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

export const useGetArchiveModelNames = () => {
	return useQuery({
		queryKey: [archiveEndpoints.all],
		queryFn: async () => (await axiosPrivate.get<string[]>(archiveEndpoints.all)).data
	})
}

export const useGetArchiveModel = (modelName: string) => {
	return useQuery({
		queryKey: [archiveEndpoints.one(modelName)],
		queryFn: async () => (await axiosPrivate.get<any[]>(archiveEndpoints.one(modelName))).data
	})
}

export const useUnArchive = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async ({ modelName, rowId }: { modelName: string; rowId: string }) => {
			return axiosPrivate.patch(archiveEndpoints.unArchive(modelName, rowId))
		},
		onSuccess: (_, args) => {
			queryClient.invalidateQueries({
				queryKey: [archiveEndpoints.one(args.modelName)]
			})
		}
	})
}

export const useForceDelete = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async ({ modelName, rowId }: { modelName: string; rowId: string }) => {
			return axiosPrivate.delete(archiveEndpoints.forceDelete(modelName, rowId))
		},
		onSuccess: (_, args) => {
			queryClient.invalidateQueries({
				queryKey: [archiveEndpoints.one(args.modelName)]
			})
		}
	})
}
