import { TablePaginationConfig } from 'antd'
import { useEffect, useState } from 'react'
import { useSearchParams } from 'react-router-dom'

export const usePagination = (initialPageSize = 10) => {
	const [searchParams, setSearchParams] = useSearchParams()
	const [total, setTotal] = useState(0)
	const [pagination, setPagination] = useState({
		current: 1,
		pageSize: initialPageSize,
		showSizeChanger: true,
		pageSizeOptions: [5, 10, 15]
	})

	const handlePaginationChange = (newPagination: TablePaginationConfig) => {
		setPagination({
			...pagination,
			current: newPagination.current ?? 1,
			pageSize: newPagination.pageSize ?? initialPageSize
		})
	}

	useEffect(() => {
		const page = parseInt(searchParams.get('page') || '1', 10)
		const pageSize = parseInt(searchParams.get('limit') || '10', 10)
		setPagination({ ...pagination, current: page, pageSize })
	}, [])

	useEffect(() => {
		searchParams.set('page', pagination.current.toString())
		searchParams.set('limit', pagination.pageSize.toString())
		setSearchParams(searchParams)
	}, [pagination])

	return {
		setTotal,
		pagination: { ...pagination, total },
		handlePaginationChange
	}
}
