import { Body, Controller, Get, Post } from '@nestjs/common';
import { NotificationService } from './notification.service';
import { ProtectedRoute } from 'src/common';
import { Me } from 'src/common/decorators/me.decorator';
import { Context } from 'src/common/decorators/context.decorator';

@Controller('notification')
export class NotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  @ProtectedRoute({
    isPublic: false,
  })
  @Get()
  async getNotifications(@Me('id') id: string) {
    return await this.notificationService.getNotifications(id);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Post('read')
  async readNotification(@Body() body: { id: string[] }) {
    const { id } = body;
    return await this.notificationService.readNotification(id);
  }
}
