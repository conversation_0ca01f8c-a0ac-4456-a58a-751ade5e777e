import { ROLE } from '../queries/organizations/get-users.queries'

export const authEndpoints = {
	login: '/auth/login',
	logout: '/auth/logout',
	refresh: '/auth/refresh',
	me: '/auth/me'
}

export const companyEndpoints = {
	all: '/company',
	one: (id: string) => `/company/${id}`
}
export const connectorEndpoints = {
	all: '/connector',
	one: (id: number) => `/connector/${id}`
}
export const connectorTypeEndpoints = {
	all: '/connector-type',
	one: (id: number) => `/connector-type/${id}`
}

export const personsEndpoints = {
	all: '/persons'
}

export const organizationEndpoints = {
	all: `/organization`,
	one: (id: string) => `/organization/${id}`,
	organizationDeactivate: (id: string) => `/organization/deactivate/${id}`,
	createChildren: `/organization/create-children`,
	underContolOrganization: () => '/organization/under-control',
	underContolOrganizationRemove: (orgId: string) => `/organization/${orgId}/remove-under-controls`,
	underControlOrganizationCreate: (id: string) => `/organization/${id}/add-under-controls`,
	organizationChildrenCreate: (id: string) =>
		`/organization/${id}/create-children?organizationId=${id}`,
	workers: (orgId: string) => `/organization/worker/${orgId}`,
	addWorkers: (orgId: string) => `/organization/${orgId}/add-workers`,
	removeUsers: (orgId: string) => `/organization/${orgId}/remove-users`,
	removeWorkers: (orgId: string) => `/organization/${orgId}/remove-workers`,
	addEmployees: (orgId: string) => `/organization/${orgId}/add-employees`,
	removeEmployees: (orgId: string) => `/organization/${orgId}/remove-employees`,
	addResponsibles: (orgId: string) => `/organization/${orgId}/add-responsibles`,
	removeResponsibles: (orgId: string) => `/organization/${orgId}/remove-responsibles`,
	usersByOrg: (orgId: string, role?: ROLE) =>
		role ? `/organization/users/${orgId}?role=${role}` : `/organization/users/${orgId}`,
	lowGradeOrganizations: (orgId: string, search: string) =>
		`organization/${orgId}/low-grades?search=${search}`,
	inspector: (organizationId: string) => `/organization/inspector/${organizationId}`
}
export const commonEndpoints = {
	referenceRegion: '/references/regions',
	referenceRegionOne: (id: number) => `/references/regions/${id}`,
	referenceDistrict: '/references/districts',
	referenceDistrictOne: (id: number) => `/references/district/${id}`,
	referencesTaskType: '/references/task-types',
	referencesTaskStates: '/references/task-states',
	referencesAttendanceTypes: '/references/attemdance-types',
	referenceGpsLocationType: '/references/gps-location-types',
	referencesOrganizationType: '/references/organization-types',
	referencesOrganizationPosition: '/references/organization-positions',
	referencesRecipientsAnswerType: '/references/recipient-answer-types',
	referencesRecipientsAnswerStates: '/references/recipient-answer-states',
	referencesGrade: '/references/grade'
}

export const taskTypesEndpoints = {
	all: '/task-type',
	search: (query: string) => `/task-type/search?name=${query}`,
	one: (id: string) => `/task-type/${id}`
}

export const taskStatesEndpoints = {
	all: '/task-state',
	one: (id: string) => `/task-state/${id}`
}

export const usersEndpoints = {
	all: `/user`,
	one: (id: string) => `/user/${id}`,
	search: (query: string) => `/user?search=${query}`,

	searchWorker: (phone: string) => `/user?phone=${phone}`
}

export const organizationTypeEndpoints = {
	all: '/organization-types',
	search: (query: string) => `/organization-types/name/${query}`,
	one: (id: string) => `/organization-types/${id}`
}

export const recipientAnswerTypeEndpoints = {
	all: 'recipient-answer-type',
	one: (id: string) => `recipient-answer-type/${id}`
}

export const organizationPositionTypeEndpoints = {
	all: '/organization-type-position',
	one: (id: string) => `organization-type-position/${id}`,
	byTypeId: (typeId: string) => `organization-type-position/type/${typeId}`,
	searchByTypeId: (typeId: string, search: string) =>
		`organization-type-position/type/${typeId}?search=${search}`
}

export const userWorkingScheduleDayEndpoints = {
	all: '/user-working-schedule-day',
	one: (id: string) => `/user-working-schedule-day/${id}`
}

export const faceIdEndpoints = {
	all: '/face-id',
	one: (id: string) => `/face-id/${id}`,
	allByOrg: (orgId: string) => `/face-id/organization/${orgId}`,
	filter: (onlineStatus: string) => `/face-id?onlineStatus=${onlineStatus}`
}

export const taskEndpoints = {
	all: '/task',
	one: (id: string) => `/task/${id}`,
	allByOrg: (type: string, state?: string) =>
		`/task/organization?type=${type}${state && state !== 'all' ? `&state=${state}` : ''}`,
	read: `/task/read-task`,
	answer: '/task/recipient-answer',
	updateAnswerState: (recipientId: string) => `/task/recipient-answer/${recipientId}`,
	usersAllTask: (incoming: string, state?: string) =>
		`/task/user?type=${incoming}${state && state !== 'all' ? `&state=${state}` : ''}`,
	complete: (taskId: string) => `task/${taskId}/complete`,
	stats: `task/statistics`,
	updateRecipient: (taskId: string) => `task/recipient/${taskId}`,
	updateRecipientInspector: (taskId: string) => `task/recipient-inspector/${taskId}`,
	taskController: (userId: string) => `/task/controller/${userId}`,
	taskCompleteController: (taskId: string) => `task/${taskId}/complete-inspector`,
	taskRecipientsController: (recipientId: string) =>
		`/recipient/inspector/${recipientId}`
}

export const dashboardEndpoints = {
	dashboardChildrenCount: '/dashboard/children-count',
	dashboardUsersCount: '/dashboard/users-count',
	dashboardTasksCount: '/dashboard/tasks-count',
	dashboardTasksState: 'dashboard/tasks-state',
	dashboardAttendance: '/dashboard/attendance',
	dashboardGpsInfo: '/dashboard/gps-info',
	table: (orgId: string) => `/dashboard/table/${orgId}`,
	dashboardSectionStats: (orgId: string) => `/dashboard/section-stats?organizationId=${orgId}`,
	dashboardSectionStatsAttendance: (orgId: string) =>
		`/dashboard/section-stats/attendance?organizationId=${orgId}`,
	dashboardSectionStatsArea: (orgId: string) =>
		`/dashboard/section-stats/area?organizationId=${orgId}`
}

export const orgEmployeeEndpoints = {
	usersByOrgId: (orgId: string) => `/organization/users/${orgId}`
}

export const attendanceUserEndpoints = {
	userAttendance: (userId: string) => `/attendance/user/${userId}`,
	personalAttendance: (personal: string) => `/attendance/personal/${personal}`,
	attendance: '/attendance/statistics',
	addAttendance: 'attendance/add-attendance',
	attendanceExcel: 'attendance/excel',
	attendanceReportExcel: 'attendance/report-excel',
	attendanceByPosition: (positionId: string) =>
		`/attendance/statistics/organization-type-position/${positionId}`,
	attendancePositionExel: (positionId: string) =>
		`attendance/statistics/organization-type-position/${positionId}/excel`
}
export const recipientEndpoints = {
	one: (id: string) => `/recipient/${id}`
}

export const fileEndpoints = {
	upload: 'file/upload/file'
}

export const userWorkingScheduleEndpoints = {
	all: 'user-working-schedule',
	one: (id: string) => `/user-working-schedule/${id}`,
	remove: (id: string) => `/user-working-schedule/${id}/disconnect-schedule`,
	byUserId: (userId: string) => `/user-working-schedule/user/${userId}`,
	addToOrganization: '/user-working-schedule/organization'
}

export const statsEndPoints = {
	findOrganizationHierarchy: (orgId: string) => `/stats/organization-hierarchy/${orgId}`,
	findOrganziationAttendance: (orgId: string) => `stats/organization-attendance/${orgId}`,
	statsOrganization: `stats/organizations`
}

export const notificationEndpoints = {
	all: '/notification',
	read: '/notification/read'
}

export const vocationEndpoints = {
	all: '/vacation',
	one: (id: string) => `/vacation/${id}`
}

export const vacancyEndpoints = {
	all: '/vacancy',
	one: (id: string) => `/vacancy/${id}`
}

export const archiveEndpoints = {
	all: '/dynamic',
	one: (name: string) => `/dynamic/${name}/inactive`,
	unArchive: (name: string, id: string) => `/dynamic/${name}/${id}/status`,
	forceDelete: (name: string, id: string) => `/dynamic/${name}/${id}`
}

export const dashboardDetails = {
	employee: (organizationId: string) =>
		`/dashboard/section-stats/employee?organizationId=${organizationId}`,
	employeeExcel: (organizationId: string) =>
		`/dashboard/section-stats-excel/employee?organizationId=${organizationId}`,
	shortVacationvacation: (organizationId: string) =>
		`/dashboard/section-stats/short-vacation?organizationId=${organizationId}`,
	shortVacationvacationExcel: (organizationId: string) =>
		`/dashboard/section-stats-excel/short-vacation?organizationId=${organizationId}`,
	vacation: (organizationId: string) =>
		`/dashboard/section-stats/vacation?organizationId=${organizationId}`,
	vacationExcel: (organizationId: string) =>
		`/dashboard/section-stats-excel/vacation?organizationId=${organizationId}`,
	patient: (organizationId: string) =>
		`/dashboard/section-stats/patient?organizationId=${organizationId}`,
	patientExcel: (organizationId: string) =>
		`/dashboard/section-stats-excel/patient?organizationId=${organizationId}`,
	vacancy: (organizationId: string) =>
		`/dashboard/section-stats/vacancy?organizationId=${organizationId}`,
	vacancyExcel: (organizationId: string) =>
		`/dashboard/section-stats-excel/vacancy?organizationId=${organizationId}`,
	section: (organizationId: string) =>
		`/dashboard/section-stats/section?organizationId=${organizationId}`,
	sectionExcel: (organizationId: string) =>
		`/dashboard/section-stats-excel/section?organizationId=${organizationId}`,
	position: (organizationId: string, positionId: string) =>
		`/dashboard/section-stats/position?organizationId=${organizationId}&positionId=${positionId}`,
	positionExcel: (organizationId: string, positionId: string) =>
		`/dashboard/section-stats-excel/position?organizationId=${organizationId}&positionId=${positionId}`,
	lateAttendance: (organizationId: string, positionId?: string) =>
		`/dashboard/section-stats/late-attendance?organizationId=${organizationId}${positionId ? `&positionId=${positionId}` : ''}`,
	lateAttendanceExcel: (organizationId: string, positionId?: string) =>
		`/dashboard/section-stats-excel/late-attendance?organizationId=${organizationId}${positionId ? `&positionId=${positionId}` : ''}`,
	noAttendance: (organizationId: string, positionId?: string) =>
		`/dashboard/section-stats/no-attendance?organizationId=${organizationId}${positionId ? `&positionId=${positionId}` : ''}`,
	noAttendanceExcel: (organizationId: string, positionId?: string) =>
		`/dashboard/section-stats-excel/no-attendance?organizationId=${organizationId}${positionId ? `&positionId=${positionId}` : ''}`,
	inArea: (organizationId: string, positionId?: string) =>
		`/dashboard/section-stats/in-area?organizationId=${organizationId}${positionId ? `&positionId=${positionId}` : ''}`,
	inAreaExcel: (organizationId: string, positionId?: string) =>
		`/dashboard/section-stats-excel/in-area?organizationId=${organizationId}${positionId ? `&positionId=${positionId}` : ''}`,
	outArea: (organizationId: string, positionId?: string) =>
		`/dashboard/section-stats/out-area?organizationId=${organizationId}${positionId ? `&positionId=${positionId}` : ''}`,
	outAreaExcel: (organizationId: string, positionId?: string) =>
		`/dashboard/section-stats-excel/out-area?organizationId=${organizationId}${positionId ? `&positionId=${positionId}` : ''}`,
	disabledArea: (organizationId: string, positionId?: string) =>
		`/dashboard/section-stats/disabled-area?organizationId=${organizationId}${positionId ? `&positionId=${positionId}` : ''}`,
	disabledAreaExcel: (organizationId: string, positionId?: string) =>
		`/dashboard/section-stats-excel/disabled-area?organizationId=${organizationId}${positionId ? `&positionId=${positionId}` : ''}`
}

export const botEndpoint = {
	all: '/bot-config',
	one: (id: string) => `/bot-config/${id}`
}

export const userConnectOrganizationEndpoints = {
	all: (organizationId: string) => `/worker?organizationId=${organizationId}`,
	workerSearch: (phone: number) => `/worker?phone=${phone}`,

	connectFindUser: (organizationId: string, userId: string) =>
		`worker/connect?organizationId=${organizationId}&userId=${userId}`
}

export const infractionEndpoints = {
	all: '/infraction',
	me: '/infraction/mains',
	one: (id: string) => `/infraction/${id}`,
	oneWorkspace: (id: string) => `/infraction/${id}/work`
}

export const infractionReasonEndpoints = {
	all: '/infraction-reason',
	one: (id: string) => `/infraction-reason/${id}`,
	create: '/infraction-reason'
}