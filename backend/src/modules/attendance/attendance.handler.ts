import { Injectable, Logger } from '@nestjs/common';
import { Attendance, AttendanceType } from '@prisma/client';
import { PrismaService } from 'src/common';
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import * as timezone from 'dayjs/plugin/timezone';
import * as isBetween from 'dayjs/plugin/isBetween';
import 'dayjs/locale/uz-latn';
import { Dayjs } from 'dayjs';
import { TimezoneHelper, DEFAULT_TIMEZONE } from 'src/helpers/timezone.helper';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isBetween);
dayjs.locale('uz-latn');

dayjs.tz.setDefault(DEFAULT_TIMEZONE);

const extractTimeOnly = (timeValue: any): { hour: number; minute: number } | null => {
  const logger = new Logger('TimeExtractor');

  try {
    if (!timeValue) {
      logger.warn('Vaqt qiymati berilmagan');
      return null;
    }

    if (typeof timeValue === 'string') {
      // Avval UTC da tekshirib, keyin mahalliy vaqtga o'tkazish
      let timeObj = dayjs.utc(timeValue);
      if (timeObj.isValid()) {
        // Mahalliy vaqtga o'tkazish
        timeObj = timeObj.tz(DEFAULT_TIMEZONE);
        logger.debug(`Stringdan vaqt ajratildi: ${timeObj.format('HH:mm')} (${DEFAULT_TIMEZONE})`);
        return { hour: timeObj.hour(), minute: timeObj.minute() };
      }
    } else if (timeValue instanceof Date || (typeof timeValue === 'object' && timeValue !== null)) {
      // Date obyektini avval UTC da tekshirib, keyin mahalliy vaqtga o'tkazish
      let timeObj = dayjs.utc(timeValue);
      if (timeObj.isValid()) {
        // Mahalliy vaqtga o'tkazish
        timeObj = timeObj.tz(DEFAULT_TIMEZONE);
        logger.debug(`Date obyektidan vaqt ajratildi: ${timeObj.format('HH:mm')} (${DEFAULT_TIMEZONE})`);
        return { hour: timeObj.hour(), minute: timeObj.minute() };
      }
    }

    logger.warn(`Vaqt qiymatini ajratib bo'lmadi, tipi: ${typeof timeValue}`);
    return null;
  } catch (error) {
    logger.error(`Vaqt ajratishda xatolik: ${error.message}`, error.stack);
    return null;
  }
};

const calculateWorkingMinutes = (
  startTime: number,
  endTime: number,
  breakStartTime: number,
  breakEndTime: number,
): number => {
  const logger = new Logger('WorkingMinutesCalculator');

  try {
    if (startTime >= endTime) {
      logger.warn(`Noto'g'ri vaqt oralig'i: boshi >= oxiri`);
      return 0;
    }

    // Tushlik vaqtini hisobga olish
    let breakDuration = 0;
    if (startTime < breakEndTime && endTime > breakStartTime) {
      breakDuration = Math.min(endTime, breakEndTime) - Math.max(startTime, breakStartTime);
      if (breakDuration < 0) breakDuration = 0;
      logger.debug(`Tushlik vaqti: ${breakDuration / (1000 * 60)} daqiqa`);
    }

    const workingMinutes = Math.floor((endTime - startTime - breakDuration) / (1000 * 60));
    logger.debug(`Ishlangan vaqt: ${workingMinutes} daqiqa`);

    return workingMinutes;
  } catch (error) {
    logger.error(`Ishlangan vaqtni hisoblashda xatolik: ${error.message}`, error.stack);
    return 0;
  }
};

@Injectable()
export class AttendanceHandler {
  private readonly logger = new Logger(AttendanceHandler.name);

  constructor(private prismaService: PrismaService) {}

  async handleAttendanceCreated(attendance: Attendance): Promise<any> {
    try {
      if (!attendance || !attendance.userId) {
        this.logger.error("Noto'g'ri davomat ma'lumotlari");
        return null;
      }

      this.logger.log(`EXIT event qayta ishlanmoqda: userId=${attendance.userId}, attendanceId=${attendance.id}`);

      // Attendance yozuvining sanasini olish (attendance.time asosida)
      const attendanceDate = TimezoneHelper.toLocalTime(attendance.time);
      if (!attendanceDate || !attendanceDate.isValid()) {
        this.logger.error(`Noto'g'ri attendance vaqti: ${attendance.time}`);
        return null;
      }

      // DB so'rovlari uchun UTC kun chegaralarini olish
      const { start: startOfDay, end: endOfDay } = TimezoneHelper.getUTCRangeForLocalDay(attendanceDate);

      // Mahalliy va UTC vaqtlarni logga chiqarish
      this.logger.debug(
        `Hisob-kitob sanasi: ${attendanceDate.format('YYYY-MM-DD')} (${DEFAULT_TIMEZONE}), UTC: ${dayjs(startOfDay).utc().format('YYYY-MM-DD HH:mm')} - ${dayjs(endOfDay).utc().format('YYYY-MM-DD HH:mm')}`,
      );

      // Kunlik davomat ma'lumotlarini bazadan olish
      const dailyAttendance = await this.getDailyAttendance(attendance.userId, startOfDay, endOfDay);

      this.logger.debug(
        `Kunlik davomat ma'lumotlari: enter=${dailyAttendance.enter ? 'mavjud' : "yo'q"}, exit=${dailyAttendance.exit ? 'mavjud' : "yo'q"}`,
      );

      if (!dailyAttendance.enter || !dailyAttendance.exit) {
        this.logger.warn(
          `Ishlangan vaqtni hisoblash uchun yetarli ma'lumot yo'q: userId=${attendance.userId}, enter=${!!dailyAttendance.enter}, exit=${!!dailyAttendance.exit}`,
        );
        return null;
      }

      // Foydalanuvchi ish jadvalini olish
      const userSchedule = await this.getUserWorkingSchedule(attendance.userId, attendanceDate);

      // Tushlik vaqtini olish
      const breakTime = await this.getBreakTime(attendance.userId, attendanceDate);

      // Ishlangan vaqtni hisoblash
      const workingTimeResult = this.calculateWorkingTime(
        dailyAttendance.enter,
        dailyAttendance.exit,
        userSchedule,
        breakTime,
        attendanceDate,
      );

      this.logger.debug('WorkingTimeResult: ', workingTimeResult);

      // Hisobotni yaratish yoki yangilash
      return this.createOrUpdateAttendanceReport(attendance.userId, dailyAttendance, workingTimeResult, attendanceDate);
    } catch (error) {
      this.logger.error(`EXIT hodisasini qayta ishlashda xatolik: ${error.message}`, error.stack);
      return null;
    }
  }

  private async getDailyAttendance(userId: string, startOfDay: Date, endOfDay: Date) {
    try {
      // Birinchi kirish vaqti
      const firstEnterAttendance = await this.prismaService.attendance.findFirst({
        where: {
          userId,
          type: AttendanceType.ENTER,
          time: {
            gte: startOfDay,
            lt: endOfDay,
          },
        },
        orderBy: { time: 'asc' },
      });

      // Oxirgi chiqish vaqti
      const lastExitAttendance = await this.prismaService.attendance.findFirst({
        where: {
          userId,
          type: AttendanceType.EXIT,
          time: {
            gte: startOfDay,
            lt: endOfDay,
          },
        },
        orderBy: { time: 'desc' },
      });

      // Kunlik barcha kirish-chiqishlar
      const allAttendances = await this.prismaService.attendance.findMany({
        where: {
          userId,
          time: {
            gte: startOfDay,
            lt: endOfDay,
          },
        },
        orderBy: { time: 'asc' },
      });

      return {
        enter: firstEnterAttendance?.time || null,
        exit: lastExitAttendance?.time || null,
        allAttendances, // Keyingi versiyalarda butun kun bo'yicha analiz qilish uchun
      };
    } catch (error) {
      this.logger.error(`Kunlik davomat ma'lumotlarini olishda xatolik: ${error.message}`, error.stack);
      return { enter: null, exit: null, allAttendances: [] };
    }
  }

  private async getUserWorkingSchedule(userId: string, today: Dayjs) {
    try {
      const userWorkingSchedule = await this.prismaService.userWorkingSchedule.findUnique({
        where: { userId },
        select: {
          days: {
            select: {
              day: true,
              startTime: true,
              endTime: true,
            },
          },
        },
      });

      if (!userWorkingSchedule) {
        return null;
      }

      const todayNumber = today.day();
      const day = userWorkingSchedule.days.find((d) => d.day === todayNumber);

      if (!day) {
        return null;
      }

      // Jadval vaqtlarini ajratib olish
      const startTimeObj = extractTimeOnly(day.startTime);
      const endTimeObj = extractTimeOnly(day.endTime);

      if (!startTimeObj || !endTimeObj) {
        return null;
      }

      // Vaqtlarni UTC joriy kunga o'rnatish
      const scheduledStart = today.hour(startTimeObj.hour).minute(startTimeObj.minute).second(0).millisecond(0);

      const scheduledEnd = today.hour(endTimeObj.hour).minute(endTimeObj.minute).second(0).millisecond(0);
      return {
        start: scheduledStart,
        end: scheduledEnd,
      };
    } catch (error) {
      this.logger.error(`Foydalanuvchi jadvalini olishda xatolik: ${error.message}`, error.stack);
      return null;
    }
  }

  private async getBreakTime(userId: string, today: Dayjs) {
    try {
      const breakStart: Dayjs = today.hour(13).minute(0).second(0).millisecond(0);
      const breakEnd: Dayjs = today.hour(14).minute(0).second(0).millisecond(0);

      return {
        start: breakStart,
        end: breakEnd,
      };
    } catch (error) {
      this.logger.error(`Tushlik vaqtini olishda xatolik: ${error.message}`, error.stack);

      // Xatolik yuz berganda standart tushlik vaqti
      return {
        start: today.hour(13).minute(0).second(0).millisecond(0),
        end: today.hour(14).minute(0).second(0).millisecond(0),
      };
    }
  }

  private calculateWorkingTime(
    enterTime: Date,
    exitTime: Date,
    userSchedule: { start: Dayjs; end: Dayjs } | null,
    breakTime: { start: Dayjs; end: Dayjs },
    today: Dayjs,
  ) {
    try {
      const enterTimeMs = enterTime.getTime();
      const exitTimeMs = exitTime.getTime();
      const breakStartMs = breakTime.start.valueOf();
      const breakEndMs = breakTime.end.valueOf();

      let actualWorkingMinutes = 0;
      let scheduledWorkingMinutes = 0;
      let underTimeMinutes = 0;
      let isLate = false;
      let isEarlyExit = false;
      let lateEnter = null;
      let earlyExit = null;
      let scheduledEnter = null;
      let scheduledExit = null;

      // Ish jadvali mavjud bo'lsa
      if (userSchedule) {
        scheduledEnter = userSchedule.start.toDate();
        scheduledExit = userSchedule.end.toDate();
        const scheduledStartMs = userSchedule.start.valueOf();
        const scheduledEndMs = userSchedule.end.valueOf();

        // Tushlik vaqti ish jadvali bilan kesishadimi
        const breakOverlapsWithSchedule = breakStartMs < scheduledEndMs && breakEndMs > scheduledStartMs;

        // Rejalashtirilgan ish vaqti (tushlik vaqtini ayirib)
        scheduledWorkingMinutes = Math.floor(
          (scheduledEndMs - scheduledStartMs - (breakOverlapsWithSchedule ? breakEndMs - breakStartMs : 0)) /
            (1000 * 60),
        );

        // Haqiqiy ish vaqti chegaralari
        const actualStartMs = Math.max(enterTimeMs, scheduledStartMs);
        const actualEndMs = Math.min(exitTimeMs, scheduledEndMs);

        // Haqiqiy ishlangan vaqt
        if (actualStartMs < actualEndMs) {
          actualWorkingMinutes = calculateWorkingMinutes(actualStartMs, actualEndMs, breakStartMs, breakEndMs);
        }

        // Kechikish va erta ketishni hisoblash
        if (enterTimeMs > scheduledStartMs) {
          isLate = true;
          lateEnter = Math.floor((enterTimeMs - scheduledStartMs) / (1000 * 60));
        }

        if (exitTimeMs < scheduledEndMs) {
          isEarlyExit = true;
          earlyExit = Math.floor((scheduledEndMs - exitTimeMs) / (1000 * 60));
        }

        // Kam ishlangan vaqt
        if (scheduledWorkingMinutes > 0 && actualWorkingMinutes < scheduledWorkingMinutes) {
          underTimeMinutes = scheduledWorkingMinutes - actualWorkingMinutes;
        }
      } else {
        // Ish jadvali mavjud bo'lmaganda
        actualWorkingMinutes = calculateWorkingMinutes(enterTimeMs, exitTimeMs, breakStartMs, breakEndMs);
      }

      return {
        actualWorkingMinutes,
        scheduledWorkingMinutes: scheduledWorkingMinutes || null,
        underTimeMinutes: underTimeMinutes || null,
        isLate,
        isEarlyExit,
        lateEnter,
        earlyExit,
        scheduledEnter,
        scheduledExit,
      };
    } catch (error) {
      this.logger.error(`Ishlangan vaqtni hisoblashda xatolik: ${error.message}`, error.stack);
      return {
        actualWorkingMinutes: 0,
        scheduledWorkingMinutes: null,
        underTimeMinutes: null,
        isLate: false,
        isEarlyExit: false,
        lateEnter: null,
        earlyExit: null,
        scheduledEnter: null,
        scheduledExit: null,
      };
    }
  }

  private async createOrUpdateAttendanceReport(
    userId: string,
    dailyAttendance: { enter: Date; exit: Date },
    workingTimeResult: any,
    today: Dayjs,
  ) {
    try {
      const { start: startOfDay, end: endOfDay } = TimezoneHelper.getUTCRangeForLocalDay(today);
      const dayNumber = today.day() === 0 ? 7 : today.day();

      const existingReport = await this.prismaService.attendanceReport.findFirst({
        where: {
          userId,
          date: {
            gte: startOfDay,
            lt: endOfDay,
          },
        },
      });

      const reportData = {
        userId,
        enter: dailyAttendance.enter,
        exit: dailyAttendance.exit,
        date: today.toISOString(),
        dayNumber,
        minutes: workingTimeResult.actualWorkingMinutes,
        scheduledMinutes: workingTimeResult.scheduledWorkingMinutes,
        underTimeMinutes: workingTimeResult.underTimeMinutes,
        isLate: workingTimeResult.isLate,
        isEarlyExit: workingTimeResult.isEarlyExit,
        lateEnter: workingTimeResult.lateEnter,
        earlyExit: workingTimeResult.earlyExit,
        scheduledEnter: workingTimeResult.scheduledEnter,
        scheduledExit: workingTimeResult.scheduledExit,
      };

      if (!existingReport) {
        const newReport = await this.prismaService.attendanceReport.create({
          data: reportData,
        });
        this.logger.log(`Hisobot yaratildi: userId=${userId}, reportId=${newReport.id}`);
        return newReport;
      } else {
        const updatedReport = await this.prismaService.attendanceReport.update({
          where: { id: existingReport.id },
          data: reportData,
        });
        this.logger.log(`Hisobot yangilandi: userId=${userId}, reportId=${updatedReport.id}`);
        return updatedReport;
      }
    } catch (error) {
      this.logger.error(`Hisobotni yaratish yoki yangilashda xatolik: ${error.message}`, error.stack);
      return null;
    }
  }
}
