import { useGetArchiveModelNames } from '@/config/queries/archives/archives.query'
import { Menu, Splitter } from 'antd'
import ArchiveTable from './archive-table'
import { useState } from 'react'

export default function Archives() {
	const archiveModelNames = useGetArchiveModelNames()
	const [selectedKey, setSelectedKey] = useState(archiveModelNames.data?.[0] ?? '')

	return (
		<div className='h-full'>
			<Splitter className='h-full'>
				<Splitter.Panel
					className='h-full'
					min={0}
					max={200}
					defaultSize={200}
					resizable={false}
					collapsible>
					<Menu
						className='!p-2'
						selectedKeys={[selectedKey]}
						mode='inline'
						theme='light'
						items={archiveModelNames.data?.map(modelName => {
							return {
								key: modelName,
								label: modelName.toUpperCase(),
								onClick: () => setSelectedKey(modelName)
							}
						})}
					/>
				</Splitter.Panel>
				<Splitter.Panel className='h-full !p-4'>
					<ArchiveTable modeName={selectedKey} />
				</Splitter.Panel>
			</Splitter>
		</div>
	)
}
