import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { PaginatedResult, PaginationParams, PaginationUtil, PrismaService, QueryUtil } from '../../common';
import { CreateUserWorkingScheduleDto, UpdateUserWorkingScheduleDto } from './dto/user-working-schedule.dto';
import { Organization, Status, UserWorkingSchedule } from '@prisma/client';
import { BaseQueryParams } from 'src/helpers/types';
import { OrganizationUpdateDto } from './dto/organization-working.dto';

@Injectable()
export class UserWorkingScheduleService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly paginationUtil: PaginationUtil,
    private queryUtil: QueryUtil,
  ) {}

  async getAllChildOrganizationsWithEmployees(orgId: string) {
    const children = await this.prisma.organization.findMany({
      where: {
        parentId: orgId,
        status: 'ACTIVE',
      },
      include: {
        Worker: { include: { UserWorkingSchedule: true } },
      },
    });

    const nested = await Promise.all(children.map((child) => this.getAllChildOrganizationsWithEmployees(child.id)));

    return [...children, ...nested.flat()];
  }

  async create(createUserWorkingScheduleDto: CreateUserWorkingScheduleDto): Promise<UserWorkingSchedule> {
    const { userId, days } = createUserWorkingScheduleDto;

    const user = await this.prisma.user.findUnique({
      where: { id: userId, status: 'ACTIVE' },
      include: { UserWorkingSchedule: true },
    });

    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found`);
    }

    // Проверяем, есть ли уже расписание у пользователя
    if (user.UserWorkingSchedule) {
      throw new BadRequestException(`User with ID "${userId}" already has a working schedule`);
    }

    const existingDays = await this.prisma.userWorkingScheduleDay.findMany({
      where: { id: { in: days } },
    });

    if (existingDays.length !== days.length) {
      throw new BadRequestException(`Some of the provided days do not exist`);
    }

    return this.prisma.userWorkingSchedule.create({
      data: {
        userId,
        days: {
          connect: days.map((dayId) => ({ id: dayId })),
        },
      },
      include: {
        days: true,
      },
    });
  }

  async findAll(
    params: PaginationParams,
    userId?: string,
    search?: string,
  ): Promise<PaginatedResult<UserWorkingSchedule>> {
    const { page, limit } = params;

    const where: any = {
      ...this.queryUtil.createSearchQuery(search, ['id', 'days']),
      status: 'ACTIVE',
    };

    if (userId) where.userId = userId;

    return this.paginationUtil.paginate(
      this.prisma.userWorkingSchedule,
      {
        page,
        limit,
      },
      where,
      {
        days: true,
      },
    );
  }

  async findOne(id: string) {
    const schedule = await this.prisma.userWorkingSchedule.findUnique({
      where: { id, status: 'ACTIVE' },
      include: { days: true },
    });

    if (!schedule) {
      throw new NotFoundException(`Schedule with ID "${id}" not found`);
    }

    return schedule;
  }

  async update(id: string, updateUserWorkingScheduleDto: UpdateUserWorkingScheduleDto) {
    const { userId, days } = updateUserWorkingScheduleDto;

    // Проверяем, существует ли пользователь
    if (userId) {
      const user = await this.prisma.user.findUnique({
        where: { id: userId, status: 'ACTIVE' },
      });

      if (!user) throw new NotFoundException(`User with ID "${userId}" not found`);
    }

    // проверка рабочае время
    if (days) {
      const existingDays = await this.prisma.userWorkingScheduleDay.findMany({
        where: { id: { in: days }, status: 'ACTIVE' },
      });

      if (existingDays.length !== days.length) throw new BadRequestException(`Some of the provided days do not exist`);
    }

    return this.prisma.userWorkingSchedule.update({
      where: { id },
      data: {
        userId,
        days: days ? { set: days.map((dayId) => ({ id: dayId })) } : undefined,
      },
      include: { days: true },
    });
  }

  async changeStatus(id: string, status: Status) {
    const schedule = await this.prisma.userWorkingSchedule.findUnique({
      where: { id, status: 'ACTIVE' },
    });

    if (!schedule) {
      throw new NotFoundException(`Schedule with ID "${id}" not found`);
    }

    return this.prisma.userWorkingSchedule.update({
      where: { id },
      data: { status },
    });
  }

  async remove(id: string) {
    const schedule = await this.prisma.userWorkingSchedule.findUnique({
      where: { id, status: 'ACTIVE' },
    });

    if (!schedule) throw new NotFoundException(`Schedule with ID "${id}" not found`);

    return this.prisma.userWorkingSchedule.update({ where: { id }, data: { status: 'INACTIVE' } });
  }

  async disconnectSchedule(userId: string, schedules: string[]) {
    const workingSchedules = await this.prisma.userWorkingSchedule.findMany({
      where: {
        id: {
          in: schedules,
        },
        status: 'ACTIVE',
        userId,
      },
    });

    if (workingSchedules?.length !== schedules?.length) {
      throw new NotFoundException('Some working schedules do not exist');
    }

    await Promise.all(
      schedules.map(async (id) => {
        await this.prisma.userWorkingSchedule.delete({
          where: { id },
        });
      }),
    );
  }

  async findByUser(userId: string, query: BaseQueryParams) {
    const { page, limit } = query;
    const $page = page ? parseInt(page) : 1;
    const $limit = limit ? parseInt(limit) : 15;

    return this.paginationUtil.paginate(
      this.prisma.userWorkingSchedule,
      { limit: $limit, page: $page },
      { userId, status: 'ACTIVE' },
      { days: true },
    );
  }

  async updateByOrganization({ days, orgId }: OrganizationUpdateDto) {
    const organization = await this.prisma.organization.findUnique({
      where: { id: orgId, status: 'ACTIVE' },
      include: { Worker: { include: { UserWorkingSchedule: true } } },
    });

    if (!organization) throw new NotFoundException('Organization not found');

    const existingDays = await this.prisma.userWorkingScheduleDay.findMany({
      where: { id: { in: days }, status: 'ACTIVE' },
    });

    if (existingDays.length !== days.length) {
      throw new BadRequestException('Some of the provided days do not exist');
    }

    const childOrganizations = await this.getAllChildOrganizationsWithEmployees(organization.id);
    const allOrganizations = [organization, ...childOrganizations];

    return await Promise.all(
      allOrganizations.flatMap((org) => {
        return org?.Worker.map((worker) =>
          this.prisma.userWorkingSchedule.upsert({
            where: { userId: worker.id },
            update: {
              days: { set: days.map((dayId) => ({ id: dayId })) },
            },
            create: {
              userId: worker.id,
              days: { connect: days.map((dayId) => ({ id: dayId })) },
            },
          }),
        );
      }),
    );
  }
}
