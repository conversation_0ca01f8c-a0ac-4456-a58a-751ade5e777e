import { OrganizationTypes } from '@/config/queries/organization-types/create.queries'
import { create } from 'zustand'

interface OrganizationTypesModalStore {
	open: boolean
	data: OrganizationTypes | null
	onOpen: (data?: OrganizationTypes) => void
	onClose: () => void
}

export const useOrganizationTypesModalStore = create<OrganizationTypesModalStore>(set => ({
	open: false,
	data: null,
	onOpen: data => set({ open: true, data: data || null }),
	onClose: () => set({ open: false, data: null })
}))
