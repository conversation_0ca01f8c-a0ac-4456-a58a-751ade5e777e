import { EditOutlined } from '@ant-design/icons'
import { Button, Space, Table } from 'antd'
import { useState } from 'react'

import { useTaskTypesModalStore } from './utils/task-types-store'
import { useGetTaskTypes } from '@/config/queries/task-types/get-all.queries'
import TaskTypesModal from './ui/task-types-modal'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { Search } from '@/shared/components/search/search'
export const TaskTypeTable = () => {
	const { onOpen } = useTaskTypesModalStore()
	const [search] = useState('')
	const [currentPage, setCurrentPage] = useState(1)
	const [pageSize, setPageSize] = useState(10)
	const taskTypes = useGetTaskTypes(search)

	return (
		<>
			<Table
				pagination={{
					total: taskTypes.data?.pages?.[0]?.meta?.total || 0,
					pageSize: pageSize,
					current: currentPage,
					showSizeChanger: true,
					pageSizeOptions: [5, 10, 20, 50, 100],
					onChange: (page, size) => {
						setCurrentPage(page)
						setPageSize(size)
						if (
							Math.ceil((page * size) / size) > (taskTypes.data?.pages?.length || 0) &&
							taskTypes.hasNextPage
						) {
							taskTypes.fetchNextPage()
						}
					}
				}}
				title={() => (
					<div className='flex items-center justify-between'>
						<Search />
						<TaskTypesModal />
					</div>
				)}
				size='small'
				scroll={{ x: 'max-content' }}
				dataSource={taskTypes.data?.pages?.flatMap(page => page.data) || []}
				rowKey={(rec: { id?: string }) => rec.id || ''}
				columns={[
					{
						key: '#',
						title: '#',

						render: (_, _rec, index) => formatTableIndex(currentPage, pageSize, index)
					},
					{
						key: 'name',
						title: 'Nomi',
						dataIndex: 'name'
					},

					{
						key: 'actions',
						title: 'Amallar',
						render: record => (
							<Space>
								<Button
									type='primary'
									icon={<EditOutlined />}
									onClick={() => onOpen(record)}
								/>
							</Space>
						)
					}
				]}
			/>
		</>
	)
}
