export const GPS_STATUS_QUEUE = 'gps-status';
export const GPS_OFFLINE_CHECK = 'gps.offline.check';
export const GPS_SET_OFFLINE = 'gps.set.offline';
export const GPS_INFRACTION_CHECK = 'gps.infraction.check';

export const GPS_OFFLINE_CHECK_TIMEOUT_MINUTES = 15;

export interface GpsOfflineCheckData {
  userId: string;
  lastLocationTime: Date;
  timeoutMinutes: number;
}

export interface GpsSetOfflineData {
  userId: string;
  reason: 'TIMEOUT' | 'MANUAL' | 'NO_GPS_SIGNAL';
}
