import { Controller, Get, Param, Query } from '@nestjs/common';
import { StatsService } from './stats.service';
import { ProtectedRoute } from 'src/common';
import { Context, ContextData } from '../../common/decorators/context.decorator';

@Controller('stats')
export class StatsController {
  constructor(private readonly statsService: StatsService) {}

  @ProtectedRoute({
    context: 'WORKSPACE',
  })
  @Get('/task/:orgId')
  async getTaskStats(@Param('orgId') orgId: string) {
    return await this.statsService.getTaskStats(orgId);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE' })
  @Get('/organization/:orgId')
  async getAllChildOrganizationIds(@Param('orgId') orgId: string) {
    return await this.statsService.getAllChildOrganizationIds(orgId);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE' })
  @Get('/organization-hierarchy/:orgId')
  async getOrganizationHierarchyStatistics(@Param('orgId') orgId: string) {
    return await this.statsService.getOrganizationHierarchyStatistics(orgId);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE' })
  @Get('/organization-attendance/:orgId')
  async getOrganizationChildrenAttendance(@Param('orgId') orgId: string) {
    return await this.statsService.getOrganizationChildrenAttendance(orgId);
  }

  @ProtectedRoute({
    context: 'WORKSPACE',
  })
  @Get('/organizations')
  async getOrganizationsByLocation(
    @Context() contextData: ContextData,
    @Query('regionId') regionId?: string,
    @Query('districtId') districtId?: string,
    @Query('sectionId') sectionId?: string,
  ) {
    return this.statsService.getOrganizationsByLocation(contextData.organizationId, regionId, districtId, sectionId);
  }
}
