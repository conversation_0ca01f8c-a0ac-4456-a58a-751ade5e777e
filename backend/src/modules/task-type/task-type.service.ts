import { Injectable, NotFoundException } from '@nestjs/common';
import { PaginationUtil, PrismaService, QueryUtil } from '../../common';
import { CreateTaskTypeDto, UpdateTaskTypeDto } from './dto/task-type.dto';
import { Status } from '@prisma/client';

@Injectable()
export class TaskTypeService {
  constructor(
    private readonly prisma: PrismaService,
    private pagination: PaginationUtil,
    private queryUtil: QueryUtil,
  ) {}

  async create(createTaskTypeDto: CreateTaskTypeDto) {
    const data = await this.prisma.taskType.create({ data: createTaskTypeDto });
    return data;
  }

  async findAll(page?: string, limit?: string, search?: string) {
    const $page = Number(page) || 1;
    const $limit = Number(limit) || 10;

    const model = this.prisma.taskType;

    const where: any = {
      ...this.queryUtil.createSearchQuery(search, ['name', 'id']),
      status: 'ACTIVE' as Status,
    };

    const withPagination = this.pagination.paginate(model, { page: $page, limit: $limit }, where);

    return withPagination;
  }

  async findByName(name: string) {
    const model = this.prisma.taskType;

    const where = this.queryUtil.createSearchQuery(name, ['name']);

    const withPagination = this.pagination.paginate(model, {}, { status: 'ACTIVE', ...where });

    return withPagination;
  }

  async findOne(id: string) {
    const taskType = await this.prisma.taskType.findUnique({ where: { id } });
    if (!taskType) {
      throw new NotFoundException(`TaskType with ID "${id}" not found`);
    }
    return taskType;
  }

  async update(id: string, updateTaskTypeDto: UpdateTaskTypeDto) {
    const task = await this.prisma.taskType.findUnique({
      where: { id },
    });

    if (!task) {
      throw new NotFoundException(`TaskType with ID "${id}" not found`);
    }

    const data = await this.prisma.taskType.update({ where: { id }, data: updateTaskTypeDto });

    return data;
  }

  async changeStatus(id: string, status: Status) {
    const taskType = await this.prisma.taskType.findUnique({ where: { id } });

    if (!taskType) {
      throw new NotFoundException(`TaskType with ID "${id}" not found`);
    }

    const response = await this.prisma.taskType.update({
      where: { id },
      data: {
        status,
      },
    });

    return response;
  }
}
