# Настройки для кэширования
map $sent_http_content_type $expires {
    default                    off;
    text/html                  epoch;
    text/css                   max;
    application/javascript     max;
    ~image/                    max;
    ~font/                     max;
}

# HTTP сервер для фронтенд домена
server {
    listen 80;
    server_name new.mnazorat.uz;

    # Настройка для Let's Encrypt (оставляем для будущего получения сертификатов)
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Редирект на HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

# HTTP сервер для бэкенд домена
server {
    listen 80;
    server_name api.new.mnazorat.uz;

    # Настройка для Let's Encrypt (оставляем для будущего получения сертификатов)
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Редирект на HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

# HTTPS сервер для фронтенд домена
server {
    listen 443 ssl;
    server_name new.mnazorat.uz;
    
    ssl_certificate /etc/letsencrypt/live/new.mnazorat.uz/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/new.mnazorat.uz/privkey.pem;
    
    # Дополнительные SSL настройкиsen
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers "EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH";
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # Let's Encrypt
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Основной проксирующий блок
    location / {
        proxy_pass http://frontend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        expires $expires;
    }

    # Настройки для статических файлов
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        proxy_pass http://frontend;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        expires max;
        add_header Cache-Control "public, max-age=31536000";
        access_log off;
    }

    # Запрещаем доступ к скрытым файлам
    location ~ /\.(?!well-known) {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# HTTPS сервер для бэкенд домена
server {
    listen 443 ssl;
    server_name api.new.mnazorat.uz;
    
    ssl_certificate /etc/letsencrypt/live/api.new.mnazorat.uz/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.new.mnazorat.uz/privkey.pem;
    
    # Дополнительные SSL настройки
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers "EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH";
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_stapling on;
    ssl_stapling_verify on;
    
    # Let's Encrypt
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Основной проксирующий блок с улучшенными настройками стабильности
    location / {
        proxy_pass http://backend:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Увеличенные таймауты
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
        
        # Буферизация
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
        
        # Включить повторные попытки при ошибках
        proxy_next_upstream error timeout http_502;
        proxy_next_upstream_tries 3;
    }

    # Эндпоинт проверки здоровья
    location /health {
        proxy_pass http://backend:8000/api/v1/health;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Запрещаем доступ к скрытым файлам
    location ~ /\.(?!well-known) {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# HTTP сервер для m-nazorat.uz (с редиректом на HTTPS)
server {
    listen 80;
    server_name m-nazorat.uz;

    # Для Let's Encrypt challenge
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Редирект на HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

# HTTPS сервер для m-nazorat.uz
server {
    listen 443 ssl;
    server_name m-nazorat.uz;

    ssl_certificate /etc/letsencrypt/live/m-nazorat.uz/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/m-nazorat.uz/privkey.pem;

    # Дополнительные SSL настройки
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers "EECDH+AESGCM:EDH+AESGCM:AES256+EECDH:AES256+EDH";
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 1d;
    ssl_stapling on;
    ssl_stapling_verify on;

    # Let's Encrypt
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }

    # Проксируем только backend
    location / {
        proxy_pass http://backend:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # Увеличенные таймауты
        proxy_connect_timeout 120s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;

        # Буферизация
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
    }
}
