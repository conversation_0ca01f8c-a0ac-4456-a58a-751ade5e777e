import { TValidationFormProps } from '@/shared/types/validation-form.type'
import { Col, Form, Input, Row, Select, TreeSelect } from 'antd'
import { useGetAllOrganizations } from '@/config/queries/organizations/get-all.queries'
import { JSX, useEffect, useState } from 'react'
import { useUsersModalStore } from '../utils/users-modal-store'
import { InputMask } from '@react-input/mask'
import { useGetOrganizationTypes } from '@/config/queries/organization-types/get.queries'
import { fetchOrganizationTypeChildren } from './fetch-organization-type-children'

interface TreeNode {
	title: string
	value: string
	key: string
	isLeaf?: boolean
	pId?: string
}

export default function UsersForm({
	validation,
	Uploader
}: TValidationFormProps & { Uploader: ({ file }: { file?: string }) => JSX.Element }) {
	const { data: organization } = useGetAllOrganizations()
	const { data: organizationTypes, fetchNextPage, hasNextPage } = useGetOrganizationTypes()
	const { data: user } = useUsersModalStore()
	const [treeData, setTreeData] = useState<TreeNode[]>([])
	const [loadingKeys, setLoadingKeys] = useState<string[]>([])

	useEffect(() => {
		if (organizationTypes && organizationTypes.pages.flatMap(page => page.data).length > 0) {
			const newData = organizationTypes?.pages
				.flatMap(page => page.data)
				.map(item => ({
					title: item.name,
					value: item.id,
					key: item.id,
					isLeaf: false,
					pId: undefined,
					selectable: false
				}))
			// @ts-ignore
			setTreeData(prev => {
				const keys = new Set(prev.map(node => node.key))
				const merged = [...prev, ...newData.filter(node => !keys.has(node.key as string))]
				return merged
			})
		}
	}, [organizationTypes])

	const updateTreeWithChildren = (nodeId: string, children: any[]) => {
		setTreeData(prev => {
			const hasExistingChildren = prev.some(node => node.pId === nodeId)
			if (hasExistingChildren) {
				return prev.map(node =>
					node.key === nodeId ? { ...node, isLeaf: children.length === 0 } : node
				)
			}

			const childNodes: TreeNode[] = children.map(child => ({
				title: child.name,
				value: child.id,
				key: child.id,
				isLeaf: false,
				pId: nodeId
			}))

			return [
				...prev.map(node =>
					node.key === nodeId ? { ...node, isLeaf: children.length === 0 } : node
				),
				...childNodes
			]
		})
	}

	const loadData = async (treeNode: any) => {
		const { key: id } = treeNode
		const hasChildren = treeData.some(node => node.pId === id)
		if (hasChildren) return
		if (loadingKeys.includes(id)) return

		setLoadingKeys(keys => [...keys, id])
		try {
			const { data: children } = await fetchOrganizationTypeChildren(id)
			if (children && children.length > 0) {
				updateTreeWithChildren(id, children)
			} else {
				setTreeData(prev => prev.map(node => (node.key === id ? { ...node, isLeaf: true } : node)))
			}
		} catch (e) {
			setTreeData(prev => prev.map(node => (node.key === id ? { ...node, isLeaf: true } : node)))
		} finally {
			setLoadingKeys(keys => keys.filter(k => k !== id))
		}
	}

	return (
		<Row gutter={[16, 16]}>
			<Col span={12}>
				<Form.Item
					name='fullName'
					label='F.I.O'
					rules={validation.name}>
					<Input />
				</Form.Item>
			</Col>

			<Col span={12}>
				<Form.Item
					name='username'
					label='Telefon raqami'
					rules={user ? undefined : [{ required: true }]}>
					<InputMask
						className='ant-input ant-input-lg css-dev-only-do-not-override-14c6sq0 ant-input-outlined'
						defaultValue={'+998'}
						mask='+998 (__) ___-__-__'
						replacement={{ _: /\d/ }}
						placeholder='Telefon raqamini kiriting'
					/>
				</Form.Item>
			</Col>

			<Col span={12}>
				<Form.Item
					name='phone'
					label="Qo'shimcha telefon raqami"
					rules={
						user
							? undefined
							: [
									{
										required: true,
										message: 'Telefon raqami, minimal 2 ta belgidan iborat',
										min: 2
									}
								]
					}>
					<InputMask
						style={{
							width: '100%',
							padding: '8px 11px',
							fontSize: '16px',
							lineHeight: '1.8',
							borderRadius: '6px',
							border: '1px solid gray'
						}}
						defaultValue={'+998'}
						mask='+998 (__) ___-__-__'
						replacement={{ _: /\d/ }}
						placeholder='Telefon raqamini kiriting'
					/>
				</Form.Item>
			</Col>

			<Col span={12}>
				<Form.Item
					name='password'
					label='Parol'
					rules={user ? undefined : [{ required: true }]}>
					<Input />
				</Form.Item>
			</Col>

			<Col span={12}>
				<Form.Item
					name='imei'
					label='IMEI'>
					<Input />
				</Form.Item>
			</Col>

			<Col span={12}>
				<Form.Item
					name='positionId'
					label='Lavozim'
					rules={validation.organizationType}>
					<TreeSelect
						allowClear
						treeDataSimpleMode={{
							id: 'value',
							pId: 'pId'
						}}
						onPopupScroll={e => {
							const target = e.target as HTMLDivElement
							if (
								target.scrollTop + target.clientHeight >= target.scrollHeight - 5 &&
								hasNextPage
							) {
								fetchNextPage()
							}
						}}
						loadData={loadData}
						treeData={treeData}
						showCheckedStrategy={TreeSelect.SHOW_ALL}
					/>
				</Form.Item>
			</Col>

			<Col span={24}>
				<Form.Item
					name='mainOrganizationId'
					label='Asosiy tashkiloti'>
					<Select
						allowClear
						onPopupScroll={e => {
							const target = e.target as HTMLDivElement
							if (
								target.scrollTop + target.clientHeight >= target.scrollHeight - 5 &&
								hasNextPage
							) {
								fetchNextPage()
							}
						}}
						options={organization?.pages?.flatMap(org => {
							return org?.data.map((item: any) => ({
								value: item?.id,
								label: item?.name
							}))
						})}
					/>
				</Form.Item>
			</Col>

			<Col span={24}>
				<Form.Item label='Rasm'>
					<Uploader file={user?.avatar?.path} />
				</Form.Item>
			</Col>
		</Row>
	)
}
