import React from 'react'
import { Info } from 'lucide-react'

interface InfoPanelProps {
	selectedFeature: any
	onClose: () => void
}

export const InfoPanel: React.FC<InfoPanelProps> = ({ selectedFeature, onClose }) => {
	if (!selectedFeature) return null

	return (
		<div className='absolute bottom-4 left-4 z-[1000] bg-white rounded-lg shadow-lg border border-gray-200 max-w-sm'>
			<div className='p-4'>
				<div className='flex items-center justify-between mb-3'>
					<h3 className='text-sm font-semibold text-gray-800 flex items-center'>
						<Info className='w-4 h-4 mr-2' />
						Информация об области
					</h3>
					<button
						onClick={onClose}
						className='p-1 hover:bg-gray-100 rounded text-gray-500'>
						✕
					</button>
				</div>
				<div className='space-y-2 text-sm'>
					<div>
						<span className='font-medium text-gray-600'>ID:</span>
						<span className='ml-2 text-gray-800'>{selectedFeature.properties?.id}</span>
					</div>
					<div>
						<span className='font-medium text-gray-600'>Тип:</span>
						<span className='ml-2 text-gray-800'>{selectedFeature.properties?.type}</span>
					</div>
					{selectedFeature.properties?.region_id && (
						<div>
							<span className='font-medium text-gray-600'>Регион:</span>
							<span className='ml-2 text-gray-800'>{selectedFeature.properties.region_id}</span>
						</div>
					)}
					{selectedFeature.properties?.district_id && (
						<div>
							<span className='font-medium text-gray-600'>Район:</span>
							<span className='ml-2 text-gray-800'>{selectedFeature.properties.district_id}</span>
						</div>
					)}
					{selectedFeature.properties?.section_id && (
						<div>
							<span className='font-medium text-gray-600'>Секция:</span>
							<span className='ml-2 text-gray-800'>{selectedFeature.properties.section_id}</span>
						</div>
					)}
					{selectedFeature.properties?.lat && selectedFeature.properties?.lng && (
						<div>
							<span className='font-medium text-gray-600'>Координаты:</span>
							<span className='ml-2 text-gray-800'>
								{selectedFeature.properties.lat}, {selectedFeature.properties.lng}
							</span>
						</div>
					)}
				</div>
			</div>
		</div>
	)
}
