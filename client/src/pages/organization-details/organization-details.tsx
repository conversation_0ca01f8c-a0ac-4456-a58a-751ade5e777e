import { useHeaderTitle } from '@/providers/header-title-providers'
import { useEffect, useState } from 'react'
import { useParams, useSearchParams, useNavigate } from 'react-router-dom'
import { OrganizationChildrenTable } from './organization-children-table'
import { useGetOneOrganization } from '@/config/queries/organizations/getOne'
import { LoadingScreen } from '@/shared/ui/suspense'
import { FaceId } from '@/shared/components/face-id/face-id'
import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { WorkingScheduleManagment } from '@/shared/components/working-schedule-managment/working-schedule-managment'

import { Tabs } from 'antd'
import { UserManagmentTab } from '@/shared/components/user-managment-tab/user-managment-modal'

const OrganizationDetails = () => {
	const { data: organization, isLoading } = useGetOneOrganization()
	const { setHeaderTitle } = useHeaderTitle()
	const { data: user, isLoading: isLoadingUser } = useAuthMe()
	const currrentOrganization = localStorage.getItem('organization')
	const { id: organizationId } = useParams()
	const [activeTab, setActiveTab] = useState('child')
	const [searchParams, setSearchParams] = useSearchParams()
	const navigate = useNavigate()
	const [tabHistory, setTabHistory] = useState(['child'])

	useEffect(() => {
		setHeaderTitle('Tashkilot malumotlari')

		const handlePopState = () => {
			if (tabHistory.length > 1) {
				const newHistory = [...tabHistory]
				newHistory.pop()
				setTabHistory(newHistory)
				setActiveTab(newHistory[newHistory.length - 1])

				if (newHistory[newHistory.length - 1] !== 'users') {
					const newParams = new URLSearchParams(searchParams)
					newParams.delete('roles')
					newParams.delete('userId')
					setSearchParams(newParams)
				}
			} else {
				setActiveTab('child')
				setTabHistory(['child'])
				const newParams = new URLSearchParams(searchParams)
				newParams.delete('roles')
				newParams.delete('userId')
				setSearchParams(newParams)
			}
		}

		window.addEventListener('popstate', handlePopState)
		return () => window.removeEventListener('popstate', handlePopState)
	}, [setHeaderTitle, tabHistory, searchParams, setSearchParams])

	const handleTabChange = (key: string) => {
		setActiveTab(key)

		setTabHistory([...tabHistory, key])
		navigate('', { state: { tab: key } })

		if (key !== 'users') {
			const newParams = new URLSearchParams(searchParams)
			newParams.delete('roles')
			newParams.delete('userId')
			setSearchParams(newParams)
		}
	}

	if (isLoading || isLoadingUser || !user?.role) return <LoadingScreen />

	return (
		<main>
			<div className='flex items-start justify-between py-5 px-3'>
				<div className='flex flex-col gap-y-2'>
					<h1 className='text-3xl font-bold'>{organization?.name}</h1>
					{organization?.address && <p className='text-md'>Manzili: {organization?.address}</p>}
				</div>
				<div className='flex flex-col items-end gap-y-2 mr-4'>
					<div className='flex items-center gap-x-3'>
						{JSON.parse(currrentOrganization ?? '').grade.level === -1 && (
							<div>
								<FaceId />
							</div>
						)}
						<div>
							<WorkingScheduleManagment />
						</div>
					</div>
				</div>
			</div>
			<div className='w-full flex flex-col gap-y-4'>
				<Tabs
					activeKey={activeTab}
					onChange={handleTabChange}
					type='card'
					items={[
						...['child', 'undercontrol'].map(i => {
							return {
								key: i,
								label: i === 'child' ? 'Quyi tashkilotlar' : 'Boshqa tashkilotlar',
								children: <OrganizationChildrenTable type={i} />
							}
						}),
						{
							key: 'users',
							label: 'Foydalanuvchilar',
							children: <UserManagmentTab organizationId={organizationId} />
						}
					]}
				/>
			</div>
		</main>
	)
}

export default OrganizationDetails
