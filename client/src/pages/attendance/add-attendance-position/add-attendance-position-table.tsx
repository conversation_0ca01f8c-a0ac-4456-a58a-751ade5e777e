import { Table, Typography, Spin, Empty } from 'antd'
import { useMemo } from 'react'
import { useGetPositionAttendance } from '@/config/queries/attendance/get-position-attendance'

const { Title } = Typography

interface AddAttendancePositionTableProps {
	selectedPositionId?: string
	selectedDate?: string
}

export default function AddAttendancePositionTable({
	selectedPositionId,
	selectedDate
}: AddAttendancePositionTableProps) {
	// Use our dedicated query for add attendance position
	const { data: attendanceData, isLoading } = useGetPositionAttendance(selectedPositionId)

	// Only show data if position is selected and data is available
	const shouldShowData = selectedPositionId && attendanceData

	const tableData = useMemo(() => {
		if (!shouldShowData || !attendanceData?.workers) return []

		return attendanceData.workers.map((worker: any, index: number) => ({
			key: worker.employee.id,
			id: worker.employee.id,
			index: index + 1,
			fullName: worker.employee.fullName,
			position: worker.employee.position,
			organization: worker.organization?.name || worker.employee.organization?.name
		}))
	}, [shouldShowData, attendanceData])

	const columns = [
		{
			title: '#',
			dataIndex: 'index',
			key: 'index'
		},
		{
			title: 'F.I.O',
			dataIndex: 'fullName',
			key: 'fullName'
		},
		{
			title: 'Lavozim',
			dataIndex: ['position', 'name'],
			key: 'position'
		},
		{
			title: 'Tashkilot',
			dataIndex: 'organization',
			key: 'organization'
		}
	]

	if (!selectedPositionId) {
		return (
			<div className='mt-6'>
				<Title
					level={5}
					className='dark:text-gray-200 mb-4'>
					Xodimlar ro'yxati
				</Title>
				<Empty
					description='Lavozimni tanlang'
					className='py-10'
				/>
			</div>
		)
	}

	if (isLoading) {
		return (
			<div className='mt-6'>
				<Title
					level={5}
					className='dark:text-gray-200 mb-4'>
					Xodimlar ro'yxati
				</Title>
				<div className='flex justify-center items-center py-20'>
					<Spin
						size='large'
						tip="Xodimlar ro'yxati yuklanmoqda..."
					/>
				</div>
			</div>
		)
	}

	return (
		<div className='mt-6'>
			<Title
				level={5}
				className='dark:text-gray-200 mb-4'>
				Xodimlar ro'yxati
				{attendanceData?.position && (
					<span className='text-blue-600 dark:text-blue-400 ml-2'>
						({attendanceData.position.name})
					</span>
				)}
			</Title>

			{selectedDate && (
				<div className='mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg'>
					<p className='text-sm text-blue-700 dark:text-blue-300'>
						<strong>Tanlangan sana:</strong> {selectedDate}
					</p>
					<p className='text-sm text-blue-700 dark:text-blue-300 mt-1'>
						Quyidagi xodimlar uchun davomat qo'shiladi: <strong>{tableData.length} kishi</strong>
					</p>
				</div>
			)}

			<div className='dark:bg-gray-800 dark:text-gray-200 overflow-hidden rounded-lg'>
				<Table
					bordered
					rowKey='id'
					size='small'
					className='attendance-table'
					scroll={{ x: 'max-content' }}
					columns={columns}
					dataSource={tableData}
					locale={{ emptyText: 'Bu lavozimda xodimlar topilmadi' }}
					pagination={{
						pageSize: 10,
						showSizeChanger: true,
						pageSizeOptions: [10, 20, 50],
						showTotal: total => `Jami: ${total} xodim`
					}}
				/>
			</div>
		</div>
	)
}
