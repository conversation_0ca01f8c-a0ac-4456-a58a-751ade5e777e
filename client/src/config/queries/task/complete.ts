import { axiosPrivate } from '@/config/api'
import { taskEndpoints } from '@/config/api/endpoints'
import { TApiErrorResponse } from '@/shared/types/api-error.type'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { notification } from 'antd'
import { AxiosError } from 'axios'

export const useCompleteTask = (taskId?: string) => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({
			id,
			stateId,
			description
		}: {
			id: string
			stateId: string
			description: string
		}) => {
			return await axiosPrivate.patch(taskEndpoints.complete(id), {
				stateId,
				description
			})
		},
		onSuccess: async () => {
			if (taskId) {
				await queryClient.invalidateQueries({ queryKey: [taskEndpoints.one(taskId)] })
				notification.success({ message: 'Topshiriq muaffaqiyatli yakunlandi!' })
			}
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response)
			notification.error({ message: 'Topshiriq yakunlashda xatolik yuz berdi!' })
		}
	})
}
