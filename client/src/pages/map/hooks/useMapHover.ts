import { useState, useCallback, useRef } from 'react'
import { useAreaNames } from './useAreaNames'
import * as L from 'leaflet'

export interface HoverInfo {
	areaName: string
	locationInfo: {
		regionName?: string | null
		districtName?: string | null
		sectionName?: string | null
		fullPath: string
	}
	position: {
		x: number
		y: number
	}
	bounds?: {
		center: { lat: number; lng: number }
		bounds: { north: number; south: number; east: number; west: number }
	}
	feature?: any
	geoCenter?: L.LatLng
}

/**
 * Хук для обработки hover событий на карте с отображением названий областей
 */
export const useMapHover = (regionId?: string | null, districtId?: string | null) => {
	const [hoverInfo, setHoverInfo] = useState<HoverInfo | null>(null)
	const { getAreaName, getLocationInfo, isLoading } = useAreaNames(regionId, districtId)
	const currentMapRef = useRef<any>(null)

	/**
	 * Вычисляет точный геометрический центр (центроид) полигона
	 */
	const calculatePolygonCentroid = useCallback((coordinates: number[][]): [number, number] => {
		let area = 0
		let x = 0
		let y = 0

		for (let i = 0; i < coordinates.length - 1; i++) {
			const xi = coordinates[i][0]
			const yi = coordinates[i][1]
			const xi1 = coordinates[i + 1][0]
			const yi1 = coordinates[i + 1][1]

			const a = xi * yi1 - xi1 * yi
			area += a
			x += (xi + xi1) * a
			y += (yi + yi1) * a
		}

		area *= 0.5
		if (Math.abs(area) < 1e-10) {
			const avgX = coordinates.reduce((sum, coord) => sum + coord[0], 0) / coordinates.length
			const avgY = coordinates.reduce((sum, coord) => sum + coord[1], 0) / coordinates.length
			return [avgX, avgY]
		}

		x /= 6 * area
		y /= 6 * area

		return [x, y]
	}, [])

	/**
	 * Получает географический центр области
	 */
	const getGeographicCenter = useCallback(
		(feature: any): L.LatLng => {
			try {
				if (feature?.geometry) {
					if (feature.geometry.type === 'Polygon') {
						const coordinates = feature.geometry.coordinates[0]
						if (coordinates && coordinates.length > 2) {
							const [lng, lat] = calculatePolygonCentroid(coordinates)
							const center = L.latLng(lat, lng)

							const geoJsonLayer = L.geoJSON(feature)
							const bounds = geoJsonLayer.getBounds()

							if (!bounds.contains(center)) {
								return bounds.getCenter()
							}
							return center
						}
					} else if (feature.geometry.type === 'MultiPolygon') {
						const polygons = feature.geometry.coordinates
						let largestPolygon = polygons[0]
						let largestArea = 0

						polygons.forEach((polygon: number[][][]) => {
							const coords = polygon[0]
							if (coords.length > largestArea) {
								largestArea = coords.length
								largestPolygon = polygon
							}
						})

						if (largestPolygon && largestPolygon[0] && largestPolygon[0].length > 2) {
							const [lng, lat] = calculatePolygonCentroid(largestPolygon[0])
							return L.latLng(lat, lng)
						}
					}

					const geoJsonLayer = L.geoJSON(feature)
					return geoJsonLayer.getBounds().getCenter()
				}
			} catch (error) {
				console.warn('Error calculating geographic center:', error)
			}

			// Fallback
			return L.latLng(41.3775, 64.5853)
		},
		[calculatePolygonCentroid]
	)

	/**
	 * Обработчик наведения мыши на область
	 */
	const handleMouseEnter = useCallback(
		(feature: any, event: MouseEvent, map?: any) => {
			if (isLoading) {
				return
			}

			if (!feature?.properties) {
				return
			}

			const areaName = getAreaName(feature.properties)

			if (!areaName) {
				return
			}

			const locationInfo = getLocationInfo(feature.properties)

			// Сохраняем ссылку на карту
			if (map) {
				currentMapRef.current = map
			}

			// Получаем географический центр области для дальнейших вычислений
			const geoCenter = getGeographicCenter(feature)

			// Используем позицию мыши для начального отображения tooltip
			const screenPosition = { x: event.clientX, y: event.clientY }

			const hoverData: HoverInfo = {
				areaName,
				locationInfo,
				position: screenPosition,
				feature,
				geoCenter
			}

			setHoverInfo(hoverData)
		},
		[getAreaName, getLocationInfo, isLoading, getGeographicCenter]
	)

	/**
	 * Обработчик ухода мыши с области
	 */
	const handleMouseLeave = useCallback(() => {
		setHoverInfo(null)
		currentMapRef.current = null
	}, [])

	/**
	 * Обработчик движения мыши для обновления позиции tooltip
	 */
	const handleMouseMove = useCallback(
		(event: MouseEvent) => {
			// Обновляем позицию tooltip при движении мыши
			if (hoverInfo) {
				setHoverInfo(prev => {
					if (!prev) return null
					return {
						...prev,
						position: { x: event.clientX, y: event.clientY }
					}
				})
			}
		},
		[hoverInfo]
	)

	return {
		hoverInfo,
		handleMouseEnter,
		handleMouseLeave,
		handleMouseMove,
		isLoading
	}
}
