import { Button, Form, Modal } from 'antd'
import { useParams } from 'react-router-dom'
import { useEffect } from 'react'
import { PlusOutlined } from '@ant-design/icons'
import RecipientAsnwerTypeForm from './recipient-answer-type-form'
import { useRecipientAnswerTypesModalStore } from '../utils/recipient-answer-type-store'
import {
	RecipientAnswerType,
	useCreateRecipientAnswerType
} from '@/config/queries/recipient-answer-type/create.queries'
import { useUpdateRecipientAnswerType } from '@/config/queries/recipient-answer-type/update.queries'

const RecipientAsnwerTypeModal = () => {
	const { data, open, onClose } = useRecipientAnswerTypesModalStore()
	const [form] = Form.useForm()
	const create = useCreateRecipientAnswerType()
	const update = useUpdateRecipientAnswerType()
	const { id } = useParams()

	const close = () => {
		form.resetFields()
		onClose()
	}

	useEffect(() => {
		if (data) {
			form.setFieldsValue(data)
		}
	}, [data, form])

	const onFinish = (formData: RecipientAnswerType) => {
		if (!data) {
			create.mutateAsync({ id: id!, ...formData }).then(close)
		} else {
			if (data.id) {
				update.mutateAsync({ id: data.id, ...formData }).then(close)
			}
		}
	}

	return (
		<div className='flex items-center justify-end'>
			<Button
				type='primary'
				icon={<PlusOutlined />}
				onClick={() => useRecipientAnswerTypesModalStore.setState({ open: true })}>
				Javob holati
			</Button>
			<Modal
				open={open}
				onCancel={close}
				destroyOnClose
				onOk={form.submit}
				title={`holatini${data ? 'ni tahrirlash' : "ni qo'shish"}`}
				okText={data ? 'Tahrirlash' : "Qo'shish"}
				cancelText='Bekor qilish'
				modalRender={node => (
					<Form
						layout={'vertical'}
						size='large'
						onFinish={onFinish}
						form={form}>
						{node}
					</Form>
				)}>
				<RecipientAsnwerTypeForm />
			</Modal>
		</div>
	)
}
export default RecipientAsnwerTypeModal
