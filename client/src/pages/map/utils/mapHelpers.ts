import { COLORS, BORDER_COLORS } from '../constants/colors'

/**
 * Получает цвет заливки по индексу
 */
export const getColorByIndex = (index: number): string => {
	return COLORS[index % COLORS.length]
}

/**
 * Получает цвет границы по индексу
 */
export const getBorderColorByIndex = (index: number): string => {
	return BORDER_COLORS[index % BORDER_COLORS.length]
}

/**
 * Создает содержимое всплывающей подсказки для области
 */
export const createPopupContent = (properties: any, isClickable: boolean = false): string => {
	return `
        <div style="font-family: Arial; padding: 8px; min-width: 200px;">
            <div style="font-weight: bold; margin-bottom: 8px; color: #374151;">
                <span style="color: #059669;">📍</span> ${properties.type}
                ${
									isClickable
										? '<span style="color: #047857; font-size: 12px; margin-left: 4px;">• Кликабельно</span>'
										: ''
								}
            </div>
            <div style="margin-bottom: 4px;">
                <strong>ID:</strong> <span style="color: #6366f1;">${properties.id}</span>
            </div>
            ${
							properties.region_id
								? `<div style="margin-bottom: 4px;"><strong>Регион:</strong> ${properties.region_id}</div>`
								: ''
						}
            ${
							properties.district_id
								? `<div style="margin-bottom: 4px;"><strong>Район:</strong> ${properties.district_id}</div>`
								: ''
						}
            ${
							properties.section_id
								? `<div style="margin-bottom: 4px;"><strong>Секция:</strong> ${properties.section_id}</div>`
								: ''
						}
            ${
							properties.lat && properties.lng
								? `<div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #e5e7eb; font-size: 12px; color: #6b7280;">Координаты: ${properties.lat}, ${properties.lng}</div>`
								: ''
						}
            <div style="margin-top: 8px; text-align: center;">
                <button onclick="window.dispatchEvent(new CustomEvent('selectFeature', {detail: ${JSON.stringify(
									{
										properties
									}
								)}}));" 
                    style="background: #3b82f6; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                    Подробнее
                </button>
                ${
									isClickable
										? `
                <button onclick="window.dispatchEvent(new CustomEvent('navigateToArea', {detail: ${JSON.stringify(
									{
										properties
									}
								)}}));" 
                    style="background: #10b981; color: white; border: none; padding: 4px 8px; margin-left: 4px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                    Перейти
                </button>`
										: ''
								}
            </div>
        </div>
    `
}

/**
 * Создает стили для области карты
 */
export const createFeatureStyle = (
	colorIndex: number,
	isHovered: boolean = false,
	isClickable: boolean = false
) => {
	const fillColor = getColorByIndex(colorIndex)
	const borderColor = getBorderColorByIndex(colorIndex)

	// Базовые стили
	const baseStyle = {
		color: isClickable ? (isHovered ? '#3b82f6' : borderColor) : borderColor,
		weight: isClickable ? (isHovered ? 4 : 3) : 2,
		opacity: 1,
		fillOpacity: isHovered ? 0.95 : 0.8,
		fillColor: isClickable ? (isHovered ? fillColor : `${fillColor}dd`) : fillColor,
		dashArray: isHovered ? '5, 5' : '',
		lineCap: 'round' as const,
		lineJoin: 'round' as const
	}

	if (isHovered) {
		return {
			...baseStyle,
			weight: 4,
			opacity: 1,
			fillOpacity: 0.95,
			color: isClickable ? '#3b82f6' : '#ffffff',
			dashArray: '5, 5'
		}
	}

	return baseStyle
}
