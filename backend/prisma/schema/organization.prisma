model Grade {
  id           String         @id @default(cuid())
  level        Int
  status       Status         @default(ACTIVE)
  name         String         @unique
  description  String?
  Organization Organization[]
}

model Organization {
  id          String  @id @default(cuid())
  name        String
  status      Status  @default(ACTIVE)
  description String?
  address     String?
  phone       String?

  type   OrganizationType @relation(fields: [typeId], references: [id])
  typeId String

  UnderControl   Organization[] @relation("UnderControl")
  ControlledBy   Organization?  @relation("UnderControl", fields: [controlledById], references: [id])
  controlledById String?

  Children Organization[] @relation("Parent")
  Parent   Organization?  @relation("Parent", fields: [parentId], references: [id])
  parentId String?

  sector            Int?
  sectorResponsible Int?

  grade   Grade  @relation(fields: [gradeId], references: [id])
  gradeId String

  regionId   String
  region     Region    @relation(fields: [regionId], references: [id])
  districtId String?
  district   District? @relation(fields: [districtId], references: [id])
  sectionId  String?
  section    Section?  @relation(fields: [sectionId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  Employee User[] // bu organizatsiya hodimi. U User modeilda MainOrganization bilan bog'liq.

  Worker User[] @relation("User") // bu organizatsiyada ishlaydigan ishchi. U User modeilda Organization bilan bog'liq.

  Responsible User[] @relation("OrganizationResponsible") // bu organizatsiyada mas'ul bo'lgan hodimlar. U User modeilda ResponsibleOrganization bilan bog'liq.

  Conroller User?   @relation("OrganizationController", fields: [userId], references: [id]) // bu tashkilotni boshqaruvchi hodimlar. U User modeilda ControllerOrganization bilan bog'liq.
  userId    String?

  Task Task[]

  Vacancy Vacancy[]

  Recipient Recipient[]

  Attendance Attendance[]

  FaceIDDevice FaceIDDevice[]

  Notification Notification[]
}

model OrganizationType {
  id                       String                     @id @default(cuid())
  name                     String                     @unique
  status                   Status                     @default(ACTIVE)
  description              String?
  Organization             Organization[]
  OrganizationTypePosition OrganizationTypePosition[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([name])
}

model OrganizationTypePosition {
  id     String @id @default(cuid())
  name   String
  status Status @default(ACTIVE)

  description String?
  type        OrganizationType @relation(fields: [typeId], references: [id])
  typeId      String

  User User[]

  Vacancy Vacancy[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([typeId])
}
