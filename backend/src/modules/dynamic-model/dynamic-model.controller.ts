import { Body, Controller, Delete, Get, Param, Patch } from '@nestjs/common';
import { DynamicModelsService } from './dynamic-models.service';
import { ProtectedRoute } from '../../common';

@Controller('dynamic')
export class DynamicModelsController {
  constructor(private readonly service: DynamicModelsService) {}

  @ProtectedRoute({
    isPublic: false,
  })
  @Get()
  getModels(): string[] {
    return this.service.getAllModelNames();
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Patch(':model/:id/status')
  async activateStatus(@Param('model') model: string, @Param('id') id: string) {
    return this.service.activateStatus(model, id);
  }

  @ProtectedRoute({ isPublic: false })
  @Patch('data/:model/:id')
  async updateData(@Param('model') model: string, @Param('id') id: string, @Body() data: any) {
    return await this.service.updateModelData(model, id, data);
  }

  @ProtectedRoute({ isPublic: false })
  @Get(':model/inactive')
  async getInactiveItems(@Param('model') model: string) {
    return this.service.getInactiveItems(model);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE' })
  @Delete(':model/:id')
  async deleteItem(@Param('model') model: string, @Param('id') id: string) {
    return this.service.forceDelete(model, id);
  }
}
