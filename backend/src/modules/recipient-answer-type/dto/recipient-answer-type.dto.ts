import { IsEnum, <PERSON>NotEmpty, IsOptional, IsString } from 'class-validator';
import { Status } from '@prisma/client';

export class CreateRecipientAnswerTypeDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsEnum(Status)
  @IsOptional()
  status?: Status;
}

export class UpdateRecipientAnswerTypeDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsEnum(Status)
  @IsOptional()
  status?: Status;
}
