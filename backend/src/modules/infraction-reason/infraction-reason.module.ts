import { Module } from '@nestjs/common';
import { InfractionReasonService } from './infraction-reason.service';
import { InfractionReasonController } from './infraction-reason.controller';
import { PrismaService } from '../../common';
import { FileService } from 'src/modules/file/file.service';

@Module({
  controllers: [InfractionReasonController],
  providers: [InfractionReasonService, PrismaService, FileService],
})
export class InfractionReasonModule {}
