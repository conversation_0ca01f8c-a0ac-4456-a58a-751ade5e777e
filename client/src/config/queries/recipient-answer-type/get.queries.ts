import { axiosPrivate } from '@/config/api'
import { recipientAnswerTypeEndpoints } from '@/config/api/endpoints'
import { useInfiniteQuery } from '@tanstack/react-query'
import { RecipientAnswerType } from './create.queries'
import { TPaginationWrapper } from '@/shared/types/pagination.type'
import { useSearchParams } from 'react-router-dom'

export const useRecipientAnswerTypes = () => {
	const limit = 10
	const page = 1

	const [searchParams] = useSearchParams()
	const search = searchParams.get('search')

	return useInfiniteQuery({
		queryKey: [recipientAnswerTypeEndpoints.all, search, 'infinite', page, limit],
		queryFn: async ({ pageParam }) => {
			return (
				await axiosPrivate.get<TPaginationWrapper<RecipientAnswerType[]>>(
					recipientAnswerTypeEndpoints.all,
					{
						params: {
							page: pageParam,
							limit,
							search
						}
					}
				)
			).data
		},
		initialPageParam: page,
		getNextPageParam: lastPage => {
			if (lastPage?.meta?.page === lastPage?.meta?.totalPages) return undefined
			return lastPage?.meta?.page + 1
		},
		getPreviousPageParam: lastPage => {
			if (lastPage?.meta?.page === 1) return undefined
			return lastPage?.meta?.page - 1
		}
	})
}
