// DTO fayli: update-task-recipients.dto.ts

import { Type } from 'class-transformer';
import { IsArray, IsOptional, IsString, ValidateNested } from 'class-validator';

export class RecipientDto {
  @IsOptional()
  @IsString()
  organizationId: string;

  @IsArray()
  @IsOptional()
  @Type(() => String)
  userId?: string[];

  @IsString()
  @IsOptional()
  positionId?: string;
}

export class UpdateTaskRecipientsBodyDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RecipientDto)
  recipients: RecipientDto[];
}
