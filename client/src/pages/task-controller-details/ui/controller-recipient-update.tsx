import { useState, useEffect } from 'react'
import ControllerRecipientUpdateForm, { TRecipent } from './controller-recipient-update-form'

interface ControllerRecipientUpdateProps {
	task: any
}

const ControllerRecipientUpdate = ({ task }: ControllerRecipientUpdateProps) => {
	// Initialize with existing recipients from task
	const [recipents, setRecipents] = useState<TRecipent[]>([])

	// Initialize with empty recipient form instead of populating existing ones
	useEffect(() => {
		// Always start with empty form
		setRecipents([{ organizationId: '', userId: [] }])
	}, [])

	return (
		<ControllerRecipientUpdateForm
			recipents={recipents}
			setRecipents={setRecipents}
			task={task}
		/>
	)
}

export default ControllerRecipientUpdate
