import { Injectable } from '@nestjs/common';
import { PrismaService } from 'src/common';

type RawQueryType = { count: bigint }[];

@Injectable()
export class StatsService {
  constructor(private prismaService: PrismaService) {}

  async getAllChildOrganizationIds(organizationId: string) {
    const organization = await this.prismaService.organization.findMany({
      where: {
        parentId: organizationId,
      },
      select: {
        id: true,
      },
    });

    const childIds = organization.map((child) => child.id);

    if (childIds.length === 0) {
      return [];
    }

    const nestedChildrenIds = await Promise.all(childIds.map((childId) => this.getAllChildOrganizationIds(childId)));

    return [...childIds, ...nestedChildrenIds.flat()];
  }

  async getOrganizationHierarchyStatistics(organizationId: string) {
    const childIds = await this.getAllChildOrganizationIds(organizationId);
    const allOrgIds = [organizationId, ...childIds];

    const now = new Date();

    const tasks = await this.prismaService.task.findMany({
      where: {
        createdByOrganizationId: {
          in: allOrgIds,
        },
      },
      select: {
        id: true,
        status: true,
        dueDate: true,
        isCompleted: true,
        completedAt: true,
      },
    });

    const taskStats = {
      completed: {
        onTime: 0,
        late: 0,
        total: 0,
      },
      incomplete: {
        expired: 0,
        active: 0,
        total: 0,
      },
      total: tasks.length,
    };

    tasks.forEach((task) => {
      if (task.isCompleted) {
        if (task.completedAt <= task.dueDate) {
          taskStats.completed.onTime++;
        } else {
          taskStats.completed.late++;
        }
        taskStats.completed.total++;
      } else {
        if (task.dueDate <= now) {
          taskStats.incomplete.expired++;
        } else {
          taskStats.incomplete.active++;
        }
        taskStats.incomplete.total++;
      }
    });

    const workers = await this.prismaService.user.findMany({
      where: {
        Organization: {
          some: {
            id: {
              in: allOrgIds,
            },
          },
        },
      },
      select: {
        id: true,
      },
    });

    const workerIds = workers.map((worker) => worker.id);

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const attendancesReport = await this.prismaService.attendanceReport.findMany({
      where: {
        userId: { in: workerIds },
        createdAt: {
          gte: today,
          lt: tomorrow,
        },
        status: 'ACTIVE',
      },
      select: {
        id: true,
        enter: true,
        exit: true,
        userId: true,
      },
    });

    const attendanceStats = {
      onTime: 0,
      late: 0,
      absent: 0,
      total: workerIds.length,
    };

    attendancesReport.forEach(async (attendance) => {
      const schedule = await this.prismaService.userWorkingScheduleDay.findFirst({
        where: { UserWorkingSchedule: { every: { userId: attendance.userId } }, day: today.getDay() },
        select: { startTime: true, endTime: true },
      });
      if (!attendance.enter || !schedule) {
        attendanceStats.absent++;
      } else if (attendance.enter <= new Date(schedule.startTime)) {
        attendanceStats.onTime++;
      } else {
        attendanceStats.late++;
      }
    });

    attendanceStats.absent = workerIds.length - (attendanceStats.onTime + attendanceStats.late);

    return {
      organizations: {
        current: organizationId,
        children: childIds,
        total: allOrgIds.length,
      },
      workers: {
        total: workerIds.length,
      },
      tasks: taskStats,
      attendance: attendanceStats,
    };
  }

  async getOrganizationChildrenAttendance(organizationId: string) {
    const organization = await this.prismaService.organization.findMany({
      where: {
        parentId: organizationId,
        grade: { level: 30 },
      },
      select: { id: true },
    });

    const childIds = organization.map((child) => child.id);
    if (childIds.length === 0) return [];

    const nestedChildrenIds = await Promise.all(childIds.map((childId) => this.getAllChildOrganizationIds(childId)));
    const organizations = [...childIds, ...nestedChildrenIds.flat()];

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Получаем всех работников
    const workers = await this.prismaService.user.findMany({
      where: {
        Organization: {
          some: { id: { in: organizations } },
        },
      },
      select: { id: true },
    });

    const workerIds = workers.map((worker) => worker.id);

    // Получаем все возможные позиции
    const positions = await this.prismaService.organizationTypePosition.findMany({
      select: { name: true },
    });

    // Создаем карту статистики, заполняем 0-ми изначально
    const statsMap = new Map();
    positions.forEach((pos) => {
      statsMap.set(pos.name, { position: pos.name, absent: 0, onTime: 0, late: 0, totalWorkers: 0 });
    });

    // Получаем отчеты посещаемости
    const attendancesReport = await this.prismaService.attendanceReport.findMany({
      where: {
        userId: { in: workerIds },
        createdAt: { gte: today, lt: tomorrow },
        status: 'ACTIVE',
      },
      select: { id: true, enter: true, exit: true, userId: true },
    });

    // Обрабатываем отчеты
    await Promise.all(
      attendancesReport.map(async (attendance) => {
        const schedule = await this.prismaService.userWorkingScheduleDay.findFirst({
          where: { UserWorkingSchedule: { every: { userId: attendance.userId } }, day: today.getDay() },
          select: { startTime: true, endTime: true },
        });

        // Получаем позицию работника
        const workerPosition = await this.prismaService.organizationTypePosition.findFirst({
          where: { User: { some: { id: attendance.userId } } },
          select: { name: true },
        });

        const positionName = workerPosition?.name || 'unknown';

        const stats = statsMap.get(positionName);
        if (!stats) return;

        stats.totalWorkers++;

        if (!attendance.enter || !schedule) {
          stats.absent++;
        } else if (attendance.enter <= new Date(schedule?.startTime)) {
          stats.onTime++;
        } else {
          stats.late++;
        }

        stats.absent = stats.totalWorkers - (stats.onTime + stats.late);
      }),
    );

    return {
      organizations: { current: organizationId, children: childIds, total: nestedChildrenIds.length },
      workers: { total: workerIds.length },
      attendance: Array.from(statsMap.values()), // Возвращаем все позиции, даже с 0
    };
  }

  async getTaskStats(orgId: string) {
    const totalIncompletedTasks = await this.prismaService.task.count({
      where: {
        createdByOrganizationId: orgId,
        isCompleted: false,
      },
    });
    const totalCompletedTasks = await this.prismaService.task.count({
      where: {
        createdByOrganizationId: orgId,
        isCompleted: true,
      },
    });

    const completedTaskAtTime = Number(
      (
        await this.prismaService.$queryRaw<RawQueryType>`
    SELECT COUNT(*) as count 
    FROM "Task"
    WHERE "isCompleted" = true 
    AND "updatedAt" <= "dueDate"
    AND "createdByOrganizationId" = ${orgId}`
      )[0].count,
    );
    const completedTaskLate = totalCompletedTasks - completedTaskAtTime;

    const incompleted = Number(
      (
        await this.prismaService.$queryRaw<RawQueryType>`
    SELECT COUNT(*) as count
    FROM "Task"
    WHERE "isCompleted" = false
    AND "dueDate" <= ${new Date()}
    AND "createdByOrganizationId" = ${orgId}
    `
      )[0].count,
    );

    const inProcess = totalIncompletedTasks - incompleted;

    return {
      totalCompletedTasks,
      totalIncompletedTasks,
      completedTaskAtTime,
      completedTaskLate,
      incompleted,
      inProcess,
    };
  }

  // async getOrganizationsByLocation(organizationId: string, regionId?: string, districtId?: string, sectionId?: string) {
  //   const allChildOrganizations = await this.prismaService.$queryRaw<{ id: string }[]>`
  //   WITH RECURSIVE org_tree AS (
  //     -- Base case: root organization
  //     SELECT id, "parentId", 1 AS level
  //     FROM "Organization"
  //     WHERE id = ${organizationId}
  //
  //     UNION ALL
  //
  //     -- Recursive case: children organizations
  //     SELECT o.id, o."parentId", t.level + 1
  //     FROM "Organization" o
  //     JOIN org_tree t ON o."parentId" = t.id
  //   )
  //   SELECT id FROM org_tree
  // `;
  //
  //   const organizationIds = allChildOrganizations.map((org) => org.id);
  //
  //   const childOrganizations = await this.prismaService.organization.findMany({
  //     where: {
  //       AND: [
  //         {
  //           id: {
  //             in: organizationIds,
  //           },
  //           grade: {
  //             level: 30,
  //           },
  //         },
  //         {
  //           ...(regionId ? { regionId } : {}),
  //           ...(districtId ? { districtId } : {}),
  //           ...(sectionId ? { sectionId } : {}),
  //         },
  //       ],
  //     },
  //     include: {
  //       region: {
  //         select: {
  //           id: true,
  //           name: true,
  //         },
  //       },
  //       district: {
  //         select: {
  //           id: true,
  //           name: true,
  //         },
  //       },
  //       section: {
  //         select: {
  //           id: true,
  //           name: true,
  //         },
  //       },
  //       grade: true,
  //     },
  //     orderBy: {
  //       createdAt: 'desc',
  //     },
  //   });
  //
  //   return childOrganizations;
  // }

  async getOrganizationsByLocation(organizationId: string, regionId?: string, districtId?: string, sectionId?: string) {
    const allChildOrganizations = await this.prismaService.$queryRaw<{ id: string }[]>`
    WITH RECURSIVE org_tree AS (
      -- Base case: root organization
      SELECT id, "parentId", 1 AS level
      FROM "Organization"
      WHERE id = ${organizationId}
      
      UNION ALL
      
      -- Recursive case: children organizations
      SELECT o.id, o."parentId", t.level + 1
      FROM "Organization" o
      JOIN org_tree t ON o."parentId" = t.id
    )
    SELECT id FROM org_tree
  `;

    const allOrganizationIds = allChildOrganizations.map((org) => org.id);

    const childOrganizations = await this.prismaService.organization.findMany({
      where: {
        AND: [
          {
            id: {
              in: allOrganizationIds,
            },
            grade: {
              level: 30,
            },
          },
          {
            ...(regionId ? { regionId } : {}),
            ...(districtId ? { districtId } : {}),
            ...(sectionId ? { sectionId } : {}),
          },
        ],
      },
      include: {
        region: {
          select: {
            id: true,
            name: true,
          },
        },
        district: {
          select: {
            id: true,
            name: true,
          },
        },
        section: {
          select: {
            id: true,
            name: true,
          },
        },
        grade: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });

    const grade30OrganizationIds = childOrganizations.map((org) => org.id);

    const workers = await this.prismaService.user.findMany({
      where: {
        Organization: {
          some: {
            id: {
              in: grade30OrganizationIds,
            },
          },
        },
      },
      select: {
        id: true,
      },
    });

    const workerIds = workers.map((worker) => worker.id);

    const tasks = await this.prismaService.task.findMany({
      where: {
        createdByOrganizationId: {
          in: grade30OrganizationIds,
        },
      },
      select: {
        id: true,
        status: true,
        dueDate: true,
        isCompleted: true,
        completedAt: true,
      },
    });

    const now = new Date();
    const taskStats = {
      completed: {
        onTime: 0,
        late: 0,
        total: 0,
      },
      incomplete: {
        expired: 0,
        active: 0,
        total: 0,
      },
      total: tasks.length,
    };

    tasks.forEach((task) => {
      if (task.isCompleted) {
        if (task.completedAt <= task.dueDate) {
          taskStats.completed.onTime++;
        } else {
          taskStats.completed.late++;
        }
        taskStats.completed.total++;
      } else {
        if (task.dueDate <= now) {
          taskStats.incomplete.expired++;
        } else {
          taskStats.incomplete.active++;
        }
        taskStats.incomplete.total++;
      }
    });

    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const attendancesReport = await this.prismaService.attendanceReport.findMany({
      where: {
        userId: { in: workerIds },
        createdAt: {
          gte: today,
          lt: tomorrow,
        },
        status: 'ACTIVE',
      },
      select: {
        id: true,
        enter: true,
        exit: true,
        userId: true,
      },
    });

    const attendanceStats = {
      onTime: 0,
      late: 0,
      absent: 0,
      total: workerIds.length,
    };

    const userAttendancePromises = attendancesReport.map(async (attendance) => {
      const schedule = await this.prismaService.userWorkingScheduleDay.findFirst({
        where: { UserWorkingSchedule: { every: { userId: attendance.userId } }, day: today.getDay() },
        select: { startTime: true, endTime: true },
      });

      if (!attendance.enter || !schedule) {
        attendanceStats.absent++;
      } else if (attendance.enter <= new Date(schedule.startTime)) {
        attendanceStats.onTime++;
      } else {
        attendanceStats.late++;
      }
    });

    await Promise.all(userAttendancePromises);

    attendanceStats.absent = workerIds.length - (attendanceStats.onTime + attendanceStats.late);

    return {
      statistics: {
        organizations: {
          count: childOrganizations.length,
          total: allOrganizationIds.length,
        },
        workers: {
          total: workerIds.length,
        },
        tasks: taskStats,
        attendance: attendanceStats,
      },
    };
  }
}
