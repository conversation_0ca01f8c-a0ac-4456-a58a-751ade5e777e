import { Controller, Get, Param } from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { ProtectedRoute } from 'src/common';
import { Context } from 'src/common/decorators/context.decorator';

@Controller('dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE' })
  @Get('children-count')
  async getChildrenCount(@Context('organizationId') organizationId: string) {
    return await this.dashboardService.getChildrenCount(organizationId);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE' })
  @Get('users-count')
  async getUsersCount(@Context('organizationId') organizationId: string) {
    return await this.dashboardService.getUsersCount(organizationId);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE' })
  @Get('tasks-count')
  async getTasksCount(@Context('organizationId') organizationId: string) {
    return await this.dashboardService.getTasksCount(organizationId);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE' })
  @Get('tasks-state')
  async getTasksState(@Context('organizationId') organizationId: string) {
    return await this.dashboardService.getTasksState(organizationId);
  }

  @Get('gps-info')
  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE' })
  async getGpsInfo(@Context('organizationId') organizationId: string) {
    return await this.dashboardService.getGpsInfo(organizationId);
  }

  @Get('table/:organizationId')
  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE' })
  async dashboardTable(@Param('organizationId') organizationId: string) {
    return await this.dashboardService.dashboardTable(organizationId);
  }
}
