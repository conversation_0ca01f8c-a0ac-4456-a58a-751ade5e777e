import { axiosPrivate } from '@/config/api'
import { organizationEndpoints, orgEmployeeEndpoints } from '@/config/api/endpoints'
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import { TGrade } from '../grade/getAll'
import { TUser } from '../users/get-all.queries'
import { TPaginationWrapper } from '@/shared/types/pagination.type'
import { useState } from 'react'
import { useSearchParams } from 'react-router-dom'

export type TOrganization = {
	id: string
	name: string
	description: string
	address?: string
	phone?: string
	typeId?: string
	grade: TGrade
	UnderControl: TOrganization[]
	Parent?: TOrganization
	Children: TOrganization[]
	sector?: number
	sectorResponsible?: number
	regionId: number
	districtId?: number
	sectionId?: number
	organizationType: unknown
}

export const useGetAllOrganizations = () => {
	const limit = 10
	const [page, setPage] = useState(1)
	const [searchParams] = useSearchParams()
	const search = searchParams.get('search')

	const query = useInfiniteQuery({
		queryKey: [organizationEndpoints.all, 'infinite', search, page, limit],
		queryFn: async ({ pageParam }) => {
			return (
				await axiosPrivate.get<TPaginationWrapper<TOrganization[]>>(organizationEndpoints.all, {
					params: {
						page: pageParam,
						limit,
						search
					}
				})
			).data
		},
		initialPageParam: page,
		getNextPageParam: lastPage => {
			if (lastPage?.meta?.page === lastPage?.meta?.totalPages) return undefined
			return lastPage?.meta?.page + 1
		},
		getPreviousPageParam: lastPage => {
			if (lastPage?.meta?.page === 1) return undefined
			return lastPage?.meta?.page - 1
		}
	})
	return {
		...query,
		setPage
	}
}

// Hook to fetch all children organizations at once
export const useGetAllChildrenOrganizations = () => {
	const [searchParams] = useSearchParams()
	const search = searchParams.get('search')

	return useQuery({
		queryKey: [organizationEndpoints.all, 'all-children', search],
		queryFn: async () => {
			// First, get the total count
			const firstPage = await axiosPrivate.get<TPaginationWrapper<TOrganization[]>>(
				organizationEndpoints.all,
				{
					params: {
						page: 1,
						limit: 1,
						search
					}
				}
			)

			const totalCount = firstPage.data.meta.total

			// If no organizations, return empty array
			if (totalCount === 0) {
				return []
			}

			// Fetch all organizations in one request with high limit
			const allOrgsResponse = await axiosPrivate.get<TPaginationWrapper<TOrganization[]>>(
				organizationEndpoints.all,
				{
					params: {
						page: 1,
						limit: totalCount,
						search
					}
				}
			)

			return allOrgsResponse.data.data
		}
	})
}

export const useGetUsersByOrgId = (orgId: string, page: number = 1, limit: number = 10) => {
	const [searchParams] = useSearchParams()
	const search = searchParams.get('search')

	return useInfiniteQuery({
		queryKey: [orgEmployeeEndpoints.usersByOrgId(orgId), 'infinite', page, limit, search],
		queryFn: async ({ pageParam }) => {
			return (
				await axiosPrivate.get<TPaginationWrapper<TUser[]>>(
					orgEmployeeEndpoints.usersByOrgId(orgId),
					{
						params: {
							page: pageParam,
							limit,
							search
						}
					}
				)
			).data
		},
		initialPageParam: page,
		getNextPageParam: lastPage => {
			if (lastPage?.meta?.page === lastPage?.meta?.totalPages) return undefined
			return lastPage?.meta?.page + 1
		},
		getPreviousPageParam: lastPage => {
			if (lastPage?.meta?.page === 1) return undefined
			return lastPage?.meta?.page - 1
		}
	})
}
