import { Body, Controller, Get, Param, Patch, Post, Query } from '@nestjs/common';
import { RecipientAnswerTypeService } from './recipient-answer-type.service';
import { CreateRecipientAnswerTypeDto, UpdateRecipientAnswerTypeDto } from './dto/recipient-answer-type.dto';
import { Status } from '@prisma/client';
import { ProtectedRoute } from '../../common';
import { BaseQueryParams } from 'src/helpers/types';

@Controller('recipient-answer-type')
export class RecipientAnswerTypeController {
  constructor(private readonly recipientAnswerTypeService: RecipientAnswerTypeService) {}

  @ProtectedRoute({
    isPublic: false,
    // context: 'WORKSPACE',
  })
  @Post()
  async create(@Body() createRecipientAnswerTypeDto: CreateRecipientAnswerTypeDto) {
    return await this.recipientAnswerTypeService.create(createRecipientAnswerTypeDto);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Get()
  async findAll(@Query() query?: BaseQueryParams) {
    return await this.recipientAnswerTypeService.findAll(query);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Get(':id')
  async findOne(@Param('id') id: string) {
    return await this.recipientAnswerTypeService.findOne(id);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateRecipientAnswerTypeDto: UpdateRecipientAnswerTypeDto) {
    return await this.recipientAnswerTypeService.update(id, updateRecipientAnswerTypeDto);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Patch(':id/status')
  async changeStatus(@Param('id') id: string, @Body('status') status: Status) {
    return await this.recipientAnswerTypeService.changeStatus(id, status);
  }
}
