import { useUploadFile } from '@/config/queries/file-upload/upload'
import { getFileUrl } from '@/shared/utils/getFileUrl'
import { InboxOutlined } from '@ant-design/icons'
import { Button, Col, Form, Input, message, Row, Upload, Divider } from 'antd'
import { UploadFile } from 'antd/lib'
import { useState } from 'react'
import { Link } from 'react-router-dom'

export const useFileUpload = () => {
	const [fileIds, setFileIds] = useState<string[]>([])
	const [filePreview, setFilePreview] = useState<{ id: string; path: string; slug: string }[]>([])
	const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({})
	const [isUploading, setIsUploading] = useState(false)
	const { mutateAsync: uploadFile, isPending } = useUploadFile()

	const handleUpload = async (file: UploadFile) => {
		const fileKey = file.uid || file.name || 'unknown'

		try {
			// Set uploading state only when we have active uploads
			setUploadProgress(prev => ({ ...prev, [fileKey]: 0 }))
			setIsUploading(true)

			// Simulate progress updates during upload
			const progressInterval = setInterval(() => {
				setUploadProgress(prev => {
					const currentProgress = prev[fileKey] || 0
					if (currentProgress < 90) {
						return { ...prev, [fileKey]: currentProgress + 10 }
					}
					return prev
				})
			}, 200)

			const fileId = await uploadFile(file as unknown as File)

			// Clear interval and set to 100%
			clearInterval(progressInterval)
			setUploadProgress(prev => ({ ...prev, [fileKey]: 100 }))

			message.success('Fayl muvaffaqiyatli yuklandi')

			// Remove progress after success and check if all uploads are done
			setTimeout(() => {
				setUploadProgress(prev => {
					const newProgress = { ...prev }
					delete newProgress[fileKey]

					// If no more uploads in progress, set isUploading to false
					if (Object.keys(newProgress).length === 0) {
						setIsUploading(false)
					}

					return newProgress
				})
			}, 1000)

			return fileId
		} catch (error) {
			message.error('File yuklashda xatolik')
			setUploadProgress(prev => {
				const newProgress = { ...prev }
				delete newProgress[fileKey]

				// If no more uploads in progress, set isUploading to false
				if (Object.keys(newProgress).length === 0) {
					setIsUploading(false)
				}

				return newProgress
			})
			return null
		}
	}

	const beforeUpload = async (file: UploadFile) => {
		// File size validation (10MB = 10 * 1024 * 1024 bytes)
		const maxSize = 10 * 1024 * 1024
		if (file.size && file.size > maxSize) {
			message.error("Fayl hajmi 10 MB dan kichik bo'lishi kerak!")
			return false
		}

		// File type validation
		const allowedTypes = [
			'image/jpeg',
			'image/jpg',
			'image/png',
			'image/gif',
			'application/pdf',
			'application/zip',
			'application/x-zip-compressed',
			'application/x-rar-compressed',
			'application/vnd.rar',
			'application/msword',
			'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
		]

		if (file.type && !allowedTypes.includes(file.type)) {
			message.error(
				"Fayl turi qo'llab-quvvatlanmaydi! Faqat PDF, ZIP, RAR, JPG, PNG, DOC/X fayllarini yuklang."
			)
			return false
		}

		// Start upload process
		const fileId = await handleUpload(file)
		if (fileId && Array.isArray(fileId)) {
			setFileIds(prev => [...prev, ...fileId.map(f => f.id)])
			setFilePreview(prev => [
				...prev,
				...fileId.map(f => ({ id: f.id, path: f.path, slug: file.name }))
			])
		}

		return false
	}

	const handleRemove = (id: string) => {
		setFileIds(prev => prev.filter(f => f !== id))
		setFilePreview(prev => prev.filter(f => f.id !== id))
	}

	const clearFiles = () => {
		setFileIds([])
		setFilePreview([])
		setUploadProgress({})
		setIsUploading(false)
	}

	return {
		fileIds,
		clearFiles,
		isUploading,
		uploadProgress,
		render: ({ file }: { file?: string } = {}) => (
			<Row gutter={16}>
				<Col span={24}>
					<Form.Item
						help="Fayl hajmi 10 MB dan kichik bo'lishi kerak."
						className='mb-4'>
						<div className='flex flex-col'>
							{filePreview.length > 0 && (
								<>
									<div className='px-3'>
										<h1 className='font-bold text-lg mb-4'>To`liqroq ma`lumot uchun fayllar: </h1>
										<ul className='flex flex-col gap-y-3'>
											{filePreview.map((preview, index) => (
												<div
													key={index}
													className='flex items-center gap-x-2'>
													<Link
														className='w-fit'
														to={getFileUrl(preview.path)}
														target='_blank'>
														{preview.slug}
													</Link>
													<Button
														danger
														size='small'
														onClick={() => handleRemove(preview.id)}>
														O'chirish
													</Button>
												</div>
											))}
										</ul>
									</div>
									<Divider />
								</>
							)}
							{file && filePreview.length === 0 && (
								<>
									<div className='px-3'>
										<h1 className='font-bold text-lg mb-4'>To`liqroq ma`lumot uchun fayllar: </h1>
										<ul className='flex flex-col gap-y-3'>
											<Link
												className='w-fit'
												to={getFileUrl(file)}
												target='_blank'>
												{file}
											</Link>
										</ul>
									</div>
									<Divider />
								</>
							)}

							{/* Upload Progress Display */}
							{Object.keys(uploadProgress).length > 0 && (
								<div className='px-3 mb-4'>
									<h3 className='font-bold text-md mb-3'>Yuklash jarayoni:</h3>
									<div className='space-y-3'>
										{Object.entries(uploadProgress).map(([fileKey, progress]) => (
											<div
												key={fileKey}
												className='bg-white border border-gray-200 rounded-xl p-4 shadow-sm'>
												<div className='flex items-center justify-between'>
													<div className='flex items-center space-x-3'>
														{/* Circular Progress */}
														<div className='relative w-12 h-12'>
															<svg
																className='w-12 h-12 transform -rotate-90'
																viewBox='0 0 36 36'>
																{/* Background circle */}
																<path
																	className='text-gray-200'
																	stroke='currentColor'
																	strokeWidth='3'
																	fill='none'
																	d='M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831'
																/>
																{/* Progress circle */}
																<path
																	className='text-blue-500'
																	stroke='currentColor'
																	strokeWidth='3'
																	strokeLinecap='round'
																	fill='none'
																	strokeDasharray={`${progress}, 100`}
																	d='M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831'
																/>
															</svg>
															{/* Percentage text */}
															<div className='absolute inset-0 flex items-center justify-center'>
																<span className='text-xs font-semibold text-gray-700'>
																	{Math.round(progress)}%
																</span>
															</div>
														</div>

														{/* File info */}
														<div className='flex-1'>
															<p className='text-sm font-medium text-gray-900 truncate max-w-xs'>
																{fileKey}
															</p>
															<p className='text-xs text-gray-500'>
																{progress === 100 ? 'Yuklash tugallandi' : 'Yuklanmoqda...'}
															</p>
														</div>
													</div>

													{/* Status icon */}
													<div className='ml-3'>
														{progress === 100 ? (
															<div className='w-6 h-6 bg-green-100 rounded-full flex items-center justify-center'>
																<svg
																	className='w-4 h-4 text-green-600'
																	fill='currentColor'
																	viewBox='0 0 20 20'>
																	<path
																		fillRule='evenodd'
																		d='M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z'
																		clipRule='evenodd'
																	/>
																</svg>
															</div>
														) : (
															<div className='w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center'>
																<svg
																	className='w-4 h-4 text-blue-600 animate-spin'
																	fill='none'
																	viewBox='0 0 24 24'>
																	<circle
																		className='opacity-25'
																		cx='12'
																		cy='12'
																		r='10'
																		stroke='currentColor'
																		strokeWidth='4'></circle>
																	<path
																		className='opacity-75'
																		fill='currentColor'
																		d='M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z'></path>
																</svg>
															</div>
														)}
													</div>
												</div>
											</div>
										))}
									</div>
								</div>
							)}

							<Upload.Dragger
								disabled={isPending || isUploading}
								accept='image/*,.pdf,.zip,.rar,.doc,.docx'
								multiple={true}
								fileList={[]}
								beforeUpload={beforeUpload}
								progress={{ showInfo: true, strokeWidth: 2 }}
								showUploadList={false}>
								<div className='py-4'>
									<p className='ant-upload-drag-icon'>
										<InboxOutlined />
									</p>
									<p className='ant-upload-text'>Fayl tashlang, yuklang yoki joylashtiring</p>
									<p className='ant-upload-hint'>
										Qo'llab-quvvatlanadigan formatlar: PDF, ZIP, RAR, JPG, PNG, DOC/X
									</p>
								</div>
							</Upload.Dragger>
						</div>
					</Form.Item>
					<Form.Item
						hidden
						name='files'>
						<Input hidden />
					</Form.Item>
				</Col>
			</Row>
		)
	}
}
