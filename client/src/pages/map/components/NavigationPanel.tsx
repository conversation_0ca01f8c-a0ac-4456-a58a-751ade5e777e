import { LeftOutlined } from '@ant-design/icons'
import { Button } from 'antd'
import React from 'react'

interface NavigationPanelProps {
	currentLevel: string
	levelTitle: string
	canNavigateBack: boolean
	onNavigateBack: () => void
	onReset: () => void
}

export const NavigationPanel: React.FC<NavigationPanelProps> = ({
	currentLevel,
	canNavigateBack,
	onNavigateBack
}) => {
	return (
		<div className='absolute bottom-4 left-4 z-[1000] bg-white rounded-lg shadow-lg p-4 min-w-[200px]'>
			<div className='flex flex-col space-y-3'>
				<div className='flex space-x-2'>
					{canNavigateBack && (
						<Button
							onClick={onNavigateBack}
							block
							variant='solid'
							color='blue'
							icon={<LeftOutlined />}
							size='small'>
							Orqaga
						</Button>
					)}
				</div>

				<div className='flex items-center space-x-1 text-xs text-gray-500'>
					<div
						className={`w-2 h-2 rounded-full ${currentLevel === 'overview' ? 'bg-blue-500' : 'bg-gray-300'}`}
					/>
					<span>Respublika</span>

					{currentLevel !== 'overview' && (
						<>
							<span>→</span>
							<div
								className={`w-2 h-2 rounded-full ${currentLevel === 'region' ? 'bg-blue-500' : 'bg-gray-300'}`}
							/>
							<span>Viloyat</span>
						</>
					)}

					{(currentLevel === 'district' || currentLevel === 'section') && (
						<>
							<span>→</span>
							<div
								className={`w-2 h-2 rounded-full ${currentLevel === 'district' ? 'bg-blue-500' : 'bg-gray-300'}`}
							/>
							<span>Tuman / Shahar</span>
						</>
					)}

					{currentLevel === 'section' && (
						<>
							<span>→</span>
							<div className={`w-2 h-2 rounded-full bg-blue-500`} />
							<span>Mahalla</span>
						</>
					)}
				</div>
			</div>
		</div>
	)
}
