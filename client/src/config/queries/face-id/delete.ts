import { axiosPrivate } from '@/config/api'
import { faceIdEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError, AxiosResponse } from 'axios'
import { TFAceIdResponse } from './create'
import { notification } from 'antd'
import { TApiErrorResponse } from '@/shared/types/api-error.type'

export const useDeleteFaceIdDevice = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async (id: string) => {
			return await axiosPrivate.delete(faceIdEndpoints.one(id))
		},
		onSuccess: async (response: AxiosResponse<TFAceIdResponse>) => {
			console.log(response)
			notification.success({ message: 'Face ID muaffaqiyatli ochirildi!' })
			queryClient.invalidateQueries({
				queryKey: [faceIdEndpoints.allByOrg(response.data.organizationId)]
			})
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response)
			notification.error({ message: 'Face ID ochirishda xatolik yuz berdi!' })
		}
	})
}
