import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, InternalServerErrorException, UnauthorizedException } from '@nestjs/common';
import { GPS, GpsJobData, GpsLocationJobData } from './interfaces/gps';
import { Job } from 'bullmq';
import { GpsService } from './gps.service';

@Injectable()
@Processor(GPS, { concurrency: 4 })
export class GPSProcessor extends WorkerHost {
  constructor(private readonly gpsService: GpsService) {
    super();
  }
  async process(job: Job, token?: string): Promise<any> {
    if (job.name === 'gps.setLocation') {
      try {
        const { authorization, body } = job.data as GpsLocationJobData;
        const [bearer, token] = authorization.split(' ');
        if (bearer !== 'Bearer') {
          throw new UnauthorizedException('Неверный токен');
        }
        return this.gpsService.setLocation(token, body);
      } catch (error) {
        throw new InternalServerErrorException(error.message);
      }
    }
  }
}
