import { axiosPrivate } from '@/config/api'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { notification } from 'antd'
import { organizationTypeEndpoints } from '@/config/api/endpoints'

export interface updateOrganizationType {
	id?: string
	name: string
}

export const useUpdateOrganizationType = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async (data: updateOrganizationType) => {
			const { id, ...updateData } = data
			return (
				await axiosPrivate.put<ApiResponse<updateOrganizationType>>(
					organizationTypeEndpoints.one(id!),
					updateData
				)
			).data
		},

		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [organizationTypeEndpoints.all] })
			notification.success({
				message: 'Muvaffaqiyatli yangilandi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: 'Yang<PERSON>shda xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
