import { useGetAllOrganizations } from '@/config/queries/organizations/get-all.queries'
// import { useGetAllUsers } from '@/config/queries/users/get-all.queries'
import { Form, Select } from 'antd'

const AddAttendanceForm = () => {
	const { data: organizations } = useGetAllOrganizations()
	// const { data: users } = useGetAllUsers()
	return (
		<>
			<Form.Item
				name='organization'
				label='Tashkilotlar'
				rules={[{ required: true, message: 'Tashkilotni tanlang' }]}>
				<Select
					placeholder='Tashkilotni tanlang'
					size='middle'
					options={organizations?.pages?.[0]?.data?.map(org => ({
						label: org.name,
						value: org.id
					}))}
				/>
			</Form.Item>
			{/* <Form.Item
				name='user'
				label='Foydalanuvchilar'
				rules={[{ required: true, message: 'Foydalanuvchini tanlang' }]}>
				<Select
					placeholder='Foydalanuvchini tanlang'
					size='middle'
					options={users?.data?.map(user => ({
						label: user.name,
						value: user.id
					}))}
				/>
			</Form.Item> */}
		</>
	)
}
export default AddAttendanceForm
