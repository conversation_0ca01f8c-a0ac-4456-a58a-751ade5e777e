import { Injectable, NotFoundException } from '@nestjs/common';
import { PaginationUtil, PrismaService, QueryUtil } from 'src/common';
import { FindAllDto } from './dto/find-all.dto';
import { GPSLocationType, IVacation, Prisma } from '@prisma/client';
import { TimezoneHelper } from '../../../helpers/timezone.helper';
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import * as timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.locale('uz');
dayjs.tz.setDefault('Asia/Tashkent');

@Injectable()
export class SectionStatsService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly paginationUtil: PaginationUtil,
    private readonly queryUtil: QueryUtil,
  ) {}

  // async getSectionBaseStats(organizationId: string) {
  //   const { childrenIds } = await this.getChildrenIds(organizationId);

  //   const todayRange = TimezoneHelper.getUTCRangeForLocalDay();
  //   const today = new Date();

  //   const [employeeCount, organization] = await this.prismaService.$transaction([
  //     this.prismaService.user.count({
  //       where: {
  //         status: 'ACTIVE',
  //         Organization: {
  //           some: {
  //             id: { in: childrenIds },
  //           },
  //         },
  //       },
  //     }),
  //     this.prismaService.organization.findUnique({
  //       where: { id: organizationId },
  //       select: {
  //         Children: {
  //           where: {
  //             grade: { level: 30 },
  //             status: 'ACTIVE',
  //             sectionId: { not: null },
  //           },
  //           orderBy: { updatedAt: 'desc' },
  //           select: {
  //             type: {
  //               select: {
  //                 id: true,
  //                 name: true,
  //                 OrganizationTypePosition: {
  //                   select: {
  //                     id: true,
  //                     name: true,
  //                     _count: { select: { User: true } },
  //                   },
  //                 },
  //               },
  //             },
  //             _count: {
  //               select: { Vacancy: true },
  //             },
  //             Worker: {
  //               where: { status: 'ACTIVE' },
  //               select: {
  //                 id: true,
  //                 position: {
  //                   select: {
  //                     id: true,
  //                     name: true,
  //                     updatedAt: true,
  //                   },
  //                 },
  //                 Vacation: {
  //                   where: {
  //                     status: 'ACTIVE',
  //                     state: true,
  //                     begin: {
  //                       lte: today,
  //                     },
  //                     end: {
  //                       gte: today,
  //                     },
  //                   },
  //                   select: {
  //                     id: true,
  //                     type: true,
  //                     begin: true,
  //                     end: true,
  //                   },
  //                 },
  //               },
  //             },
  //           },
  //         },
  //       },
  //     }),
  //   ]);

  //   if (!organization) {
  //     throw new NotFoundException('Organization not found');
  //   }

  //   const allWorkers = organization.Children.flatMap((child) => child.Worker);

  //   const uniqueWorkers = Array.from(new Map(allWorkers.map((worker) => [worker.id, worker])).values());

  //   const isActiveToday = (begin: Date, end: Date) => {
  //     const beginDate = new Date(begin);
  //     const endDate = new Date(end);

  //     return beginDate <= today && endDate >= today;
  //   };

  //   const countByType = (type: string) =>
  //     uniqueWorkers
  //       .flatMap((w) => w.Vacation)
  //       .filter((v) => v.type === type && v.begin && v.end && isActiveToday(v.begin, v.end)).length;

  //   const patientCount = countByType('PATIONS');
  //   const vacationCount = countByType('VACATION');
  //   const shortVacationCount = countByType('SHORT_VACATION');

  //   const positionsCount = uniqueWorkers
  //     .filter((w) => w.position)
  //     .reduce(
  //       (acc, worker) => {
  //         const existing = acc.find((p) => p.id === worker.position.id);
  //         if (existing) {
  //           existing.userCount += 1;
  //         } else {
  //           acc.push({
  //             id: worker.position.id,
  //             name: worker.position.name,
  //             updatedAt: worker.position.updatedAt,
  //             userCount: 1,
  //           });
  //         }
  //         return acc;
  //       },
  //       [] as { id: string; name: string; userCount: number; updatedAt: Date }[],
  //     )
  //     .sort((a, b) => a.updatedAt.getTime() - b.updatedAt.getTime());

  //   const vacancyCount = organization.Children.reduce((acc, child) => acc + child._count.Vacancy, 0);

  //   return {
  //     data: {
  //       employeeCount,
  //       patientCount,
  //       vacationCount,
  //       shortVacationCount,
  //       vacancyCount,
  //       sectionCount: organization.Children.length,
  //       positionsCount,
  //     },
  //   };
  // }

  // async getSectionBaseStats(organizationId: string) {
  //   const { childrenIds } = await this.getChildrenIds(organizationId);

  //   const todayRange = TimezoneHelper.getUTCRangeForLocalDay();
  //   const today = new Date();

  //   // Bugungi sanani faqat sana qismi bilan taqqoslash uchun vaqtni 00:00:00 ga o'rnatamiz
  //   const todayDateOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());

  //   const [employeeCount, organization] = await this.prismaService.$transaction([
  //     this.prismaService.user.count({
  //       where: {
  //         status: 'ACTIVE',
  //         Organization: {
  //           some: {
  //             id: { in: childrenIds },
  //           },
  //         },
  //       },
  //     }),
  //     this.prismaService.organization.findUnique({
  //       where: { id: organizationId },
  //       select: {
  //         Children: {
  //           where: {
  //             grade: { level: 30 },
  //             status: 'ACTIVE',
  //             sectionId: { not: null },
  //           },
  //           orderBy: { updatedAt: 'desc' },
  //           select: {
  //             type: {
  //               select: {
  //                 id: true,
  //                 name: true,
  //                 OrganizationTypePosition: {
  //                   select: {
  //                     id: true,
  //                     name: true,
  //                     _count: { select: { User: true } },
  //                   },
  //                 },
  //               },
  //             },
  //             _count: {
  //               select: { Vacancy: true },
  //             },
  //             Worker: {
  //               where: { status: 'ACTIVE' },
  //               select: {
  //                 id: true,
  //                 position: {
  //                   select: {
  //                     id: true,
  //                     name: true,
  //                     updatedAt: true,
  //                   },
  //                 },
  //                 Vacation: {
  //                   where: {
  //                     status: 'ACTIVE',
  //                     state: true,
  //                     begin: {
  //                       lte: today,
  //                     },
  //                     end: {
  //                       gte: today,
  //                     },
  //                   },
  //                   select: {
  //                     id: true,
  //                     type: true,
  //                     begin: true,
  //                     end: true,
  //                   },
  //                 },
  //               },
  //             },
  //           },
  //         },
  //       },
  //     }),
  //   ]);

  //   if (!organization) {
  //     throw new NotFoundException('Organization not found');
  //   }

  //   const allWorkers = organization.Children.flatMap((child) => child.Worker);

  //   const uniqueWorkers = Array.from(new Map(allWorkers.map((worker) => [worker.id, worker])).values());

  //   const isActiveToday = (begin: Date, end: Date) => {
  //     // Sanalarni faqat sana qismi bilan taqqoslash uchun vaqtni olib tashlaymiz
  //     const beginDate = new Date(begin.getFullYear(), begin.getMonth(), begin.getDate());
  //     const endDate = new Date(end.getFullYear(), end.getMonth(), end.getDate());

  //     return beginDate <= todayDateOnly && endDate >= todayDateOnly;
  //   };

  //   const countByType = (type: string) =>
  //     uniqueWorkers
  //       .flatMap((w) => w.Vacation)
  //       .filter((v) => v.type === type && v.begin && v.end && isActiveToday(v.begin, v.end)).length;

  //   const patientCount = countByType('PATIONS');
  //   const vacationCount = countByType('VACATION');
  //   const shortVacationCount = countByType('SHORT_VACATION');

  //   const positionsCount = uniqueWorkers
  //     .filter((w) => w.position)
  //     .reduce(
  //       (acc, worker) => {
  //         const existing = acc.find((p) => p.id === worker.position.id);
  //         if (existing) {
  //           existing.userCount += 1;
  //         } else {
  //           acc.push({
  //             id: worker.position.id,
  //             name: worker.position.name,
  //             updatedAt: worker.position.updatedAt,
  //             userCount: 1,
  //           });
  //         }
  //         return acc;
  //       },
  //       [] as { id: string; name: string; userCount: number; updatedAt: Date }[],
  //     )
  //     .sort((a, b) => a.updatedAt.getTime() - b.updatedAt.getTime());

  //   const vacancyCount = organization.Children.reduce((acc, child) => acc + child._count.Vacancy, 0);

  //   return {
  //     data: {
  //       employeeCount,
  //       patientCount,
  //       vacationCount,
  //       shortVacationCount,
  //       vacancyCount,
  //       sectionCount: organization.Children.length,
  //       positionsCount,
  //     },
  //   };
  // }

  async getSectionBaseStats(organizationId: string) {
    const { childrenIds } = await this.getChildrenIds(organizationId);

    const todayRange = TimezoneHelper.getUTCRangeForLocalDay();
    const today = new Date();

    const todayDateOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    const [employeeCount, organization] = await this.prismaService.$transaction([
      this.prismaService.user.count({
        where: {
          status: 'ACTIVE',
          Organization: {
            some: {
              id: { in: childrenIds },
            },
          },
        },
      }),
      this.prismaService.organization.findUnique({
        where: { id: organizationId },
        select: {
          Children: {
            where: {
              grade: { level: 30 },
              status: 'ACTIVE',
              sectionId: { not: null },
            },
            orderBy: { updatedAt: 'desc' },
            select: {
              type: {
                select: {
                  id: true,
                  name: true,
                  OrganizationTypePosition: {
                    select: {
                      id: true,
                      name: true,
                      _count: { select: { User: true } },
                    },
                  },
                },
              },
              _count: {
                select: { Vacancy: true },
              },
              Worker: {
                where: { status: 'ACTIVE' },
                select: {
                  id: true,
                  position: {
                    select: {
                      id: true,
                      name: true,
                      updatedAt: true,
                    },
                  },
                  Vacation: {
                    where: {
                      status: 'ACTIVE',
                      end: {
                        gte: todayRange.start,
                      },
                    },
                    select: {
                      id: true,
                      type: true,
                      begin: true,
                      end: true,
                    },
                  },
                },
              },
            },
          },
        },
      }),
    ]);

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const allWorkers = organization.Children.flatMap((child) => child.Worker);

    const uniqueWorkers = Array.from(new Map(allWorkers.map((worker) => [worker.id, worker])).values());

    const countByType = (type: string) =>
      uniqueWorkers.flatMap((w) => w.Vacation).filter((v) => v.type === type && v.begin && v.end).length;

    const patientCount = countByType('PATIONS');
    const vacationCount = countByType('VACATION');
    const shortVacationCount = countByType('SHORT_VACATION');

    const positionsCount = uniqueWorkers
      .filter((w) => w.position)
      .reduce(
        (acc, worker) => {
          const existing = acc.find((p) => p.id === worker.position.id);
          if (existing) {
            existing.userCount += 1;
          } else {
            acc.push({
              id: worker.position.id,
              name: worker.position.name,
              updatedAt: worker.position.updatedAt,
              userCount: 1,
            });
          }
          return acc;
        },
        [] as { id: string; name: string; userCount: number; updatedAt: Date }[],
      )
      .sort((a, b) => a.updatedAt.getTime() - b.updatedAt.getTime());

    const vacancyCount = organization.Children.reduce((acc, child) => acc + child._count.Vacancy, 0);

    return {
      data: {
        employeeCount,
        patientCount,
        vacationCount,
        shortVacationCount,
        vacancyCount,
        sectionCount: organization.Children.length,
        positionsCount,
      },
    };
  }

  async getSection(organizationId: string, params: FindAllDto) {
    const { childrenIds, sectionIds } = await this.getChildrenIds(organizationId);
    const searchQuery = this.queryUtil.createSearchQuery(params.search, ['name', 'description', 'phone']);

    return await this.paginationUtil.paginate(
      this.prismaService.organization,
      params,
      {
        status: 'ACTIVE',
        grade: { level: 30 },
        id: { in: childrenIds }, // Faqat bolalar tashkilotlari
        sectionId: { in: sectionIds }, // Faqat o‘sha tashkilotlarga tegishli bo‘lgan sectionIdlar
        ...searchQuery,
      },
      null,
      null,
      {
        id: true,
        name: true,
        address: true,
        description: true,
        createdAt: true,
        phone: true,
        region: true,
        district: true,
        section: true,
        type: true,
      },
    );
  }

  async getSectionAttendanceStats(organizationId: string) {
    const { start, end } = TimezoneHelper.getUTCRangeForLocalDay();

    const organization = await this.prismaService.organization.findUnique({
      where: { id: organizationId },
    });

    if (!organization) throw new NotFoundException('Organization not found');

    const { childrenIds } = await this.getChildrenIds(organizationId);

    const users = await this.prismaService.user.findMany({
      where: {
        status: 'ACTIVE',
        OR: [
          { mainOrganizationId: { in: childrenIds } },
          {
            Organization: {
              some: { id: { in: childrenIds } },
            },
          },
        ],
      },
      select: {
        id: true,
        fullName: true,
        position: {
          select: {
            id: true,
            name: true,
            updatedAt: true,
          },
        },
        AttendanceReport: {
          where: {
            date: {
              gte: start,
              lt: end,
            },
            status: 'ACTIVE',
          },
          take: 1,
          orderBy: { createdAt: 'desc' },
          select: { isLate: true },
        },
      },
    });

    const lateStats = new Map<string, { id: string; name: string; count: number; updatedAt: Date }>();
    const noAttendanceStats = new Map<string, { id: string; name: string; count: number; updatedAt: Date }>();

    for (const user of users) {
      const posId = user.position?.id || 'unknown';
      const posName = user.position?.name?.trim() || 'Nomaʼlum lavozim';
      const updatedAt = user.position?.updatedAt || new Date(0); // unknown lavozimlar eng boshida turadi
      const reports = user.AttendanceReport;

      const noAttendance = reports.length === 0;
      const wasLate = reports.length > 0 && reports[0].isLate === true;

      // No Attendance
      if (!noAttendanceStats.has(posId)) {
        noAttendanceStats.set(posId, { id: posId, name: posName, count: 0, updatedAt });
      }
      if (noAttendance) {
        noAttendanceStats.get(posId)!.count++;
      }

      // Late
      if (!lateStats.has(posId)) {
        lateStats.set(posId, { id: posId, name: posName, count: 0, updatedAt });
      }
      if (wasLate) {
        lateStats.get(posId)!.count++;
      }
    }

    const lateAttendanceList = Array.from(lateStats.values()).sort(
      (a, b) => a.updatedAt.getTime() - b.updatedAt.getTime(),
    );

    const noAttendanceList = Array.from(noAttendanceStats.values()).sort(
      (a, b) => a.updatedAt.getTime() - b.updatedAt.getTime(),
    );

    return {
      lateAttendance: {
        total: lateAttendanceList.reduce((acc, curr) => acc + curr.count, 0),
        list: lateAttendanceList.map(({ updatedAt, ...rest }) => rest), // updatedAt ni tashlab yuboramiz
      },
      noAttendance: {
        total: noAttendanceList.reduce((acc, curr) => acc + curr.count, 0),
        list: noAttendanceList.map(({ updatedAt, ...rest }) => rest), // updatedAt ni tashlab yuboramiz
      },
    };
  }

  async getNoAttendance(organizationId: string, params: FindAllDto, positionId?: string) {
    const { childrenIds } = await this.getChildrenIds(organizationId);
    const { start, end } = TimezoneHelper.getUTCRangeForLocalDay();

    const limit = params.limit ?? 10;
    const page = params.page ?? 1;
    const offset = (page - 1) * limit;

    // Build user filter
    const userFilter: any = {
      status: 'ACTIVE',
      AND: [],
    };

    if (positionId) {
      userFilter.AND.push({ positionId });
    }

    // Add organization filters
    userFilter.AND.push({
      OR: [
        { mainOrganizationId: { in: childrenIds } },
        {
          Organization: {
            some: {
              id: { in: childrenIds },
            },
          },
        },
      ],
    });

    // No Attendance condition: NOT EXISTS
    userFilter.AND.push({
      AttendanceReport: {
        none: {
          date: {
            gte: start,
            lte: end,
          },
        },
      },
    });

    // Search
    if (params.search) {
      const search = params.search;
      userFilter.AND.push({
        OR: [
          { username: { contains: search, mode: 'insensitive' } },
          { fullName: { contains: search, mode: 'insensitive' } },
          { phone: { contains: search, mode: 'insensitive' } },
          {
            id: {
              equals: isNaN(+search) ? undefined : +search,
            },
          },
        ],
      });
    }

    // Get users with relations
    const [users, total] = await this.prismaService.$transaction([
      this.prismaService.user.findMany({
        where: userFilter,
        skip: offset,
        take: limit,
        orderBy: { id: 'asc' },
        select: {
          id: true,
          fullName: true,
          username: true,
          phone: true,
          positionId: true,
          position: {
            select: {
              id: true,
              name: true,
              description: true,
            },
          },
          AttendanceReport: {
            where: {
              date: {
                gte: start,
                lte: end,
              },
            },
          },
          Organization: {
            where: {
              id: { in: childrenIds },
            },
            select: {
              id: true,
              name: true,
              phone: true,
              address: true,
              description: true,
              grade: true,
            },
          },
        },
      }),

      this.prismaService.user.count({
        where: userFilter,
      }),
    ]);

    return {
      data: users.map((user) => ({
        ...user,
        AttendanceReport: [], // intentionally empty
      })),
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasMore: page * limit < total,
      },
    };
  }

  async getLateAttendance(organizationId: string, params: FindAllDto, positionId?: string) {
    const { childrenIds } = await this.getChildrenIds(organizationId);
    const startDate = dayjs.utc().startOf('day').toDate();
    const endDate = dayjs.utc().endOf('day').toDate();

    let positionFilter: any;

    if (positionId) {
      const isValid = await this.prismaService.organizationTypePosition.findFirst({
        where: {
          id: positionId,
          typeId: {
            in: await this.getOrganizationTypeIds(childrenIds),
          },
        },
        select: { id: true },
      });

      if (!isValid) {
        return {
          data: [],
          meta: {
            total: 0,
            page: params.page ?? 1,
            limit: params.limit ?? 10,
            totalPages: 0,
            hasMore: false,
          },
        };
      }

      positionFilter = positionId;
    } else {
      const positions = await this.getOrganizationPositions(childrenIds);
      positionFilter = positions.map((p) => p.id);
    }

    const search = params.search?.trim();
    const searchFilter = search
      ? {
          OR: [
            { username: { contains: search, mode: Prisma.QueryMode.insensitive } },
            { fullName: { contains: search, mode: Prisma.QueryMode.insensitive } },
            { phone: { contains: search, mode: Prisma.QueryMode.insensitive } },
          ],
        }
      : {};

    const limit = params.limit ?? 10;
    const page = params.page ?? 1;
    const offset = (page - 1) * limit;

    const [users, total] = await this.prismaService.$transaction([
      this.prismaService.user.findMany({
        where: {
          status: 'ACTIVE',
          positionId: { in: Array.isArray(positionFilter) ? positionFilter : [positionFilter] },
          Organization: {
            some: {
              id: { in: childrenIds },
            },
          },
          AttendanceReport: {
            some: {
              date: {
                gte: startDate,
                lte: endDate,
              },
              isLate: true,
            },
          },
          ...searchFilter,
        },
        orderBy: { id: 'asc' },
        skip: offset,
        take: limit,
        select: {
          id: true,
          fullName: true,
          username: true,
          phone: true,
          role: true,
          position: {
            select: {
              id: true,
              name: true,
              description: true,
              type: true,
            },
          },
          AttendanceReport: {
            where: {
              date: {
                gte: startDate,
                lte: endDate,
              },
              isLate: true,
            },
            select: {
              id: true,
              date: true,
              enter: true,
              exit: true,
              minutes: true,
              description: true,
            },
          },
          Organization: {
            select: {
              id: true,
              name: true,
              phone: true,
              address: true,
              description: true,
              grade: true,
            },
          },
        },
      }),

      this.prismaService.user.count({
        where: {
          status: 'ACTIVE',
          positionId: { in: Array.isArray(positionFilter) ? positionFilter : [positionFilter] },
          Organization: {
            some: {
              id: { in: childrenIds },
            },
          },
          AttendanceReport: {
            some: {
              date: {
                gte: startDate,
                lte: endDate,
              },
              isLate: true,
            },
          },
          ...searchFilter,
        },
      }),
    ]);

    return {
      data: users,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasMore: page * limit < total,
      },
    };
  }

  async getSectionAreaStats(organizationId: string) {
    const { childrenIds } = await this.getChildrenIds(organizationId);

    const organizationTypeIdsRaw = await this.prismaService.organization.findMany({
      where: {
        id: {
          in: childrenIds,
        },
      },
      select: {
        typeId: true,
      },
    });

    const typeIds = [...new Set(organizationTypeIdsRaw.map((org) => org.typeId))];

    const organizationTypePositions = await this.prismaService.organizationTypePosition.findMany({
      where: {
        typeId: {
          in: typeIds,
        },
      },
      select: {
        id: true,
        name: true,
        User: {
          select: {
            id: true,
            fullName: true,
            GPSLocationReport: {
              orderBy: {
                createdAt: 'desc',
              },
              take: 1,
              select: {
                type: true,
              },
            },
          },
        },
      },
    });

    const inArea: any[] = [];
    const outArea: any[] = [];
    const disconnected: any[] = [];

    for (const pos of organizationTypePositions) {
      const users = pos.User || [];

      const inCount = users.filter((u) => u.GPSLocationReport[0]?.type === 'IN').length;
      const outCount = users.filter((u) => u.GPSLocationReport[0]?.type === 'OUT').length;
      const disconnectedCount = users.filter((u) => u.GPSLocationReport[0]?.type === 'DISABLED').length;

      inArea.push({ id: pos.id, name: pos.name.trim(), count: inCount });
      outArea.push({ id: pos.id, name: pos.name.trim(), count: outCount });
      disconnected.push({ id: pos.id, name: pos.name.trim(), count: disconnectedCount });
    }

    return {
      inArea: {
        total: inArea.reduce((sum, item) => sum + item.count, 0),
        list: inArea,
      },
      outArea: {
        total: outArea.reduce((sum, item) => sum + item.count, 0),
        list: outArea,
      },
      disconnected: {
        total: disconnected.reduce((sum, item) => sum + item.count, 0),
        list: disconnected,
      },
    };
  }

  async getEmployee(organizationId: string, params: FindAllDto) {
    const { childrenIds } = await this.getChildrenIds(organizationId);
    const searchQuery = this.queryUtil.createSearchQuery(params.search, ['username', 'fullName', 'phone', '']);

    return await this.paginationUtil.paginate(
      this.prismaService.user,
      params,
      {
        status: 'ACTIVE',
        Organization: { some: { id: { in: childrenIds } } },
        ...searchQuery,
      },
      null,
      null,
      {
        id: true,
        fullName: true,
        avatar: true,
        phone: true,
        position: true,
        Organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    );
  }

  // async getVacation(organizationId: string, params: FindAllDto, type: IVacation) {
  //   const { childrenIds } = await this.getChildrenIds(organizationId);

  //   return await this.paginationUtil.paginate(
  //     this.prismaService.vacation,
  //     params,
  //     {
  //       status: 'ACTIVE',
  //       type,
  //       OR: [
  //         { user: { mainOrganizationId: { in: childrenIds } } },
  //         {
  //           user: {
  //             Organization: {
  //               some: { id: { in: childrenIds } },
  //             },
  //           },
  //         },
  //       ],
  //     },
  //     null,
  //     null,
  //     {
  //       id: true,
  //       begin: true,
  //       end: true,
  //       state: true,
  //       type: true,
  //       description: true,
  //       createdAt: true,
  //       user: {
  //         select: {
  //           id: true,
  //           fullName: true,
  //           avatar: true,
  //           phone: true,
  //           position: true,
  //           Organization: { select: { id: true, name: true } },
  //         },
  //       },
  //     },
  //   );
  // }

  async getVacation(organizationId: string, params: FindAllDto, type: IVacation) {
    const { childrenIds } = await this.getChildrenIds(organizationId);
    const { start } = TimezoneHelper.getUTCRangeForLocalDay();

    return await this.paginationUtil.paginate(
      this.prismaService.vacation,
      params,
      {
        status: 'ACTIVE',
        type,
        end: {
          gte: start,
        },
        OR: [
          {
            user: {
              mainOrganizationId: { in: childrenIds },
            },
          },
          {
            user: {
              Organization: {
                some: {
                  id: { in: childrenIds },
                },
              },
            },
          },
        ],
      },
      null,
      null,
      {
        id: true,
        begin: true,
        end: true,
        state: true,
        type: true,
        description: true,
        createdAt: true,
        user: {
          select: {
            id: true,
            fullName: true,
            avatar: true,
            phone: true,
            position: true,
            Organization: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    );
  }

  async getVacancy(organizationId: string, params: FindAllDto) {
    const { childrenIds } = await this.getChildrenIds(organizationId);

    return await this.paginationUtil.paginate(
      this.prismaService.vacancy,
      params,
      {
        status: 'ACTIVE',
        Organization: { id: { in: childrenIds } },
      },
      null,
      null,
      {
        id: true,
        createdAt: true,
        Organization: {
          select: {
            id: true,
            name: true,
            address: true,
            description: true,
            createdAt: true,
            phone: true,
            region: true,
            district: true,
            section: true,
            type: true,
          },
        },
        OrganizationTypePosition: {
          select: {
            id: true,
            name: true,
            description: true,
            createdAt: true,
          },
        },
      },
    );
  }

  async getPositions(organizationId: string, params: FindAllDto, positionId?: string) {
    const GRADE = 30;
    const organization = await this.prismaService.organization.findUnique({
      where: { id: organizationId, status: 'ACTIVE' },
      select: {
        Children: {
          where: {
            status: 'ACTIVE',
            grade: {
              level: GRADE,
            },
          },
          select: {
            Worker: {
              where: {
                status: 'ACTIVE',
                ...(positionId ? { positionId } : {}),
              },
              select: {
                id: true,
                fullName: true,
                phone: true,
                Organization: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
                position: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    const allWorkers = organization.Children.flatMap((child) => child.Worker);

    const uniqueWorkersMap = new Map<string, any>();

    for (const worker of allWorkers) {
      const existing = uniqueWorkersMap.get(worker.id);
      if (existing) {
        const existingOrgIds = new Set(existing.Organization.map((org) => org.id));
        for (const org of worker.Organization) {
          if (!existingOrgIds.has(org.id)) {
            existing.Organization.push(org);
          }
        }
      } else {
        uniqueWorkersMap.set(worker.id, {
          ...worker,
          Organization: [...worker.Organization], // nusxa olish muhim!
        });
      }
    }

    return {
      data: Array.from(uniqueWorkersMap.values()),
    };
  }

  async getArea(organizationId: string, params: FindAllDto, type: GPSLocationType, positionId?: string) {
    const { childrenIds } = await this.getChildrenIds(organizationId);
    const organizationTypePositions = await this.getOrganizationPositions(childrenIds, positionId);
    const searchQuery = this.queryUtil.createSearchQuery(params.search, ['username', 'fullName', 'phone']);

    const allUsers = await this.prismaService.user.findMany({
      where: {
        status: 'ACTIVE',
        positionId: { in: organizationTypePositions.map(({ id }) => id) },
        Organization: {
          some: {
            id: { in: childrenIds },
          },
        },
        ...searchQuery,
      },
      select: {
        id: true,
        fullName: true,
        username: true,
        phone: true,
        position: { select: { id: true, name: true, description: true, type: true } },
        role: true,
        Organization: {
          select: {
            id: true,
            name: true,
          },
        },
        GPSLocationReport: {
          orderBy: { createdAt: 'desc' },
          take: 1,
          select: {
            id: true,
            type: true,
            createdAt: true,
          },
        },
      },
    });

    const filteredUsers = allUsers.filter(
      (user) => user.GPSLocationReport.length > 0 && user.GPSLocationReport[0].type === type,
    );

    const { page = 1, limit = 10 } = params;

    return {
      data: filteredUsers.slice((page - 1) * limit, page * limit),
      meta: {
        total: filteredUsers.length,
        page,
        limit,
        totalPages: Math.ceil(filteredUsers.length / limit),
        hasMore: page * limit < filteredUsers.length,
      },
    };
  }

  private async getOrganizationTypeIds(orgIds: string[]): Promise<string[]> {
    const organizations = await this.prismaService.organization.findMany({
      where: {
        id: { in: orgIds },
      },
      select: { typeId: true },
    });

    return [...new Set(organizations.map((org) => org.typeId))];
  }

  private async getChildrenIds(organizationId: string) {
    const organization = await this.prismaService.organization.findUnique({
      where: {
        id: organizationId,
        status: 'ACTIVE',
        grade: { level: 20 },
      },
      select: {
        Children: {
          where: { grade: { level: 30 } },
          select: { id: true, sectionId: true },
        },
      },
    });
    if (!organization) throw new NotFoundException('organization not found');

    const childrenIds = organization.Children.map(({ id }) => id);
    const sectionIds = organization.Children.map(({ sectionId }) => sectionId).filter((id): id is string =>
      Boolean(id),
    );

    return { childrenIds, sectionIds };
  }

  private async getOrganizationPositions(childrenIds: string[], positionId?: string) {
    const startGetOrganizationPositions = performance.now();
    const result = await this.prismaService.organizationTypePosition.findMany({
      where: {
        status: 'ACTIVE',
        ...(positionId ? { id: positionId } : {}),
      },
      select: {
        name: true,
        id: true,
        _count: {
          select: { User: true },
        },
        User: {
          where: {
            status: 'ACTIVE',
            Organization: {
              some: { id: { in: childrenIds } },
            },
          },
          select: {
            id: true,
            AttendanceReport: true,
            Attendance: true,
            UserWorkingSchedule: {
              include: { days: true },
            },
            GPSLocationReport: {
              orderBy: { createdAt: 'desc' },
              take: 1,
              select: {
                id: true,
                createdAt: true,
                isInArea: true,
              },
            },
          },
        },
      },
    });

    return result;
  }
}
