import { useGetTaskStats } from '@/config/queries/task/get-task-stats'
import { DownloadOutlined } from '@ant-design/icons'
import { Button, Col, DatePicker, Flex, Form, Row } from 'antd'
import { Dayjs } from 'dayjs'

export default function SearchForExcel() {
	const download = useGetTaskStats()

	const onSubmit = (data: { range: Dayjs[] }) => {
		const startDate = data.range ? data?.range[0]?.startOf('day').toISOString() : undefined
		const endDate = data.range ? data?.range[1]?.endOf('day').toISOString() : undefined
		download.mutateAsync({ startDate, endDate })
	}

	return (
		<>
			<Flex>
				<Form
					onFinish={onSubmit}
					layout='vertical'
					size='small'>
					<Row
						gutter={16}
						align='middle'>
						<Col>
							<Form.Item
								name='range'
								label='Vaqt oralig’i'
								style={{ width: '100%' }}>
								<DatePicker.RangePicker />
							</Form.Item>
						</Col>
						<Col>
							<Form.Item label='Yuklash'>
								<Button
									htmlType='submit'
									variant='solid'
									color='blue'
									icon={<DownloadOutlined />}>
									Excel
								</Button>
							</Form.Item>
						</Col>
					</Row>
				</Form>
			</Flex>
		</>
	)
}
