import React, { createContext, useState, useContext } from 'react'

type HeaderTitleContextType = {
	headerTitle: string
	setHeaderTitle: (title: string) => void
}

const HeaderTitleContext = createContext<HeaderTitleContextType | undefined>(undefined)

export const HeaderTitleProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
	const [headerTitle, setHeaderTitle] = useState('')

	return (
		<HeaderTitleContext.Provider value={{ headerTitle, setHeaderTitle }}>
			{children}
		</HeaderTitleContext.Provider>
	)
}

// eslint-disable-next-line react-refresh/only-export-components
export const useHeaderTitle = () => {
	const context = useContext(HeaderTitleContext)
	if (!context) {
		throw new Error('useHeaderTitle must be used within a HeaderTitleProvider')
	}
	return context
}

export { HeaderTitleContext }
