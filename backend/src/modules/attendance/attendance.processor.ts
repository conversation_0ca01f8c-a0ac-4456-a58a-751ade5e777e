import { InjectQueue, Processor, WorkerHost } from '@nestjs/bullmq';
import { ATTENDANCE, ATTENDANCE_REPORT, AttendanceData } from './interface/attendance';
import { Job, Queue } from 'bullmq';
import { BadRequestException, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { AttendanceType } from '@prisma/client';
import * as dayjs from 'dayjs';
import { Dayjs } from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import * as timezone from 'dayjs/plugin/timezone';
import * as duration from 'dayjs/plugin/duration';
import 'dayjs/locale/uz-latn';
import { TimezoneHelper } from 'src/helpers/timezone.helper';

// Dayjs pluginlarini o'rnatish
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(duration);
dayjs.locale('uz-latn');

// Standart vaqt zonasini belgilash
const DEFAULT_TIMEZONE = 'Asia/Tashkent';
dayjs.tz.setDefault(DEFAULT_TIMEZONE);

const parseDateTime = (dateTimeValue: string | Date): Dayjs | null => {
  return TimezoneHelper.parseDateTime(dateTimeValue);
};

@Injectable()
@Processor(ATTENDANCE, {
  concurrency: 4,
  limiter: {
    max: 50,
    duration: 1000,
  },
})
export class AttendanceProcessor extends WorkerHost {
  private readonly logger = new Logger(AttendanceProcessor.name);

  constructor(
    private prismaService: PrismaService,
    private readonly eventEmitter: EventEmitter2,
    @InjectQueue(ATTENDANCE_REPORT) private attendanceReportQueue: Queue,
  ) {
    super();
  }

  async process(job: Job<AttendanceData>, token?: string) {
    try {
      this.logger.log(`Davomat ma'lumotini qayta ishlash boshlandi: jobId=${job.id}`);

      if (!this.validateJobData(job.data)) {
        throw new BadRequestException("Noto'g'ri job ma'lumotlari");
      }

      const { user, device } = await this.validateUserAndDevice(job.data);

      const eventDateUTC = parseDateTime(job.data.dateTime);
      if (!eventDateUTC) {
        throw new BadRequestException("Noto'g'ri sana va vaqt formati");
      }

      this.logger.debug(
        `Davomat eventi: userId=${user.id}, deviceMAC=${device.MAC}, vaqt=${eventDateUTC.toISOString()} (UTC)`,
      );

      const dailyAttendanceState = await this.analyzeDailyAttendanceState(user.id, eventDateUTC);

      const result = await this.processAttendanceRecord(
        user.id,
        device.organizationId,
        eventDateUTC,
        dailyAttendanceState,
      );

      return result;
    } catch (error) {
      this.logger.error(`Davomatni qayta ishlashda xatolik: ${error.message}`, error.stack);
      throw error;
    }
  }

  private validateJobData(data: AttendanceData): boolean {
    if (!data) {
      this.logger.error("Job ma'lumotlari bo'sh");
      return false;
    }

    if (!data.AccessControllerEvent?.employeeNoString) {
      this.logger.error('Xodim ID si mavjud emas');
      return false;
    }

    if (!data.macAddress) {
      this.logger.error('Qurilma MAC adresi mavjud emas');
      return false;
    }

    if (!data.dateTime) {
      this.logger.error('Sana va vaqt mavjud emas');
      return false;
    }

    return true;
  }

  private async validateUserAndDevice(data: AttendanceData) {
    try {
      const user = await this.prismaService.user.findUnique({
        where: { id: data.AccessControllerEvent.employeeNoString },
        select: {
          id: true,
          fullName: true,
        },
      });

      if (!user) {
        this.logger.error(`Foydalanuvchi topilmadi: id=${data.AccessControllerEvent.employeeNoString}`);
        throw new NotFoundException('Foydalanuvchi topilmadi');
      }

      const device = await this.prismaService.faceIDDevice.findUnique({
        where: { MAC: data.macAddress },
        select: {
          id: true,
          MAC: true,
          organizationId: true,
        },
      });

      if (!device) {
        this.logger.error(`Face ID qurilma topilmadi: MAC=${data.macAddress}`);
        throw new NotFoundException('Face ID qurilma topilmadi');
      }

      return { user, device };
    } catch (error) {
      this.logger.error(`Foydalanuvchi yoki qurilmani tekshirishda xatolik: ${error.message}`);
      throw error;
    }
  }

  private async analyzeDailyAttendanceState(userId: string, eventDateUTC: Dayjs) {
    try {
      const { start: dayRangeStart, end: dayRangeEnd } = TimezoneHelper.getUTCRangeForLocalDay(eventDateUTC);

      this.logger.debug(
        `Kunlik tahlil oralig'i: ${TimezoneHelper.debugFormat(dayRangeStart)} - ${TimezoneHelper.debugFormat(dayRangeEnd)}`,
      );

      const dailyAttendances = await this.prismaService.attendance.findMany({
        where: {
          userId,
          time: {
            gte: dayRangeStart,
            lt: dayRangeEnd,
          },
        },
        orderBy: { time: 'asc' },
        select: {
          id: true,
          type: true,
          time: true,
        },
      });

      const enterRecords = dailyAttendances.filter((a) => a.type === AttendanceType.ENTER);
      const exitRecords = dailyAttendances.filter((a) => a.type === AttendanceType.EXIT);

      const lastEnterRecord = enterRecords.length > 0 ? enterRecords[enterRecords.length - 1] : null;
      const lastExitRecord = exitRecords.length > 0 ? exitRecords[exitRecords.length - 1] : null;

      this.logger.debug(
        `Kunlik holat: ENTER=${enterRecords.length}, EXIT=${exitRecords.length}, oxirgi ENTER=${lastEnterRecord?.time}, oxirgi EXIT=${lastExitRecord?.time}`,
      );

      return {
        dailyAttendances,
        enterRecords,
        exitRecords,
        lastEnterRecord,
        lastExitRecord,
        dayRange: { start: dayRangeStart, end: dayRangeEnd },
      };
    } catch (error) {
      this.logger.error(`Kunlik davomat holatini tahlil qilishda xatolik: ${error.message}`);
      throw error;
    }
  }

  private async processAttendanceRecord(userId: string, organizationId: string, eventDateUTC: Dayjs, dailyState: any) {
    try {
      const eventTimeISO = eventDateUTC.toISOString();

      if (dailyState.dailyAttendances.length === 0) {
        this.logger.debug('Bu kunning birinchi davomati, ENTER va EXIT yozuvlari yaratilmoqda');

        const enterRecords = await this.createEnterRecord(userId, organizationId, eventTimeISO);

        const exitRecords = await this.createExitRecord(userId, organizationId, eventTimeISO);

        return [...enterRecords, ...exitRecords];
      }

      const firstAttendance = dailyState.dailyAttendances[0];
      if (firstAttendance.type !== AttendanceType.ENTER) {
        this.logger.debug("Kunning birinchi davomati ENTER emas, uni ENTER ga o'zgartiramiz");
        const updatedAttendance = await this.prismaService.attendance.update({
          where: { id: firstAttendance.id },
          data: { type: AttendanceType.ENTER },
        });

        await this.emitEventSafely('attendance.ENTER', updatedAttendance);

        this.logger.debug('Yangi EXIT yozuvi yaratilmoqda');
        return await this.createExitRecord(userId, organizationId, eventTimeISO);
      }

      this.logger.debug('Yangi EXIT yozuvi yaratilmoqda');
      return await this.createExitRecord(userId, organizationId, eventTimeISO);
    } catch (error) {
      this.logger.error(`Davomat yozuvini qayta ishlashda xatolik: ${error.message}`);
      throw error;
    }
  }

  private async createEnterRecord(userId: string, organizationId: string, eventTimeISO: string) {
    const user = await this.prismaService.user.findUnique({
      where: { id: userId },
      select: {
        fullName: true,
        Organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    try {
      const attendanceRecords = await this.prismaService.$transaction(async (tx) => {
        return await Promise.all(
          user.Organization.map(async (org) => {
            return tx.attendance.create({
              data: {
                userId,
                organizationId: org.id,
                time: eventTimeISO,
                type: AttendanceType.ENTER,
              },
            });
          }),
        );
      });

      for (const attendance of attendanceRecords) {
        this.logger.debug(`ENTER event yuborilmoqda: userId=${userId}, attendanceId=${attendance.id}`);
        await this.emitEventSafely('attendance.ENTER', attendance);
      }

      return attendanceRecords;
    } catch (error) {
      this.logger.error(`ENTER yozuvini yaratishda xatolik: ${error.message}`);
      throw error;
    }
  }

  private async createExitRecord(userId: string, organizationId: string, eventTimeISO: string) {
    const user = await this.prismaService.user.findUnique({
      where: { id: userId },
      select: {
        fullName: true,
        Organization: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    this.logger.debug(`EXIT yozuvi yaratilmoqda: userId=${userId}, vaqt=${eventTimeISO}`);

    try {
      const attendanceRecords = await this.prismaService.$transaction(async (tx) => {
        return Promise.all(
          user.Organization.map(async (org) => {
            const attendance = await tx.attendance.create({
              data: {
                userId,
                organizationId: org.id,
                time: eventTimeISO,
                type: AttendanceType.EXIT,
              },
            });

            this.logger.debug(`EXIT yozuvi yaratildi: id=${attendance.id}, tashkilot=${org.name}`);
            return attendance;
          }),
        );
      });

      for (const attendance of attendanceRecords) {
        this.logger.debug(`EXIT event yuborilmoqda: userId=${userId}, attendanceId=${attendance.id}`);
        await this.emitEventSafely('attendance.EXIT', attendance);
      }

      this.logger.log(`EXIT yozuvlari muvaffaqiyatli yaratildi: userId=${userId}, soni=${attendanceRecords.length}`);
      return attendanceRecords;
    } catch (error) {
      this.logger.error(`EXIT yozuvini yaratishda xatolik: ${error.message}`);
      throw error;
    }
  }

  private async emitEventSafely(event: string, data: any) {
    try {
      await new Promise((resolve) => setTimeout(resolve, 10));
      await this.attendanceReportQueue.add(event, data);
    } catch (error) {
      this.logger.error(`Event emit xatoligi: ${event}`, error.message);
    }
  }
}
