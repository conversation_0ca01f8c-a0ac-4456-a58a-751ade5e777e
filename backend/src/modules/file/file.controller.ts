import {
  BadRequestException,
  Controller,
  OnModuleInit,
  Param,
  Post,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { FileService } from './file.service';
import { FilesInterceptor } from '@nestjs/platform-express';
import { MulterService } from 'src/common/multer/multer.service';
import { ProtectedRoute } from 'src/common';

@Controller('file')
export class FileController {
  private multerService = new MulterService();

  constructor(private readonly fileService: FileService) {}

  @Post('upload/:type')
  @ProtectedRoute({ isPublic: true })
  @UseInterceptors(FilesInterceptor('file', 5, { dest: 'static/temp' }))
  async universalUpload(@UploadedFiles() files: Express.Multer.File[], @Param('type') type: string) {
    if (!['image', 'file', 'archive'].includes(type)) {
      throw new BadRequestException('Invalid file type. Must be one of: image, file, archive');
    }

    let validatedFiles: Express.Multer.File[];
    try {
      const uploadPath = 'static/temp';

      const options = {
        image: this.multerService.createImageOptions(uploadPath),
        file: this.multerService.createDocumentOptions(uploadPath),
        archive: this.multerService.createArchiveOptions(uploadPath),
      };

      validatedFiles = this.validateFiles(files, options[type]);
    } catch (error) {
      throw new BadRequestException(`File validation failed: ${error.message}`);
    }

    return this.fileService.saveFileAsTemp(validatedFiles);
  }

  /**
   * Validates files against multer options
   * In a real implementation this would validate mime types, file sizes, etc.
   */
  private validateFiles(files: Express.Multer.File[], options: any): Express.Multer.File[] {
    return files;
  }
}
