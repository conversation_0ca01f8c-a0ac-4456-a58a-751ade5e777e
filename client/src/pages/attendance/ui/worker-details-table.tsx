import { Table, Tag, Tooltip } from 'antd'
import dayjs from 'dayjs'
import { DailyReport } from '@/config/queries/attendance/getAll'
import { convertMinutesToHours } from '@/shared/utils/convertMinutesToHours'
import { DayStatusBadge } from './day-status-badge'
import { formatUzTime } from '@/shared/utils/format-utils'
import { TimezoneHelper, DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'

// Dayjs setup bir marta bajariladi
dayjs.tz.setDefault(DEFAULT_TIMEZONE)

interface WorkerDetailTableProps {
	dailyReports: DailyReport[]
}

export const WorkerDetailTable = ({ dailyReports }: WorkerDetailTableProps) => {
	const dataSource = dailyReports.map((report, index) => ({
		...report,
		key: index,
		formattedDate: TimezoneHelper.formatLocal(report.formattedDate, 'DD.MM.YYYY (ddd)')
	}))

	const columns = [
		{
			title: 'Sana',
			dataIndex: 'formattedDate',
			width: 150,
			fixed: 'left' as const,
			className: 'font-medium'
		},
		{
			title: 'Status',
			width: 150,
			render: (record: DailyReport) => <DayStatusBadge report={record} />
		},
		{
			title: 'Ish vaqti',
			width: 200,
			render: (record: DailyReport) => (
				<div>
					{record.scheduledStartTime && record.scheduledEndTime && (
						<div className='text-blue-500'>
							{formatUzTime(record.scheduledStartTime)} - {formatUzTime(record.scheduledEndTime)}
						</div>
					)}
				</div>
			)
		},
		{
			title: 'Kelgan',
			width: 100,
			render: (record: DailyReport) => (
				<div>
					{record.actualEnterTime ? (
						<Tooltip
							title={
								record.lateMinutes > 0
									? `${record.lateMinutes} daqiqa kech kelgan`
									: `O'z vaqtida kelgan`
							}>
							<Tag color={record.lateMinutes > 0 ? 'orange' : 'green'}>
								{formatUzTime(record.actualEnterTime)}
								{record.lateMinutes > 0 && `${record.lateMinutes}m`}
							</Tag>
						</Tooltip>
					) : record.isWorkDay ? (
						<Tag color='red'>Kelmagan</Tag>
					) : (
						<span className='text-gray-400'>-</span>
					)}
				</div>
			)
		},
		{
			title: 'Ketgan',
			width: 100,
			render: (record: DailyReport) => (
				<div>
					{record.actualExitTime ? (
						<Tooltip
							title={
								record.earlyExitMinutes > 0
									? `${record.earlyExitMinutes} daqiqa erta ketgan`
									: `O'z vaqtida ketgan`
							}>
							<Tag color={record.earlyExitMinutes > 0 ? 'volcano' : 'red'}>
								{formatUzTime(record.actualExitTime)}
							</Tag>
						</Tooltip>
					) : record.isWorkDay && record.cameToWork ? (
						<Tag color='processing'>Chiqmagan</Tag>
					) : (
						<span className='text-gray-400'>-</span>
					)}
				</div>
			)
		},
		{
			title: 'Ishlagan',
			width: 100,
			render: (record: DailyReport) => (
				<div>
					{record.minutesWorked > 0 ? (
						<Tooltip
							title={
								record.scheduledMinutes && record.scheduledMinutes > 0
									? `Reja bo'yicha: ${convertMinutesToHours(record.scheduledMinutes)}`
									: undefined
							}>
							<Tag color='purple'>{convertMinutesToHours(record.minutesWorked)}</Tag>
						</Tooltip>
					) : record.isWorkDay ? (
						<Tag color='red'>0 soat</Tag>
					) : (
						<span className='text-gray-400'>-</span>
					)}
				</div>
			)
		}
	]

	return (
		<Table
			dataSource={dataSource}
			columns={columns}
			pagination={false}
			size='small'
			scroll={{ y: 300 }}
			className='dark:bg-gray-700 dark:text-gray-200'
		/>
	)
}
