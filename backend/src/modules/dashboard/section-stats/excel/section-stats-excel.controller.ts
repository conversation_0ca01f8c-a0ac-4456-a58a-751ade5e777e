import { Controller, Get, Query, Res, UseInterceptors } from '@nestjs/common';
import { Context } from 'src/common/decorators/context.decorator';
import { FindAllDto } from '../dto/find-all.dto';
import { writeExcelResponse } from 'src/common/interceptors/excel-export.interceptor';
import { ProtectedRoute } from '../../../../common';
import { SectionStatsService } from '../section-stats.service';
import { SectionStatsQuery } from '../dto/section-stats.dto';
import { SectionStatsExcelService } from './section-stats-excel.service';
import { Response } from 'express';
import * as ExcelJS from 'exceljs';

import {
  disabledAreaMapper,
  employeeMapper,
  inAreaMapper,
  noAttendanceMapper,
  outAreaMapper,
  patientMapper,
  positionExcelMapper,
  sectionMapper,
  shortVacationMapper,
  vacancyMapper,
  vacationMapper,
} from './excel.mapper';
import {
  disabledAreaColumns,
  employeeColumns,
  inAreaColumns,
  lateAttendanceColumns,
  lateAttendanceMapper,
  noAttendanceColumns,
  outAreaColumns,
  patientColumns,
  positionExcelColumns,
  sectionColumns,
  shortVacationColumns,
  vacancyColumns,
  vacationColumns,
} from './excel.columns';
import * as dayjs from 'dayjs';
import { TimezoneHelper } from 'src/helpers/timezone.helper';

@Controller('dashboard/section-stats-excel')
@ProtectedRoute({ isPublic: false, context: 'WORKSPACE' })
export class SectionStatsExcelController {
  constructor(
    private sectionStatsExcel: SectionStatsExcelService,
    private readonly sectionStatsService: SectionStatsService,
  ) {}

  @Get('section')
  async exportSectionExcel(
    @Query() { organizationId, ...params }: FindAllDto,
    @Context('organizationId') xOrgId: string,
    @Res() res: Response,
  ) {
    const data = await this.sectionStatsExcel.getSectionExcel(organizationId || xOrgId, params);

    await writeExcelResponse({
      res,
      data,
      filename: 'tashkilotlar.xlsx',
      worksheetName: 'Tashkilotlar',
      columns: sectionColumns,
      mapper: sectionMapper,
    });
  }

  @Get('late-attendance')
  async exportLateAttendanceExcel(@Query() query: FindAllDto, @Res() res: Response) {
    const { organizationId, positionId, ...params } = query;
    const xOrgId = (res.req as any).user?.organizationId || '';

    const data = await this.sectionStatsExcel.getAllLateAttendance(organizationId || xOrgId, params, positionId);

    const seenKeys = new Set<string>();

    const flatData = data.flatMap((user) => {
      const organizationNames = user.Organization.map((org) => org.name).join(', ');

      return user.AttendanceReport.map((report) => {
        const reportDate = dayjs(report.date).format('YYYY-MM-DD');
        const uniqueKey = `${user.id}-${reportDate}`;

        if (seenKeys.has(uniqueKey)) {
          return null;
        }

        seenKeys.add(uniqueKey);

        return {
          fullName: user.fullName,
          phone: user.phone,
          position: user.position?.name || '',
          organization: organizationNames,
          date: reportDate,
          enter: report.enter ? TimezoneHelper.formatLocal(report.enter, 'YYYY-MM-DD HH:mm') : '',
          exit: report.exit ? TimezoneHelper.formatLocal(report.exit, 'YYYY-MM-DD HH:mm') : '',
        };
      }).filter(Boolean);
    });

    await writeExcelResponse({
      res,
      filename: `kechikkanlar.xlsx`,
      worksheetName: 'Kechikkanlar',
      columns: lateAttendanceColumns,
      mapper: lateAttendanceMapper,
      data: flatData,
    });
  }

  @Get('no-attendance')
  async exportNoAttendanceExcel(
    @Query() { organizationId, positionId, ...params }: FindAllDto,
    @Context('organizationId') xOrgId: string,
    @Res() res: Response,
  ) {
    const users = await this.sectionStatsExcel.getAllNoAttendance(organizationId || xOrgId, params, positionId);

    await writeExcelResponse({
      res,
      filename: `Ishga-kelmaganlar.xlsx`,
      worksheetName: 'Ishga-kelmaganlar',
      columns: noAttendanceColumns,
      mapper: noAttendanceMapper,
      data: users,
    });
  }

  @Get('position')
  async exportPositionExcel(
    @Query() { organizationId, positionId, ...params }: FindAllDto,
    @Context('organizationId') xOrgId: string,
    @Res() res: Response,
  ) {
    const data = await this.sectionStatsExcel.getPositionsExcel(organizationId || xOrgId, params, positionId);

    await writeExcelResponse({
      res,
      data,
      filename: 'Lavozimlar.xlsx',
      worksheetName: 'Lavozimdagilar',
      columns: positionExcelColumns,
      mapper: positionExcelMapper,
    });
  }

  @Get('employee')
  async exportEmployeesExcel(
    @Query() { organizationId, ...params }: any,
    @Context('organizationId') xOrgId: string,
    @Res() res: Response,
  ) {
    const result = await this.sectionStatsExcel.getEmployeeExcel(organizationId || xOrgId, params);
    await writeExcelResponse({
      res,
      data: result,
      filename: 'Xodimlar.xlsx',
      columns: employeeColumns,
      mapper: employeeMapper,
    });
  }

  @Get('vacation')
  async exportVacationExcel(
    @Res() res: Response,
    @Query() { organizationId }: FindAllDto,
    @Context('organizationId') xOrgId: string,
  ) {
    const data = await this.sectionStatsExcel.getVacationListForExcel(organizationId || xOrgId, 'VACATION');
    return writeExcelResponse({
      res,
      data,
      filename: 'Tatildagilar.xlsx',
      worksheetName: 'Ta’til',
      columns: vacationColumns,
      mapper: vacationMapper,
    });
  }

  @Get('short-vacation')
  async exportShortVacationExcel(
    @Res() res: Response,
    @Query() { organizationId }: FindAllDto,
    @Context('organizationId') xOrgId: string,
  ) {
    const data = await this.sectionStatsExcel.getVacationListForExcel(organizationId || xOrgId, 'SHORT_VACATION');
    return writeExcelResponse({
      res,
      data,
      filename: 'Qisqa-tatildagilar.xlsx',
      worksheetName: 'Qisqa-ta’til',
      columns: shortVacationColumns,
      mapper: shortVacationMapper,
    });
  }

  @Get('pation')
  async exportPatientExcel(
    @Res() res: Response,
    @Query() { organizationId }: FindAllDto,
    @Context('organizationId') xOrgId: string,
  ) {
    const data = await this.sectionStatsExcel.getVacationListForExcel(organizationId || xOrgId, 'PATIONS');
    return writeExcelResponse({
      res,
      data,
      filename: 'Bemorlar.xlsx',
      worksheetName: 'Bemorlar',
      columns: shortVacationColumns,
      mapper: shortVacationMapper,
    });
  }

  @Get('vacancy')
  async exportVacancyExcel(
    @Res() res: Response,
    @Query() { organizationId, ...params }: FindAllDto,
    @Context('organizationId') xOrgId: string,
  ) {
    const data = await this.sectionStatsExcel.getVacancyExcel(organizationId || xOrgId, params);

    return writeExcelResponse({
      res,
      data,
      filename: 'vakansiyalar.xlsx',
      columns: vacancyColumns,
      mapper: vacancyMapper,
    });
  }

  @Get('in-area')
  async exportInAreaExcel(
    @Res() res: Response,
    @Query() { organizationId, positionId, ...params }: FindAllDto,
    @Context('organizationId') xOrgId: string,
  ) {
    const data = await this.sectionStatsExcel.getAreaExcel(organizationId || xOrgId, params, 'IN', positionId);

    return writeExcelResponse({
      res,
      data,
      filename: 'hududdagilar.xlsx',
      columns: inAreaColumns,
      mapper: inAreaMapper,
    });
  }

  @Get('out-area')
  async exportOutAreaExcel(
    @Res() res: Response,
    @Query() { organizationId, positionId, ...params }: FindAllDto,
    @Context('organizationId') xOrgId: string,
  ) {
    const data = await this.sectionStatsExcel.getAreaExcel(organizationId || xOrgId, params, 'OUT', positionId);

    return writeExcelResponse({
      res,
      data,
      filename: 'Hududdan-tashqaridagilar.xlsx',
      columns: outAreaColumns,
      mapper: outAreaMapper,
    });
  }

  @Get('disabled-area')
  async exportDisabledAreaExcel(
    @Res() res: Response,
    @Query() { organizationId, positionId, ...params }: FindAllDto,
    @Context('organizationId') xOrgId: string,
  ) {
    const data = await this.sectionStatsExcel.getAreaExcel(organizationId || xOrgId, params, 'DISABLED', positionId);

    return writeExcelResponse({
      res,
      data,
      filename: "GPS_O'chirilgan.xlsx",
      columns: disabledAreaColumns,
      mapper: disabledAreaMapper,
    });
  }
}
