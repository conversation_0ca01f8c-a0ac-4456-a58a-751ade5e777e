import { axiosPrivate } from '@/config/api'
import { vacancyEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError } from 'axios'
import { TApiErrorResponse } from '@/shared/types/api-error.type'
import { notification } from 'antd'
import { TVacancy } from './create.vacancy'

export type TUserResponse = TVacancy

export const useDeleteVacancy = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async (id: string) => {
			return await axiosPrivate.delete(vacancyEndpoints.one(id))
		},
		onSuccess: async () => {
			queryClient.invalidateQueries({ queryKey: [vacancyEndpoints.all] })
			notification.success({
				message: "Muvaffaqiyatli o'chirildi",
				placement: 'bottomRight'
			})
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response?.data.message)
		}
	})
}
