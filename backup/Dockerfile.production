FROM alpine:latest

RUN apk update && \
    apk add --no-cache postgresql-client bash curl wget

# Установка MinIO Client (mc)
RUN wget https://dl.min.io/client/mc/release/linux-amd64/mc -O /usr/local/bin/mc && \
    chmod +x /usr/local/bin/mc

WORKDIR /app

COPY backup.sh /app/
COPY restore.sh /app/
COPY entrypoint.sh /app/

RUN chmod +x /app/backup.sh /app/restore.sh /app/entrypoint.sh

ENTRYPOINT ["/app/entrypoint.sh"]