import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-leaflet'
import GeojsonMap from './ui/geojson-map'

import { useSearchParams } from 'react-router-dom'
import { useEffect, useMemo } from 'react'
import { Col, Row } from 'antd'
import LocationStats from './ui/location-stats'
export default function Map() {
	const organization = useMemo(() => {
		const org = window.localStorage.getItem('organization')
		return org ? JSON.parse(org) : {}
	}, [])

	const { region, district, section, grade } = organization
	const [search, setSearchParams] = useSearchParams()

	useEffect(() => {
		if (region && !search.has('districtId')) {
			search.set('regionId', region.id)
		}
		if (district && grade.level === 20 && !search.has('districtId')) {
			search.set('districtId', district.id.substring(7, 4))
		}
		if (section && grade.level === 30 && !search.has('sectionId')) {
			search.set('sectionId', section.id.substring(7))
		}
		setSearchParams(search.toString())
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [])

	return (
		<div className='h-full '>
			<Row
				className='h-full'
				gutter={16}>
				<Col
					xs={16}
					className='relative rounded-2xl overflow-hidden'>
					<MapContainer
						center={[41.311081, 69.240562]}
						zoom={13}
						style={{ height: '100%', width: '100%' }}
						zoomControl={false}
						attributionControl={false}>
						<TileLayer
							attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
							url='https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png'
						/>
						<GeojsonMap />
					</MapContainer>
				</Col>
				<Col
					xs={8}
					className='h-full'>
					<LocationStats />
				</Col>
			</Row>
		</div>
	)
}
