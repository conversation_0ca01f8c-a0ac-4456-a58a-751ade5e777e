import {
	useGetGrade,
	useGetReferenceDistricts,
	useGetReferenceRegions,
	useGetReferenceSections
} from '@/config/queries/common/common-queries'
import { useGetOrganizationTypes } from '@/config/queries/organization-types/get.queries'
import { TValidationFormProps } from '@/shared/types/validation-form.type'
import { Form, Input, Select } from 'antd'
import { useCallback, useEffect, useState } from 'react'
import { useOrganizationsModalStore } from '../utils/organizations-modal-store'
import { debounce } from 'lodash'

const { Item } = Form

export default function OrganizationForm({ validation }: TValidationFormProps) {
	const { data } = useOrganizationsModalStore()
	const { data: grades, isLoading: isLoadingGrade } = useGetGrade()
	const {
		data: organizationTypes,
		isLoading: isLoadingOrganizationTypes,
		fetchNextPage
	} = useGetOrganizationTypes()
	const { data: regions, isLoading: isLoadingRegions } = useGetReferenceRegions()
	const [activeRegionId, setActiveRegionId] = useState<number | null>(null)
	const [activeDistrictId, setActiveDistrictId] = useState<number | null>(null)
	const {
		data: districts,
		refetch: refetchDistrict,
		isLoading: isLoadingDistricts
	} = useGetReferenceDistricts(activeRegionId ?? data?.regionId ?? -1)
	const {
		data: sections,
		refetch: refetchSections,
		isLoading: isLoadingSections
	} = useGetReferenceSections(activeDistrictId ?? data?.districtId ?? -1)

	useEffect(() => {
		if (activeRegionId) refetchDistrict()
	}, [activeRegionId])

	useEffect(() => {
		if (activeDistrictId) refetchSections()
	}, [activeDistrictId])

	const handlePopupScroll = useCallback(
		debounce((e: React.UIEvent<HTMLDivElement>) => {
			const target = e.target as HTMLDivElement
			const isNearBottom = target.scrollTop + target.offsetHeight >= target.scrollHeight - 10
			if (isNearBottom) {
				fetchNextPage()
			}
		}, 100),
		[]
	)

	return (
		<div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '16px' }}>
			<Item
				name='name'
				label='Tashkilot nomi'
				rules={validation.name}>
				<Input />
			</Item>

			<Item
				name='address'
				label='Tashkilot Manzili'
				rules={validation.address}>
				<Input />
			</Item>

			<Item
				name='sector'
				label='Sektor'
				rules={validation.sector}>
				<Select options={[1, 2, 3, 4].map(item => ({ value: item, label: item }))} />
			</Item>

			<Item
				name='sectorResponsible'
				label='Sektor Javobgari'
				rules={validation.sectorResponsible}>
				<Select options={[1, 2, 3, 4].map(item => ({ value: item, label: item }))} />
			</Item>

			<Item
				name='gradeId'
				label='Tashkilot lavozimi'
				rules={validation.gradeId}>
				<Select
					loading={isLoadingGrade}
					options={grades}
					fieldNames={{ label: 'name', value: 'id' }}
				/>
			</Item>

			<Item
				name='typeId'
				label='Tashkilot turi'
				rules={validation.typeId}>
				<Select
					loading={isLoadingOrganizationTypes}
					options={organizationTypes?.pages.flatMap(page => page.data)}
					fieldNames={{ label: 'name', value: 'id' }}
					onPopupScroll={handlePopupScroll}
				/>
			</Item>

			<Item
				name='description'
				label='Tashkilot haqida'
				rules={validation.description}>
				<Input />
			</Item>

			<Item
				name='regionId'
				label='Viloyat'
				rules={validation.regionId}>
				<Select
					onChange={value => setActiveRegionId(value)}
					allowClear
					loading={isLoadingRegions}
					options={regions}
					fieldNames={{ label: 'name', value: 'id' }}
				/>
			</Item>

			<Item
				name='districtId'
				label='Tuman'
				rules={validation.districtId}>
				<Select
					onChange={value => setActiveDistrictId(value)}
					allowClear
					loading={isLoadingDistricts}
					options={districts}
					fieldNames={{ label: 'name', value: 'id' }}
				/>
			</Item>

			<Item
				name='sectionId'
				label='Mahalla'
				rules={validation.sectionId}>
				<Select
					allowClear
					loading={isLoadingSections}
					options={sections}
					fieldNames={{ label: 'name', value: 'id' }}
				/>
			</Item>
		</div>
	)
}
