import {
	useGetGrade,
	useGetReferenceDistricts,
	useGetReferenceRegions,
	useGetReferenceSections
} from '@/config/queries/common/common-queries'
import { useGetOrganizationTypes } from '@/config/queries/organization-types/get.queries'
import { TValidationFormProps } from '@/shared/types/validation-form.type'
import { Form, Input, Select } from 'antd'
import { useEffect, useState } from 'react'
import { useOrganizationsModalStore } from '../utils/organizations-modal-store'

const { Item } = Form

export default function OrganizationForm({ validation }: TValidationFormProps) {
	const { data } = useOrganizationsModalStore()
	const { data: grades, isLoading: isLoadingGrade } = useGetGrade()
	const {
		data: organizationTypes,
		isLoading: isLoadingOrganizationTypes,
		hasNextPage,
		fetchNextPage
	} = useGetOrganizationTypes()
	const { data: regions, isLoading: isLoadingRegions } = useGetReferenceRegions()
	const [activeRegionId, setActiveRegionId] = useState<number | null>(data?.regionId || null)
	const [activeDistrictId, setActiveDistrictId] = useState<number | null>(data?.districtId || null)
	const {
		data: districts,
		refetch: refetchDistrict,
		isLoading: isLoadingDistricts
	} = useGetReferenceDistricts(activeRegionId)
	const {
		data: sections,
		refetch: refetchSections,
		isLoading: isLoadingSections
	} = useGetReferenceSections(activeDistrictId)

	useEffect(() => {
		if (activeRegionId) refetchDistrict()
	}, [activeRegionId, refetchDistrict])

	useEffect(() => {
		if (activeDistrictId) refetchSections()
	}, [activeDistrictId, refetchSections])

	return (
		<div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '16px' }}>
			<Item
				name='name'
				label='Tashkilot nomi'
				rules={validation.name}>
				<Input />
			</Item>

			<Item
				name='address'
				label='Tashkilot Manzili'
				rules={validation.address}>
				<Input />
			</Item>

			<Item
				name='sector'
				label='Sektor'
				rules={validation.sector}>
				<Select options={[1, 2, 3, 4].map(item => ({ value: item, label: item }))} />
			</Item>

			<Item
				name='sectorResponsible'
				label='Sektor Javobgari'
				rules={validation.sectorResponsible}>
				<Select options={[1, 2, 3, 4].map(item => ({ value: item, label: item }))} />
			</Item>

			<Item
				name='gradeId'
				label='Tashkilot lavozimi'
				rules={validation.gradeId}>
				<Select
					loading={isLoadingGrade}
					options={grades}
					fieldNames={{ label: 'name', value: 'id' }}
				/>
			</Item>

			<Item
				name='typeId'
				label='Tashkilot turi'
				rules={validation.typeId}>
				<Select
					onPopupScroll={e => {
						const target = e.target as HTMLDivElement
						if (target.scrollTop + target.clientHeight >= target.scrollHeight - 5 && hasNextPage) {
							fetchNextPage()
						}
					}}
					loading={isLoadingOrganizationTypes}
					options={organizationTypes?.pages?.flatMap(
						type =>
							type.data?.map(item => ({
								value: item?.id,
								label: item?.name
							})) ?? []
					)}
				/>
			</Item>

			<Item
				name='description'
				label='Tashkilot haqida'
				rules={validation.description}>
				<Input />
			</Item>

			<Item
				name='regionId'
				label='Viloyat'
				rules={validation.regionId}>
				<Select
					onChange={value => setActiveRegionId(value)}
					allowClear
					loading={isLoadingRegions}
					options={regions}
					fieldNames={{ label: 'name', value: 'id' }}
					defaultValue={data?.regionId}
				/>
			</Item>

			<Item
				name='districtId'
				label='Tuman'
				rules={validation.districtId}>
				<Select
					onChange={value => setActiveDistrictId(value)}
					allowClear
					loading={isLoadingDistricts}
					options={districts}
					fieldNames={{ label: 'name', value: 'id' }}
					defaultValue={data?.districtId}
				/>
			</Item>

			<Item
				name='sectionId'
				label='Mahalla'
				rules={validation.sectionId}>
				<Select
					allowClear
					loading={isLoadingSections}
					options={sections}
					fieldNames={{ label: 'name', value: 'id' }}
					defaultValue={data?.sectionId}
				/>
			</Item>
		</div>
	)
}
