enum UserData {
  MAIN_ORGANIZATION
  PARENT_ORGANIZATION
  WORKER
  UNDER_CONTROL
}

enum UserRole {
  ADMIN
  USER
}

model User {
  id       String @id @default(cuid())
  fullName String
  username String @unique
  password String
  status   Status @default(ACTIVE)

  phone String?

  position   OrganizationTypePosition @relation(fields: [positionId], references: [id])
  positionId String

  avatar   File?   @relation("Avatar", fields: [avatarId], references: [id])
  avatarId String?

  userData UserData[] @default([MAIN_ORGANIZATION])

  faceIdImage   File?   @relation("FaceIdImage", fields: [faceIdImageId], references: [id])
  faceIdImageId String?

  MainOrganization   Organization? @relation(fields: [mainOrganizationId], references: [id])
  mainOrganizationId String? // hodim 

  ResponsibleFor Organization[] @relation("OrganizationResponsible") // masul
  Organization   Organization[] @relation("User") // ishchi

  imei       String?
  telegramId String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  Task      Task[]

  Recipient           Recipient[]
  UserWorkingSchedule UserWorkingSchedule?

  Attendance Attendance[]

  AttendanceCreatedBy Attendance[] @relation("AttendanceCreatedBy")

  AttendanceReport AttendanceReport[]

  GPSLocation GPSLocation[]

  Imei Imei[]

  GPSLocationReport GPSLocationReport[]

  role         UserRole       @default(USER)
  Notification Notification[]

  Vacation               Vacation[]
  ControlledTask         Task[]         @relation("ContollerUser")
  ControlledOrganization Organization[] @relation("OrganizationController")
  Infraction             Infraction[]

  @@index([fullName])
  @@index([username])
  @@index([mainOrganizationId])
  @@index([positionId])
}

model UserWorkingSchedule {
  id String @id @default(cuid())

  status Status @default(ACTIVE)

  days      UserWorkingScheduleDay[]
  createdAt DateTime                 @default(now())
  updatedAt DateTime                 @updatedAt

  User   User   @relation(fields: [userId], references: [id])
  userId String @unique
}

model UserWorkingScheduleDay {
  id        String @id @default(cuid())
  name      String @unique
  day       Int
  startTime String
  endTime   String
  status    Status @default(ACTIVE)

  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  UserWorkingSchedule UserWorkingSchedule[]
}
