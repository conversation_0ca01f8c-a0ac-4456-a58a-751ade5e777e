import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from 'src/common';
import { GPS_OFFLINE_CHECK_TIMEOUT_MINUTES, GPS_SET_OFFLINE, GPS_STATUS_QUEUE } from './interfaces/gps-status';
import { InjectQueue } from '@nestjs/bullmq';
import { Queue } from 'bullmq';
import { GPS_STATUS } from './interfaces/gps';

@Injectable()
export class GpsSchedule {
  private readonly logger = new Logger(GpsSchedule.name);
  private readonly OFFLINE_TIMEOUT_MINUTES = GPS_OFFLINE_CHECK_TIMEOUT_MINUTES;

  constructor(
    private prismaService: PrismaService,
    @InjectQueue(GPS_STATUS) private gpsStatusQueue: Queue,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleSchedule() {
    const lastTwoMonth = new Date(new Date().setMonth(new Date().getMonth() - 2)).toISOString();
    await this.prismaService.gPSLocationReport.deleteMany({
      where: {
        createdAt: {
          lt: lastTwoMonth,
        },
      },
    });

    await this.prismaService.gPSLocation.deleteMany({
      where: {
        createdAt: {
          lt: lastTwoMonth,
        },
      },
    });

    this.logger.log('Deleted old GPS location reports');
  }

  async checkOfflineUsers() {
    const now = new Date();
    const lastOneHour = new Date(now.getTime() - 60 * 60 * 1000);
    try {
      const lastLocationReports = await this.prismaService.gPSLocationReport.findMany({
        where: {
          status: 'ACTIVE',
          createdAt: {
            gte: lastOneHour,
          },
        },
        select: {
          createdAt: true,
          id: true,
          type: true,
          isInArea: true,
          userId: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      const offlineUsers = lastLocationReports.filter((report) => {
        const diffMinutes = (now.getTime() - new Date(report.createdAt).getTime()) / (1000 * 60);
        return diffMinutes >= this.OFFLINE_TIMEOUT_MINUTES && report.type !== 'DISABLED';
      });

      const uniqueOfflineUsers = new Map<string, (typeof offlineUsers)[number]>();

      for (const report of offlineUsers) {
        if (!uniqueOfflineUsers.has(report.userId)) {
          uniqueOfflineUsers.set(report.userId, report);
        }
      }

      for (const report of uniqueOfflineUsers.values()) {
        await this.gpsStatusQueue.add(
          GPS_SET_OFFLINE,
          {
            userId: report.userId,
            reason: 'TIMEOUT',
          },
          {
            priority: 1,
            jobId: `set-offline-${report.userId}`,
            removeOnComplete: 50,
            removeOnFail: 10,
          },
        );
      }

      this.logger.log(`Offline users checked: ${uniqueOfflineUsers.size} users set to offline`);
    } catch (error) {
      this.logger.error(`Error checking offline users: ${error.message}`);
      this.logger.error(error.stack);
    }
  }

  //
  // @Cron('0 22 * * *', {
  //   timeZone: 'Asia/Tashkent',
  // })
  // async dailyOfflineCleanup() {
  //   try {
  //     const twoDaysAgo = new Date(Date.now() - 2 * 24 * 60 * 60 * 1000);
  //
  //     const longOfflineUsers = await this.prismaService.$queryRaw<{ userId: string }[]>`
  //       SELECT DISTINCT
  //       ON ("userId") "userId"
  //       FROM "GPSLocationReport"
  //       WHERE "status" = 'ACTIVE'
  //         AND "type" != 'DISABLED'
  //         AND "createdAt"
  //           < ${twoDaysAgo}
  //       ORDER BY "userId", "createdAt" DESC
  //     `;
  //
  //     for (const user of longOfflineUsers) {
  //       await this.gpsStatusQueue.add(
  //         GPS_SET_OFFLINE,
  //         {
  //           userId: user.userId,
  //           reason: 'DAILY_CLEANUP_2_DAYS',
  //         },
  //         {
  //           priority: 5,
  //           jobId: `daily-cleanup-${user.userId}`,
  //         },
  //       );
  //     }
  //
  //     this.logger.log(`Kunlik cleanup: ${longOfflineUsers.length} user offline qilindi`);
  //   } catch (error) {
  //     this.logger.error(`Kunlik cleanup xatoligi: ${error.message}`);
  //   }
  // }
}
