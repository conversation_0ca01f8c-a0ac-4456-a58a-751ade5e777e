import { useDashboardGpsInfo } from '@/config/queries/dashboard/get.queries'
import { Pie } from '@ant-design/plots'
import { Card } from 'antd'
const StatsPieChart = () => {
	const { data: gpsInfo } = useDashboardGpsInfo()
	const data = [
		{ type: 'Kiruvchi', value: gpsInfo?.inCount || 0 },
		{ type: 'Chiquvchi', value: gpsInfo?.outCount || 0 },
		{ type: 'Offline', value: gpsInfo?.disabledCount || 0 }
	]

	const config = {
		appendPadding: 10,
		data,
		angleField: 'value',
		colorField: 'type',
		radius: 0.8,
		label: {
			type: 'outer',
			content: '{name} {percentage}'
		},
		interactions: [
			{
				type: 'pie-legend-active'
			},
			{
				type: 'element-active'
			}
		]
	}

	return (
		<Card className='h-[500px]'>
			<Pie {...config} />
		</Card>
	)
}

export default StatsPieChart
