import { useGetAllFaceIdDevices } from '@/config/queries/face-id/getAll'
import { Search } from '@/shared/components/search/search'
import { FaceIDType } from '@/shared/types/face-id.type'
import { Badge, Table, Select } from 'antd'
import { ArrowDown, ArrowUp } from 'lucide-react'
import { useState } from 'react'
import { useSearchParams } from 'react-router-dom'

const { Option } = Select

const FaceIdTable = () => {
	const [currentPage, setCurrentPage] = useState(1)
	const [pageSize, setPageSize] = useState(10)
	const [searchParams, setSearchParams] = useSearchParams()

	const { data: faceIdDevices, hasNextPage, fetchNextPage } = useGetAllFaceIdDevices()

	const handleOnlineStatusChange = (value: string) => {
		const newSearchParams = new URLSearchParams(searchParams)

		if (value === 'all') {
			newSearchParams.delete('onlineStatus')
		} else {
			newSearchParams.set('onlineStatus', value)
		}

		setSearchParams(newSearchParams)
		setCurrentPage(1) // Reset to first page when filter changes
	}

	const currentOnlineStatus = searchParams.get('onlineStatus') || 'all'

	return (
		<Table
			title={() => (
				<div className='flex items-center justify-between gap-4'>
					<Search />
					<div className='flex items-center gap-2'>
						<span className='text-sm font-medium'>Holat bo'yicha filter:</span>
						<Select
							value={currentOnlineStatus}
							onChange={handleOnlineStatusChange}
							style={{ width: 150 }}
							placeholder='Holatni tanlang'>
							<Option value='all'>Barchasi</Option>
							<Option value='true'>Onlayn</Option>
							<Option value='false'>Oflayn</Option>
						</Select>
					</div>
				</div>
			)}
			dataSource={faceIdDevices?.pages?.flatMap(page => page.data) || []}
			pagination={{
				total: faceIdDevices?.pages?.[0]?.meta?.total || 0,
				pageSize: pageSize,
				current: currentPage,
				showSizeChanger: true,
				pageSizeOptions: [5, 10, 20, 50, 100],
				onChange: (page, size) => {
					setCurrentPage(page)
					setPageSize(size)
					if (
						Math.ceil((page * size) / size) > (faceIdDevices?.pages?.length || 0) &&
						hasNextPage
					) {
						fetchNextPage()
					}
				}
			}}
			columns={[
				{ key: '#', title: '#', render: (_, __, index) => ++index },
				{ key: 'MAC', title: 'MAC', dataIndex: 'MAC' },
				{ key: 'IP', title: 'IP', dataIndex: 'IP' },
				{ key: 'organization', title: 'Tashkilot', dataIndex: ['Organization', 'name'] },
				{
					key: 'heartbeat',
					title: 'Holat',
					dataIndex: 'heartbeat',
					render: rec => {
						if (!rec) {
							return (
								<Badge
									status='error'
									text='Ulanmagan'
								/>
							)
						}
						return (
							<Badge
								status='success'
								text={`Soat: ${new Date(rec.dateTime).toLocaleTimeString([], {
									hour: '2-digit',
									minute: '2-digit',
									day: '2-digit',
									month: '2-digit'
								})}`}
							/>
						)
					}
				},
				{
					key: 'type',
					title: 'Turi',
					render: rec =>
						rec.type === FaceIDType.ENTER ? (
							<div className='flex items-center gap-x-1'>
								<ArrowUp /> Kirish
							</div>
						) : (
							<div className='flex items-center gap-x-1'>
								<ArrowDown />
								Chiqish
							</div>
						)
				}
			]}
		/>
	)
}
export default FaceIdTable
