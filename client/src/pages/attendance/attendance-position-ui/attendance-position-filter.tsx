import { ClearOutlined, SearchOutlined } from '@ant-design/icons'
import { Button, Col, DatePicker, Form, Row, TimeRangePickerProps, Tooltip } from 'antd'
import dayjs, { Dayjs } from 'dayjs'
import { useSearchParams } from 'react-router-dom'
import { useEffect } from 'react'

import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import 'dayjs/locale/uz-latn'
import AttendancePositionSelectTree from './attendance-position-select-tree'
// import { useGetPositionAttendanceReportExcel } from '@/config/queries/common/common-queries'

dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.locale('uz-latn')

const timeZone = 'Asia/Tashkent'

const rangePresets: TimeRangePickerProps['presets'] = [
	{
		label: 'Bugun',
		value: [dayjs().tz(timeZone).startOf('day'), dayjs().tz(timeZone).endOf('day')]
	},
	{
		label: 'Kecha',
		value: [
			dayjs().tz(timeZone).subtract(1, 'day').startOf('day'),
			dayjs().tz(timeZone).subtract(1, 'day').endOf('day')
		]
	},
	{
		label: 'Ohirgi 3 kun',
		value: [dayjs().tz(timeZone).subtract(3, 'day'), dayjs().tz(timeZone)]
	},
	{
		label: 'Ohirgi 1 hafta',
		value: [dayjs().tz(timeZone).subtract(7, 'day'), dayjs().tz(timeZone)]
	},
	{
		label: 'Ohirgi 2 hafta',
		value: [dayjs().tz(timeZone).subtract(14, 'day'), dayjs().tz(timeZone)]
	},
	{
		label: 'Ohirgi 1 oy',
		value: [dayjs().tz(timeZone).subtract(1, 'month'), dayjs().tz(timeZone)]
	},
	{
		label: 'Ohirgi 3 oy',
		value: [dayjs().tz(timeZone).subtract(3, 'month'), dayjs().tz(timeZone)]
	},
	{
		label: 'Ohirgi 6 oy',
		value: [dayjs().tz(timeZone).subtract(6, 'month'), dayjs().tz(timeZone)]
	},
	{
		label: 'Ohirgi 1 yil',
		value: [dayjs().tz(timeZone).subtract(1, 'year'), dayjs().tz(timeZone)]
	}
]

export default function AttendancePositionFilter() {
	const [searchParams, setSearchParams] = useSearchParams()
	const [form] = Form.useForm()
	// const download = useGetPositionAttendanceReportExcel()

	const handleReset = () => {
		const todayPreset = rangePresets?.find(i => i.label === 'Bugun')?.value as [Dayjs, Dayjs]

		form.resetFields()

		// @ts-ignore
		setSearchParams(prev => {
			const newParams: Record<string, string> = {}

			if (todayPreset) {
				newParams.startDate = todayPreset[0].tz(timeZone).format('YYYY-MM-DDTHH:mm:ssZ')
				newParams.endDate = todayPreset[1].tz(timeZone).format('YYYY-MM-DDTHH:mm:ssZ')
			}
			newParams.grade = '30'
			return newParams
		})
	}

	const handleSearch = () => {
		const formValues = form.getFieldsValue()
		setSearchParams(prev => {
			const newParams = { ...Object.fromEntries(prev) }

			if (formValues.positionId && formValues.positionId.length > 0) {
				newParams.positionId = Array.isArray(formValues.positionId)
					? formValues.positionId.join(',')
					: formValues.positionId
			} else {
				delete newParams.positionId
			}
			newParams.grade = '30'

			return newParams
		})
	}

	useEffect(() => {
		const startDate = searchParams.get('startDate')
		const endDate = searchParams.get('endDate')

		if (!startDate || !endDate) {
			const todayPreset = rangePresets?.find(i => i.label === 'Bugun')?.value as [Dayjs, Dayjs]
			setSearchParams(prev => {
				const currentParams = Object.fromEntries(prev)
				const newParams = {
					...currentParams,
					grade: '30',
					startDate: todayPreset[0].tz(timeZone).format('YYYY-MM-DDTHH:mm:ssZ'),
					endDate: todayPreset[1].tz(timeZone).format('YYYY-MM-DDTHH:mm:ssZ')
				}
				return newParams
			})
		}
	}, [setSearchParams, searchParams])

	useEffect(() => {
		const positionId = searchParams.get('positionId')
		const startDate = searchParams.get('startDate')
		const endDate = searchParams.get('endDate')

		const formValues: any = {}

		if (positionId) {
			formValues.positionId = positionId.split(',')
		}

		if (startDate && endDate) {
			formValues.period = [dayjs(startDate), dayjs(endDate).endOf('day')]
		} else {
			const todayPreset = rangePresets?.find(i => i.label === 'Bugun')?.value
			if (todayPreset) {
				formValues.period = todayPreset
			}
		}

		form.setFieldsValue(formValues)
	}, [searchParams, form])

	return (
		<Form
			form={form}
			className='flex w-full p-4'
			onReset={handleReset}
			layout='vertical'>
			<Row
				className='w-full'
				gutter={[16, 16]}>
				<Col xs={6}>
					<Form.Item
						label='Davomat davri'
						name='period'>
						<DatePicker.RangePicker
							allowClear={false}
							onChange={dates => {
								setSearchParams(prev => {
									const newParams = { ...Object.fromEntries(prev) }
									if (dates && dates[0] && dates[1]) {
										newParams.startDate = dates[0]
											.tz(timeZone)
											.startOf('day')
											.format('YYYY-MM-DDTHH:mm:ssZ')
										newParams.endDate = dates[1]
											.tz(timeZone)
											.endOf('day')
											.format('YYYY-MM-DDTHH:mm:ssZ')
									} else {
										delete newParams.startDate
										delete newParams.endDate
									}
									newParams.grade = '30'
									return newParams
								})
							}}
							className='w-full'
							placeholder={['Boshlanish sanasi', 'Tugash sanasi']}
							presets={rangePresets}
						/>
					</Form.Item>
				</Col>
				<Col xs={14}>
					<Form.Item
						label='Lavozimlar'
						name='positionId'>
						<AttendancePositionSelectTree />
					</Form.Item>
				</Col>
				<Col
					className='!p-0'
					xs={4}>
					<Form.Item
						label='Amallar'
						className='flex'>
						<Tooltip title='Qidiruv'>
							<Button
								size='small'
								variant='solid'
								color='blue'
								className='mr-2'
								icon={<SearchOutlined />}
								onClick={handleSearch}
							/>
						</Tooltip>
						{/* <Tooltip title='Hisobotni Excel fayl qilib yuklab olish'>
							<Button
								size='small'
								variant='solid'
								color='green'
								className='mr-2'
								onClick={() => {
									download.mutate()
								}}
								icon={<DownloadOutlined />}
							/>
						</Tooltip> */}
						<Tooltip title='Qidiruv natijalarini tozalash'>
							<Button
								size='small'
								className='mr-2'
								variant='solid'
								htmlType='reset'
								color='red'
								icon={<ClearOutlined />}
								onClick={handleReset}
							/>
						</Tooltip>
					</Form.Item>
				</Col>
			</Row>
		</Form>
	)
}
