import { Form, Input, InputNumber } from 'antd'

const TaskStatesForm = () => {
	return (
		<>
			<Form.Item
				name='name'
				label='Nomi'
				rules={[{ required: true, message: 'Nomini kiriting' }]}>
				<Input
					placeholder='Nomini kiriting'
					size='middle'
				/>
			</Form.Item>
			<Form.Item
				name='key'
				label='Kalit'
				rules={[{ required: true, message: 'Kalitni kiriting' }]}>
				<Input
					placeholder='Kalitni kiriting'
					size='middle'
				/>
			</Form.Item>
			<Form.Item
				name='order'
				label='Tartib'
				rules={[{ required: true, message: 'Tartibni kiriting' }]}>
				<InputNumber
					type='number'
					style={{ width: '100%' }}
					placeholder='Tartibni kiriting'
					size='middle'
				/>
			</Form.Item>
		</>
	)
}
export default TaskStatesForm
