import { axiosPrivate } from '@/config/api'
import { vocationEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { TVocationCreate } from './create.query'
import { notification } from 'antd'

export type TVocationUpdate = {
	data: Partial<TVocationCreate>
	vocationId: string
}

export const useUpdateVocation = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({ vocationId, data }: TVocationUpdate) => {
			return await axiosPrivate.put(vocationEndpoints.one(vocationId), data)
		},
		onSuccess: async () => {
			queryClient.invalidateQueries({ queryKey: [vocationEndpoints.all] })
			notification.success({
				message: 'Muvaffaqiyatli Yangila<PERSON>',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: '<PERSON><PERSON><PERSON><PERSON> xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
