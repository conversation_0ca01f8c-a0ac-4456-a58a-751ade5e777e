import { Controller, Get, Param, Query } from '@nestjs/common';
import { ReferencesService } from './references.service';

@Controller('references')
export class ReferencesController {
  constructor(private readonly referencesService: ReferencesService) {}

  @Get('task-types')
  async taskTypes() {
    return await this.referencesService.taskTypes();
  }

  @Get('grade')
  async grade() {
    return await this.referencesService.grade();
  }

  @Get('task-states')
  async taskStates() {
    return await this.referencesService.taskTypes();
  }

  @Get('attemdance-types')
  async attendanceTypes() {
    return await this.referencesService.attendanceTypes();
  }

  @Get('gps-location-types')
  async gpsLocationTypes() {
    return await this.referencesService.gpsLocationTypes();
  }

  @Get('organization-types')
  async organizationTypes() {
    return await this.referencesService.organizationTypes();
  }

  @Get('organization-positions')
  async organizationPositions(@Query('grade') grade?: number) {
    return await this.referencesService.organizationPositions(grade);
  }

  @Get('recipient-answer-types')
  async recipientAnswerTypes() {
    return await this.referencesService.recipientAnswerTypes();
  }

  @Get('recipient-answer-states')
  async recipientAnswerStates() {
    return await this.referencesService.recipientAnswerStates();
  }

  @Get('regions')
  async regions() {
    return await this.referencesService.regions();
  }

  @Get('regions/:id')
  async regionDistrict(@Param('id') id: string) {
    return await this.referencesService.regionDistrict(id);
  }

  @Get('districts')
  async districts() {
    return await this.referencesService.districts();
  }

  @Get('district/:id')
  async districtSection(@Param('id') id: string) {
    return await this.referencesService.districtSection(id);
  }
}
