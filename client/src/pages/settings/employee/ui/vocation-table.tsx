import { useDeleteVocation } from '@/config/queries/vocation/delete.query'
import { TVocation, useGetAllVocations } from '@/config/queries/vocation/getAll.query'
import { LoadingScreen } from '@/shared/ui/suspense'
import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
import { Button, Modal, Space, Table } from 'antd'
import dayjs from 'dayjs'
import { useState } from 'react'
import { EmployeeVocationCreate } from './employee-vocation-create'
import { useParams } from 'react-router-dom'
import { useAuthMe } from '@/config/queries/auth/verify.queries'

type Props = {
	workspace?: 'organization' | 'personal'
}

export const VocationTable = ({ workspace = 'organization' }: Props) => {
	const { id } = useParams()
	const { data: user } = useAuthMe()
	const { data: vocations, isLoading } = useGetAllVocations(user?.id)
	const { mutateAsync: deleteVocation } = useDeleteVocation()
	const [selectedVocation, setSelectedVocation] = useState<TVocation | null>(null)
	const [isVocationModalOpen, setIsVocationModalOpen] = useState(false)
	const [selectedEmployeeId, setSelectedEmployeeId] = useState<string | null>(null)

	const handleDelete = (vocationId: string) => {
		Modal.confirm({
			title: 'Ishonchingiz komilmi?',
			content: "Ushbu tatil tarixini o'chirishni xoxlaysizmi?",
			okText: 'Ha',
			cancelText: "Yo'q",
			onOk: () => {
				deleteVocation(vocationId)
			}
		})
	}

	if (!user?.id && !id) return
	if (isLoading) return <LoadingScreen />

	return (
		<>
			<div className='flex items-center justify-between mb-3'>
				<h1 className='font-bold'>Tatil tarixi</h1>
				{workspace === 'organization' && (
					<>
						<Button
							type='primary'
							onClick={() => {
								setSelectedEmployeeId(id ?? '')
								setIsVocationModalOpen(true)
							}}>
							Tatil berish
						</Button>

						<EmployeeVocationCreate
							open={isVocationModalOpen}
							onClose={() => setIsVocationModalOpen(false)}
							employeeId={selectedEmployeeId}
						/>
					</>
				)}
			</div>
			<Table
				dataSource={vocations?.data || []}
				columns={[
					{ key: '#', title: '#', render: (_, __, index) => ++index },
					{
						key: 'type',
						title: 'Tatil sababi',
						render: item => (item.type === 'VACATION' ? 'Tatil' : 'Kasal')
					},
					{
						key: 'description',
						title: 'Izoh',
						render: item => (item.description ? item.description : '-')
					},
					{
						key: 'begin',
						title: 'Boshlanish vaqti',
						render: item => dayjs(item.begin).format('DD.MM.YYYY')
					},
					{
						key: 'end',
						title: 'Tugash vaqti',
						render: item => dayjs(item.end).format('DD.MM.YYYY')
					},
					workspace === 'organization'
						? {
								key: 'actions',
								title: 'Amallar',
								render: (record: TVocation) => (
									<Space>
										<Button
											type='primary'
											icon={<EditOutlined />}
											onClick={e => {
												e.stopPropagation()
												setSelectedVocation(record)
											}}
										/>
										<Button
											danger
											icon={<DeleteOutlined />}
											onClick={e => {
												e.stopPropagation()
												handleDelete(record.id)
											}}
										/>
									</Space>
								)
							}
						: { key: 'actions' }
				]}
			/>
			{workspace === 'organization' && (
				<EmployeeVocationCreate
					employeeId={id ?? ''}
					onClose={() => setSelectedVocation(null)}
					open={selectedVocation !== null}
					record={selectedVocation}
				/>
			)}
		</>
	)
}
