import { Button, Flex, Form, Input, Modal, Select, Space } from 'antd'
import { useGetTaskStates } from '@/config/queries/task-states/get-queries.ts'
import { useParams } from 'react-router-dom'
import { useCompleteTask } from '@/config/queries/task/complete.ts'

const { Item } = Form

interface Props {
	open: boolean
	onCancel: () => void
}

function TaskConfirmForm({ open, onCancel }: Props) {
	const taskTypes = useGetTaskStates()
	const { id } = useParams()
	const { mutate } = useCompleteTask()
	const [form] = Form.useForm()
	const onFinish = (values: { stateId: string; description: string }) => {
		mutate(
			{
				id: id!,
				stateId: values.stateId,
				description: values.description
			},
			{
				onSuccess: () => {
					form.resetFields()
					onCancel()
				}
			}
		)
	}

	return (
		<Modal
			open={open}
			title='Topshiriqni yakunlash'
			footer={null}
			destroyOnClose
			closable={false}
			keyboard={false}
			onCancel={onCancel}>
			<Form
				onFinish={onFinish}
				form={form}
				layout='vertical'
				size='large'>
				<Space
					direction='vertical'
					className='w-full'
					size='large'>
					<Item
						label='Topshiriqni yakunlash sababi'
						help='Topshiriqni yakunlash uchun sababni tanlang'
						rules={[{ required: true }]}
						name='stateId'>
						<Select
							placeholder='Topshiriqni yakunlash sababi'
							options={taskTypes.data?.pages?.flatMap(page =>
								page.data.map(data => ({
									label: data.name,
									value: data.id
								}))
							)}
						/>
					</Item>
					<Item
						rules={[{ required: true }]}
						label='Topshiriq natijasi'
						help='Topshiriq natijasini kiriting'
						name='description'>
						<Input.TextArea placeholder='Izoh' />
					</Item>
				</Space>
				<Item>
					<Flex
						gap={16}
						className='!mt-4'>
						<Button
							block
							htmlType='reset'
							onClick={onCancel}
							variant='solid'
							color='red'>
							Bekor Qilish
						</Button>
						<Button
							htmlType='submit'
							block
							variant='solid'
							color='primary'>
							Yakunlash
						</Button>
					</Flex>
				</Item>
			</Form>
		</Modal>
	)
}

export default TaskConfirmForm
