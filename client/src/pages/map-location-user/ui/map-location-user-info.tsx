import { useGetUserDailyLocation } from '@/config/queries/gps/gps.queries.ts'
import { Card, DatePicker, Flex, Space, Tag } from 'antd'
import { useSearchParams } from 'react-router-dom'
import dayjs, { Dayjs } from 'dayjs'
import VirtualList from 'rc-virtual-list'

export default function MapLocationUserInfo() {
	const { data, isLoading } = useGetUserDailyLocation()
	const [search, setSearch] = useSearchParams()
	const day = search.get('day') ? dayjs(search.get('day')) : dayjs()
	const from = search.get('from') ? dayjs(search.get('from')) : dayjs().startOf('day')
	const to = search.get('to') ? dayjs(search.get('to')) : dayjs().endOf('day')
	const handleDaySearch = (day: Dayjs) => {
		const dayString = day.format('YYYY-MM-DD')
		if (dayString) {
			search.set('day', dayString)
			setSearch(search.toString())
		} else {
			search.delete('day')
			setSearch(search.toString())
		}
	}

	const handleFromSearch = (from: Dayjs) => {
		const fromString = from.format('YYYY-MM-DD HH:mm')
		if (fromString) {
			search.set('from', fromString)
			setSearch(search.toString())
		} else {
			search.delete('from')
			setSearch(search.toString())
		}
	}

	const handleToSearch = (to: Dayjs) => {
		const toString = to.format('YYYY-MM-DD HH:mm')
		if (toString) {
			search.set('to', toString)
			setSearch(search.toString())
		} else {
			search.delete('to')
			setSearch(search.toString())
		}
	}

	const colorMap = {
		IN: 'green',
		OUT: 'yellow',
		DISABLED: 'red',
		ENABLED: 'blue',
		NOT_WORKING_TIME: 'orange'
	}

	if (isLoading) {
		return <div>Yuklanmoqda...</div>
	}

	return (
		<>
			<h1 className='text-lg font-semibold mb-4'>Foydalanuvchi ma'lumotlari</h1>
			<Card
				size='small'
				className='!mb-4'>
				<Space direction='vertical'>
					<p>
						<strong>Foydalanuvchi:</strong> {data?.user?.fullName}
					</p>
					<p>
						<strong>Lavozim:</strong> {data?.user?.position}
					</p>
					<p>
						<strong>Telefon:</strong> {data?.user?.phone}
					</p>
					<p>
						<strong>Tashkilotlar:</strong>{' '}
						{data?.user?.organization?.map(org => org.name).join(', ')}
					</p>
				</Space>
			</Card>
			<Flex
				vertical
				gap={16}>
				<Flex
					vertical
					justify='space-between'
					gap={16}>
					<h2>Foydalanuvchi tarixi</h2>
					<DatePicker
						value={day}
						size={'small'}
						onChange={handleDaySearch}
						allowClear
						format=' YYYY-MM-DD'
						className={' w-full'}
					/>
					<Flex gap={16}>
						<DatePicker.TimePicker
							value={from}
							format={' HH:mm'}
							onChange={handleFromSearch}
							className={' w-full'}
							allowClear
							size={'small'}
						/>
						<DatePicker.TimePicker
							value={to}
							size={'small'}
							format={' HH:mm'}
							onChange={handleToSearch}
							className={' w-full'}
							allowClear
						/>
					</Flex>
				</Flex>
				<VirtualList
					height={500}
					itemHeight={130}
					data={data?.historyReport ?? []}
					itemKey='id'>
					{item => {
						return (
							<Card
								key={item.id}
								className='!mb-2'
								size='small'>
								<Space direction='vertical'>
									<p>
										<strong>Foydalanuvchi:</strong> {item.User.fullName}
									</p>
									<p>
										<strong>Vaqt:</strong> {new Date(item.createdAt).toLocaleString()}
									</p>
									<p>
										<strong>Holat:</strong>{' '}
										<Tag color={colorMap[item.type]}>
											{item.type === 'IN'
												? ' Hududda'
												: item.type === 'OUT'
													? ' Hududdan tashqarida'
													: item.type === 'DISABLED'
														? ' OFFLINE'
														: ' Ish vaqti emas'}
										</Tag>
									</p>
								</Space>
							</Card>
						)
					}}
				</VirtualList>
			</Flex>
		</>
	)
}
