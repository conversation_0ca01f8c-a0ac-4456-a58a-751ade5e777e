import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { PaginationUtil, PrismaService, QueryUtil } from '../../common';
import { CreateTaskStateDto, UpdateTaskStateDto } from './dto/task-state.dto';
import { Status } from '@prisma/client';

@Injectable()
export class TaskStateService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly paginationUtil: PaginationUtil,
    private readonly queryUtil: QueryUtil,
  ) {}

  async create(createTaskStateDto: CreateTaskStateDto) {
    const existingTaskStateByKey = await this.prisma.taskState.findUnique({
      where: { key: createTaskStateDto.key },
    });

    if (existingTaskStateByKey) {
      throw new BadRequestException(`Key "${createTaskStateDto.key}" allaqachon mavjud`);
    }

    const existingTaskStateByName = await this.prisma.taskState.findUnique({
      where: { name: createTaskStateDto.name },
    });

    if (existingTaskStateByName) {
      throw new BadRequestException(`Name "${createTaskStateDto.name}" allaqachon mavjud`);
    }

    const existingTaskStateByOrder = await this.prisma.taskState.findFirst({
      where: { order: createTaskStateDto.order },
    });

    if (existingTaskStateByOrder) {
      throw new BadRequestException(`Order "${createTaskStateDto.order}" allaqachon mavjud`);
    }

    const data = await this.prisma.taskState.create({ data: createTaskStateDto });
    return { message: 'Muvaffaqqiyatli yaratildi', data };
  }

  async findAll(search?: string, page?: string, limit?: string) {
    const $page = page ? parseInt(page) : 1;
    const $limit = limit ? parseInt(limit) : 10;

    const where: any = {
      ...this.queryUtil.createSearchQuery(search, ['name', 'key', 'id']),
      status: 'ACTIVE' as Status,
    };

    return this.paginationUtil.paginate(this.prisma.taskState, { page: $page, limit: $limit }, where);
  }

  async findOne(id: string) {
    const taskState = await this.prisma.taskState.findUnique({ where: { id } });
    if (!taskState) {
      throw new NotFoundException(`TaskState with ID "${id}" not found`);
    }
    return { data: taskState, status: 200, message: 'Muvaffaqqiyatli topildi' };
  }

  async update(id: string, updateTaskStateDto: UpdateTaskStateDto) {
    const taskState = await this.prisma.taskState.findUnique({
      where: { id },
    });

    if (!taskState) {
      throw new NotFoundException(`TaskState with ID "${id}" not found`);
    }

    if (updateTaskStateDto?.order) {
      const existingTaskStateByOrder = await this.prisma.taskState.findFirst({
        where: { order: updateTaskStateDto.order },
      });

      if (existingTaskStateByOrder) {
        throw new BadRequestException(`Order "${updateTaskStateDto.order}" allaqachon mavjud`);
      }
    }

    const data = await this.prisma.taskState.update({
      where: { id },
      data: {
        ...updateTaskStateDto,
        ...(updateTaskStateDto.name && { name: updateTaskStateDto.name }),
        ...(updateTaskStateDto.key && { key: updateTaskStateDto.key }),
        ...(updateTaskStateDto.order && { order: updateTaskStateDto.order }),
        ...(updateTaskStateDto.status && { status: updateTaskStateDto.status }),
      },
    });

    return { message: 'Muvaffaqqiyatli o`zgartirildi', data };
  }
}
