import { useGetDashboardDetailsPosition } from '@/config/queries/dashboard-details/get-dashboard-details'
import { useGetPositionExcel } from '@/config/queries/dashboard-details/get-dashboard-details-excel'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { Button, Table } from 'antd'
import { DownloadOutlined } from '@ant-design/icons'
import { useSearchParams } from 'react-router-dom'

const SectionStatsPositionTable = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const positionId = searchParams.get('positionId')
	const organizationId = searchParams.get('orgId')

	// Get pagination values from URL or use defaults
	const currentPage = parseInt(searchParams.get('page') || '1')
	const pageSize = parseInt(searchParams.get('limit') || '10')

	const { data: position, isLoading } = useGetDashboardDetailsPosition(positionId || '')
	const positionExcel = useGetPositionExcel()

	const handleDownloadExcel = () => {
		positionExcel.mutate({
			orgId: organizationId || undefined,
			posId: positionId || undefined
		})
	}

	const columns = [
		{
			key: '#',
			title: '#',
			render: (_: any, _rec: any, index: number) => formatTableIndex(currentPage, pageSize, index)
		},
		{
			title: 'FIO',
			dataIndex: 'fullName',
			key: 'fullName'
		},
		{
			title: 'Telefon raqami',
			dataIndex: 'phone',
			key: 'phone'
		},
		{
			title: 'Lavozimi',
			dataIndex: ['position', 'name'],
			key: 'positionName'
		},
		{
			title: 'Tashkilot',
			dataIndex: ['Organization'],
			key: 'organizationName',
			render: (orgs: any[]) => orgs?.map(org => org.name).join(', ')
		}
	]
	const handleTableChange = (pagination: any) => {
		const newPage = pagination.current
		const newPageSize = pagination.pageSize

		// Update URL parameters
		setSearchParams(prev => {
			const newParams = new URLSearchParams(prev)
			newParams.set('page', newPage.toString())
			newParams.set('limit', newPageSize.toString())
			return newParams
		})
	}

	return (
		<div className='p-4'>
			<div className='flex justify-between items-center mb-4'>
				<h1 className='text-2xl font-bold'>Hodimlar ro`yhati</h1>
				<Button
					type='primary'
					icon={<DownloadOutlined />}
					onClick={handleDownloadExcel}
					loading={positionExcel.isPending}>
					Excel yuklab olish
				</Button>
			</div>
			<Table
				columns={columns}
				dataSource={position?.data || []}
				rowKey='id'
				pagination={{
					current: currentPage,
					total: position?.meta?.total,
					showTotal: (total, range) => `${range[0]}-${range[1]} tadan ${total}  ta`,
					pageSize: position?.meta?.limit
				}}
				loading={isLoading}
				onChange={handleTableChange}
			/>
		</div>
	)
}
export default SectionStatsPositionTable
