import { Controller, Get, Query } from '@nestjs/common';
import { CoordsService } from './coords.service';
import { query } from 'express';
import { ProtectedRoute } from 'src/common';

@Controller('coords')
export class CoordsController {
  constructor(private readonly coordsService: CoordsService) {}

  @ProtectedRoute({
    isPublic: true,
  })
  @Get()
  async getCoords(@Query() query: { regionId?: string; districtId?: string; sectionId?: string }) {
    const { regionId, districtId, sectionId } = query;

    return await this.coordsService.getCoords(regionId, districtId, sectionId);
  }
}
