import { Bar } from 'react-chartjs-2'
import { Chart as ChartJS, BarElement, CategoryScale, LinearScale, Tooltip, Legend } from 'chart.js'

ChartJS.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend)

type TData = {
	title: string
	value: number
}

type TStatsChart = {
	title: string
	value: number
	data: TData[]
}

const getRandomColor = () => {
	const r = Math.floor(Math.random() * 256)
	const g = Math.floor(Math.random() * 256)
	const b = Math.floor(Math.random() * 256)
	return `rgba(${r}, ${g}, ${b}, 0.5)`
}

export const StatsChart = ({ title, value, data }: TStatsChart) => {
	const chartData = {
		labels: data.map(item => item.title),
		datasets: [
			{
				label: title,
				data: data.map(item => item.value),
				backgroundColor: data.map(() => getRandomColor()),
				borderColor: data.map(() => getRandomColor().replace('0.5', '1')),
				borderWidth: 1
			}
		]
	}

	const options = {
		responsive: true,
		maintainAspectRatio: false,
		plugins: {
			legend: { display: true },
			tooltip: { enabled: true }
		},
		scales: {
			x: { beginAtZero: true },
			y: { beginAtZero: true }
		}
	}

	return (
		<div style={{ height: '400px', width: '700px' }}>
			<h2>{title}</h2>
			<p>{value}</p>
			<Bar
				data={chartData}
				options={options}
			/>
		</div>
	)
}
