DATABASE_URL="postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}?schema=public&connection_limit=20"
PORT=${BACKEND_PORT}

JWT_SECRET="THIS_IS_SECRET_KEY_FOR_MNAZORAT_NODEJS_APPLICATION_LEAST_32_CHARACTERS"
JWT_EXPIRED="1h"
JWT_REFRESH_SECRET="ENERGY24_JWT_REFRESH_SECRET_KEY"
JWT_REFRESH_EXPIRED="1d"

REDIS_HOST=${GLOBAL_REDIS_HOST}
REDIS_PORT=${GLOBAL_REDIS_PORT}
REDIS_PASSWORD_ENV=${GLOBAL_REDIS_PASSWORD}

NODE_ENV=${BACKEND_NODE_ENV}
TELEGRAM_BOT_TOKEN=${BACKEND_TELEGRAM_BOT_TOKEN}
TELEGRAM_BOT_WEBHOOK_URL=${BACKEND_TELEGRAM_BOT_WEBHOOK_URL}
