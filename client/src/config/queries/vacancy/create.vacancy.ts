import { axiosPrivate } from '@/config/api'
import { vacancyEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { notification } from 'antd'
import { useSearchParams } from 'react-router-dom'

export type TVacancy = {
	id?: string
	organizationId: string
	organizationTypePositionId: string
	status?: string
	Organization?: {
		id: string
		name: string
		status: string
	}
	OrganizationTypePosition?: {
		id: string
		name: string
		status: string
	}
}

export const useCreateVacancy = () => {
	const queryClient = useQueryClient()
	const [searchParams] = useSearchParams()

	const page = searchParams.get('page')
	const limit = searchParams.get('limit')
	const search = searchParams.get('search')

	return useMutation({
		mutationFn: async (data: TVacancy) =>
			(await axiosPrivate.post<ApiResponse<TVacancy>>(vacancyEndpoints.all, data)).data,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [vacancyEndpoints.all] })
			queryClient.refetchQueries({
				queryKey: [vacancyEndpoints.all, page, limit, search],
				exact: true
			})
			notification.success({
				message: 'Muvaffaqiyatli yaratildi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: 'Yaratishda xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
