import { axiosPrivate } from '@/config/api'
import { faceIdEndpoints } from '@/config/api/endpoints'
import { FaceIDType } from '@/shared/types/face-id.type'
import { TPaginationWrapper } from '@/shared/types/pagination.type'
import { STATUS } from '@/shared/types/status.type'
import { useInfiniteQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'

export type FaceId = {
	id: string
	MAC: string
	IP: string
	status: STATUS
	organizationId: string
	type: FaceIDType
	Organization: {
		name: string
	}
}

export const useGetAllFaceIdByOrg = (orgId: string) => {
	const limit = 10
	const page = 1

	const [searchParams] = useSearchParams()
	const search = searchParams.get('search')

	return useInfiniteQuery({
		queryKey: [faceIdEndpoints.allByOrg(orgId), 'infinite', page, search, limit],
		queryFn: async ({ pageParam }) => {
			return (
				await axiosPrivate.get<TPaginationWrapper<FaceId[]>>(faceIdEndpoints.allByOrg(orgId), {
					params: {
						page: pageParam,
						limit,
						search
					}
				})
			).data
		},
		initialPageParam: page,
		getNextPageParam: lastPage => {
			if (lastPage?.meta?.page === lastPage?.meta?.totalPages) return undefined
			return lastPage?.meta?.page + 1
		},
		getPreviousPageParam: lastPage => {
			if (lastPage?.meta?.page === 1) return undefined
			return lastPage?.meta?.page - 1
		},
		enabled: !!orgId
	})
}
