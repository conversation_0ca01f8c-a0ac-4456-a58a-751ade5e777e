import { useGetAllAttendance } from '@/config/queries/attendance/getAll'
import { Typography, Table, Spin, Empty } from 'antd'
import { useMemo, useState } from 'react'
import { getDatesBetween } from '@/shared/utils/getDatesBetween'
import { useSearchParams } from 'react-router-dom'

import AttendanceFilter from './attendance-filter'
import { useAttendanceColumns } from './attendance-column'
import { ReportInfo } from './attendance-report-info'
import { WorkerDetails } from './worker-table'
import dayjs from 'dayjs'
import { DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'

// Dayjs setup bir marta bajariladi
dayjs.tz.setDefault(DEFAULT_TIMEZONE)

const { Title } = Typography

export const AttendanceTable = () => {
	const [searchParams] = useSearchParams()
	const startDate = searchParams.get('startDate')
	const endDate = searchParams.get('endDate')
	const { data: attendanceData, isLoading } = useGetAllAttendance()

	const [selectedWorkerId, setSelectedWorkerId] = useState<string | null>(null)

	const days = useMemo(() => {
		if (startDate && endDate) {
			return getDatesBetween(dayjs(startDate).toDate(), dayjs(endDate).toDate())
		}
		if (startDate) {
			return getDatesBetween(dayjs(startDate).toDate(), dayjs().toDate())
		}
		return []
	}, [startDate, endDate])

	const tableData = useMemo(() => {
		if (!attendanceData || !attendanceData.workers) return []

		return attendanceData.workers.map(worker => ({
			key: worker.employee.id,
			id: worker.employee.id,
			fullName: worker.employee.fullName,
			position: worker.employee.position,
			organization: worker.organization.name,
			summary: worker.summary,
			dailyReports: worker.dailyReports,
			workSchedule: worker.workSchedule,
			breakSchedule: worker.breakSchedule
		}))
	}, [attendanceData])

	const selectedWorker = useMemo(() => {
		if (!selectedWorkerId || !tableData.length) return null
		return tableData.find(worker => worker.id === selectedWorkerId) || null
	}, [selectedWorkerId, tableData])

	const columns = useAttendanceColumns(days, setSelectedWorkerId)

	return (
		<>
			<Title
				level={4}
				className='dark:text-gray-200'
				style={{ marginBottom: '20px' }}>
				Davomat hisoboti
			</Title>

			<AttendanceFilter />

			{attendanceData && attendanceData.reportPeriod && (
				<ReportInfo reportPeriod={attendanceData.reportPeriod} />
			)}

			{selectedWorker && (
				<WorkerDetails
					worker={selectedWorker}
					onClose={() => setSelectedWorkerId(null)}
				/>
			)}

			{isLoading ? (
				<div className='flex justify-center items-center py-20'>
					<Spin
						size='large'
						tip="Ma'lumotlar yuklanmoqda..."
					/>
				</div>
			) : !attendanceData || !attendanceData.workers || attendanceData.workers.length === 0 ? (
				<Empty
					description="Ma'lumotlar topilmadi"
					className='py-10 dark:text-gray-300'
				/>
			) : (
				<div className='dark:bg-gray-800 dark:text-gray-200 overflow-hidden rounded-lg'>
					<Table
						bordered
						rowKey='id'
						size='small'
						className='attendance-table overflow-auto'
						scroll={{ x: 'max-content' }}
						columns={columns as any}
						dataSource={tableData}
						pagination={{
							pageSize: 10,
							showSizeChanger: true,
							pageSizeOptions: [10, 20, 50, 100],
							showTotal: total => `Jami: ${total} xodim`
						}}
					/>
				</div>
			)}
		</>
	)
}
