import { ROLE } from '../types/role.type'

type TAction = 'create:*' | 'update:*' | 'delete:*'

export const usePermissions = () => {
	const permissions: Record<ROLE, TAction[]> = {
		[ROLE.ADMIN]: ['create:*', 'update:*', 'delete:*'],
		[ROLE.USER]: []
	}

	const hasPermission = (role: ROLE, actions: TAction[]) => {
		return actions.every(action => permissions[role]?.includes(action))
	}

	return { hasPermission }
}
