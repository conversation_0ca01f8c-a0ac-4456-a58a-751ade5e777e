model File {
    id     String @id @default(cuid())
    slug   String
    path   String
    size   Int
    status Status @default(ACTIVE)

    mimeType  String
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    User User[] @relation("Avatar")

    UserFaceId User[] @relation("FaceIdImage")

    Task Task[] @relation("files")

    CompleteTask Task[] @relation("CompleteFile")

    RecipientAnswer RecipientAnswer[]

    @@index([slug])
    @@index([path])
    InfractionReason InfractionReason[]
}
