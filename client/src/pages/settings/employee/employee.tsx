import { Button, Space, Table, notification } from 'antd'
import { useGetUsersByOrgId } from '@/config/queries/organizations/get-all.queries'
import { Eye, Copy } from 'lucide-react'
import { useState } from 'react'
import EmployeeScheduleCreate from './ui/employee-schedule-create'
import { useNavigate } from 'react-router-dom'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { TUser } from '@/config/queries/users/get-all.queries'
import { EmployeeVocationCreate } from './ui/employee-vocation-create'
import { Search } from '@/shared/components/search/search'

export type TSchedule = {
	id: string
	userId: string
}

const Employee = () => {
	const navigate = useNavigate()
	const orgId = JSON.parse(localStorage.getItem('organizationId') || '')
	const [currentPage, setCurrentPage] = useState(1)
	const [pageSize, setPageSize] = useState(10)

	const { data: employees, isLoading } = useGetUsersByOrgId(orgId || '', currentPage, pageSize)

	const [isModalOpen, setIsModalOpen] = useState(false)
	const [isVocationModalOpen, setIsVocationModalOpen] = useState(false)
	const [selectedEmployeeId, setSelectedEmployeeId] = useState<string | null>(null)
	const [userSchedule, setUserSchedule] = useState<TSchedule | null>(null)

	const handleCopyId = (id: string, e: React.MouseEvent) => {
		e.stopPropagation()
		try {
			navigator.clipboard.writeText(id)
			notification.success({
				message: 'Muvaffaqiyatli nusxa ko`chirildi'
			})
		} catch {
			notification.error({
				message: 'Nusxa ko`chirishda xatolik yuz berdi'
			})
		}
	}

	interface EmployeeRecord {
		id: string
		fullName: string
		username: string
		phone: string
		role: string
	}

	const handleRowClick = (record: EmployeeRecord) => {
		navigate(`/workspace/settings/employee/${record.id}`)
	}

	const columns = [
		{
			title: '#',
			key: '#',
			render: (_: TUser, __: TUser, index: number) => formatTableIndex(currentPage, pageSize, index)
		},
		{
			title: 'Id',
			dataIndex: 'id',
			key: 'id',
			className: 'w-20',
			render: (id: string) => (
				<Button
					type='link'
					icon={<Copy size={16} />}
					onClick={e => handleCopyId(id, e)}
					style={{ padding: 0 }}>
					<span className='text-start truncate w-20'>{id}</span>
				</Button>
			)
		},
		{
			title: 'FIO',
			dataIndex: 'fullName',
			key: 'fullName'
		},
		{
			title: 'Telefon raqami',
			dataIndex: 'username',
			key: 'username'
		},
		{
			title: "Qo'shimcha telefon raqami",
			dataIndex: 'phone',
			key: 'phone'
		},
		{
			title: 'Lavozim',
			dataIndex: 'role',
			key: 'role'
		},
		{
			key: 'isVocation',
			title: 'Tatildami?',
			render: (item: { Vacation: { state: boolean }[] }) =>
				item.Vacation?.filter((vocation: any) => vocation.status === 'ACTIVE').some(
					vocation => !!vocation.state
				)
					? 'Ha'
					: 'Yoq'
		},
		{
			title: 'Amallar',
			key: 'actions',

			render: (_: unknown, record: any) => (
				<Space>
					<Button
						type='link'
						onClick={() => handleRowClick(record as unknown as EmployeeRecord)}>
						<Eye />
					</Button>
					<Button
						onClick={() => {
							setSelectedEmployeeId(record.id)
							setIsModalOpen(true)
							setUserSchedule(record.UserWorkingSchedule)
						}}>
						+ Ish jadvali qo'shish
					</Button>
					<Button
						onClick={() => {
							setSelectedEmployeeId(record.id)
							setIsVocationModalOpen(true)
						}}>
						Tatil berish
					</Button>
				</Space>
			)
		}
	]

	return (
		<>
			<Table
				title={() => (
					<div className='flex items-center justify-between'>
						<Search />
					</div>
				)}
				pagination={{
					total: employees?.pages?.[0]?.meta?.total || 0,
					pageSize: pageSize,
					current: currentPage,
					showSizeChanger: true,
					pageSizeOptions: [5, 10, 20, 50, 100],
					onChange: (page, size) => {
						setCurrentPage(page)
						setPageSize(size)
					}
				}}
				loading={isLoading}
				dataSource={employees?.pages?.flatMap(page => page.data) || []}
				columns={columns}
				scroll={{ x: 'max-content' }}
				rowKey='id'
			/>
			<EmployeeScheduleCreate
				employeeId={selectedEmployeeId}
				open={isModalOpen}
				record={userSchedule}
				onClose={() => {
					setIsModalOpen(false)
					setSelectedEmployeeId(null)
				}}
			/>
			<EmployeeVocationCreate
				employeeId={selectedEmployeeId}
				open={isVocationModalOpen}
				onClose={() => {
					setIsVocationModalOpen(false)
					setSelectedEmployeeId(null)
				}}
			/>
		</>
	)
}
export default Employee
