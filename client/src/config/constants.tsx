import { MenuProps } from 'antd'
import { JSX } from 'react'
import { Link } from 'react-router-dom'
import {
	Building2,
	LayoutDashboard,
	Users,
	PencilLine,
	ScanFace,
	Settings,
	Building,
	GraduationCap,
	ListTodo,
	CircleCheck,
	ListFilter,
	CalendarDays,
	Plus,
	ArrowDown,
	ArrowUp,
	Users2,
	UserCog,
	Calendar,
	Map,
	SquareUser,
	ArchiveIcon,
	UserPlus,
	UserRoundPen,
	Info
} from 'lucide-react'

export interface Endpoint {
	key: string
	label: string
	title: string
	path: string
	type?: string
	icon: JSX.Element
	children?: Endpoint[]
	workspace: 'personal' | 'organization' | 'all'
	taskCreate?: boolean
	adminOnly?: boolean
}

export const clientEndpoints: Endpoint[] = [
	{
		key: 'Xarita',
		title: 'Xarita',
		label: 'Xarita',
		path: 'map',
		icon: <Map size={'1.4em'} />,
		workspace: 'organization'
	},
	{
		key: 'Dashboard',
		title: 'Dashboard',
		label: 'Dashboard',
		path: '',
		icon: <LayoutDashboard size={'1.4em'} />,
		workspace: 'organization'
	},
	{
		key: 'Sektorlar',
		title: 'Sektor<PERSON>',
		label: 'Sektor<PERSON>',
		path: 'sectors',
		icon: <Map size={'1.4em'} />,
		workspace: 'organization'
	},
	{
		key: 'Davomat',
		title: 'Davomat',
		label: 'Davomat',
		path: 'attendance',
		icon: <Calendar size={'1.4em'} />,
		workspace: 'organization',
		children: [
			{
				key: 'Tashkilotlar davomatlari',
				title: 'Tashkilotlar',
				label: 'Tashkilotlar',
				path: '',
				icon: <Building2 size={'1.4em'} />,
				workspace: 'organization'
			},
			{
				key: 'Lavozimlar',
				title: 'Lavozimlar',
				label: 'Lavozimlar',
				path: '/position',
				icon: <Users2 size={'1.4em'} />,
				workspace: 'organization'
			},
			{
				key: "Qo'shish",
				title: "Qo'shish",
				label: "Q'oshish",
				path: '/add-attendance-position',
				icon: <UserPlus size={'1.4em'} />,
				workspace: 'organization'
			}
		]
	},
	{
		key: 'Tashkilotlar',
		title: 'Tashkilotlar',
		label: 'Tashkilotlar',
		path: 'organizations',
		icon: <Building2 size={'1.4em'} />,
		workspace: 'organization'
	},

	{
		key: 'Topshiriq yaratish',
		title: 'Topshiriq yaratish',
		label: 'Topshiriq yaratish',
		path: 'task-create',
		icon: <Plus size={'1.4em'} />,
		workspace: 'organization'
	},

	{
		key: 'Topshiriqlar',
		title: 'Topshiriqlar',
		label: 'Topshiriqlar',
		path: 'tasks',
		icon: <PencilLine size={'1.4em'} />,
		workspace: 'all',
		children: [
			{
				key: 'Kirivuchi',
				title: 'Kirivuchi',
				label: 'Kirivuchi',
				path: '/tasks-incomming',
				icon: <ArrowDown size={'1.4em'} />,
				workspace: 'all'
			},
			{
				key: 'Chiquvchi',
				title: 'Chiquvchi',
				label: 'Chiquvchi',
				path: '/tasks-outgoing',
				icon: <ArrowUp size={'1.4em'} />,
				workspace: 'organization'
			},
			{
				key: 'Nazoratimdagilar',
				title: 'Nazoratimdagilar',
				label: 'Nazoratimdagilar',
				path: '/tasks-controller',
				icon: <UserRoundPen size={'1.4em'} />,
				workspace: 'personal'
			}

		]
	},
	{
		key: 'Qoidabuzarliklarim',
		title: 'Qoidabuzarliklarim',
		label: 'Qoidabuzarliklarim',
		path: 'infraction',
		icon: <Info  size={'1.4em'} />,
		workspace: 'personal'
	},
	{
		key: 'Qoidabuzarliklar',
		title: 'Qoidabuzarliklar',
		label: 'Qoidabuzarliklar',
		path: 'infraction',
		icon: <Info  size={'1.4em'} />,
		workspace: 'organization'
	},
	{
		key: 'Settings',
		title: 'Sozlamalar',
		label: 'Sozlamalar',
		path: 'settings',
		icon: <Settings size={'1.4em'} />,
		workspace: 'all',
		children: [
			{
				key: 'Ishchilar',
				title: 'Ishchilar',
				label: 'Ishchilar',
				path: '/employee',
				icon: <Users2 size={'1.4em'} />,
				workspace: 'organization'
			},
			{
				key: 'Foydalanuvchilar',
				title: 'Foydalanuvchilar',
				label: 'Foydalanuvchilar',
				path: '/users',
				icon: <Users size={'1.4em'} />,
				workspace: 'organization',
				adminOnly: true
			},
			{
				key: 'Face ID qurilmalari',
				title: 'Face ID Qurilmalari',
				label: 'Face ID Qurilmalari',
				path: '/face-id',
				icon: <ScanFace size={'1.4em'} />,
				workspace: 'organization'
				// adminOnly: true
			},
			{
				key: 'Vakansiyalar',
				title: 'Vakansiyalar',
				label: 'Vakansiyalar',
				path: '/vacancies',
				icon: <SquareUser size={'1.4em'} />,
				workspace: 'organization'
				// adminOnly: true
			},
			{
				key: 'Tashkilot-turlari',
				title: 'Tashkilot turlari',
				label: 'Tashkilot turlari',
				path: '/organization-types',
				icon: <Building size={'1.4em'} />,
				workspace: 'organization',
				adminOnly: true
			},
			{
				key: 'Tashkilot-lavozimlari',
				title: 'Tashkilot lavozimlari',
				label: 'Tashkilot lavozimlari',
				path: '/organization-position-types',
				icon: <GraduationCap size={'1.4em'} />,
				workspace: 'organization',
				adminOnly: true
			},
			{
				key: 'Topshiriq-turlari',
				title: 'Topshiriq turlari',
				label: 'Topshiriq turlari',
				path: '/task-types',
				icon: <ListTodo size={'1.4em'} />,
				workspace: 'organization',
				adminOnly: true
			},
			{
				key: 'Topshiriq holati',
				title: 'Topshiriq holati',
				label: 'Topshiriq holati',
				path: '/task-states',
				icon: <CircleCheck size={'1.4em'} />,
				workspace: 'organization',
				adminOnly: true
			},
			{
				key: 'Javob holati',
				title: 'Javob holati',
				label: 'Javob holati',
				path: '/recipient-answer-type',
				icon: <ListFilter size={'1.4em'} />,
				workspace: 'organization',
				adminOnly: true
			},
			{
				key: 'Foydalanuvchi ish jadvali',
				title: 'Foydalanuvchi ish jadvali',
				label: 'Foydalanuvchi ish jadvali',
				path: '/user-working-schedule-day',
				icon: <CalendarDays size={'1.4em'} />,
				workspace: 'organization',
				adminOnly: true
			},
			{
				key: 'Profile',
				title: 'Profile',
				label: 'Profile',
				path: '/profile',
				icon: <UserCog size={'1.4em'} />,
				workspace: 'personal'
			},
			{
				key: 'Arxiv',
				title: 'Arxiv',
				label: 'Arxiv',
				path: '/archives',
				icon: <ArchiveIcon size={'1.4em'} />,
				workspace: 'organization',
				adminOnly: true
			}
		]
	}
]

export const buildMenuItems = (
	endpoints: typeof clientEndpoints,
	parent?: Endpoint
): MenuProps['items'] => {
	return endpoints.map(item => ({
		key: item.key,
		label: item.children ? (
			item.label
		) : (
			<Link to={`${parent ? `${parent.path}` : ''}${item.path}`}>{item.label}</Link>
		),
		icon: item.icon,
		type: item.type ? item.type : 'item',
		children: item.children ? buildMenuItems(item.children, item) : undefined
	})) as MenuProps['items']
}
