import { axiosPrivate } from '@/config/api'
import { taskEndpoints } from '@/config/api/endpoints'
import { TPaginationWrapper } from '@/shared/types/pagination.type'
import { useInfiniteQuery, useQuery } from '@tanstack/react-query'
import { TTask } from './getOne'
import { useSearchParams } from 'react-router-dom'
import { useState } from 'react'

export const useGetAllTaskByOrgId = (type: string) => {
	return useQuery({
		queryKey: [taskEndpoints.allByOrg(type)],
		queryFn: async () => {
			return await axiosPrivate.get(taskEndpoints.allByOrg(type))
		}
	})
}

export const useGetTasks = (workspace: string, type: string, state?: string) => {
	const defaultLimit = 10
	const defaultPage = 1
	const [page, setPage] = useState(defaultPage)
	const [searchParams] = useSearchParams()
	const filterState = searchParams.get('filterState')
	const complatedState = searchParams.get('complatedState')
	const search = searchParams.get('search')

	const key = [
		workspace === 'personal' ? 'userTasks' : 'orgTasks',
		type,
		state,
		filterState,
		complatedState,
		search,
		page
	]

	const query = useInfiniteQuery({
		queryKey: key,
		queryFn: async ({ pageParam }) => {
			const endpoint =
				workspace === 'personal'
					? taskEndpoints.usersAllTask(type, state)
					: taskEndpoints.allByOrg(type, state)
			return (
				await axiosPrivate.get<TPaginationWrapper<TTask[]>>(endpoint, {
					params: {
						page: pageParam,
						limit: defaultLimit,
						filterState: filterState !== 'all' && type !== 'incomming' ? filterState : undefined,
						complatedState:
							filterState === 'completed' && type !== 'incomming'
								? complatedState !== 'all'
									? complatedState
									: undefined
								: undefined,
						search
					}
				})
			).data
		},
		initialPageParam: page,
		getNextPageParam: lastPage => {
			const meta = lastPage?.meta
			if (meta?.page === meta?.totalPages) return undefined
			return lastPage?.meta?.page + 1
		},
		getPreviousPageParam: lastPage => {
			if (lastPage?.meta?.page === 1) return undefined
			return lastPage?.meta?.page - 1
		}
	})

	return {
		...query,
		setPage
	}
}
