#!/bin/bash

if [ -z "$1" ]; then
  echo "Использование: $0 <имя_файла_бэкапа>"
  exit 1
fi

BACKUP_FILE=$1
LOCAL_PATH="/backups/$(basename ${BACKUP_FILE})"

# Скачивание с S3
echo "Загрузка резервной копии из S3..."
mc cp s3/${S3_BUCKET}/${S3_FOLDER}/${BACKUP_FILE} ${LOCAL_PATH}

# Восстановление из резервной копии
echo "Восстановление базы данных..."
PGPASSWORD=${DB_PASSWORD} psql -h ${DB_HOST} -p ${DB_PORT} -U ${DB_USER} -d ${DB_NAME} < ${LOCAL_PATH}

echo "Восстановление завершено"