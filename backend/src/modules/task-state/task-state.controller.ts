import { Body, Controller, Get, Param, Patch, Post, Query } from '@nestjs/common';
import { TaskStateService } from './task-state.service';
import { CreateTaskStateDto, UpdateTaskStateDto } from './dto/task-state.dto';
import { ProtectedRoute } from '../../common';

@Controller('task-state')
export class TaskStateController {
  constructor(private readonly taskStateService: TaskStateService) {}

  @ProtectedRoute({
    isPublic: false,
  })
  @Post()
  async create(@Body() createTaskStateDto: CreateTaskStateDto) {
    return await this.taskStateService.create(createTaskStateDto);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Get()
  async findAll(@Query('page') page?: string, @Query('limit') limit?: string, @Query('search') search?: string) {
    return await this.taskStateService.findAll(search, page, limit);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Get(':id')
  async findOne(@Param('id') id: string) {
    return await this.taskStateService.findOne(id);
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Patch(':id')
  async update(@Param('id') id: string, @Body() updateTaskStateDto: UpdateTaskStateDto) {
    return await this.taskStateService.update(id, updateTaskStateDto);
  }
}
