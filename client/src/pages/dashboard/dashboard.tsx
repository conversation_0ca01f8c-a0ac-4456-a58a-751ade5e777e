import { useDashboardTasksState } from '@/config/queries/dashboard/get.queries'
import { StatStates } from './ui/stats-states'
import { Col, Row, Segmented, Button } from 'antd'
import StatsPieChart from './ui/stats-pie-chart'
import { useGetOneOrganization } from '@/config/queries/organizations/getOne'
import CardsPart from './ui/card-part'
import { useEffect, useState } from 'react'
import { TOrganization } from '@/config/queries/organizations/get-all.queries'
import { removeDuplicates } from '@/shared/utils/removeDuplicatedData'
import {
	useOrganizationAttendanceStats,
	useOrganizationHierarchyStats
} from '@/config/queries/stats/get.queries'
import StatsOrganizationAttendance from './ui/stats-organization-attendance'
import NeighborhoodDashboard from './neighborhood-dashboard'
import { useSearchParams } from 'react-router-dom'

const Dashboard = () => {
	const defaultOrgId = localStorage.getItem('organizationId') || ''
	const [selectedOrgId, setSelectedOrgId] = useState(JSON.parse(defaultOrgId))
	const { data: organization, refetch } = useGetOneOrganization(
		selectedOrgId ?? JSON.parse(defaultOrgId)
	)
	const { data: organizationAttendanceStats, isLoading: isAttendanceStatsLoading } =
		useOrganizationAttendanceStats(selectedOrgId)
	const [selectedChild, setSelectedChild] = useState<any>(null)
	const taskStates = useDashboardTasksState()
	const { data: organizationHierarchyStats } = useOrganizationHierarchyStats(selectedOrgId)
	const [_, setBreadCrumbOrgs] = useState<TOrganization[]>([])
	const currrentOrganization = localStorage.getItem('organization')
	const gradeLevel = JSON.parse(currrentOrganization ?? '').grade.level
	const [selectedSegment, setSelectedSegment] = useState(
		gradeLevel >= 30 || gradeLevel === -1 ? 'dashboard' : 'mahalla'
	)

	const handleSelectChild = (child: any) => {
		setSelectedChild(child)
		setSelectedOrgId(child.id)
	}

	useEffect(() => {
		const localBreadCrumbOrgs = localStorage.getItem('breadCrumbOrgs')
		const breadCrumbOrgs = localBreadCrumbOrgs ? JSON.parse(localBreadCrumbOrgs) : []
		if (breadCrumbOrgs.length > 0) {
			setSelectedOrgId(breadCrumbOrgs[breadCrumbOrgs.length - 1]?.id)
		} else {
			setSelectedOrgId(JSON.parse(defaultOrgId))
		}

		setBreadCrumbOrgs(localBreadCrumbOrgs ? JSON.parse(localBreadCrumbOrgs) : [])
	}, [defaultOrgId])

	useEffect(() => {
		let res = []
		const localBreadCrumbOrgs = localStorage.getItem('breadCrumbOrgs')
		const breadCrumbOrgs = localBreadCrumbOrgs ? JSON.parse(localBreadCrumbOrgs) : []

		if (localBreadCrumbOrgs) {
			res = removeDuplicates([
				...breadCrumbOrgs,
				{ name: organization?.name, id: organization?.id }
			])
		} else {
			res = [{ name: organization?.name, id: organization?.id }]
		}

		const currentBreadCrumbIndex = breadCrumbOrgs.findIndex(
			(org: Pick<TOrganization, 'id' | 'name'>) => org.id === organization?.id
		)

		if (currentBreadCrumbIndex > 0) {
			res = res.slice(0, currentBreadCrumbIndex + 1)
		}

		localStorage.setItem('breadCrumbOrgs', JSON.stringify(res))

		refetch()
		setBreadCrumbOrgs(localBreadCrumbOrgs ? JSON.parse(localBreadCrumbOrgs) : [])
	}, [organization, selectedOrgId, refetch])

	const renderContent = () => {
		if (selectedSegment === 'mahalla') {
			return (
				<>
					<NeighborhoodDashboard />
				</>
			)
		}

		return (
			<>
				<CardsPart organizationStats={organizationHierarchyStats} />
				<Row gutter={16}>
					<Col
						xs={24}
						xl={12}>
						<StatsOrganizationAttendance
							organizationAttendanceStats={organizationAttendanceStats}
							isLoading={isAttendanceStatsLoading}
						/>
					</Col>
					<Col
						xs={24}
						xl={12}>
						<StatsPieChart />
					</Col>
				</Row>
				<div className='my-10'>
					<StatStates
						title='Task holati'
						stats={
							Array.isArray(taskStates.data)
								? taskStates.data.map((state: { name: string; count: string }) => ({
										label: state.name,
										value: 0,
										count: Number(state.count),
										color: '#fff',
										percent:
											Array.isArray(taskStates.data) && organizationHierarchyStats?.tasks.total
												? (Number(state.count) * 100) /
													Number(organizationHierarchyStats?.tasks.total)
												: 0
									}))
								: []
						}
					/>
				</div>
			</>
		)
	}

	const [searchParams, setSearchParams] = useSearchParams()
	const xOrgId = localStorage.getItem('organizationId')

	return (
		<>
			<Segmented
				className='mb-4'
				options={
					gradeLevel >= 30 || gradeLevel === -1
						? [{ label: 'Dashboard', value: 'dashboard' }]
						: [
								{ label: 'Dashboard', value: 'dashboard' },
								{ label: 'Mahalla kesimida', value: 'mahalla' }
							]
				}
				value={selectedSegment}
				onChange={value => {
					setSelectedSegment(value.toString())

					if (organization) {
						searchParams.set('grade', JSON.parse(currrentOrganization ?? '')?.grade?.level)
					}

					searchParams.set('orgId', JSON.parse(xOrgId ?? ''))
					setSearchParams(searchParams)
				}}
			/>

			{selectedSegment === 'dashboard' && (
				<div className='mb-4 mt-3'>
					<div className='flex gap-2 mb-2'>
						{organization?.Children?.map(child => (
							<Button
								size='small'
								key={child.id}
								type={selectedChild?.id === child.id ? 'primary' : 'default'}
								onClick={() => handleSelectChild(child)}>
								<span className='text-start truncate w-full max-w-32'>{child.name}</span>
							</Button>
						))}
					</div>

					{selectedChild && selectedChild.Children && (
						<div className='flex gap-2'>
							{selectedChild.Children.map((subChild: any) => (
								<Button
									key={subChild.id}
									type={selectedOrgId === subChild.id ? 'primary' : 'default'}
									onClick={() => handleSelectChild(subChild)}>
									{subChild.name}
								</Button>
							))}
						</div>
					)}
				</div>
			)}

			{renderContent()}
		</>
	)
}
export default Dashboard
