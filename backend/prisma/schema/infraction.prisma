model Infraction {
  id               String             @id @default(cuid())
  user             User               @relation(fields: [userId], references: [id])
  userId           String
  name             String?
  description      String?
  data             Json?
  infractionDate   DateTime           @default(now())
  InfractionReason InfractionReason[]
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
}

model InfractionReason {
  id           String     @id @default(cuid())
  file         File?      @relation(fields: [fileId], references: [id])
  fileId       String?
  name        String?
  description   String?
  Infraction   Infraction @relation(fields: [infractionId], references: [id])
  infractionId String
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt
}
