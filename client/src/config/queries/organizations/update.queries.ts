import { axiosPrivate } from '@/config/api'
import { organizationEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { TOrganization } from './get-all.queries'
import { AxiosError, AxiosResponse } from 'axios'
import { TApiErrorResponse } from '@/shared/types/api-error.type'
import { TApiUpdate } from '@/shared/types/api-update.type'
import { notification } from 'antd'

export type TOrganizationRequest = Partial<Omit<TOrganization, 'id' | 'children'>>
export type TOrganizationResponse = TOrganization

export const useUpdateOrganization = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({ id, data }: TApiUpdate<TOrganizationRequest>) => {
			return await axiosPrivate.put(organizationEndpoints.one(id), data)
		},
		onSuccess: async (response: AxiosResponse<TOrganizationResponse>) => {
			console.log(response)
			queryClient.invalidateQueries({ queryKey: [organizationEndpoints.all] })
			notification.success({ message: "Tashkilot muaffaqiyatli o'zgartirildi!" })
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response?.data.message)
			notification.error({ message: "Tashkilot o'zgartirishda xatolik yuz berdi!" })
		}
	})
}

export const useDeActiveOrganization = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({ id, data }: TApiUpdate<TOrganizationRequest>) => {
			return await axiosPrivate.put(organizationEndpoints.organizationDeactivate(id), data)
		},
		onSuccess: async (response: AxiosResponse<TOrganizationResponse>) => {
			console.log(response)
			queryClient.invalidateQueries({ queryKey: [organizationEndpoints.all] })
			notification.success({ message: "O'chirildi!" })
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response?.data.message)
			notification.error({ message: "Tashkilot o'chirishda xatolik yuz berdi!" })
		}
	})
}
