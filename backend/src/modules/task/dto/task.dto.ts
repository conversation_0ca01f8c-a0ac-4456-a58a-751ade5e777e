import { Type } from 'class-transformer';
import { ArrayMinSize, IsArray, IsDateString, IsOptional, IsString, ValidateNested, ValidateIf } from 'class-validator';

class Recipient {
  @IsOptional()
  @IsString()
  organizationId?: string;

  @IsOptional()
  @IsArray()
  @Type(() => String)
  userId?: string[];

  @IsOptional()
  @IsString()
  positionId?: string;
}

export class CreateTaskDto {
  @IsString()
  name: string;

  @IsString()
  description: string;

  @IsString()
  @IsOptional()
  taskTypeId?: string;

  @IsDateString()
  @IsOptional()
  dueDate?: Date;

  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1)
  @Type(() => Recipient)
  recipients: Recipient[];

  @IsString()
  @IsOptional()
  parentId?: string;

  @IsString({ each: true })
  @IsOptional()
  files?: string[];

  @IsString()
  @IsOptional()
  controllerId?: string;
}
