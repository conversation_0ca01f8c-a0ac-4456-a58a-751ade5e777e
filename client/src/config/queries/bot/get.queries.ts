import { axiosPrivate } from '@/config/api'
import { botEndpoint } from '@/config/api/endpoints'
import { useInfiniteQuery } from '@tanstack/react-query'

export const useGetAllBots = () => {
	return useInfiniteQuery({
		queryKey: [botEndpoint.all, 'infinite'],

		queryFn: async () => {
			return (await axiosPrivate.get(botEndpoint.all)).data
		},

		initialPageParam: 1,
		getNextPageParam: lastPage => {
			if (lastPage?.meta?.page === lastPage?.meta?.totalPages) return undefined
			return lastPage?.meta?.page + 1
		},
		getPreviousPageParam: lastPage => {
			if (lastPage?.meta?.page === 1) return undefined
			return lastPage?.meta?.page - 1
		}
	})
}
//
