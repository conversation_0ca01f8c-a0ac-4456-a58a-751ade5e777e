import { exec } from 'child_process';

const seeds = [
  './seed/organizationType.ts',
  './seed/position.ts',
  './seed/user.ts',
  './seed/region.ts',
  './seed/district.ts',
  './seed/section.ts',
  './seed/grade.ts',
  './seed/organization.ts',
];

async function runSeeds() {
  for (const seed of seeds) {
    console.log(`Запуск ${seed}...`);
    await new Promise<void>((resolve, reject) => {
      // Используем обратные кавычки для интерполяции переменных
      // Правильно экранируем JSON параметры
      exec(`yarn ts-node --project ./tsconfig.dev.json ${seed}`, (error, stdout, stderr) => {
        if (error) {
          console.error(`Ошибка в ${seed}:`);
          reject(error);
          return;
        }
        console.log(stdout);
        console.error(stderr);
        resolve();
      });
    });
  }

  console.log('✅ Все сиды выполнены!');
}

runSeeds().catch((err) => {
  console.error('Ошибка выполнения сидов:', err);
  process.exit(1);
});
