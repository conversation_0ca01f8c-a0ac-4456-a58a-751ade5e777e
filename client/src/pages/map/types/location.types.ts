export interface Organization {
	id: string
	name: string
}

export interface Position {
	name: string
}

export interface LocationItem {
	id: string
	fullName: string
	isInArea: boolean
	type: 'IN' | 'OUT' | 'DISABLED'
	position: Position
	organization: Organization[]
	createdAt: string
}

export interface LocationStats {
	total: number
	active: number
	inactive: number
}

export type StatusFilter = 'all' | 'active' | 'inactive'
export type SortBy = 'name' | 'position' | 'time' | 'status'
export type SortOrder = 'asc' | 'desc'
