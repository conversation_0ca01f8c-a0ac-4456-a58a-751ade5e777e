import { TreeSelect } from 'antd'
import { useEffect, useState, useCallback } from 'react'
import { useSearchParams } from 'react-router-dom'
import {
	useGetAllOrganizations,
	TOrganization
} from '@/config/queries/organizations/get-all.queries'
import { fetchOrganizationChildren } from './fetch-organization-children'

interface TreeNode {
	title: string
	value: string
	key: string
	isLeaf?: boolean
	pId?: string
}

interface Props {
	setSearchParams: ReturnType<typeof useSearchParams>[1]
}

export default function AttendanceSelectTree({ setSearchParams }: Props) {
	const organizations = useGetAllOrganizations()
	const [treeData, setTreeData] = useState<TreeNode[]>([])
	const [loadingKeys, setLoadingKeys] = useState<string[]>([]) // Махсус node'лар ясаш учун функция
	useEffect(() => {
		if (organizations.data?.pages) {
			const newData: TreeNode[] = []

			// Добавляем опцию "Hammasi" в начало
			const selectAllOption: TreeNode = {
				title: 'Hammasi',
				value: 'all',
				key: 'all',
				isLeaf: true,
				pId: undefined
			}
			newData.push(selectAllOption)

			organizations.data.pages.forEach(org => {
				org.data?.forEach((item: TOrganization) => {
					// Асосий organization node'и
					const mainNode: TreeNode = {
						title: item.name,
						value: item.id,
						key: item.id,
						isLeaf: false,
						pId: undefined
					}
					newData.push(mainNode)
				})
			})

			setTreeData(prev => {
				const keys = new Set(prev.map(node => node.key))
				const merged = [...prev, ...newData.filter(node => !keys.has(node.key))]
				return merged
			})
		}
	}, [organizations.data])

	const updateTreeWithChildren = useCallback((nodeId: string, children: TOrganization[]) => {
		setTreeData(prev => {
			const hasExistingChildren = prev.some(node => node.pId === nodeId)
			if (hasExistingChildren) {
				return prev.map(node =>
					node.key === nodeId ? { ...node, isLeaf: children.length === 0 } : node
				)
			}

			// Добавляем новые дочерние узлы
			const childNodes: TreeNode[] = []

			// Если есть дочерние элементы, добавляем опцию "Hammasi" для них
			if (children.length > 0) {
				const selectAllChildrenOption: TreeNode = {
					title: 'Hammasi',
					value: `all_children_${nodeId}`,
					key: `all_children_${nodeId}`,
					isLeaf: true,
					pId: nodeId
				}
				childNodes.push(selectAllChildrenOption)
			}

			// Добавляем обычные дочерние узлы
			children.forEach(child => {
				childNodes.push({
					title: child.name,
					value: child.id,
					key: child.id,
					isLeaf: false,
					pId: nodeId
				})
			})

			return [
				...prev.map(node =>
					node.key === nodeId ? { ...node, isLeaf: children.length === 0 } : node
				),
				...childNodes
			]
		})
	}, [])

	const loadData = useCallback(
		async (treeNode: any) => {
			const { key: id } = treeNode
			const hasChildren = treeData.some(node => node.pId === id)
			if (hasChildren) return
			if (loadingKeys.includes(id)) return
			setLoadingKeys(keys => [...keys, id])
			try {
				const children = await fetchOrganizationChildren(id)
				if (children && children.length > 0) {
					updateTreeWithChildren(id, children)
				} else {
					setTreeData(prev =>
						prev.map(node => (node.key === id ? { ...node, isLeaf: true } : node))
					)
				}
			} catch (e) {
				console.error('Error loading organization children:', e)
				setTreeData(prev => prev.map(node => (node.key === id ? { ...node, isLeaf: true } : node)))
			} finally {
				setLoadingKeys(keys => keys.filter(k => k !== id))
			}
		},
		[treeData, loadingKeys, updateTreeWithChildren]
	)

	const handleChange = useCallback(
		(value: any) => {
			setSearchParams(prev => {
				const newParams = { ...Object.fromEntries(prev) }
				if (!value || value.length === 0) {
					prev.delete('organizationId')
					return prev
				} else {
					const processedValues = value as string[]
					const finalValues: string[] = []

					processedValues.forEach(val => {
						if (val === 'all') {
							// Если выбрано "Hammasi" (общее), берем все корневые организации
							const allOrgIds = treeData
								.filter(
									node => node.key !== 'all' && !node.pId && !node.key.startsWith('all_children_')
								)
								.map(node => node.value)
							finalValues.push(...allOrgIds)
						} else if (val.startsWith('all_children_')) {
							// Если выбрано "Hammasi" для дочерних элементов
							const parentId = val.replace('all_children_', '')
							const childIds = treeData
								.filter(node => node.pId === parentId && !node.key.startsWith('all_children_'))
								.map(node => node.value)
							finalValues.push(...childIds)
						} else {
							// Обычный ID организации
							finalValues.push(val)
						}
					})

					// Убираем дубликаты
					const uniqueValues = [...new Set(finalValues)]
					newParams.organizationId = uniqueValues.join(',')
				}
				return newParams
			})
		},
		[setSearchParams, treeData]
	)

	return (
		<TreeSelect
			treeDataSimpleMode={{
				id: 'value',
				pId: 'pId'
			}}
			allowClear
			loadData={loadData}
			treeCheckable
			multiple
			onPopupScroll={e => {
				const target = e.target as HTMLDivElement
				if (target.scrollTop + target.clientHeight >= target.scrollHeight) {
					organizations.fetchNextPage()
				}
			}}
			onChange={handleChange}
			showCheckedStrategy={TreeSelect.SHOW_PARENT}
			treeData={treeData}
			className='w-full flex-1'
			placeholder='Tashkilotlarni tanlang'
		/>
	)
}
