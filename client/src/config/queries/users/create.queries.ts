import { axiosPrivate } from '@/config/api'
import { userConnectOrganizationEndpoints, usersEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { TUser } from './get-all.queries'
import { ApiResponse } from '../types'
import { notification } from 'antd'
import { useSearchParams } from 'react-router-dom'

export type TUserRequest = Omit<TUser, 'id'>
export type TUserResponse = TUser

export const useCreateUser = () => {
	const queryClient = useQueryClient()
	const [searchParams] = useSearchParams()

	const page = searchParams.get('page')
	const limit = searchParams.get('limit')
	const search = searchParams.get('search')

	return useMutation({
		mutationFn: async (data: TUserRequest) =>
			(await axiosPrivate.post<ApiResponse<TUserResponse>>(usersEndpoints.all, data)).data,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [usersEndpoints.all, page, limit, search] })
			notification.success({
				message: 'Muvaffaqiyatli yaratildi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: 'Yaratishda xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}

export const useConnectUserOrg = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async (data: any) => {
			const { organizationId, userId, ...requestBody } = data
			return (
				await axiosPrivate.post<ApiResponse<any>>(
					userConnectOrganizationEndpoints.all(organizationId),
					requestBody
				)
			).data
		},
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: [userConnectOrganizationEndpoints.all]
			})
			notification.success({
				message: 'Yangi foydalanuvchi muvaffaqiyatli yaratildi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: 'Foydalanuvchi yaratishda xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}

export const useConnectFindUser = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async (data: { organizationId: string; userId: string }) =>
			(
				await axiosPrivate.patch<ApiResponse<any>>(
					userConnectOrganizationEndpoints.connectFindUser(data.organizationId, data.userId),
					data
				)
			).data,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: [userConnectOrganizationEndpoints.connectFindUser]
			})
			notification.success({
				message: 'Muvaffaqiyatli yaratildi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: 'Yaratishda xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
