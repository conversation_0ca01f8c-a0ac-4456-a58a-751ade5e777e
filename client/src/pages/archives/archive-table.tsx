import {
	useForceDele<PERSON>,
	useGetArchiveModel,
	useUnArchive
} from '@/config/queries/archives/archives.query'
import {
	ClearOutlined,
	DeleteOutlined,
	DisconnectOutlined,
	DownloadOutlined,
	FilterOutlined,
	ReloadOutlined,
	SearchOutlined
} from '@ant-design/icons'
import {
	Badge,
	Button,
	Card,
	Dropdown,
	Flex,
	Input,
	type MenuProps,
	Popconfirm,
	Space,
	Table,
	Tag,
	Tooltip,
	Typography
} from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { useCallback, useEffect, useMemo, useState } from 'react'

const { Title, Text } = Typography

interface Props {
	modeName: string
}

function prettifyKey(key: string) {
	return key
		.replace(/_/g, ' ')
		.replace(/([a-z])([A-Z])/g, '$1 $2')
		.replace(/^./, s => s.toUpperCase())
}

function getColumnIcon(key: string) {
	if (key === 'id' || key.toLowerCase().includes('id')) return '🆔'
	if (key.toLowerCase().includes('name') || key.toLowerCase().includes('title')) return '📝'
	if (key.toLowerCase().includes('date') || key.toLowerCase().includes('time')) return '📅'
	if (key.toLowerCase().includes('status')) return '📊'
	if (key.toLowerCase().includes('email')) return '📧'
	if (key.toLowerCase().includes('phone')) return '📞'
	return '📋'
}

export default function ArchiveTable({ modeName }: Props) {
	const { data, isLoading, refetch } = useGetArchiveModel(modeName)
	const keys = useMemo(() => Object.keys(data?.[0] ?? {}), [data])
	const unArchive = useUnArchive()
	const forceDelete = useForceDelete()

	// Search states
	const [inputValue, setInputValue] = useState<string>('')
	const [debouncedSearchText, setDebouncedSearchText] = useState<string>('')
	const [_, setIsSearching] = useState<boolean>(false)

	// Debounce hook
	useEffect(() => {
		setIsSearching(true)
		const timeoutId = setTimeout(() => {
			setDebouncedSearchText(inputValue)
			setIsSearching(false)
		}, 300) // 300ms debounce

		return () => {
			clearTimeout(timeoutId)
		}
	}, [inputValue])

	// Memoized filtered data with debounced search
	const filteredData = useMemo(() => {
		if (!debouncedSearchText.trim()) return data ?? []

		const lowercasedValue = debouncedSearchText.toLowerCase()
		return (
			data?.filter(item =>
				keys.some(key => {
					const value = item[key]
					return (
						value !== null &&
						value !== undefined &&
						String(value).toLowerCase().includes(lowercasedValue)
					)
				})
			) ?? []
		)
	}, [data, debouncedSearchText, keys])

	const handleUnarchive = useCallback(
		(rowId: string) => {
			unArchive.mutate(
				{
					modelName: modeName,
					rowId: rowId
				},
				{
					onSuccess: () => {
						refetch()
					}
				}
			)
		},
		[unArchive, modeName, refetch]
	)

	const handleForceDelete = useCallback(
		(rowId: string) => {
			forceDelete.mutate(
				{
					modelName: modeName,
					rowId: rowId
				},
				{
					onSuccess: () => {
						refetch()
					}
				}
			)
		},
		[forceDelete, modeName, refetch]
	)

	const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
		setInputValue(e.target.value)
	}, [])

	const handleClearSearch = useCallback(() => {
		setInputValue('')
		setDebouncedSearchText('')
	}, [])

	const exportMenuItems: MenuProps['items'] = [
		{
			key: 'csv',
			label: 'CSV formatda',
			icon: <DownloadOutlined />
		},
		{
			key: 'excel',
			label: 'Excel formatda',
			icon: <DownloadOutlined />
		}
	]

	const columns: ColumnsType<any> = [
		{
			key: '#',
			fixed: 'left' as const,
			width: 60,
			render: (_: any, __: any, index: number) => (
				<div className='flex items-center justify-center'>
					<Badge
						count={index + 1}
						style={{
							backgroundColor: '#f0f0f0',
							color: '#666',
							fontSize: '12px',
							minWidth: '20px',
							height: '20px',
							lineHeight: '20px'
						}}
					/>
				</div>
			)
		},
		...keys.map((key, idx) => {
			// Width calculator function
			const getColumnWidth = (columnKey: string) => {
				const lowerKey = columnKey.toLowerCase()

				// ID columns - fixed width
				if (lowerKey === 'id' || lowerKey.includes('id')) return 180

				// Date/time columns
				if (
					lowerKey.includes('date') ||
					lowerKey.includes('time') ||
					lowerKey.includes('created') ||
					lowerKey.includes('updated')
				)
					return 160

				// Status columns
				if (lowerKey.includes('status') || lowerKey.includes('state')) return 120

				// Email columns
				if (lowerKey.includes('email')) return 200

				// Phone columns
				if (lowerKey.includes('phone') || lowerKey.includes('tel')) return 140

				// Name/title columns - wider
				if (
					lowerKey.includes('name') ||
					lowerKey.includes('title') ||
					lowerKey.includes('description')
				)
					return 250

				// URL columns
				if (lowerKey.includes('url') || lowerKey.includes('link')) return 200

				// Boolean columns
				if (lowerKey.includes('is_') || lowerKey.includes('has_') || lowerKey.includes('can_'))
					return 100

				// Count/number columns
				if (lowerKey.includes('count') || lowerKey.includes('amount') || lowerKey.includes('price'))
					return 120

				// Default width
				return 150
			}

			return {
				title: (
					<div className='flex items-center gap-2'>
						<span style={{ fontSize: '14px' }}>{getColumnIcon(key)}</span>
						<span className='font-semibold'>{prettifyKey(key)}</span>
					</div>
				),
				className: 'whitespace-normal',
				dataIndex: key,
				width: getColumnWidth(key),
				key,
				ellipsis: {
					showTitle: false
				},
				fixed: idx === 0 || idx === 1 ? ('left' as const) : undefined,
				sorter: (a: Record<string, string>, b: any) => {
					const valueA = a[key]
					const valueB = b[key]

					if (typeof valueA === 'string' && typeof valueB === 'string') {
						return valueA.localeCompare(valueB)
					}

					return (valueA ?? '') > (valueB ?? '') ? 1 : -1
				},
				render(value: string | boolean | null | undefined) {
					const lowerKey = key.toLowerCase()

					if (value === null || value === undefined || value === '') {
						return (
							<div className='flex items-center justify-center'>
								<Tag
									color='default'
									style={{ margin: 0, borderRadius: '12px' }}>
									<Text
										type='secondary'
										style={{ fontSize: '12px' }}>
										—
									</Text>
								</Tag>
							</div>
						)
					}

					// ID columns
					if (lowerKey === 'id' || lowerKey.includes('id')) {
						return (
							<Tag
								color='blue'
								style={{
									fontFamily: 'monospace',
									fontSize: '10px',
									borderRadius: '6px',
									maxWidth: '160px',
									overflow: 'hidden',
									textOverflow: 'ellipsis'
								}}>
								{String(value).length > 15 ? `${String(value).substring(0, 15)}...` : value}
							</Tag>
						)
					}

					// Date/time columns
					if (
						lowerKey.includes('date') ||
						lowerKey.includes('time') ||
						lowerKey.includes('created') ||
						lowerKey.includes('updated')
					) {
						return (
							<Tag
								color='green'
								style={{ fontSize: '11px', borderRadius: '6px' }}>
								{new Date(String(value)).toLocaleDateString('uz-UZ')}
							</Tag>
						)
					}

					// Status columns
					if (lowerKey.includes('status') || lowerKey.includes('state')) {
						const statusColors: { [key: string]: string } = {
							active: 'green',
							inactive: 'red',
							pending: 'orange',
							completed: 'blue',
							draft: 'gray'
						}
						const color = statusColors[String(value).toLowerCase()] || 'default'
						return (
							<Tag
								color={color}
								style={{ borderRadius: '6px' }}>
								{value}
							</Tag>
						)
					}

					// Boolean columns
					if (lowerKey.includes('is_') || lowerKey.includes('has_') || lowerKey.includes('can_')) {
						return (
							<Tag
								color={value === 'true' || value === true ? 'green' : 'red'}
								style={{ borderRadius: '6px' }}>
								{value === 'true' || value === true ? '✓ Ha' : "✗ Yo'q"}
							</Tag>
						)
					}

					// Email columns
					if (lowerKey.includes('email')) {
						return (
							<Tooltip title={String(value)}>
								<Tag
									color='purple'
									style={{
										borderRadius: '6px',
										maxWidth: '180px',
										overflow: 'hidden',
										textOverflow: 'ellipsis'
									}}>
									{String(value).length > 20 ? `${String(value).substring(0, 20)}...` : value}
								</Tag>
							</Tooltip>
						)
					}

					// URL columns
					if (lowerKey.includes('url') || lowerKey.includes('link')) {
						return (
							<Tooltip title={String(value)}>
								<Tag
									color='cyan'
									style={{
										borderRadius: '6px',
										maxWidth: '180px',
										overflow: 'hidden',
										textOverflow: 'ellipsis'
									}}>
									{String(value).length > 25 ? `${String(value).substring(0, 25)}...` : value}
								</Tag>
							</Tooltip>
						)
					}

					// Long text content
					const stringValue = String(value)
					if (stringValue.length > 50) {
						return (
							<Tooltip
								title={stringValue}
								placement='topLeft'>
								<div className='text-sm hover:text-blue-600 transition-colors cursor-default line-clamp-2'>
									{stringValue.substring(0, 50)}...
								</div>
							</Tooltip>
						)
					}

					// Default render
					return (
						<Tooltip
							title={stringValue}
							placement='topLeft'>
							<div className='text-sm hover:text-blue-600 transition-colors cursor-default'>
								{stringValue}
							</div>
						</Tooltip>
					)
				}
			}
		}),
		{
			title: (
				<div className='flex items-center gap-2 justify-center'>
					<span>⚡</span>
					<span className='font-semibold'>Amallar</span>
				</div>
			),
			key: 'actions',
			fixed: 'right' as const,
			width: 120,
			render: (_: any, row: any) => (
				<Space
					size='small'
					className='flex justify-center'>
					<Tooltip title='Arxivdan qaytarish'>
						<Button
							type='primary'
							icon={<DisconnectOutlined />}
							size='small'
							onClick={() => handleUnarchive(row.id)}
							loading={unArchive.isPending}
							style={{
								borderRadius: '8px',
								background: 'linear-gradient(135deg, #52c41a, #73d13d)'
							}}
						/>
					</Tooltip>
					<Popconfirm
						title='Ochirib tashlashni tasdiqlaysizmi?'
						description='Bu amal qaytarib bo`lmaydi!'
						okText='Ha'
						cancelText='Yo`q'
						okButtonProps={{
							danger: true,
							loading: forceDelete.isPending,
							style: { borderRadius: '8px' }
						}}
						cancelButtonProps={{
							style: { borderRadius: '8px' }
						}}
						onConfirm={() => handleForceDelete(row.id)}>
						<Tooltip title="Butunlay o'chirish">
							<Button
								type='primary'
								danger
								icon={<DeleteOutlined />}
								size='small'
								style={{
									borderRadius: '8px',
									background: 'linear-gradient(135deg, #ff4d4f, #ff7875)'
								}}
							/>
						</Tooltip>
					</Popconfirm>
				</Space>
			)
		}
	]

	return (
		<div className='p-4'>
			<Card
				style={{
					borderRadius: '16px',
					boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
					border: '1px solid #f0f0f0'
				}}>
				{/* Header */}
				<div className='mb-6'>
					<div className='flex items-center gap-3 mb-4'>
						<div className='w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center'>
							<span className='text-white text-lg'>📦</span>
						</div>
						<div>
							<Title
								level={4}
								style={{ margin: 0, color: '#1f2937' }}>
								Arxivlangan ma'lumotlar
							</Title>
							<Text
								type='secondary'
								style={{ fontSize: '14px' }}>
								{modeName} modeli uchun arxiv
							</Text>
						</div>
					</div>

					{/* Controls */}
					<Flex
						justify='space-between'
						align='middle'
						gap={16}
						wrap='wrap'>
						<div className='flex items-center gap-3'>
							<Input.Search
								placeholder='Ma`lumotlarni qidirish...'
								value={inputValue}
								onChange={handleSearchChange}
								onSearch={value => setInputValue(value)}
								style={{
									width: 320,
									borderRadius: '12px'
								}}
								size='large'
								allowClear
								prefix={<SearchOutlined style={{ color: '#1890ff' }} />}
							/>
							<Badge
								count={filteredData.length}
								showZero>
								<Button
									icon={<FilterOutlined />}
									style={{ borderRadius: '12px' }}
									size='large'>
									Filter
								</Button>
							</Badge>
						</div>

						<Space size='middle'>
							<Button
								onClick={handleClearSearch}
								icon={<ClearOutlined />}
								size='large'
								style={{ borderRadius: '12px' }}>
								Tozalash
							</Button>
							<Dropdown
								menu={{ items: exportMenuItems }}
								placement='bottomRight'>
								<Button
									icon={<DownloadOutlined />}
									size='large'
									style={{ borderRadius: '12px' }}>
									Eksport
								</Button>
							</Dropdown>
							<Button
								onClick={() => refetch()}
								type='primary'
								loading={isLoading}
								size='large'
								style={{
									borderRadius: '12px',
									background: 'linear-gradient(135deg, #1890ff, #40a9ff)'
								}}
								icon={<ReloadOutlined />}>
								Yangilash
							</Button>
						</Space>
					</Flex>
				</div>

				{/* Statistics */}
				<div className='mb-4 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-100'>
					<div className='flex items-center justify-between'>
						<div className='flex items-center gap-4'>
							<div className='text-center'>
								<div className='text-2xl font-bold text-blue-600'>{filteredData.length}</div>
								<div className='text-sm text-gray-600'>Jami qatorlar</div>
							</div>
							<div className='w-px h-8 bg-gray-300'></div>
							<div className='text-center'>
								<div className='text-2xl font-bold text-purple-600'>{keys.length}</div>
								<div className='text-sm text-gray-600'>Ustunlar soni</div>
							</div>
						</div>
						{debouncedSearchText && (
							<Tag
								color='blue'
								style={{ borderRadius: '12px' }}>
								<SearchOutlined /> Qidiruv: "{debouncedSearchText}"
							</Tag>
						)}
					</div>
				</div>

				{/* Table */}
				<Table
					size='middle'
					dataSource={filteredData}
					loading={isLoading || unArchive.isPending || forceDelete.isPending}
					columns={columns}
					rowKey={row => row.id ?? row.key ?? row[keys[0]]}
					pagination={{
						defaultPageSize: 20,
						showSizeChanger: true,
						pageSizeOptions: [10, 20, 50, 100],
						position: ['bottomRight'],
						showTotal: (total, range) => (
							<span className='text-gray-600'>
								<strong>
									{range[0]}-{range[1]}
								</strong>{' '}
								dan <strong>{total}</strong> ta
							</span>
						),
						style: { marginTop: '16px' }
					}}
					scroll={{ x: 'max-content', y: 600 }}
					bordered
					sticky
					rowClassName={(_, index) =>
						`hover:bg-gradient-to-r hover:from-blue-50 hover:to-transparent transition-all duration-200 ${
							index % 2 === 0 ? 'bg-gray-50/30' : 'bg-white'
						}`
					}
					style={{
						borderRadius: '12px',
						overflow: 'hidden'
					}}
				/>
			</Card>
		</div>
	)
}
