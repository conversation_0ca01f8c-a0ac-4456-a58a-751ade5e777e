import SubTaskCreateForm from './ui/subtask-create-form'
import { useState } from 'react'

export type TRecipent = {
	organizationId: string
	userId: string[]
}

const SubTaskCreate = () => {
	const [recipents, setRecipents] = useState<TRecipent[]>([
		{
			organizationId: '',
			userId: []
		}
	])

	return (
		<SubTaskCreateForm
			recipents={recipents}
			setRecipents={setRecipents}
		/>
	)
}

export default SubTaskCreate
