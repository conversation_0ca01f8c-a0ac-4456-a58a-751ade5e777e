import { useGetUserOne } from '@/config/queries/users/get-all.queries'
import { Link, useNavigate, useParams } from 'react-router-dom'
import { Avatar, Button, Card, Descriptions, Form, Modal, Select, Tabs } from 'antd'
import { EditOutlined, UserOutlined } from '@ant-design/icons'
import { getImageUrl } from '@/shared/utils/getImageUrl'
import ScheduleTable from '../../employee/ui/schedule-table'
import { Attendance } from '../tabs/attendance'
import { Organizations } from '../tabs/organizations'
import EmployeeScheduleCreate from '../../employee/ui/employee-schedule-create'
import { useState } from 'react'
import { useGetUserWorkingSchedule } from '@/config/queries/user-working-schedule/get-queries'
import { Vocation } from '../tabs/vocation'
import { useGetLowGradeOrganizations } from '@/config/queries/organizations/get-low-grads.queries'
import { useForm } from 'antd/es/form/Form'
import { useAddUsersToOrganization } from '@/config/queries/organizations/add-users.queries'
import { UsersModal } from './users-modal'
import { useUsersModalStore } from '../utils/users-modal-store'
import { useDeleteUser } from '@/config/queries/users/delete.queries.ts'

const { Item } = Form

const locations = {
	IN: 'Hududda',
	OUT: 'Hududda emas',
	OFFLINE: 'Offline'
}

const UserDetails = () => {
	const { id } = useParams()
	const { data: user } = useGetUserOne(id!)
	const orgId = localStorage.getItem('organizationId')
	const { data: organizations, isLoading } = useGetLowGradeOrganizations(JSON.parse(orgId ?? ''))
	const navigate = useNavigate()
	const [isModalOpen, setIsModalOpen] = useState(false)
	const { data: scheduleData } = useGetUserWorkingSchedule()
	const [isOrgModalOpen, setIsOrgModalOpen] = useState(false)
	const [form] = useForm()
	const { mutateAsync: addUserToOrg } = useAddUsersToOrganization(JSON.parse(orgId ?? ''))
	const { onOpen } = useUsersModalStore()

	const userData = user

	const deleteUser = useDeleteUser()

	const onFinish = (values: any) => {
		addUserToOrg({ orgId: values.organization, data: { workers: [id!] }, who: 'workers' }).then(
			() => setIsOrgModalOpen(false)
		)
	}

	const handleEditUser = () => {
		if (userData) {
			onOpen(userData)
		}
	}

	return (
		<div style={{ padding: '24px' }}>
			<Card
				title="Foydalanuvchi ma'lumotlari"
				extra={
					<Button
						type='primary'
						icon={<EditOutlined />}
						onClick={handleEditUser}>
						Tahrirlash
					</Button>
				}>
				<div style={{ display: 'flex', gap: '24px', marginBottom: '24px' }}>
					<Avatar
						size={200}
						icon={<UserOutlined />}
						src={getImageUrl(userData?.avatar?.path ?? '')}
					/>
					<div>
						<h2>{userData?.fullName}</h2>
						<p>@{userData?.username}</p>
					</div>
					<Button
						type='primary'
						onClick={() => navigate(`/workspace/map/location-user/${userData?.id}`)}>
						Xaritada ko'rish
					</Button>
					<Button
						type='primary'
						danger
						onClick={() => userData && deleteUser.mutate(userData.id)}>
						O'chirish
					</Button>
				</div>
				<div className='w-full flex justify-between gap-x-5'>
					<div className='w-[32%]'>
						<Descriptions
							bordered
							column={1}>
							<Descriptions.Item label='FIO'>{userData?.fullName}</Descriptions.Item>
							<Descriptions.Item label='Telefon raqami'>{userData?.username}</Descriptions.Item>
							<Descriptions.Item label="Qo'shimcha telefon raqami">
								{userData?.phone}
							</Descriptions.Item>
							<Descriptions.Item label='Asosiy tashkiloti'>
								<Link to={`/workspace/organizations/${userData?.MainOrganization?.id}`}>
									{userData?.MainOrganization?.name}
								</Link>
							</Descriptions.Item>
							<Descriptions.Item label="Ma'sul">
								{Array.isArray(userData?.ResponsibleFor) ? userData.ResponsibleFor[0]?.name : ''}
							</Descriptions.Item>
							<Descriptions.Item label='Lavozim'>
								{(userData?.position as { name: string })?.name}
							</Descriptions.Item>
							<Descriptions.Item label='IMEI'>{userData?.imei}</Descriptions.Item>
							<Descriptions.Item label='Ta`tildami'>
								{userData?.isinVocation ? 'Ha' : 'Yo`q'}
							</Descriptions.Item>
							{!!userData?.completedTaskCount && !!userData.inCompletedTaskCount ? (
								<Descriptions.Item label='Topshiriqlar statistikasi'>
									{((userData.completedTaskCount + userData.inCompletedTaskCount) *
										userData.completedTaskCount) /
										100}
									%
								</Descriptions.Item>
							) : (
								<Descriptions.Item label='Topshiriqlar statistikasi'>
									Topshiriqlar yo`q
								</Descriptions.Item>
							)}
							<Descriptions.Item label='Loqatsiyasi'>
								{locations[userData?.gpsStatus as keyof typeof locations]}
							</Descriptions.Item>
						</Descriptions>
					</div>
					<div className='flex-auto'>
						<Tabs
							type='card'
							items={[
								{
									label: 'Tashkilotlar',
									key: '3',
									children: (
										<div>
											<div className='flex items-center justify-between'>
												<h1>Tashkilotlar</h1>
												<Button
													onClick={() => setIsOrgModalOpen(true)}
													type='primary'>
													Tashkilotga biriktirish
												</Button>
											</div>

											<Modal
												title='Tashkilotni tanlash'
												open={isOrgModalOpen}
												okText='Qo`shish'
												onOk={() => form.submit()}
												onCancel={() => setIsOrgModalOpen(false)}>
												<Form
													form={form}
													layout='vertical'
													onFinish={onFinish}>
													<Item
														name='organization'
														label='Tashkilot'
														rules={[{ required: true, message: 'Tashkilotni tanlang' }]}>
														<Select
															loading={isLoading}
															options={organizations}
															fieldNames={{ label: 'name', value: 'id' }}
														/>
													</Item>
												</Form>
											</Modal>

											<Organizations organizations={userData?.Organization ?? []} />
										</div>
									)
								},
								{
									label: 'Ish jadvali',
									key: '2',
									children: (
										<div>
											<div className='flex items-center justify-between'>
												<h1 className='font-bold'>Ish jadvali</h1>
												<Button
													onClick={() => setIsModalOpen(true)}
													type='primary'>
													Yangi ish kuni biriktirish
												</Button>
												{id && (
													<EmployeeScheduleCreate
														employeeId={id}
														open={isModalOpen}
														// @ts-ignore
														record={scheduleData?.data?.[0]}
														onClose={() => {
															setIsModalOpen(false)
														}}
													/>
												)}
											</div>
											<ScheduleTable />
										</div>
									)
								},
								{ label: 'Davomat', key: '1', children: <Attendance /> },
								{ label: 'Ta`til tarixi', key: '4', children: <Vocation /> }
							]}
						/>
					</div>
				</div>
			</Card>

			{/* Users Modal for editing */}
			<UsersModal hideAddButton />
		</div>
	)
}
export default UserDetails
