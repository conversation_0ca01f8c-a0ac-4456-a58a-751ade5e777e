Stack trace:
Frame         Function      Args
0007FFFF7860  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF6760) msys-2.0.dll+0x1FE8E
0007FFFF7860  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF7B38) msys-2.0.dll+0x67F9
0007FFFF7860  000210046832 (000210286019, 0007FFFF7718, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF7860  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF7860  000210068E24 (0007FFFF7870, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF7B40  00021006A225 (0007FFFF7870, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8B41C0000 ntdll.dll
7FF8B2F90000 KERNEL32.DLL
7FF8B1830000 KERNELBASE.dll
7FF8B25E0000 USER32.dll
000210040000 msys-2.0.dll
7FF8B1320000 win32u.dll
7FF8B28F0000 GDI32.dll
7FF8B1C00000 gdi32full.dll
7FF8B1350000 msvcp_win.dll
7FF8B16E0000 ucrtbase.dll
7FF8B2090000 advapi32.dll
7FF8B2840000 msvcrt.dll
7FF8B3250000 sechost.dll
7FF8B30E0000 RPCRT4.dll
7FF8B0820000 CRYPTBASE.DLL
7FF8B1640000 bcryptPrimitives.dll
7FF8B1F50000 IMM32.DLL
7FF86CD00000 windhawk.dll
