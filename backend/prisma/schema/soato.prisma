model Region {
  id       String     @id @default(cuid())
  name     String
  District District[]
  status   Status     @default(ACTIVE)

  Organization Organization[]
}

enum Status {
  ACTIVE
  INACTIVE
}

model District {
  id       String @id @default(cuid())
  name     String
  regionId String
  status   Status @default(ACTIVE)

  region  Region    @relation(fields: [regionId], references: [id])
  Section Section[]

  Organization Organization[]
}

model Section {
  id         String @id @default(cuid())
  name       String
  districtId String
  status     Status @default(ACTIVE)

  district District @relation(fields: [districtId], references: [id])

  Organization Organization[]
}
