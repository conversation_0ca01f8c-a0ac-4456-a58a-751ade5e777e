import { Card } from 'antd'

interface NeighborhoodStatCardProps {
	title: string
	value: string
	icon: React.ReactNode
	onClick?: () => void
}
const NeighborhoodStatCard = ({ title, value, icon, onClick }: NeighborhoodStatCardProps) => {
	return (
		<Card
			className='border shadow-sm hover:cursor-pointer'
			onClick={onClick}>
			<div className='p-2'>
				<div className='flex justify-between items-center'>
					<div className='w-full'>
						<h3 className='text-2xl font-medium text-gray-500 mb-4 dark:text-white border-b border-gray-300 pb-2 w-full'>
							{title}
						</h3>

						<div className='flex items-center justify-between gap-30'>
							<p className='text-4xl font-bold text-gray-700 dark:text-white'>{value}</p>
							<div>{icon}</div>
						</div>
					</div>
				</div>
			</div>
		</Card>
	)
}
export default NeighborhoodStatCard
