import { Button, Form, Modal } from 'antd'
import { useEffect } from 'react'
import { PlusOutlined } from '@ant-design/icons'
import { useOrganizationPositionTypesModalStore } from '../utils/organization-position-types-store'
import {
	OrganizationPositionTypes,
	useCreateOrganizationPositionType
} from '@/config/queries/organization-position-types/create.queries'
import { useUpdateOrganizationPositionType } from '@/config/queries/organization-position-types/update.queries'
import OrganizationPositionTypesForm from './organization-position-types-form'

const OrganizationPositionTypesModal = () => {
	const { data, open, onClose } = useOrganizationPositionTypesModalStore()
	const [form] = Form.useForm()
	const create = useCreateOrganizationPositionType()
	const update = useUpdateOrganizationPositionType()

	const close = () => {
		form.resetFields()
		onClose()
	}

	useEffect(() => {
		if (data) {
			form.setFieldsValue(data)
		}
	}, [data, form])
	const onFinish = (formData: OrganizationPositionTypes) => {
		if (!data) {
			create.mutateAsync({ ...formData }).then(close)
		} else {
			if (data.id) {
				update.mutateAsync({ id: data.id, ...formData }).then(close)
			}
		}
	}
	return (
		<div className='flex items-center justify-end'>
			<Button
				type='primary'
				icon={<PlusOutlined />}
				onClick={() => useOrganizationPositionTypesModalStore.setState({ open: true })}>
				Tashkilot lavozimini qo'shish
			</Button>
			<Modal
				open={open}
				onCancel={close}
				destroyOnClose
				onOk={form.submit}
				title={`Tashkilot lavozimini${data ? 'ni tahrirlash' : "ni qo'shish"}`}
				okText={data ? 'Tahrirlash' : "Qo'shish"}
				cancelText='Bekor qilish'
				modalRender={node => (
					<Form
						layout={'vertical'}
						size='large'
						onFinish={onFinish}
						form={form}>
						{node}
					</Form>
				)}>
				<OrganizationPositionTypesForm />
			</Modal>
		</div>
	)
}
export default OrganizationPositionTypesModal
