<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="dataSourceStorageLocal" created-in="WS-251.26927.40">
    <data-source name="dev_postgres@localhost" uuid="1bf3ad6d-8de4-4b9a-8373-1606be6936e6">
      <database-info product="PostgreSQL" version="15.13 (Debian 15.13-1.pgdg120+1)" jdbc-version="4.2" driver-name="PostgreSQL JDBC Driver" driver-version="42.7.3" dbms="POSTGRES" exact-version="15.13" exact-driver-version="42.7">
        <identifier-quote-string>&quot;</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="exact" />
      <secret-storage>master_key</secret-storage>
      <user-name>dev_postgres</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="database" qname="@">
            <node kind="schema" qname="@" />
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
  </component>
</project>