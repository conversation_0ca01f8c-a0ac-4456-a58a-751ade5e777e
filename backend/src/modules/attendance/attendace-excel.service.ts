import { Injectable } from '@nestjs/common';
import * as ExcelJS from 'exceljs';
import { join } from 'path';
import * as fs from 'fs';
import { AttendanceService } from './attendance.service';
import * as dayjs from 'dayjs';
import { TimezoneHelper, DEFAULT_TIMEZONE } from '../../helpers/timezone.helper';

// Dayjs setup bir marta bajariladi
dayjs.tz.setDefault(DEFAULT_TIMEZONE);

@Injectable()
export class AttendanceExcelService {
  constructor(private readonly attendanceService: AttendanceService) {}

  async generateAttendanceReportExcel(orgIds: string[], startDate?: string, endDate?: string): Promise<string> {
    try {
      // Получаем данные отчета через метод getAttendanceReport
      const reportData = await this.attendanceService.getAttendanceReport(orgIds, startDate, endDate);

      if (!reportData || !reportData.workers || reportData.workers.length === 0) {
        throw new Error("Hisobot ma'lumotlari topilmadi");
      }

      // Создаем новую рабочую книгу Excel
      const workbook = new ExcelJS.Workbook();

      // Устанавливаем метаданные документа
      workbook.creator = 'Monitoring Nazorat';
      workbook.lastModifiedBy = 'Monitoring Nazorat';
      workbook.created = TimezoneHelper.now().toDate();
      workbook.modified = TimezoneHelper.now().toDate();

      // Создаем лист с подробным отчетом о посещаемости
      this.createDetailedAttendanceReportSheet(
        workbook,
        reportData.workers,
        TimezoneHelper.formatLocal(reportData.reportPeriod.startDate, 'YYYY-MM-DD'),
        TimezoneHelper.formatLocal(reportData.reportPeriod.endDate, 'YYYY-MM-DD'),
      );

      // Создаем лист со сводным отчетом
      this.createSummaryReportSheet(
        workbook,
        reportData.workers,
        TimezoneHelper.formatLocal(reportData.reportPeriod.startDate, 'YYYY-MM-DD'),
        TimezoneHelper.formatLocal(reportData.reportPeriod.endDate, 'YYYY-MM-DD'),
      );

      // Создаем папку для хранения файлов Excel, если она не существует
      const excelDir = join(process.cwd(), 'uploads', 'excel');
      if (!fs.existsSync(excelDir)) {
        fs.mkdirSync(excelDir, { recursive: true });
      }

      // Сохраняем Excel-файл с текущей датой в названии
      const fileName = `davomat_hisoboti_${TimezoneHelper.now().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`;
      const filePath = join(excelDir, fileName);

      await workbook.xlsx.writeFile(filePath);

      return filePath;
    } catch (error) {
      throw new Error(`Excel fayl yaratishda xatolik: ${error.message}`);
    }
  }

  async generateAttendanceReportByOrganizationTypePositionExcel(
    orgId: string,
    positionId: string,
    startDate?: string,
    endDate?: string,
    page: number = 1,
    limit: number = 20,
  ): Promise<string> {
    try {
      // Получаем данные отчета через метод getAttendanceReportByOrganizationTypePosition
      const reportData = await this.attendanceService.getAttendanceReportByOrganizationTypePosition(
        orgId,
        positionId,
        startDate,
        endDate,
        page,
        limit,
      );

      if (!reportData || !reportData.workers || reportData.workers.length === 0) {
        throw new Error("Hisobot ma'lumotlari topilmadi");
      }

      // Создаем новую рабочую книгу Excel
      const workbook = new ExcelJS.Workbook();

      // Устанавливаем метаданные документа
      workbook.creator = 'Monitoring Nazorat';
      workbook.lastModifiedBy = 'Monitoring Nazorat';
      workbook.created = TimezoneHelper.now().toDate();
      workbook.modified = TimezoneHelper.now().toDate();

      // Создаем лист с подробным отчетом о посещаемости для конкретной позиции
      this.createDetailedAttendanceReportSheetByPosition(
        workbook,
        reportData.workers,
        reportData.position,
        TimezoneHelper.formatLocal(reportData.reportPeriod.startDate, 'YYYY-MM-DD'),
        TimezoneHelper.formatLocal(reportData.reportPeriod.endDate, 'YYYY-MM-DD'),
      );

      // Создаем лист со сводным отчетом для конкретной позиции
      this.createSummaryReportSheetByPosition(
        workbook,
        reportData.workers,
        reportData.position,
        TimezoneHelper.formatLocal(reportData.reportPeriod.startDate, 'YYYY-MM-DD'),
        TimezoneHelper.formatLocal(reportData.reportPeriod.endDate, 'YYYY-MM-DD'),
      );

      // Создаем папку для хранения файлов Excel, если она не существует
      const excelDir = join(process.cwd(), 'uploads', 'excel');
      if (!fs.existsSync(excelDir)) {
        fs.mkdirSync(excelDir, { recursive: true });
      }

      // Сохраняем Excel-файл с текущей датой в названии
      const fileName = `lavozim_davomat_hisoboti_${TimezoneHelper.now().format('YYYY-MM-DD_HH-mm-ss')}.xlsx`;
      const filePath = join(excelDir, fileName);

      await workbook.xlsx.writeFile(filePath);

      return filePath;
    } catch (error) {
      throw new Error(`Excel fayl yaratishda xatolik: ${error.message}`);
    }
  }

  private getAllUniqueDates(employeesData: any[], startDate: string, endDate: string): string[] {
    const datesSet = new Set<string>();

    // Барча ходимларнинг саналарини тўплаш
    employeesData.forEach((employee) => {
      employee.dailyReports.forEach((report) => {
        const dateKey = TimezoneHelper.formatLocal(report.date, 'YYYY-MM-DD');
        datesSet.add(dateKey);
      });
    });

    // Саналарни сортлаш
    const sortedDates = Array.from(datesSet).sort();
    return sortedDates;
  }

  private createDetailedAttendanceReportSheet(
    workbook: ExcelJS.Workbook,
    employeesData: any[],
    startDate: string,
    endDate: string,
  ) {
    // Создаем лист для подробного отчета
    const worksheet = workbook.addWorksheet('Batafsil hisobot', {
      properties: {
        tabColor: { argb: '2F75B5' },
        defaultRowHeight: 25, // Увеличили высоту строк с 20 до 25
      },
    });

    // Получаем все уникальные даты из отчетов
    const allDates = this.getAllUniqueDates(employeesData, startDate, endDate);

    // Заголовок отчета
    const titleRow = worksheet.addRow(['Xodimlarning Batafsil Davomat Hisoboti']);
    titleRow.font = { size: 16, bold: true };
    titleRow.height = 35;

    // Информация о периоде
    const periodRow = worksheet.addRow([`Davr: ${startDate} - ${endDate}`]);
    periodRow.font = { size: 12, bold: true };
    periodRow.height = 25;

    // Пустая строка для разделения
    worksheet.addRow([]);

    // Создаем заголовки - основные колонки плюс новые колонки времени плюс динамические даты
    const baseHeaders = ['№', 'Tashkilot', 'F.I.O', 'Lavozim', 'Reja vaqt\n(umumiy)', 'Fakt vaqt\n(umumiy)'];

    // Сначала добавляем заголовки дат (без подзаголовков)
    const dateHeaders = [];
    allDates.forEach((date) => {
      const formattedDate = dayjs(date).format('DD.MM');
      const dayName = this.getDayName(dayjs(date).day());
      dateHeaders.push(`${formattedDate}\n${dayName}`);
      dateHeaders.push(''); // Пустые для объединения
      dateHeaders.push(''); // Пустые для объединения
    });

    const allHeaders = [...baseHeaders, ...dateHeaders];
    const headerRow = worksheet.addRow(allHeaders);

    // Создаем второй ряд заголовков для подзаголовков
    const subHeaders = ['', '', '', '', '', '']; // Пустые для основных колонок (6 штук)
    allDates.forEach(() => {
      subHeaders.push('Kelish', 'Ketish', 'Ishlagan');
    });
    const subHeaderRow = worksheet.addRow(subHeaders);

    // Стили для основных заголовков
    headerRow.eachCell((cell, colNumber) => {
      cell.font = { bold: true, size: 14, color: { argb: 'FFFFFF' } }; // Увеличили размер шрифта заголовков
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '2F75B5' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };

      // Устанавливаем ширину колонок
      if (colNumber <= 6) {
        // Основные колонки
        const widths = [5, 20, 25, 18, 15, 15]; // Қўшилган 2 та вақт устуни учун кенглик
        worksheet.getColumn(colNumber).width = widths[colNumber - 1];
      } else {
        // Колонки дат - делаем шире для 3 подколонок (увеличили с 6 до 12 для лучшей читаемости)
        worksheet.getColumn(colNumber).width = 12;
      }
    });

    // Стили для подзаголовков
    subHeaderRow.eachCell((cell, colNumber) => {
      if (colNumber > 6) {
        cell.font = { bold: true, size: 12, color: { argb: 'FFFFFF' } }; // Увеличили размер шрифта подзаголовков с 9 до 12

        // Определяем тип подколонки для элегантных цветов
        const subColIndex = (colNumber - 7) % 3;
        let bgColor = '2F75B5'; // По умолчанию синий

        if (subColIndex === 0) {
          bgColor = '6C9BD1'; // Мягкий синий для "Kelish"
        } else if (subColIndex === 1) {
          bgColor = 'E8A87C'; // Мягкий оранжевый для "Ketish"
        } else {
          bgColor = '98D8C8'; // Мягкий зеленый для "Ishlagan"
        }

        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: bgColor },
        };
        cell.alignment = { horizontal: 'center', vertical: 'middle' };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      } else {
        // Для основных колонок делаем такой же стиль как основной заголовок
        cell.font = { bold: true, size: 14, color: { argb: 'FFFFFF' } }; // Увеличили размер шрифта
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '2F75B5' },
        };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      }
    });

    headerRow.height = 35; // Увеличили высоту заголовков с 30 до 35
    subHeaderRow.height = 30; // Увеличили высоту подзаголовков с 25 до 30

    // Объединяем ячейки для заголовка и периода
    const totalColumns = baseHeaders.length + allDates.length * 3;
    worksheet.mergeCells(1, 1, 1, totalColumns);
    worksheet.mergeCells(2, 1, 2, totalColumns);

    // Объединяем ячейки для дат (каждая дата занимает 3 колонки)
    let currentCol = 7; // Начинаем после основных колонок (6 шт)
    allDates.forEach(() => {
      worksheet.mergeCells(4, currentCol, 4, currentCol + 2); // Объединяем 3 колонки для каждой даты
      currentCol += 3;
    });

    // Объединяем основные заголовки по вертикали
    worksheet.mergeCells(4, 1, 5, 1); // №
    worksheet.mergeCells(4, 2, 5, 2); // Tashkilot
    worksheet.mergeCells(4, 3, 5, 3); // F.I.O
    worksheet.mergeCells(4, 4, 5, 4); // Lavozim
    worksheet.mergeCells(4, 5, 5, 5); // Reja vaqt (umumiy)
    worksheet.mergeCells(4, 6, 5, 6); // Fakt vaqt (umumiy)

    // Добавляем данные по каждому сотруднику
    employeesData.forEach((employee, index) => {
      // Вычисляем общее время
      let totalPlanMinutes = 0;
      let totalFactMinutes = 0;

      employee.dailyReports.forEach((report) => {
        if (report.isWorkDay) {
          // Считаем плановое время (например, 8 часов = 480 минут)
          totalPlanMinutes += 480; // Можно изменить в зависимости от требований

          // Считаем фактическое время
          if (report.minutesWorked > 0) {
            totalFactMinutes += report.minutesWorked;
          }
        }
      });

      const rowData = [
        index + 1,
        employee.organization?.name || '-',
        employee.employee?.fullName || '-',
        employee.employee?.position?.name || '-',
        this.formatMinutesToTime(totalPlanMinutes), // Reja vaqt (umumiy)
        this.formatMinutesToTime(totalFactMinutes), // Fakt vaqt (umumiy)
      ];

      // Создаем карту отчетов по датам для быстрого поиска
      const reportsByDate = new Map();
      employee.dailyReports.forEach((report) => {
        const dateKey = TimezoneHelper.formatLocal(report.date, 'YYYY-MM-DD');
        reportsByDate.set(dateKey, report);
      });

      // Добавляем данные для каждой даты
      allDates.forEach((date) => {
        const dateKey = dayjs(date).format('YYYY-MM-DD');
        const report = reportsByDate.get(dateKey);

        if (report) {
          // Проверяем статус дня
          if (!report.isWorkDay) {
            // Если не рабочий день - объединяем в одну ячейку "Dam"
            rowData.push('Dam', '', '');
          } else if (!report.cameToWork) {
            // Если не пришел на работу - объединяем в одну ячейку "Yo'q"
            rowData.push("Yo'q", '', '');
          } else {
            // Обычный рабочий день с данными
            const enterTime = report.actualEnterTime ? TimezoneHelper.formatTime(report.actualEnterTime) : '-';
            const exitTime = report.actualExitTime ? TimezoneHelper.formatTime(report.actualExitTime) : '-';
            const workTime = report.minutesWorked > 0 ? this.formatMinutesToTime(report.minutesWorked) : '0:00';

            rowData.push(enterTime, exitTime, workTime);
          }
        } else {
          // Если нет данных для этой даты
          rowData.push('-', '-', '-');
        }
      });

      const dataRow = worksheet.addRow(rowData);

      // Настраиваем стили для строки данных
      dataRow.eachCell((cell, colNumber) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
        cell.font = { size: 12, bold: true }; // Увеличили размер шрифта с 8 до 12

        // Применяем цвета для дат в зависимости от статуса
        if (colNumber > 6) {
          const dateIndex = Math.floor((colNumber - 7) / 3);
          const dateKey = dayjs(allDates[dateIndex]).format('YYYY-MM-DD');
          const report = reportsByDate.get(dateKey);

          // Определяем базовый цвет для типа столбца
          const subColIndex = (colNumber - 7) % 3;
          let baseColor = '';

          if (subColIndex === 0) {
            baseColor = '6C9BD1'; // Мягкий синий для "Kelish"
          } else if (subColIndex === 1) {
            baseColor = 'E8A87C'; // Мягкий оранжевый для "Ketish"
          } else {
            baseColor = '98D8C8'; // Мягкий зеленый для "Ishlagan"
          }

          // Если нет данных - используем светлую версию базового цвета
          const lightColor = this.lightenColor(baseColor);
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: lightColor },
          };
        }
      });

      // Объединяем ячейки для "Yo'q" и "Dam"
      allDates.forEach((date, dateIndex) => {
        const dateKey = dayjs(date).format('YYYY-MM-DD');
        const report = reportsByDate.get(dateKey);

        if (report) {
          if (!report.isWorkDay || !report.cameToWork) {
            // Объединяем 3 колонки для "Dam" или "Yo'q"
            const startCol = 7 + dateIndex * 3; // 7 - начало колонок дат
            const endCol = startCol + 2;
            worksheet.mergeCells(dataRow.number, startCol, dataRow.number, endCol);
          }
        }
      });
    });

    // Закрепляем заголовок и первые 6 колонок
    worksheet.views = [{ state: 'frozen', xSplit: 6, ySplit: 5 }];
  }

  private createSummaryReportSheet(
    workbook: ExcelJS.Workbook,
    employeesData: any[],
    startDate: string,
    endDate: string,
  ) {
    // Создаем лист для сводного отчета
    const worksheet = workbook.addWorksheet('Umumiy hisobot', {
      properties: {
        tabColor: { argb: '4472C4' },
        defaultRowHeight: 25, // Увеличили высоту строк с 20 до 25
      },
    });

    // Заголовок отчета
    const titleRow = worksheet.addRow(['Xodimlarning Umumiy Davomat Hisoboti']);
    titleRow.font = { size: 16, bold: true };
    titleRow.height = 35;

    // Информация о периоде
    const periodRow = worksheet.addRow([`Davr: ${startDate} - ${endDate}`]);
    periodRow.font = { size: 12, bold: true };
    periodRow.height = 25;

    // Пустая строка для разделения
    worksheet.addRow([]);

    // Создаем заголовки таблицы
    const headerRow = worksheet.addRow([
      '№',
      'Tashkilot',
      'F.I.O',
      'Lavozim',
      'Ish kunlari',
      'Kelgan kunlar',
      'Davomat (foiz)',
      'Reja vaqt (soat)',
      'Fakt vaqt (soat)',
      'Kechikishlar soni',
      'Kechikish (minut)',
      'Erta ketishlar soni',
      'Erta ketish (minut)',
    ]);

    // Стили для заголовков
    headerRow.eachCell((cell) => {
      cell.font = { bold: true, size: 14, color: { argb: 'FFFFFF' } }; // Увеличили размер шрифта заголовков сводки
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '4472C4' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };
    });
    headerRow.height = 35; // Увеличили высоту заголовков сводки с 30 до 35

    // Объединяем ячейки для заголовка и периода
    worksheet.mergeCells(1, 1, 1, 13);
    worksheet.mergeCells(2, 1, 2, 13);

    // Добавляем сводные данные по сотрудникам
    employeesData.forEach((employee, index) => {
      const summary = employee.summary;

      // Вычисляем количество опозданий и ранних уходов
      const lateCount = employee.dailyReports.filter((report) => report.isLate).length;
      const earlyExitCount = employee.dailyReports.filter((report) => report.isEarlyExit).length;

      // Добавляем строку с данными сотрудника
      const dataRow = worksheet.addRow([
        index + 1,
        employee.organization?.name || '-',
        employee.employee?.fullName || '-',
        employee.employee?.position?.name || '-',
        summary.totalWorkDays || 0,
        summary.totalAttendanceDays || 0,
        `${summary.attendanceRate ? summary.attendanceRate.toFixed(1) : 0}%`,
        summary.planHours || '0:00',
        summary.factHours || '0:00',
        lateCount,
        summary.totalLateMinutes || 0,
        earlyExitCount,
        summary.totalEarlyExitMinutes || 0,
      ]);

      // Настраиваем стили для строки данных
      dataRow.eachCell((cell) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };

        cell.alignment = { vertical: 'middle', horizontal: 'center' };
        cell.font = { size: 11, bold: false }; // Добавили увеличенный размер шрифта для данных
      });

      // Выделяем цветом строки сотрудников с низкой посещаемостью
      if (summary.attendanceRate < 80) {
        dataRow.eachCell((cell) => {
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FFC7CE' }, // Светло-красный для низкой посещаемости
          };
        });
      }
    });

    // Устанавливаем ширину колонок
    worksheet.getColumn(1).width = 5; // №
    worksheet.getColumn(2).width = 25; // Tashkilot
    worksheet.getColumn(3).width = 30; // F.I.O.
    worksheet.getColumn(4).width = 20; // Lavozim
    worksheet.getColumn(5).width = 15; // Ish kunlari
    worksheet.getColumn(6).width = 15; // Kelgan kunlar
    worksheet.getColumn(7).width = 15; // Davomat (foiz)
    worksheet.getColumn(8).width = 15; // Reja vaqt
    worksheet.getColumn(9).width = 15; // Fakt vaqt
    worksheet.getColumn(10).width = 15; // Kechikishlar soni
    worksheet.getColumn(11).width = 15; // Kechikish (minut)
    worksheet.getColumn(12).width = 15; // Erta ketishlar soni
    worksheet.getColumn(13).width = 15; // Erta ketish (minut)

    // Закрепляем заголовок
    worksheet.views = [{ state: 'frozen', xSplit: 0, ySplit: 4 }];
  }

  private createDetailedAttendanceReportSheetByPosition(
    workbook: ExcelJS.Workbook,
    employeesData: any[],
    position: any,
    startDate: string,
    endDate: string,
  ) {
    // Создаем лист для подробного отчета
    const worksheet = workbook.addWorksheet('Batafsil hisobot', {
      properties: {
        tabColor: { argb: '2F75B5' },
        defaultRowHeight: 25,
      },
    });

    // Получаем все уникальные даты из отчетов
    const allDates = this.getAllUniqueDates(employeesData, startDate, endDate);

    // Заголовок отчета
    const titleRow = worksheet.addRow([`Lavozim: ${position.name} - Batafsil Davomat Hisoboti`]);
    titleRow.font = { size: 16, bold: true };
    titleRow.height = 35;

    // Информация о периоде
    const periodRow = worksheet.addRow([`Davr: ${startDate} - ${endDate}`]);
    periodRow.font = { size: 12, bold: true };
    periodRow.height = 25;

    // Пустая строка для разделения
    worksheet.addRow([]);

    // Создаем заголовки - основные колонки плюс динамические даты
    const baseHeaders = ['№', 'Tashkilot', 'F.I.O', 'Telefon', 'Reja vaqt\n(umumiy)', 'Fakt vaqt\n(umumiy)'];

    // Добавляем заголовки дат
    const dateHeaders = [];
    allDates.forEach((date) => {
      const formattedDate = dayjs(date).format('DD.MM');
      const dayName = this.getDayName(dayjs(date).day());
      dateHeaders.push(`${formattedDate}\n${dayName}`);
      dateHeaders.push(''); // Пустые для объединения
      dateHeaders.push(''); // Пустые для объединения
    });

    const allHeaders = [...baseHeaders, ...dateHeaders];
    const headerRow = worksheet.addRow(allHeaders);

    // Создаем второй ряд заголовков для подзаголовков
    const subHeaders = ['', '', '', '', '', '']; // Пустые для основных колонок
    allDates.forEach(() => {
      subHeaders.push('Kelish', 'Ketish', 'Ishlagan');
    });
    const subHeaderRow = worksheet.addRow(subHeaders);

    // Стили для основных заголовков
    headerRow.eachCell((cell, colNumber) => {
      cell.font = { bold: true, size: 14, color: { argb: 'FFFFFF' } };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '2F75B5' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };

      // Устанавливаем ширину колонок
      if (colNumber <= 6) {
        const widths = [5, 20, 25, 15, 15, 15];
        worksheet.getColumn(colNumber).width = widths[colNumber - 1];
      } else {
        worksheet.getColumn(colNumber).width = 12;
      }
    });

    // Стили для подзаголовков
    subHeaderRow.eachCell((cell, colNumber) => {
      if (colNumber > 6) {
        cell.font = { bold: true, size: 12, color: { argb: 'FFFFFF' } };

        const subColIndex = (colNumber - 7) % 3;
        let bgColor = '2F75B5';

        if (subColIndex === 0) {
          bgColor = '6C9BD1'; // Синий для "Kelish"
        } else if (subColIndex === 1) {
          bgColor = 'E8A87C'; // Оранжевый для "Ketish"
        } else {
          bgColor = '98D8C8'; // Зеленый для "Ishlagan"
        }

        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: bgColor },
        };
        cell.alignment = { horizontal: 'center', vertical: 'middle' };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      } else {
        cell.font = { bold: true, size: 14, color: { argb: 'FFFFFF' } };
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '2F75B5' },
        };
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
      }
    });

    headerRow.height = 35;
    subHeaderRow.height = 30;

    // Объединяем ячейки для заголовка и периода
    const totalColumns = baseHeaders.length + allDates.length * 3;
    worksheet.mergeCells(1, 1, 1, totalColumns);
    worksheet.mergeCells(2, 1, 2, totalColumns);

    // Объединяем ячейки для дат
    let currentCol = 7;
    allDates.forEach(() => {
      worksheet.mergeCells(4, currentCol, 4, currentCol + 2);
      currentCol += 3;
    });

    // Объединяем основные заголовки по вертикали
    worksheet.mergeCells(4, 1, 5, 1); // №
    worksheet.mergeCells(4, 2, 5, 2); // Tashkilot
    worksheet.mergeCells(4, 3, 5, 3); // F.I.O
    worksheet.mergeCells(4, 4, 5, 4); // Telefon
    worksheet.mergeCells(4, 5, 5, 5); // Reja vaqt
    worksheet.mergeCells(4, 6, 5, 6); // Fakt vaqt

    // Добавляем данные по каждому сотруднику
    employeesData.forEach((employee, index) => {
      const rowData = [
        index + 1,
        employee.organization?.name || employee.mainOrganization?.name || '-',
        employee.employee?.fullName || '-',
        employee.employee?.phone || '-',
        employee.summary?.planHours || '0:00',
        employee.summary?.factHours || '0:00',
      ];

      // Создаем карту отчетов по датам
      const reportsByDate = new Map();
      employee.dailyReports.forEach((report) => {
        const dateKey = TimezoneHelper.formatLocal(report.date, 'YYYY-MM-DD');
        reportsByDate.set(dateKey, report);
      });

      // Добавляем данные для каждой даты
      allDates.forEach((date) => {
        const dateKey = dayjs(date).format('YYYY-MM-DD');
        const report = reportsByDate.get(dateKey);

        if (report) {
          if (!report.isWorkDay) {
            rowData.push('Dam', '', '');
          } else if (!report.cameToWork) {
            rowData.push("Yo'q", '', '');
          } else {
            const enterTime = report.actualEnterTime ? TimezoneHelper.formatTime(report.actualEnterTime) : '-';
            const exitTime = report.actualExitTime ? TimezoneHelper.formatTime(report.actualExitTime) : '-';
            const workTime = report.minutesWorked > 0 ? this.formatMinutesToTime(report.minutesWorked) : '0:00';
            rowData.push(enterTime, exitTime, workTime);
          }
        } else {
          rowData.push('-', '-', '-');
        }
      });

      const dataRow = worksheet.addRow(rowData);

      // Настройка стилей для строки данных
      dataRow.eachCell((cell, colNumber) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
        cell.font = { size: 12, bold: true };

        if (colNumber > 6) {
          const dateIndex = Math.floor((colNumber - 7) / 3);
          const dateKey = dayjs(allDates[dateIndex]).format('YYYY-MM-DD');
          const report = reportsByDate.get(dateKey);

          const subColIndex = (colNumber - 7) % 3;
          let baseColor = '';

          if (subColIndex === 0) {
            baseColor = '6C9BD1';
          } else if (subColIndex === 1) {
            baseColor = 'E8A87C';
          } else {
            baseColor = '98D8C8';
          }

          const lightColor = this.lightenColor(baseColor);
          cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: lightColor },
          };
        }
      });

      // Объединяем ячейки для "Yo'q" и "Dam"
      allDates.forEach((date, dateIndex) => {
        const dateKey = dayjs(date).format('YYYY-MM-DD');
        const report = reportsByDate.get(dateKey);

        if (report) {
          if (!report.isWorkDay || !report.cameToWork) {
            const startCol = 7 + dateIndex * 3;
            const endCol = startCol + 2;
            worksheet.mergeCells(dataRow.number, startCol, dataRow.number, endCol);
          }
        }
      });
    });

    // Закрепляем заголовок и первые 6 колонок
    worksheet.views = [{ state: 'frozen', xSplit: 6, ySplit: 5 }];
  }

  private createSummaryReportSheetByPosition(
    workbook: ExcelJS.Workbook,
    employeesData: any[],
    position: any,
    startDate: string,
    endDate: string,
  ) {
    // Создаем лист для сводного отчета
    const worksheet = workbook.addWorksheet('Umumiy hisobot', {
      properties: {
        tabColor: { argb: '4472C4' },
        defaultRowHeight: 25,
      },
    });

    // Заголовок отчета
    const titleRow = worksheet.addRow([`Lavozim: ${position.name} - Umumiy Davomat Hisoboti`]);
    titleRow.font = { size: 16, bold: true };
    titleRow.height = 35;

    // Информация о периоде
    const periodRow = worksheet.addRow([`Davr: ${startDate} - ${endDate}`]);
    periodRow.font = { size: 12, bold: true };
    periodRow.height = 25;

    // Пустая строка для разделения
    worksheet.addRow([]);

    // Заголовки для сводного отчета
    const summaryHeaders = [
      '№',
      'Tashkilot',
      'F.I.O',
      'Telefon',
      'Umumiy ish kunlari',
      'Kelgan kunlar',
      'Kechikishlar (daqiqa)',
      'Erta ketishlar (daqiqa)',
      'Davomat foizi (%)',
      'Reja vaqt',
      'Fakt vaqt',
    ];

    const summaryHeaderRow = worksheet.addRow(summaryHeaders);

    // Стили для заголовков
    summaryHeaderRow.eachCell((cell, colNumber) => {
      cell.font = { bold: true, size: 12, color: { argb: 'FFFFFF' } };
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '4472C4' },
      };
      cell.alignment = { horizontal: 'center', vertical: 'middle', wrapText: true };
      cell.border = {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' },
      };

      // Устанавливаем ширину колонок
      const widths = [5, 20, 25, 15, 12, 12, 15, 15, 12, 12, 12];
      worksheet.getColumn(colNumber).width = widths[colNumber - 1];
    });

    summaryHeaderRow.height = 30;

    // Объединяем ячейки для заголовка и периода
    worksheet.mergeCells(1, 1, 1, summaryHeaders.length);
    worksheet.mergeCells(2, 1, 2, summaryHeaders.length);

    // Добавляем данные сводного отчета
    employeesData.forEach((employee, index) => {
      const summaryData = [
        index + 1,
        employee.organization?.name || employee.mainOrganization?.name || '-',
        employee.employee?.fullName || '-',
        employee.employee?.phone || '-',
        employee.summary?.totalWorkDays || 0,
        employee.summary?.totalAttendanceDays || 0,
        employee.summary?.totalLateMinutes || 0,
        employee.summary?.totalEarlyExitMinutes || 0,
        employee.summary?.attendanceRate ? employee.summary.attendanceRate.toFixed(1) : '0.0',
        employee.summary?.planHours || '0:00',
        employee.summary?.factHours || '0:00',
      ];

      const summaryRow = worksheet.addRow(summaryData);

      // Стили для данных
      summaryRow.eachCell((cell, colNumber) => {
        cell.border = {
          top: { style: 'thin' },
          left: { style: 'thin' },
          bottom: { style: 'thin' },
          right: { style: 'thin' },
        };
        cell.alignment = { vertical: 'middle', horizontal: 'center' };
        cell.font = { size: 11 };

        // Цветовое кодирование для процента посещаемости
        if (colNumber === 9) {
          const attendanceRate = parseFloat(cell.value as string);
          if (attendanceRate >= 90) {
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'C6EFCE' }, // Зеленый
            };
          } else if (attendanceRate >= 70) {
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFEB9C' }, // Желтый
            };
          } else {
            cell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFC7CE' }, // Красный
            };
          }
        }
      });
    });

    // Закрепляем заголовки
    worksheet.views = [{ state: 'frozen', xSplit: 0, ySplit: 4 }];
  }

  private formatMinutesToTime(minutes: number | null | undefined): string {
    if (!minutes || minutes < 0) return '0:00';

    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }

  private getDayName(dayNumber: number): string {
    const days = ['Yakshanba', 'Dushanba', 'Seshanba', 'Chorshanba', 'Payshanba', 'Juma', 'Shanba'];
    return days[dayNumber] || "Noma'lum";
  }

  private lightenColor(hexColor: string): string {
    // Hex rangni RGB ga o'tkazamiz
    const r = parseInt(hexColor.substr(0, 2), 16);
    const g = parseInt(hexColor.substr(2, 2), 16);
    const b = parseInt(hexColor.substr(4, 2), 16);

    // Rangni 30% yorqinlashtitamiz
    const factor = 0.3;
    const newR = Math.min(255, Math.floor(r + (255 - r) * factor));
    const newG = Math.min(255, Math.floor(g + (255 - g) * factor));
    const newB = Math.min(255, Math.floor(b + (255 - b) * factor));

    // Yana hex formatga qaytaramiz
    return newR.toString(16).padStart(2, '0') + newG.toString(16).padStart(2, '0') + newB.toString(16).padStart(2, '0');
  }
}
