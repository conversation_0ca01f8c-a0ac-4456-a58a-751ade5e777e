import { Inject, Injectable, UnauthorizedException } from '@nestjs/common';
import { PrismaService } from '../../common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { LoginDto } from './dto/login.dto';
import * as bcrypt from 'bcrypt';
import { Cache } from 'cache-manager';
import * as ms from 'ms';

@Injectable()
export class AuthService {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
    @Inject(CACHE_MANAGER) private readonly cacheService: Cache,
  ) {}

  async login(loginDto: LoginDto) {
    if (!loginDto.username || !loginDto.password) {
      throw new UnauthorizedException('Username va parol majburiy');
    }
    const user = await this.hasUser(loginDto.username);

    if (!user || !(await bcrypt.compare(loginDto.password, user.password))) {
      throw new UnauthorizedException('Username yoki parol noto‘g‘ri');
    }

    const payload = {
      id: user.id,
    };

    const accessToken = this.jwtService.sign(payload, {
      secret: this.configService.get<string>('JWT_SECRET'),
      algorithm: 'HS256',
      expiresIn: this.configService.get<string>('JWT_EXPIRED'),
    });

    const refreshToken = this.jwtService.sign(payload, {
      secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
      expiresIn: this.configService.get<string>('JWT_REFRESH_EXPIRED'),
    });

    const refreshExpiry = this.configService.get<string>('JWT_REFRESH_EXPIRED') as string;
    const refreshTtlMs = ms(refreshExpiry as ms.StringValue);

    // const refreshTtlSeconds = refreshTtlMs ? (typeof refreshTtlMs === 'number' ? refreshTtlMs / 1000 : 0) : 0;
    const refreshTtlSeconds = refreshTtlMs / 1000;
    await this.cacheService.set(`user-${user.id}`, refreshToken, refreshTtlMs);

    return { accessToken, refreshToken };
  }

  async refresh(refreshToken: string) {
    try {
      const verifyToken = this.jwtService.verify(refreshToken, {
        secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
      });

      const user = await this.hasUser(verifyToken.id, true);

      if (!user) {
        throw new UnauthorizedException(`Foydalanuvchi topilmadi`);
      }

      const cachedToken = await this.cacheService.get<string>(`user-${user.id}`);

      // if (cachedToken !== refreshToken) {
      //   throw new UnauthorizedException('Yaroqsiz refresh token');
      // }

      if (!cachedToken) {
        throw new UnauthorizedException('Refresh token mavjud emas');
      }

      if (cachedToken.trim() !== refreshToken.trim()) {
        throw new UnauthorizedException('Yaroqsiz refresh token');
      }

      const payload = {
        id: user.id,
      };

      const accessToken = this.jwtService.sign(payload, {
        secret: this.configService.get<string>('JWT_SECRET'),
        expiresIn: this.configService.get<string>('JWT_EXPIRED'),
      });

      const newRefreshToken = this.jwtService.sign(payload, {
        secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
        expiresIn: this.configService.get<string>('JWT_REFRESH_EXPIRED'),
      });

      await this.cacheService.set(`user-${user.id}`, newRefreshToken);

      return { accessToken, refreshToken: newRefreshToken };
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new UnauthorizedException('Refresh token has expired');
      } else if (error.name === 'JsonWebTokenError') {
        throw new UnauthorizedException('Invalid refresh token');
      } else {
        throw error;
      }
    }
  }

  async logout(userId: string) {
    try {
      await this.cacheService.del(`user-${userId}`);
      return { message: 'User is logged out' };
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new UnauthorizedException('Access token has expired');
      } else if (error.name === 'JsonWebTokenError') {
        throw new UnauthorizedException('Invalid access token');
      } else {
        throw new UnauthorizedException('Failed to authenticate access token');
      }
    }
  }

  private async hasUser(username: string, withId = false) {
    return this.prismaService.user.findUnique({
      where: withId
        ? { id: username }
        : {
            username,
          },
      select: {
        id: true,
        imei: true,
        password: true,
        MainOrganization: {
          select: {
            id: true,
            name: true,
          },
        },
        Organization: {
          select: {
            id: true,
            name: true,
          },
        },
        ResponsibleFor: {
          select: {
            id: true,
            name: true,
            region: true,
            district: true,
            section: true,
            grade: true,
          },
        },
      },
    });
  }
}
