import { axiosPrivate } from '@/config/api'
import { userWorkingScheduleEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { notification } from 'antd'

export const useRemoveUserWorkingSchedule = (userId?: string) => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({ userId, id }: { userId: string; id: string }) => {
			return axiosPrivate.post(userWorkingScheduleEndpoints.remove(userId), { schedules: [id] })
		},
		onSuccess: () => {
			if (userId) {
				queryClient.invalidateQueries({ queryKey: [userWorkingScheduleEndpoints.one(userId)] })
			}
			notification.success({
				message: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ajratildi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: '<PERSON><PERSON><PERSON><PERSON><PERSON> xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
