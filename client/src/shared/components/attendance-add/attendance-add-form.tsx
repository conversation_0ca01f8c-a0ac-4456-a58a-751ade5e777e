import { DatePicker, Form, TimePicker } from 'antd'

export const AttendanceAddForm = () => {
	return (
		<>
			<Form.Item
				name='date'
				label='Sana'
				rules={[{ required: true, message: 'Sani kiriting' }]}>
				<DatePicker className='w-full' />
			</Form.Item>
			<Form.Item
				name='time'
				label='Vaqt'
				rules={[{ required: true, message: 'Vaqtni kiriting' }]}>
				<TimePicker
					format={'HH:mm'}
					className='w-full'
				/>
			</Form.Item>
		</>
	)
}
