import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { PropsWithChildren } from 'react'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { persistQueryClient } from '@tanstack/react-query-persist-client'
import { createSyncStoragePersister } from '@tanstack/query-sync-storage-persister'

const localStoragePersister = createSyncStoragePersister({
	storage: window.localStorage
})

const queryClient = new QueryClient({
	defaultOptions: {
		queries: {
			staleTime: 1000 * 5,
			gcTime: 1000 * 5,
			refetchOnWindowFocus: true,
			refetchOnReconnect: true,
			refetchInterval: 1000 * 45,
			refetchIntervalInBackground: true,
			retry: 2,
			retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000)
		},
		mutations: {
			retry: 2,
			retryDelay: 1000
		}
	}
})

persistQueryClient({
	queryClient,
	persister: localStoragePersister,
	maxAge: 1000 * 60 * 60 * 24
})

export const QueryProvider = ({ children }: PropsWithChildren) => {
	return (
		<QueryClientProvider client={queryClient}>
			{children}
			<ReactQueryDevtools initialIsOpen={false} />
		</QueryClientProvider>
	)
}
