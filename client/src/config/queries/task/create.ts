import { axiosPrivate } from '@/config/api'
import { taskEndpoints } from '@/config/api/endpoints'
import { TApiErrorResponse } from '@/shared/types/api-error.type'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { notification } from 'antd'
import { AxiosError, AxiosResponse } from 'axios'
import { TTask } from './getOne'

export type TTaskRequest = {
	name: string
	description: string
	taskStateId?: string
	taskTypeId: string
	dueDate: string
	files?: string[]
	recipients: {
		organizationId?: string
		userId?: string[]
		positionId?: string
	}[]
	controllerId: string
}

export const useCreateTask = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async (data: TTaskRequest) => {
			return await axiosPrivate.post(taskEndpoints.all, data)
		},
		onSuccess: async (response: AxiosResponse<TTask>) => {
			console.log(response)
			notification.success({ message: 'Topshiriq muaffaqiyatli ya<PERSON>!' })
			queryClient.invalidateQueries({ queryKey: [taskEndpoints.all] })
			queryClient.invalidateQueries({ queryKey: ['userTasks'] })
			queryClient.invalidateQueries({ queryKey: ['orgTasks'] })
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response)
			notification.error({ message: 'Topshiriq yaratishda xatolik yuz berdi!' })
		}
	})
}
