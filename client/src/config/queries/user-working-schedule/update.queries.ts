import { axiosPrivate } from '@/config/api'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { userWorkingScheduleEndpoints } from '@/config/api/endpoints'
import { notification } from 'antd'
import { useSearchParams } from 'react-router-dom'

export interface UserWorkingScheduleTypes {
	data: {
		id?: string
		name: string
		day: number
		startTime: string
		endTime: string
		userId?: string
	}
	scheduleId: string
}

export const useUpdateUserWorkingSchedule = () => {
	const queryClient = useQueryClient()
	const [searchParams] = useSearchParams()
	const page = searchParams.get('page') ?? '1'
	const limit = searchParams.get('limit') ?? '10'
	const xOrgId = localStorage.getItem('organizationId')

	return useMutation({
		mutationFn: async ({ data, scheduleId }: UserWorkingScheduleTypes) =>
			(
				await axiosPrivate.patch<ApiResponse<UserWorkingScheduleTypes>>(
					userWorkingScheduleEndpoints.one(scheduleId),
					data
				)
			).data,
		onSuccess: (_, { data }) => {
			queryClient.invalidateQueries({ queryKey: [userWorkingScheduleEndpoints.all] })
			queryClient.invalidateQueries({
				queryKey: ['users', JSON.parse(xOrgId ?? ''), +page, +limit]
			})
			queryClient.invalidateQueries({
				queryKey: [userWorkingScheduleEndpoints.byUserId(data.userId ?? '')]
			})
			notification.success({
				message: 'Muvaffaqiyatli yangilandi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: 'Yangilashda xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
