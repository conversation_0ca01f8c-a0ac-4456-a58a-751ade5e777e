import { ClearOutlined, DownloadOutlined, SearchOutlined } from '@ant-design/icons'
import { Button, Col, DatePicker, Form, Row, TimeRangePickerProps, Tooltip } from 'antd'
import dayjs, { Dayjs } from 'dayjs'
import AttendanceSelectTree from './attendance-select-tree'
import { useSearchParams } from 'react-router-dom'
import { useEffect } from 'react'
import { useGetAttendanceReportExcel } from '@/config/queries/attendance/getExcel'
import { TimezoneHelper, DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'

// Dayjs setup bir marta bajariladi
dayjs.tz.setDefault(DEFAULT_TIMEZONE)

const rangePresets: TimeRangePickerProps['presets'] = [
	{
		label: 'Bugun',
		value: [TimezoneHelper.getStartOfDay(), TimezoneHelper.getEndOfDay()]
	},
	{
		label: 'Kecha',
		value: [
			TimezoneHelper.getStartOfDay().subtract(1, 'day'),
			TimezoneHelper.getEndOfDay().subtract(1, 'day')
		]
	},
	{
		label: 'Ohirgi 3 kun',
		value: [TimezoneHelper.now().subtract(3, 'day'), TimezoneHelper.now()]
	},
	{
		label: 'Ohirgi 1 hafta',
		value: [TimezoneHelper.now().subtract(7, 'day'), TimezoneHelper.now()]
	},
	{
		label: 'Ohirgi 2 hafta',
		value: [TimezoneHelper.now().subtract(14, 'day'), TimezoneHelper.now()]
	},
	{
		label: 'Ohirgi 1 oy',
		value: [TimezoneHelper.now().subtract(1, 'month'), TimezoneHelper.now()]
	},
	{
		label: 'Ohirgi 3 oy',
		value: [TimezoneHelper.now().subtract(3, 'month'), TimezoneHelper.now()]
	},
	{
		label: 'Ohirgi 6 oy',
		value: [TimezoneHelper.now().subtract(6, 'month'), TimezoneHelper.now()]
	},
	{
		label: 'Ohirgi 1 yil',
		value: [TimezoneHelper.now().subtract(1, 'year'), TimezoneHelper.now()]
	}
]

export default function AttendanceFilter() {
	const [_, setSearchParams] = useSearchParams()
	const download = useGetAttendanceReportExcel()

	const handleReset = () => {
		const todayPreset = rangePresets?.find(i => i.label === 'Bugun')?.value as [Dayjs, Dayjs]
		if (todayPreset) {
			const { startDate, endDate } = TimezoneHelper.formatDateRange(todayPreset)
			setSearchParams({ startDate, endDate })
		}
	}

	useEffect(() => {
		const todayPreset = rangePresets?.find(i => i.label === 'Bugun')?.value as [Dayjs, Dayjs]
		if (todayPreset) {
			const { startDate, endDate } = TimezoneHelper.formatDateRange(todayPreset)
			setSearchParams({ startDate, endDate })
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [])

	return (
		<Form
			className='flex w-full p-4'
			onReset={handleReset}
			layout='vertical'>
			<Row
				className='w-full'
				gutter={[16, 16]}>
				<Col xs={6}>
					<Form.Item
						label='Davomat davri'
						initialValue={rangePresets?.find(item => item.label === 'Bugun')?.value}
						name='period'>
						<DatePicker.RangePicker
							allowClear={false}
							onChange={dates => {
								setSearchParams(prev => {
									const newParams = { ...Object.fromEntries(prev) }
									if (dates && dates[0] && dates[1]) {
										const { startDate, endDate } = TimezoneHelper.formatDateRange([
											dates[0],
											dates[1]
										])
										newParams.startDate = startDate
										newParams.endDate = endDate
									} else {
										newParams.startDate = ''
										newParams.endDate = ''
									}
									return newParams
								})
							}}
							className='w-full'
							placeholder={['Boshlanish sanasi', 'Tugash sanasi']}
							presets={rangePresets}
						/>
					</Form.Item>
				</Col>
				<Col xs={14}>
					<Form.Item
						label='Tashkilotlar'
						name='organizationId'>
						<AttendanceSelectTree setSearchParams={setSearchParams} />
					</Form.Item>
				</Col>
				<Col
					className='!p-0'
					xs={4}>
					<Form.Item
						label='Amallar'
						className='flex'>
						<Tooltip title='Hisobotni excel formatda yuklab olish'>
							<Button
								size='small'
								variant='solid'
								color='blue'
								className='mr-2'
								icon={<SearchOutlined />}
							/>
						</Tooltip>
						<Tooltip title='Hisobotni Excel fayl qilib yuklab olish'>
							<Button
								size='small'
								variant='solid'
								color='green'
								className='mr-2'
								onClick={() => {
									download.mutate()
								}}
								icon={<DownloadOutlined />}
							/>
						</Tooltip>
						<Tooltip title='Qidiruv natijalarini tozalash'>
							<Button
								size='small'
								className='mr-2'
								variant='solid'
								htmlType='reset'
								color='red'
								icon={<ClearOutlined />}
								onClick={() => {
									const todayPreset = rangePresets?.find(i => i.label === 'Bugun')?.value as [
										Dayjs,
										Dayjs
									]
									if (todayPreset) {
										const { startDate, endDate } = TimezoneHelper.formatDateRange(todayPreset)
										setSearchParams({ startDate, endDate })
									}
								}}
							/>
						</Tooltip>
					</Form.Item>
				</Col>
			</Row>
		</Form>
	)
}
