import { Button, Modal } from 'antd'
import { useLogout } from '@/config/queries/auth/logout.queries'
import { LogOutIcon } from 'lucide-react'
import { useState } from 'react'

const Logout = () => {
	const { mutate: logout, isPending: isPendingLogout } = useLogout()
	const [isModalOpen, setIsModalOpen] = useState(false)

	function handleLogOut() {
		logout()
		setIsModalOpen(false)
	}

	function handleCancel() {
		setIsModalOpen(false)
	}

	return (
		<>
			<Button
				danger
				size='middle'
				icon={<LogOutIcon className='w-7 h-4' />}
				data-testid='logout-button'
				loading={isPendingLogout}
				onClick={() => setIsModalOpen(true)}
				type='primary'></Button>
			<Modal
				title='Chiqish'
				centered
				open={isModalOpen}
				onOk={handleLogOut}
				onCancel={handleCancel}
				okText='Chiqish'
				cancelText='Bekor qilish'
				okButtonProps={{ className: 'bg-red-500 hover:bg-red-600 px-6 text-white' }}
				cancelButtonProps={{
					className: 'dark:bg-gray-800 bg-gray-200 border dark:border-gray-600 border-black p-2'
				}}
				className='[&_.ant-modal-footer]:space-x-4'>
				<p>Tizimdan chiqishni xohlaysizmi?</p>
			</Modal>
		</>
	)
}
export default Logout
