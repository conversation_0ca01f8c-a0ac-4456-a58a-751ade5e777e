import { axiosPrivate } from '@/config/api'
import { useInfiniteQuery } from '@tanstack/react-query'
import { OrganizationPositionTypes } from './create.queries'
import { organizationPositionTypeEndpoints } from '@/config/api/endpoints'
import { TPaginationWrapper } from '@/shared/types/pagination.type'
import { useState } from 'react'
import { useSearchParams } from 'react-router-dom'

export const useGetOrganizationPositionType = () => {
	const limit = 10
	const [page, setPage] = useState(1)
	const [searchParams] = useSearchParams()
	const search = searchParams.get('search')

	const query = useInfiniteQuery({
		queryKey: [organizationPositionTypeEndpoints.all, 'infinite', page, search, limit],
		queryFn: async ({ pageParam }) => {
			return (
				await axiosPrivate.get<TPaginationWrapper<OrganizationPositionTypes[]>>(
					organizationPositionTypeEndpoints.all,
					{
						params: {
							page: pageParam,
							limit,
							search
						}
					}
				)
			).data
		},
		initialPageParam: page,
		getNextPageParam: lastPage => {
			if (lastPage?.meta?.page === lastPage?.meta?.totalPages) return undefined
			return lastPage?.meta?.page + 1
		},
		getPreviousPageParam: lastPage => {
			if (lastPage?.meta?.page === 1) return undefined
			return lastPage?.meta?.page - 1
		}
	})
	return {
		...query,
		setPage
	}
}
export const useInfiniteOrganizationPositionTypeQuery = () => {
	const limit = 10
	const page = 1

	return useInfiniteQuery({
		queryKey: [organizationPositionTypeEndpoints.all, 'infinite', page, limit],
		queryFn: async ({ pageParam }) => {
			return (
				await axiosPrivate.get<TPaginationWrapper<OrganizationPositionTypes[]>>(
					organizationPositionTypeEndpoints.all,
					{
						params: {
							page: pageParam,
							limit
						}
					}
				)
			).data
		},
		initialPageParam: page,
		getNextPageParam: lastPage => {
			if (lastPage?.meta?.page === lastPage?.meta?.totalPages) return undefined
			return lastPage?.meta?.page + 1
		},
		getPreviousPageParam: lastPage => {
			if (lastPage?.meta?.page === 1) return undefined
			return lastPage?.meta?.page - 1
		}
	})
}
