// React и React Router
import { useCallback, useMemo, useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'

// Библиотеки
import { Badge, Col, Row, Button, Modal, Form, Input, Select } from 'antd'

import dayjs from 'dayjs'

// Компоненты и хуки проекта
import { LoadingScreen } from '@/shared/ui/suspense'

import { DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'

// Запросы API
import { useGetTaskById } from '@/config/queries/task/get-task-by-id'
import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { useReadTask } from '@/config/queries/task/read'
import { useTaskStates } from '@/shared/hooks/useTaskStates'
// import { useUpdateTaskAnswerState } from '@/config/queries/task/update-answer-state'
import {
	useGetNotifications,
	useReadNotification
} from '@/config/queries/notification/notification.queries'
import { useCompleteControllerTask } from '@/config/queries/task/complete-controller'

// Компоненты страницы (reusing from task-details)
import { TaskRecipients } from '../task-details/ui/task-reciepent'
import { TaskDescription } from '../task-details/ui/task-description'
import { TaskFiles } from '../task-details/ui/task-files'
import { TaskHeader } from '../task-details/ui/task-header'
import { TaskInfo } from '../task-details/ui/task-info'
import { TaskActions } from '../task-details/ui/task-action'
import { TaskResults } from '../task-details/ui/task-results'

dayjs.tz.setDefault(DEFAULT_TIMEZONE)

const TaskControllerDetails = () => {
	const { id } = useParams<{ id: string }>()
	const { data: notifications } = useGetNotifications()
	const { mutate: readNotification } = useReadNotification()
	const { data: task, isLoading } = useGetTaskById(id || '')
	const navigate = useNavigate()
	const orgId = localStorage.getItem('organizationId')
	const parsedOrgId = orgId ? JSON.parse(orgId) : null
	const { data: user, isLoading: isLoadingUser } = useAuthMe()
	const { mutateAsync: readTask } = useReadTask()
	const taskStatesData = useTaskStates()

	// Complete controller task functionality
	const { mutateAsync: completeControllerTask, isPending: isCompletingTask } = useCompleteControllerTask()
	const [completeModalVisible, setCompleteModalVisible] = useState(false)
	const [completeForm] = Form.useForm()

	// Same logic as task-details
	const isOwn = task?.createdById === user?.id
	const currentRecipient = task?.Recipients?.find(
		(recipient: any) => recipient.userId === user?.id || recipient.organizationId === parsedOrgId
	)

	const [readStatusFilter, setReadStatusFilter] = useState<'all' | 'read' | 'unread'>('all')
	const [showAllRecipients, setShowAllRecipients] = useState(false)

	const filteredRecipients = useMemo(() => {
		if (!task?.Recipients) return []
		let filtered = task.Recipients

		if (readStatusFilter === 'read') {
			filtered = filtered.filter((r: any) => r.isRead)
		} else if (readStatusFilter === 'unread') {
			filtered = filtered.filter((r: any) => !r.isRead)
		}

		return showAllRecipients ? filtered : filtered.slice(0, 5)
	}, [task?.Recipients, readStatusFilter, showAllRecipients])

	const recipientCounts = useMemo(() => {
		if (!task?.Recipients) return { total: 0, read: 0, unread: 0 }
		return {
			total: task.Recipients.length,
			read: task.Recipients.filter((r: any) => r.isRead).length,
			unread: task.Recipients.filter((r: any) => !r.isRead).length
		}
	}, [task?.Recipients])



	const handleMarkAsRead = useCallback(async () => {
		if (!currentRecipient || !id) return

		await readTask(id)

		const notification = notifications?.find(
			(n: any) => n.taskId === id && n.recipientId === currentRecipient.id
		)
		if (notification) {
			readNotification([notification.id])
		}
	}, [currentRecipient, id, readTask, notifications, readNotification])

	// Handle complete controller task - uses controller endpoint
	const handleCompleteControllerTask = useCallback(async (values: { stateId: string; description: string }) => {
		if (!id) return

		await completeControllerTask({
			taskId: id,
			stateId: values.stateId,
			description: values.description
		})

		setCompleteModalVisible(false)
		completeForm.resetFields()
		navigate('/personal/tasks/tasks-controller')
	}, [id, completeControllerTask, completeForm, navigate])

	if (isLoading || isLoadingUser) return <LoadingScreen />
	if (!task) return <div>Task not found</div>

	return (
		<main className=' px-4 py-8'>
			<Badge.Ribbon
				text={task.TaskState?.name || 'Unknown'}
				color={task.TaskState?.color || 'default'}
				style={{
					fontSize: 20,
					height: 34,
					top: -2
				}}>
				<div className='border border-[#363850] rounded-lg h-full w-full pb-5 '>
					<Row
						gutter={16}
						className='w-full py-10 px-3'>
						<Col
							xs={8}
							className='flex flex-col gap-y-4 space-y-4'>
							<TaskHeader title={task.name} />
							<TaskInfo task={task} />
						</Col>

						<Col
							xs={16}
							className='flex flex-col gap-y-3 items-end w-fit flex-1'>
							<div className="flex justify-between items-center w-full">
								<TaskActions
									isOwnTask={false}
									orgId={orgId}
									navigate={navigate}
									taskCompleted={task.isCompleted}
									isUpdating={false}
									updateTaskAnswerState={() => {}}
									task={task}
									taskStates={taskStatesData}
									currentRecipient={currentRecipient}
									onMarkAsRead={handleMarkAsRead}
								/>

								{/* Controller-specific complete button - uses /complete-inspector endpoint */}
								{isOwn && (
									<Button
										type="primary"
										danger
										className='my-2'
										onClick={() => setCompleteModalVisible(true)}
										disabled={task.isCompleted}
									>
										{task.isCompleted ? 'Yakunlangan' : 'Topshiriqni yakunlash'}
									</Button>
								)}
							</div>

							{/* Task Results - recipient answers (topshiriq natijalari) - positioned under complete button */}
							<div className="mt-10">
								<TaskResults
									task={task}
									user={user}
									orgId={orgId}
								/>
							</div>

						</Col>
					</Row>

					<TaskDescription description={task.description} />

					{!!task.files?.length && <TaskFiles files={task.files} />}

					<TaskRecipients
						filteredRecipients={filteredRecipients}
						recipientCounts={recipientCounts}
						readStatusFilter={readStatusFilter}
						setReadStatusFilter={setReadStatusFilter}
						showAllRecipients={showAllRecipients}
						setShowAllRecipients={setShowAllRecipients}
						isOwnTask={isOwn}
						navigate={navigate}
						currentRecipient={currentRecipient}
						task={task}
					/>


				</div>
			</Badge.Ribbon>

			{/* Complete Controller Task Modal */}
			<Modal
				title="Topshiriqni yakunlash"
				open={completeModalVisible}
				onCancel={() => setCompleteModalVisible(false)}
				footer={null}
				width={600}
			>
				<Form
					form={completeForm}
					layout="vertical"
					onFinish={handleCompleteControllerTask}
				>
					<Form.Item
						name="stateId"
						label="Holat"
						rules={[{ required: true, message: 'Holatni tanlang!' }]}
					>
						<Select
							placeholder="Holatni tanlang"
							options={taskStatesData?.taskStates?.pages?.[0]?.data || []}
							fieldNames={{ label: 'name', value: 'id' }}
						/>
					</Form.Item>

					<Form.Item
						name="description"
						label="Izoh"
						rules={[{ required: true, message: 'Izoh yozing!' }]}
					>
						<Input.TextArea
							rows={4}
							placeholder="Topshiriq yakunlanganligi haqida izoh yozing..."
						/>
					</Form.Item>

					<Form.Item className="mb-0 text-right">
						<Button
							onClick={() => setCompleteModalVisible(false)}
							className="mr-2"
						>
							Bekor qilish
						</Button>
						<Button
							type="primary"
							danger
							htmlType="submit"
							loading={isCompletingTask}
						>
							Topshiriqni yakunlash
						</Button>
					</Form.Item>
				</Form>
			</Modal>
		</main>
	)
}

export default TaskControllerDetails
