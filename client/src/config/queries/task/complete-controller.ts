import { axiosPrivate } from '@/config/api'
import { taskEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { notification } from 'antd'

interface CompleteControllerTaskRequest {
	taskId: string
	stateId: string
	description?: string
}

export const useCompleteControllerTask = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({ taskId, stateId, description }: CompleteControllerTaskRequest) => {
			return (
				await axiosPrivate.patch(taskEndpoints.taskCompleteController(taskId), {
					stateId,
					description
				})
			).data
		},
		onSuccess: (_data, variables) => {
			notification.success({
				message: 'Topshiriq muvaffaqiyatli yakunlandi',
				description: 'Nazoratchi sifatida topshiriq yakunlandi'
			})

			// Invalidate related queries
			queryClient.invalidateQueries({ queryKey: ['task-by-id', variables.taskId] })
			queryClient.invalidateQueries({ queryKey: ['task-controller'] })
		},
		onError: (error: any) => {
			const message = error?.response?.data?.message || 'Topshiriqni yakunlashda xatolik yuz berdi'
			notification.error({
				message: 'Xatolik',
				description: Array.isArray(message) ? message.join(', ') : message
			})
		}
	})
}
