describe('login spec', () => {
	beforeEach(() => {
		cy.visit('/auth/login')
	})

	it('should validate inputs', () => {
		cy.getById('login-submit-button').click()
		cy.get('#login_username_help > .ant-form-item-explain-error').should(
			'have.text',
			'Iltimos usernameni kiriting'
		)
		cy.get('#login_password_help > .ant-form-item-explain-error').should(
			'have.text',
			'Iltimos parolni kiring'
		)
	})

	it('should not login', () => {
		cy.getById('login-username-input').type('user')
		cy.getById('login-password-input').type('user')
		cy.getById('login-submit-button').click()
		cy.get('.ant-notification-notice-error').should('be.visible')
		cy.isPathname('/auth/login')
	})

	it('should login as admin', () => {
		cy.getById('login-username-input').type('admin')
		cy.getById('login-password-input').type('1234')
		cy.getById('login-submit-button').click()
		cy.get('.ant-notification-notice-success').should('be.visible')
		cy.isPathname('/')
	})

	it('should login as user', () => {
		cy.getById('login-username-input').type('user')
		cy.getById('login-password-input').type('1234')
		cy.getById('login-submit-button').click()
		cy.get('.ant-notification-notice-success').should('be.visible')
		cy.isPathname('/')
	})
})
