import { Module } from '@nestjs/common';
import { GpsService } from './gps.service';
import { GpsController } from './gps.controller';
import { BullModule } from '@nestjs/bullmq';
import { GPS, GPS_STATUS } from './interfaces/gps';
import { BullBoardModule } from '@bull-board/nestjs';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { GPSProcessor } from './gps.processor';
import { GpsSchedule } from './gps.schedule';
import { CoordsService } from '../coords/coords.service';
import { ConfigService } from '@nestjs/config';
import { GpsStatus } from './gps-status.processor';
import { HistoryReportService } from './services/history-report.service';
import { InfractionCreateService } from '../infraction/service/infraction-create.service';
import { InfractionService } from '../infraction/infraction.service';

@Module({
  imports: [
    BullModule.registerQueue({
      name: GPS,
      defaultJobOptions: {
        removeOnComplete: 100,
      },
    }),
    BullModule.registerQueue({
      name: GPS_STATUS,
      defaultJobOptions: {
        removeOnComplete: 100,
      },
    }),
    BullBoardModule.forFeature({
      name: GPS,
      adapter: BullMQAdapter,
    }),
    BullBoardModule.forFeature({
      name: GPS_STATUS,
      adapter: BullMQAdapter,
    }),
  ],
  controllers: [GpsController],
  providers: [
    CoordsService,
    GpsService,
    ConfigService,
    GPSProcessor,
    GpsStatus,
    GpsSchedule,
    HistoryReportService,
    InfractionCreateService,
    InfractionService,
  ],
})
export class GpsModule {}
