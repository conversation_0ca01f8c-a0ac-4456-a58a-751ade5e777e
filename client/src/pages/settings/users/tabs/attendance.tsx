import dayjs from 'dayjs'
import { ClockCircleOutlined } from '@ant-design/icons'
import { Button, Modal, Table } from 'antd'
import { DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'
import { useEffect, useState } from 'react'
import { History } from 'lucide-react'
import { AttendanceAddModal } from '@/shared/components/attendance-add/attendance-add-modal'
import { useGetUserAttendance } from '@/config/queries/attendance/get-user-attendance'
// import { DailyReport } from '@/config/queries/attendance/getAll'
// import { convertMinutesToHours } from '@/shared/utils/convertMinutesToHours'
// Dayjs setup bir marta bajariladi
dayjs.tz.setDefault(DEFAULT_TIMEZONE)

// const { Text } = Typography

type Props = {
	workspace?: 'organization' | 'personal'
}

export const Attendance = ({ workspace = 'organization' }: Props) => {
	const dayObj = dayjs(new Date())
	const [isAddAttendanceModalOpen, setIsAddAttendanceModalOpen] = useState(false)
	const [attendanceModalOpen, setAttendanceModalOpen] = useState(false)
	const { data: userAttendance } = useGetUserAttendance()

	useEffect(() => {
		console.log(userAttendance)
	}, [userAttendance])

	return (
		<div>
			<div className='flex items-center justify-between gap-x-3'>
				<Button
					onClick={() => setAttendanceModalOpen(true)}
					className='mb-2 w-full'
					type='dashed'>
					<History size={16} />
					To`liq tarixni ko`rish
				</Button>
				{workspace === 'organization' && (
					<Button
						onClick={() => setIsAddAttendanceModalOpen(true)}
						className='mb-2 w-full'
						type='dashed'>
						<ClockCircleOutlined />
						Davomat qo`shish
					</Button>
				)}
			</div>
			<AttendanceAddModal
				isOpen={isAddAttendanceModalOpen}
				closeFn={() => setIsAddAttendanceModalOpen(false)}
			/>
			<Modal
				title='Davomat tarixi'
				footer={null}
				width={1000}
				open={attendanceModalOpen}
				onCancel={() => setAttendanceModalOpen(false)}>
				<Table
					dataSource={userAttendance?.data}
					columns={[
						{
							title: '#',
							width: 50,
							render: (_, __, index) => index + 1
						},
						{
							title: 'Sana',
							render: (_, rec) => dayjs(rec.time).format('DD.MM.YYYY')
						},
						{
							title: 'Kelgan',
							render: () => dayjs(/* in time */).format('HH:mm')
						},
						{
							title: 'Ketgan',
							render: () => dayjs(/* in time */).format('HH:mm')
						},
						{
							title: 'Ishlagan',
							render: () => dayjs(/* in time */).diff(dayjs(/* out time */))
						}
					]}
				/>
			</Modal>
			<div className='text-center font-medium px-2 py-1 bg-blue-50 dark:bg-blue-900/30 dark:text-blue-200 rounded'>
				<div>{dayObj.format('DD.MM.YYYY')}</div>{' '}
				<div className='text-xs text-gray-500 dark:text-gray-400'>
					{dayObj.locale('uz').tz(DEFAULT_TIMEZONE).format('dddd')}
				</div>
			</div>
			<div className='text-center'>
				<div className='grid grid-cols-3 mt-4 gap-x-4 items-start'>
					<div>
						<div className='text-center bg-green-50 dark:bg-green-900/20 py-1 rounded-sm'>
							<span className='text-green-600 dark:text-green-400 font-medium'>Kelgan</span>
						</div>
						<h1>1</h1>
					</div>
					<div>
						<div className='text-center bg-red-50 dark:bg-red-900/20 py-1 rounded-sm'>
							<span className='text-red-600 dark:text-red-400 font-medium'>Ketgan</span>
						</div>
						<h1>2</h1>
					</div>
					<div>
						<div className='text-center bg-purple-50 dark:bg-purple-900/20 py-1 rounded-sm'>
							<span className='text-purple-600 dark:text-purple-400 font-medium'>
								<ClockCircleOutlined className='mr-1' />
								Ishlagan
							</span>
						</div>
						<h1>3</h1>
					</div>
				</div>
			</div>
		</div>
	)
}
