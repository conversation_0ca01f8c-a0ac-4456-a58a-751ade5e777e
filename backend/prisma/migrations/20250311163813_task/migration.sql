/*
  Warnings:

  - You are about to drop the `_RecipientToTask` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `taskId` to the `Recipient` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "_RecipientToTask" DROP CONSTRAINT "_RecipientToTask_A_fkey";

-- DropForeignKey
ALTER TABLE "_RecipientToTask" DROP CONSTRAINT "_RecipientToTask_B_fkey";

-- AlterTable
ALTER TABLE "Recipient" ADD COLUMN     "taskId" TEXT NOT NULL;

-- DropTable
DROP TABLE "_RecipientToTask";

-- AddForeignKey
ALTER TABLE "Recipient" ADD CONSTRAINT "Recipient_taskId_fkey" FOREIGN KEY ("taskId") REFERENCES "Task"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
