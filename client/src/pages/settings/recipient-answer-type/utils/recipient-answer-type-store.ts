import { RecipientAnswerType } from '@/config/queries/recipient-answer-type/create.queries'
import { create } from 'zustand'

interface RecipientAnswerTypesModalStore {
	open: boolean
	data: RecipientAnswerType | null
	onOpen: (data?: RecipientAnswerType) => void
	onClose: () => void
}

export const useRecipientAnswerTypesModalStore = create<RecipientAnswerTypesModalStore>(set => ({
	open: false,
	data: null,
	onOpen: data => set({ open: true, data: data || null }),
	onClose: () => set({ open: false, data: null })
}))
