services:
  db:
    container_name: postgres-mnazorat
    image: postgres:15
    restart: always
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: dev_postgres
      POSTGRES_PASSWORD: dev_postgres
      POSTGRES_DB: dev_postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - dev_network

  redis:
    container_name: redis-mnazorat
    image: redis:latest
    restart: always
    ports:
      - "6379:6379"
    networks:
      - dev_network

volumes:
  postgres_data:


networks:
  dev_network:
