import { Row, Button } from 'antd'
import { Plus } from 'lucide-react'
import { TRecipent } from '../task-create'

type TAddRecipientButtonProps = {
	recipents: TRecipent[]
	setRecipents: React.Dispatch<React.SetStateAction<TRecipent[]>>
	recipientType: string
}

export const AddRecipientButton = ({
	recipents,
	setRecipents,
	recipientType
}: TAddRecipientButtonProps) => {
	const isDisabled = recipents.some(item => {
		if (recipientType === 'positions') {
			return !item.positionId
		}
		return !item.organizationId
	})

	const handleAddRecipient = () => {
		if (!isDisabled) {
			const newRecipient: TRecipent = {
				organizationId: '',
				userId: [],
				...(recipientType === 'positions' && { positionId: '' })
			}
			setRecipents(prev => [...prev, newRecipient])
		}
	}

	// Don't show add recipient button for organizations
	if (recipientType === 'organizations') {
		return null
	}

	return (
		<Row
			gutter={[14, 14]}
			className='mb-5'>
			<Button
				disabled={isDisabled}
				onClick={handleAddRecipient}
				className='mx-2 w-full'
				type='dashed'>
				<Plus />
				<span className='text-md'>Qabul qiluvchini qo'shish</span>
			</Button>
		</Row>
	)
}
