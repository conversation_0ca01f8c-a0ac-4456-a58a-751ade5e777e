-- ============================================
-- 1. PROD_USER yaratish (to'liq huquqlar bilan)
-- ============================================

DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'prod_user') THEN
CREATE ROLE prod_user WITH LOGIN PASSWORD 'secure_password';
RAISE NOTICE '✅ Пользователь prod_user создан';
ELSE
        RAISE NOTICE 'ℹ️ Пользователь prod_user уже существует';
END IF;
END
$$;

GRANT CONNECT, CREATE ON DATABASE current_database() TO prod_user;

GRANT USAGE ON SCHEMA public TO prod_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO prod_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO prod_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO prod_user;

ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT ALL PRIVILEGES ON TABLES TO prod_user;

ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT ALL PRIVILEGES ON SEQUENCES TO prod_user;

ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT ALL PRIVILEGES ON FUNCTIONS TO prod_user;

ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT USAGE ON TYPES TO prod_user;

ALTER DEFAULT PRIVILEGES IN SCHEMA public
GRANT EXECUTE ON ROUTINES TO prod_user;


-- ============================================
-- 2. EXPORTER yaratish (faqat monitoring uchun)
-- ============================================

DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'exporter') THEN
CREATE ROLE exporter WITH LOGIN PASSWORD 'exporter';
RAISE NOTICE '✅ Пользователь exporter создан';
ELSE
        RAISE NOTICE 'ℹ️ Пользователь exporter уже существует';
END IF;
END
$$;

-- Monitoring view'lariga ruxsat beramiz
GRANT pg_monitor TO exporter;

-- Qo‘shimcha view'lar uchun aniq SELECT huquqlari
GRANT SELECT ON pg_stat_activity TO exporter;
GRANT SELECT ON pg_stat_database TO exporter;
GRANT SELECT ON pg_stat_bgwriter TO exporter;
GRANT SELECT ON pg_stat_replication TO exporter;
