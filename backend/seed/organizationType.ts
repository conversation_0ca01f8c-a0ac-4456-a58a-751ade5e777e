import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  const organizationsType = [
    {
      name: 'admin',
    },
  ];

  for (const organizationType of organizationsType) {
    const existingPosition = await prisma.organizationType.findFirst({
      where: { name: organizationType.name },
    });

    if (!existingPosition) {
      await prisma.organizationType.create({ data: organizationType });
      console.log(`OrganizationType "${organizationType.name}" created.`);
    } else {
      console.warn(`OrganizationType "${organizationType.name}" already exists. Skipping creation.`);
    }
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
