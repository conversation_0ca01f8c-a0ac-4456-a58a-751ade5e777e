import dayjs, { Dayjs } from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'

dayjs.extend(utc)
dayjs.extend(timezone)

const tz = 'Asia/Tashkent'

export function getDatesBetween(startDate: Dayjs | Date, endDate: Dayjs | Date) {
	const dates = []
	let current = dayjs(startDate).tz(tz)
	const end = dayjs(endDate).tz(tz)

	while (current.isBefore(end.tz(tz), 'day') || current.isSame(end.tz(tz), 'day')) {
		dates.push(current.format('YYYY-MM-DD'))
		current = current.add(1, 'day')
	}

	return dates
}
