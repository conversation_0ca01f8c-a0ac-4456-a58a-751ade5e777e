import { axiosPrivate } from '@/config/api'
import { recipientEndpoints, taskEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError, AxiosResponse } from 'axios'
import { TTask } from './getOne'
import { notification } from 'antd'
import { TApiErrorResponse } from '@/shared/types/api-error.type'

type TUpdateTaskAnswerState = {
	recipientAnswerId: string
	data: {
		state: string
		rejectReason?: string
	}
}

export const useUpdateRecipientAnswerState = (recipientId: string) => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({ recipientAnswerId, data }: TUpdateTaskAnswerState) => {
			return await axiosPrivate.patch(taskEndpoints.updateAnswerState(recipientAnswerId), data)
		},
		onSuccess: async (response: AxiosResponse<TTask>) => {
			console.log(response)
			notification.success({ message: "Topshiriq holati muaffaqiyatli o'zgartirildi!" })
			queryClient.invalidateQueries({ queryKey: [recipientEndpoints.one(recipientId)] })
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response)
			notification.error({ message: 'Topshiriq holatini o`zgartirishda xatolik yuz berdi!' })
		}
	})
}
