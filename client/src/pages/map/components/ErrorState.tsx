import React from 'react'

interface ErrorStateProps {
	message?: string
	description?: string
}

export const ErrorState: React.FC<ErrorStateProps> = ({
	message = 'Ошибка загрузки данных',
	description = 'Попробуйте обновить страницу'
}) => {
	return (
		<div className='h-full w-full flex items-center justify-center bg-gray-50'>
			<div className='flex flex-col items-center space-y-4 text-center'>
				<div className='text-red-500 text-4xl'>⚠️</div>
				<p className='text-red-600 font-medium'>{message}</p>
				<p className='text-gray-500 text-sm'>{description}</p>
			</div>
		</div>
	)
}
