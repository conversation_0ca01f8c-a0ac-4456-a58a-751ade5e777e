import { useMemo } from 'react'
import {
	useGetReferenceRegions,
	useGetReferenceDistricts,
	useGetReferenceSections,
	ReferenceRegion,
	ReferenceDistrict,
	ReferenceSection
} from '@/config/queries/common/common-queries'

/**
 * Хук для получения названий территориальных единиц по их ID
 */
export const useAreaNames = (regionId?: string | null, districtId?: string | null) => {
	// Получаем список всех регионов
	const { data: regions, isLoading: regionsLoading } = useGetReferenceRegions()

	// Получаем районы для выбранного региона (если есть)
	// При прямом входе в секцию нужно загрузить районы
	const { data: districts, isLoading: districtsLoading } = useGetReferenceDistricts(
		regionId ? parseInt(regionId) : null
	)

	// Получаем секции для выбранного района (если есть)
	const { data: sections, isLoading: sectionsLoading } = useGetReferenceSections(
		districtId ? parseInt(`${regionId}${districtId}`) : null
	)

	// Создаем карты для быстрого поиска названий по ID
	const regionMap = useMemo(() => {
		if (!regions) return new Map<string, string>()
		return new Map(regions.map((region: ReferenceRegion) => [region.id, region.name]))
	}, [regions])

	const districtMap = useMemo(() => {
		if (!districts) return new Map<string, string>()
		return new Map(districts.map((district: ReferenceDistrict) => [district.id, district.name]))
	}, [districts])

	const sectionMap = useMemo(() => {
		if (!sections) return new Map<string, string>()
		return new Map(sections.map((section: ReferenceSection) => [section.id, section.name]))
	}, [sections])

	/**
	 * Получает название области по её свойствам
	 */
	const getAreaName = (properties: any): string | null => {
		// Определяем тип области и получаем соответствующее название
		if (properties.sectionId || properties.section_id) {
			const id = properties.sectionId || properties.section_id
			const regionId = properties.regionId || properties.region_id
			const districtId = properties.districtId || properties.district_id
			const sectionKey = regionId.toString() + districtId.toString() + id.toString()
			const name = sectionMap.get(sectionKey) || `Махалла ${id}`
			return name
		}
		if (properties.districtId || properties.district_id) {
			const id = properties.districtId || properties.district_id
			const regionId = properties.regionId || properties.region_id
			const name = districtMap.get(regionId.toString() + id.toString()) || `Район ${id}`
			return name
		}
		if (properties.regionId || properties.region_id) {
			const id = properties.regionId || properties.region_id
			const name = regionMap.get(id.toString()) || `Регион ${id}`
			return name
		}
		return null
	}

	/**
	 * Получает детальную информацию о местоположении
	 */
	const getLocationInfo = (properties: any) => {
		const regionId = properties.regionId || properties.region_id
		const districtId = properties.districtId || properties.district_id
		const sectionId = properties.sectionId || properties.section_id

		const regionName = regionId ? regionMap.get(regionId.toString()) : null

		// Для района формируем ключ как regionId + districtId
		const districtName =
			districtId && regionId ? districtMap.get(regionId.toString() + districtId.toString()) : null

		// Для секции формируем ключ как regionId + districtId + sectionId
		const sectionName =
			sectionId && districtId && regionId
				? sectionMap.get(regionId.toString() + districtId.toString() + sectionId.toString())
				: null

		return {
			regionName,
			districtName,
			sectionName,
			fullPath: [regionName, districtName, sectionName].filter(Boolean).join(' > ')
		}
	}

	const isLoading = regionsLoading || districtsLoading || sectionsLoading

	return {
		getAreaName,
		getLocationInfo,
		isLoading,
		regionMap,
		districtMap,
		sectionMap
	}
}
