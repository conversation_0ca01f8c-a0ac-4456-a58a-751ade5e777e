import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/common';
import { ApiResponse } from 'src/helpers/apiRespons';

@Injectable()
export class RecipientService {
  constructor(private prismaService: PrismaService) {}

  // create(createRecipientDto: CreateRecipientDto) {
  //   return 'This action adds a new recipient';
  // }

  // findAll() {
  //   return `This action returns all recipient`;
  // }

  async findOne(id: string) {
    const recipient = await this.prismaService.recipient.findFirst({
      where: { id },
      select: {
        createdAt: true,
        id: true,
        isCompleted: true,
        isRead: true,
        organizationId: true,
        status: true,
        taskId: true,
        updatedAt: true,
        userId: true,
        Answer: {
          select: {
            id: true,
            recipientId: true,
            status: true,
            createdAt: true,
            updatedAt: true,
            description: true,
            files: true,
            rejectReason: true,
            state: true,
            type: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        Organization: {
          select: {
            id: true,
            name: true,
            address: true,
            controlledById: true,
            createdAt: true,
            updatedAt: true,
            description: true,
            districtId: true,
            gradeId: true,
            parentId: true,
            phone: true,
            regionId: true,
            sectionId: true,
            sector: true,
            sectorResponsible: true,
            status: true,
            typeId: true,
          },
        },
        Task: {
          select: {
            completeDesc: true,
            completeFileId: true,
            completedAt: true,
            createdAt: true,
            createdById: true,
            createdByOrganizationId: true,
            description: true,
            dueDate: true,
            id: true,
            isCompleted: true,
            name: true,
            parentId: true,
            status: true,
            taskStateId: true,
            taskTypeId: true,
            updatedAt: true,
          },
        },
        User: {
          select: {
            avatarId: true,
            createdAt: true,
            faceIdImageId: true,
            fullName: true,
            id: true,
            imei: true,
            mainOrganizationId: true,
            phone: true,
            positionId: true,
            status: true,
            telegramId: true,
            updatedAt: true,
            username: true,
            userData: true,
          },
        },
      },
    });

    if (!recipient) {
      throw new NotFoundException('Recipient not found');
    }

    return recipient;
  }

  // update(id: number, updateRecipientDto: UpdateRecipientDto) {
  //   return `This action updates a #${id} recipient`;
  // }

  // remove(id: number) {
  //   return `This action removes a #${id} recipient`;
  // }
}
