import { Divider, Form, Select, Button } from 'antd'
import TextArea from 'antd/es/input/TextArea'

interface TaskAnswerFormProps {
	form: any
	handleSubmit: (values: { answer: string; taskAnswerState: string }) => void
	recipientAnswerTypes: any
	isLoadingRecipientAnswerTypes: boolean
	FileUpload: React.ComponentType
	isPending: boolean
}

export const TaskAnswerForm = ({
	form,
	handleSubmit,
	recipientAnswerTypes,
	isLoadingRecipientAnswerTypes,
	FileUpload,
	isPending
}: TaskAnswerFormProps) => {
	return (
		<>
			<Divider />
			<div className='mt-6 mb-4 space-y-4 px-3 flex flex-col gap-y-3'>
				<Form
					layout='vertical'
					id='answer-form'
					form={form}
					onFinish={handleSubmit}>
					<Form.Item
						name='taskAnswerState'
						label='Javob holati'
						rules={[{ required: true, message: 'Javob holatini tanlang' }]}>
						<Select
							options={recipientAnswerTypes?.pages.flatMap((page: any) => page.data)}
							fieldNames={{ label: 'name', value: 'id' }}
							placeholder='Javob holatini tanlang'
							loading={isLoadingRecipientAnswerTypes}
						/>
					</Form.Item>
					<Form.Item
						name='answer'
						rules={[{ required: true, message: 'Javobni kiriting' }]}>
						<TextArea
							rows={4}
							required
							placeholder='Javob yozish'
							className='w-full'
						/>
					</Form.Item>
				</Form>
				<FileUpload />
				<div className='flex items-end justify-end mt-3'>
					<Button
						form='answer-form'
						htmlType='submit'
						loading={isPending}
						disabled={isPending}
						type='primary'
						className='justify-end px-6'>
						Yuborish
					</Button>
				</div>
			</div>
		</>
	)
}
