import { useMemo } from 'react'
import { Badge, Tag, Tooltip, Typography } from 'antd'
import dayjs from 'dayjs'
import 'dayjs/locale/uz'

import { ClockCircleOutlined } from '@ant-design/icons'
import { TimezoneHelper, DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'

// Dayjs setup bir marta bajariladi
dayjs.tz.setDefault(DEFAULT_TIMEZONE)

const { Text } = Typography

export const useAttendancePositionColumns = (
	days: string[],
	setSelectedWorkerId: (id: string | null) => void
) => {
	return useMemo(() => {
		return [
			{
				title: '#',
				render: (_: any, __: any, index: number) => ++index,
				width: 50,
				fixed: 'left',
				className: 'bg-gray-100 dark:bg-gray-700 dark:text-gray-200'
			},
			{
				title: 'F.I.O',
				dataIndex: 'fullName',
				fixed: 'left',
				width: 180,
				className: 'bg-gray-50 dark:bg-gray-700 dark:text-gray-200 font-medium',
				render: (text: string, record: any) => (
					<div
						className='cursor-pointer '
						onClick={() => setSelectedWorkerId(record.id)}>
						{text}
					</div>
				)
			},
			{
				title: 'Lavozim',
				dataIndex: ['position', 'name'],
				width: 150
			},
			{
				title: 'Tashkilot',
				dataIndex: 'organization',
				width: 150,
				className: 'dark:text-gray-300'
			},

			...days.map(day => {
				const dayObj = dayjs(day).tz(DEFAULT_TIMEZONE)
				const dateHeader = (
					<div className='text-center font-medium px-2 py-1 bg-blue-50 dark:bg-blue-900/30 dark:text-blue-200 rounded'>
						<div>{dayObj.format('DD.MM.YYYY')}</div>
						<div className='text-xs text-gray-500 dark:text-gray-400'>
							{dayObj.locale('uz').format('dddd')}
						</div>
					</div>
				)

				const getReportForDate = (record: any) => {
					return record.dailyReports?.find((r: any) => {
						const reportDate = dayjs(r.date).format('YYYY-MM-DD')
						const currentDay = dayjs(day).tz(DEFAULT_TIMEZONE).format('YYYY-MM-DD')

						return reportDate === currentDay
					})
				}

				return {
					key: day,
					title: dateHeader,
					className: 'date-column-group',
					children: [
						{
							title: (
								<div className='text-center bg-green-50 dark:bg-green-900/20 py-1'>
									<span className='text-green-600 dark:text-green-400 font-medium'>Kelgan</span>
								</div>
							),
							width: 140,
							className: 'enter-time-column',
							render: (record: any) => {
								const report = getReportForDate(record)

								if (!report) {
									return <div className='text-center text-gray-400'>-</div>
								}

								if (!report.isWorkday || report.status === 'DAY_OFF') {
									return (
										<div className='text-center'>
											<Text
												type='secondary'
												italic>
												Dam olish
											</Text>
										</div>
									)
								}

								if (report.status === 'ABSENT') {
									return (
										<div className='text-center'>
											<Badge status='error' />
											<Text type='danger'>Kelmagan</Text>
										</div>
									)
								}

								if (!report.enter) {
									return (
										<div className='text-center'>
											<Badge status='error' />
											<Text type='danger'>Kelmagan</Text>
										</div>
									)
								}

								const enterTime = TimezoneHelper.formatTime(report.enter)

								return (
									<div className='text-center'>
										{report.lateMinutes > 0 ? (
											<Tooltip title={`${report.lateMinutes} daqiqa kech kelgan`}>
												<Tag
													color='orange'
													className='font-medium'>
													{enterTime}
												</Tag>
											</Tooltip>
										) : (
											<Tag
												color='green'
												className='font-medium'>
												{enterTime}
											</Tag>
										)}
									</div>
								)
							}
						},
						{
							width: 140,
							title: (
								<div className='text-center bg-red-50 dark:bg-red-900/20 py-1'>
									<span className='text-red-600 dark:text-red-400 font-medium'>Ketgan</span>
								</div>
							),
							className: 'exit-time-column',
							render: (record: any) => {
								const report = getReportForDate(record)

								if (!report) {
									return <div className='text-center text-gray-400'>-</div>
								}

								if (!report.isWorkday || report.status === 'DAY_OFF') {
									return (
										<div className='text-center'>
											<Text
												type='secondary'
												italic>
												Dam olish
											</Text>
										</div>
									)
								}

								if (report.status === 'ABSENT') {
									return (
										<div className='text-center'>
											<Badge status='error' />
											<Text type='danger'>Kelmagan</Text>
										</div>
									)
								}

								if (!report.enter) {
									return (
										<div className='text-center'>
											<Badge status='error' />
											<Text type='danger'>Kelmagan</Text>
										</div>
									)
								}

								if (!report.exit) {
									return <div className='text-center text-gray-400'>-</div>
								}

								const exitTime = TimezoneHelper.formatTime(report.exit)

								return (
									<div className='text-center'>
										{report.earlyExitMinutes > 0 ? (
											<Tooltip title={`${report.earlyExitMinutes} daqiqa erta ketgan`}>
												<Tag
													color='volcano'
													className='font-medium'>
													{exitTime}
												</Tag>
											</Tooltip>
										) : (
											<Tag
												color='red'
												className='font-medium'>
												{exitTime}
											</Tag>
										)}
									</div>
								)
							}
						},
						{
							width: 140,
							title: (
								<div className='text-center bg-purple-50 dark:bg-purple-900/20 py-1'>
									<span className='text-purple-600 dark:text-purple-400 font-medium'>
										<ClockCircleOutlined className='mr-1' />
										Ishlagan
									</span>
								</div>
							),
							className: 'work-time-column',
							render: (record: any) => {
								const report = getReportForDate(record)

								if (!report) {
									return <div className='text-center text-gray-400'>-</div>
								}
								if (!report.isWorkday || report.status === 'DAY_OFF') {
									return (
										<div className='text-center'>
											<Text
												type='secondary'
												italic>
												Dam olish
											</Text>
										</div>
									)
								}

								if (report.status === 'ABSENT') {
									return (
										<div className='text-center'>
											<Badge status='error' />
											<Text type='danger'>Kelmagan</Text>
										</div>
									)
								}
								if (!report.minutes || report.minutes === 0) {
									return (
										<div className='text-center'>
											<Tag color='red'>0 s</Tag>
										</div>
									)
								}
								const hours = Math.floor(report.minutes / 60)
								const minutes = report.minutes % 60
								const workTime = `${hours}soat ${minutes}min`

								return (
									<div className='text-center'>
										<Tag
											color='purple'
											className='font-medium'>
											{workTime}
										</Tag>
									</div>
								)
							}
						}
					]
				}
			})
		]
	}, [days, setSelectedWorkerId])
}
