import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateOrganizationTypePositionDto } from './dto/create-organization-type-position.dto';
import { UpdateOrganizationTypePositionDto } from './dto/update-organization-type-position.dto';
import { PaginationUtil, PrismaService, QueryUtil } from 'src/common';
import { BaseQueryParams } from 'src/helpers/types';
import { Prisma } from '@prisma/client';

@Injectable()
export class OrganizationTypePositionService {
  constructor(
    private prismaService: PrismaService,
    private pagination: PaginationUtil,
    private queryUtil: QueryUtil,
  ) {}

  async create(CreateOrganizationTypePositionDto: CreateOrganizationTypePositionDto) {
    const type = await this.prismaService.organizationType.findUnique({
      where: { id: CreateOrganizationTypePositionDto.typeId },
    });

    if (!type) {
      throw new NotFoundException('Organization Type not found');
    }

    return this.prismaService.organizationTypePosition.create({
      data: {
        name: CreateOrganizationTypePositionDto.name,
        description: CreateOrganizationTypePositionDto.description,
        type: {
          connect: {
            id: CreateOrganizationTypePositionDto.typeId,
          },
        },
      },
    });
  }

  async findAll(query: BaseQueryParams) {
    const paginate = {
      page: query.page ? parseInt(query.page, 10) : 1,
      limit: query.limit ? parseInt(query.limit, 10) : 10,
    };

    const where: any = {
      ...this.queryUtil.createSearchQuery(query.search, ['name', 'id', 'description']),
      status: 'ACTIVE',
    };

    const withPagination = await this.pagination.paginate(
      this.prismaService.organizationTypePosition,
      paginate,
      where,
      {
        type: true,
      },
    );

    return withPagination;
  }

  async findByOrganizationType(typeId: string, query: BaseQueryParams) {
    const type = this.prismaService.organizationType.findUnique({
      where: { id: typeId },
    });

    if (!type) {
      throw new NotFoundException('Organization Type not found');
    }

    const searchQuery = await this.queryUtil.createSearchQuery(query.search, ['name', 'id', 'description']);

    return this.pagination.paginate(
      this.prismaService.organizationTypePosition,
      {
        page: +query.page || 1,
        limit: +query.limit || 10,
      },
      { typeId, ...searchQuery },
    );
  }

  async update(id: string, updateOrganizationTypePositionDto: UpdateOrganizationTypePositionDto) {
    const { typeId, ...data } = updateOrganizationTypePositionDto;

    const existingPosition = await this.prismaService.organizationTypePosition.findUnique({
      where: { id },
    });

    if (!existingPosition) {
      throw new NotFoundException('Organization Type Position not found');
    }

    const updateData: Prisma.OrganizationTypePositionUpdateInput = { ...data };

    if (typeId) {
      const type = await this.prismaService.organizationType.findUnique({
        where: { id: typeId },
      });

      if (!type) {
        throw new NotFoundException('Organization Type not found');
      }

      updateData.type = {
        connect: { id: typeId },
      };
    }

    return this.prismaService.organizationTypePosition.update({
      where: { id },
      data: updateData,
    });
  }

  remove(id: string) {
    return this.prismaService.organizationTypePosition.update({
      where: { id },
      data: { status: 'INACTIVE' },
    });
  }
}
