import { Modu<PERSON> } from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { DashboardController } from './dashboard.controller';
import { SectionStatsModule } from './section-stats/section-stats.module';
import { SectionStatsExcelModule } from './section-stats/excel/section-stats-excel.module';

@Module({
  controllers: [DashboardController],
  providers: [DashboardService],
  imports: [SectionStatsModule, SectionStatsExcelModule],
})
export class DashboardModule {}
