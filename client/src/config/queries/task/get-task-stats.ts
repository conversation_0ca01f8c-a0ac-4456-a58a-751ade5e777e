import { axiosPrivate } from '@/config/api'
import { taskEndpoints } from '@/config/api/endpoints'
import { useMutation } from '@tanstack/react-query'

export const useGetTaskStats = () => {
	return useMutation({
		mutationFn: async ({ startDate, endDate }: { startDate?: string; endDate?: string }) =>
			axiosPrivate.get(taskEndpoints.stats, {
				params: {
					startDate,
					endDate
				},
				responseType: 'arraybuffer',
				headers: {
					Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
				}
			}),
		onSuccess: response => {
			const blob = new Blob([response.data], {
				type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
			})

			const url = window.URL.createObjectURL(blob)
			const a = document.createElement('a')
			a.href = url
			a.download = 'task-statistics.xlsx'
			document.body.appendChild(a)
			a.click()
			document.body.removeChild(a)
			window.URL.revokeObjectURL(url)
		}
	})
}
