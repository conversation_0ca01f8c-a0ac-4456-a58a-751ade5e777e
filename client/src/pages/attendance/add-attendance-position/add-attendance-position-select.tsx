import { useGetReferenceOrganizationPosition } from '@/config/queries/common/common-queries'
import { Select } from 'antd'
import { useSearchParams } from 'react-router-dom'
import { useEffect } from 'react'

interface AddAttendancePositionSelectProps {
	value?: string
	onChange?: (value: string) => void
	placeholder?: string
}

export default function AddAttendancePositionSelect({
	value,
	onChange,
	placeholder = 'Lavozimni tanlang'
}: AddAttendancePositionSelectProps) {
	const [, setSearchParams] = useSearchParams()
	const organizationPositions = useGetReferenceOrganizationPosition()

	// Set grade and positionId in search params like task creation
	useEffect(() => {
		setSearchParams(prev => {
			const newParams = { ...Object.fromEntries(prev) }
			newParams.grade = '30'
			if (value) {
				newParams.positionId = value
			} else {
				delete newParams.positionId
			}
			return newParams
		})
	}, [value, setSearchParams])

	const handleChange = (selectedValue: string) => {
		onChange?.(selectedValue)

		// Update search params when position changes
		setSearchParams(prev => {
			const newParams = { ...Object.fromEntries(prev) }
			newParams.grade = '30'
			if (selectedValue) {
				newParams.positionId = selectedValue
			} else {
				delete newParams.positionId
			}
			return newParams
		})
	}

	return (
		<div>
			<Select
				allowClear
				placeholder={placeholder}
				value={value}
				onChange={handleChange}
				loading={organizationPositions.isLoading}
				className='w-full'
				size='middle'
				showSearch
				filterOption={(input, option) =>
					(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
				}
				options={
					organizationPositions.data?.map(position => ({
						label: position.name,
						value: position.id
					})) || []
				}
			/>
		</div>
	)
}
