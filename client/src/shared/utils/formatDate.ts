/**
 * Formats a given date string into a human-readable format.
 *
 * @param {string} date - The date string to format.
 * @returns {string} - The formatted date string in 'uz' locale.
 *
 * @example
 * const formattedDate = formatDate('2023-10-05');
 * console.log(formattedDate); // Outputs: '5 October 2023'
 */
export const formatDate = (date: string): string => {
	const options: Intl.DateTimeFormatOptions = {
		year: 'numeric',
		month: 'numeric',
		day: 'numeric'
	}
	return new Intl.DateTimeFormat('ru', options).format(new Date(date))
}
