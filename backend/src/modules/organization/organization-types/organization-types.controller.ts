import { Body, Controller, Get, Param, Post, Put, Query } from '@nestjs/common';
import { OrganizationTypesService } from './organization-types.service';
import { CreateOrganizationTypeDto } from './dto/create-organization-type.dto';
import { BaseQueryParams } from 'src/helpers/types';

@Controller('organization-types')
export class OrganizationTypesController {
  constructor(private readonly organizationTypesService: OrganizationTypesService) {}

  @Get()
  async getAll(@Query() query: BaseQueryParams) {
    return await this.organizationTypesService.getAll(query);
  }

  @Get('name/:name')
  async getByName(@Param('name') name: string) {
    return await this.organizationTypesService.getByName(name);
  }

  @Get(':id')
  async getById(@Param('id') id: string) {
    return await this.organizationTypesService.getById(id);
  }

  @Post()
  async create(@Body() data: CreateOrganizationTypeDto) {
    return await this.organizationTypesService.create(data);
  }

  @Put(':id')
  async update(@Param('id') id: string, @Body() data: CreateOrganizationTypeDto) {
    return await this.organizationTypesService.update(id, data);
  }
}
