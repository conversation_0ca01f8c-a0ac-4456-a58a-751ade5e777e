import { BadRequestException, ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { CreateVacationDto } from './dto/create-vacation.dto';
import { UpdateVacationDto } from './dto/update-vacation.dto';
import { PaginationUtil, PrismaService, QueryUtil } from 'src/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { BaseQueryParams } from 'src/helpers/types';
import { TimezoneHelper } from 'src/helpers/timezone.helper';
import * as isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import * as isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
@Injectable()
export class VacationService {
  constructor(
    private prisma: PrismaService,
    private pagination: PaginationUtil,
    private queryUtil: QueryUtil,
  ) {}

  // async create(createVacationDto: CreateVacationDto) {
  //   let state = false;
  //   const user = await this.prisma.user.findUnique({ where: { id: createVacationDto.userId, status: 'ACTIVE' } });

  //   if (!user) throw new NotFoundException('user not found');

  //   if (new Date(createVacationDto.begin) < new Date() && new Date(createVacationDto.end) > new Date()) {
  //     state = true;
  //   }

  //   return await this.prisma.vacation.create({ data: { ...createVacationDto, state } });
  // }

  // async create(createVacationDto: CreateVacationDto) {
  //   const user = await this.prisma.user.findUnique({
  //     where: { id: createVacationDto.userId, status: 'ACTIVE' },
  //   });

  //   if (!user) {
  //     throw new NotFoundException('User not found');
  //   }

  //   const beginDate = TimezoneHelper.parseDateTime(createVacationDto.begin);
  //   const endDate = TimezoneHelper.parseDateTime(createVacationDto.end);
  //   const now = TimezoneHelper.now();

  //   if (!beginDate || !endDate) {
  //     throw new BadRequestException('Invalid date format');
  //   }

  //   if (beginDate.isAfter(endDate) || beginDate.isSame(endDate)) {
  //     throw new BadRequestException('Begin date must be before end date');
  //   }

  //   const state = beginDate.isSameOrBefore(now) && endDate.isSameOrAfter(now);

  //   return await this.prisma.vacation.create({
  //     data: {
  //       ...createVacationDto,
  //       state,
  //     },
  //   });
  // }

  async create(createVacationDto: CreateVacationDto) {
    const user = await this.prisma.user.findUnique({
      where: { id: createVacationDto.userId, status: 'ACTIVE' },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const beginDate = TimezoneHelper.parseDateTime(createVacationDto.begin);
    const endDate = TimezoneHelper.parseDateTime(createVacationDto.end);

    if (!beginDate || !endDate) {
      throw new BadRequestException('Invalid date format');
    }

    // if (beginDate.isAfter(endDate) || beginDate.isSame(endDate)) {
    //   throw new BadRequestException('Begin date must be before end date');
    // }

    if (beginDate.isAfter(endDate)) {
      throw new BadRequestException('Begin date must be before end date');
    }

    const today = new Date();
    const todayDateOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    const beginDateJs = new Date(beginDate.format('YYYY-MM-DD') + 'T00:00:00');
    const endDateJs = new Date(endDate.format('YYYY-MM-DD') + 'T00:00:00');

    const beginDateOnly = new Date(beginDateJs.getFullYear(), beginDateJs.getMonth(), beginDateJs.getDate());
    const endDateOnly = new Date(endDateJs.getFullYear(), endDateJs.getMonth(), endDateJs.getDate());

    const state = beginDateOnly <= todayDateOnly && endDateOnly >= todayDateOnly;

    return await this.prisma.vacation.create({
      data: {
        ...createVacationDto,
        state,
      },
    });
  }

  async findAll({ page, limit, userId, search }: BaseQueryParams) {
    const $page = Number(page) || 1;
    const $limit = Number(limit) || 10;

    const where: any = {
      ...this.queryUtil.createSearchQuery(search, ['id', 'description']),
      status: 'ACTIVE',
      userId,
    };

    return await this.pagination.paginate(this.prisma.vacation, { page: $page, limit: $limit }, where, { user: true });
  }

  async findOne(id: string) {
    return await this.prisma.vacation.findUnique({ where: { status: 'ACTIVE', id }, include: { user: true } });
  }

  @Cron(CronExpression.EVERY_2_HOURS)
  async cron() {
    const vacation = await this.prisma.vacation.findMany({ where: { status: 'ACTIVE' } });

    Promise.all(
      vacation.map(async (el) => {
        let state = false;

        if (new Date(el.begin) < new Date() && new Date(el.end) > new Date()) {
          state = true;
        }

        await this.prisma.vacation.update({ where: { id: el.id }, data: { state } });
      }),
    );

    return null;
  }

  async update(id: string, updateVacationDto: UpdateVacationDto) {
    const vacation = await this.prisma.vacation.findUnique({ where: { status: 'ACTIVE', id } });
    let state = vacation?.state || false;

    if (!vacation) throw new NotFoundException('vacation not found');

    const user = await this.prisma.user.findUnique({ where: { id: updateVacationDto.userId, status: 'ACTIVE' } });

    if (!user) throw new NotFoundException('user not found');

    if (
      new Date(updateVacationDto.begin || vacation.begin) < new Date() &&
      new Date(updateVacationDto.end || vacation.end) > new Date()
    ) {
      state = true;
    }

    return await this.prisma.vacation.update({ where: { id }, data: { ...updateVacationDto, state } });
  }

  async remove(id: string) {
    const vacation = await this.prisma.vacation.findUnique({ where: { id, status: 'ACTIVE' } });
    if (!vacation) throw new NotFoundException('vacations not found');
    return await this.prisma.vacation.update({ where: { id }, data: { status: 'INACTIVE' } });
  }
}
