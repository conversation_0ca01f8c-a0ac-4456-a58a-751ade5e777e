import { IsDateString, IsInt, IsISO8601, IsNot<PERSON><PERSON><PERSON>, IsOptional, IsString, Max, <PERSON> } from 'class-validator';
import { Status } from '@prisma/client';

export class CreateUserWorkingScheduleDayDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsInt()
  @Min(1)
  @Max(7)
  day: number;

  @IsNotEmpty()
  @IsString()
  @IsISO8601()
  startTime: string;

  @IsNotEmpty()
  @IsDateString()
  endTime: string;

  @IsOptional()
  status?: Status;

  @IsString()
  @IsOptional()
  userWorkingScheduleId?: string;
}

export class UpdateUserWorkingScheduleDayDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsInt()
  @Min(1)
  @Max(7)
  @IsOptional()
  day?: number;

  @IsString()
  @IsOptional()
  startTime?: string;

  @IsString()
  @IsOptional()
  endTime?: string;

  @IsOptional()
  status?: Status;

  @IsString()
  @IsOptional()
  userWorkingScheduleId?: string;
}
