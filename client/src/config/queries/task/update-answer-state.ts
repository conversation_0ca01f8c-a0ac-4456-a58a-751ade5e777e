import { axiosPrivate } from '@/config/api'
import { taskEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { AxiosError, AxiosResponse } from 'axios'
import { TTask } from './getOne'
import { notification } from 'antd'
import { TApiErrorResponse } from '@/shared/types/api-error.type'

type TUpdateTaskAnswerState = {
	taskId: string
	taskStateId: string
}

export const useUpdateTaskAnswerState = (taskId: string) => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({ taskId, taskStateId }: TUpdateTaskAnswerState) => {
			return await axiosPrivate.patch(taskEndpoints.one(taskId), { taskStateId })
		},
		onSuccess: async (response: AxiosResponse<TTask>) => {
			console.log(response)
			notification.success({ message: "Topshiriq holati muaffaqiyatli o'zgartirildi!" })
			queryClient.invalidateQueries({ queryKey: [taskEndpoints.one(taskId)] })
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response)
			notification.error({ message: 'Topshiriq holatini o`zgartirishda xatolik yuz berdi!' })
		}
	})
}
