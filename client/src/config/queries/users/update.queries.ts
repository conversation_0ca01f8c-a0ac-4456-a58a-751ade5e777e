import { axiosPrivate } from '@/config/api'
import { usersEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { TUser } from './get-all.queries'
import { ApiResponse } from '../types'
import { notification } from 'antd'

export type TUserRequest = Partial<Omit<TUser, 'id'>>
export type TUserResponse = TUser

export const useUpdateUser = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({ id, ...data }: { id: string }) =>
			(await axiosPrivate.patch<ApiResponse<TUser>>(usersEndpoints.one(id), data)).data,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [usersEndpoints.all] })
			notification.success({
				message: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> yangilandi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: 'Yangilashda xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
