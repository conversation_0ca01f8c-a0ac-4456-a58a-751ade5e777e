import { useGetLocationsBySection } from '@/config/queries/gps/gps.queries.ts'
import { Card, Typography, Empty, Spin } from 'antd'
import { useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import HeaderSection from '../components/HeaderSection'
import FiltersPanel from '../components/FiltersPanel'
import LocationList from '../components/LocationList'
import { formatDate } from '../utils/dateUtils'
import { LocationItem as LocationItemType } from '../types/location.types'

const { Text } = Typography

export default function LocationStats() {
	const { data, isLoading, refetch } = useGetLocationsBySection()
	const navigate = useNavigate()

	// Статистика для дашборда
	const stats = useMemo(() => {
		if (!data?.history) return { total: 0, active: 0, inactive: 0, filtered: 0 }

		const total = data.history.length
		const active = data.history.filter(location => location.isInArea).length
		const inactive = total - active

		return { total, active, inactive }
	}, [data])

	// Handle user click - navigate to harakat yo'li
	const handleUserClick = (user: LocationItemType) => {
		// Navigate to user's harakat yo'li page
		navigate(`/workspace/map/location-user/${user.id}`)
	}

	if (isLoading) {
		return (
			<div className='flex justify-center items-center h-64'>
				<Spin size='large' />
			</div>
		)
	}

	if (!data || !data.history) {
		return (
			<Card className='h-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 border-0'>
				<Empty
					image={Empty.PRESENTED_IMAGE_SIMPLE}
					description={
						<Text className='text-gray-500 dark:text-gray-400'>
							Hozircha ma'lumotlar mavjud emas
						</Text>
					}
					className='my-8'
				/>
			</Card>
		)
	}

	return (
		<div className='h-full flex flex-col space-y-2 overflow-hidden'>
			<HeaderSection
				stats={stats}
				onRefresh={() => refetch()}
			/>

			<FiltersPanel />

			<div className='flex-1 overflow-y-auto overflow-x-hidden space-y-3 mt-4'>
				<LocationList
					filteredData={data.history}
					formatDate={formatDate}
					onUserClick={handleUserClick}
				/>
			</div>
		</div>
	)
}
