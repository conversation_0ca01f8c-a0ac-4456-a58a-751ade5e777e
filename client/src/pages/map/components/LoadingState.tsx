import React from 'react'

interface LoadingStateProps {
	message?: string
}

export const LoadingState: React.FC<LoadingStateProps> = ({
	message = 'Координаты загружаются...'
}) => {
	return (
		<div className='h-full w-full flex items-center justify-center bg-gray-50'>
			<div className='flex flex-col items-center space-y-4'>
				<div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500'></div>
				<p className='text-gray-600'>{message}</p>
			</div>
		</div>
	)
}
