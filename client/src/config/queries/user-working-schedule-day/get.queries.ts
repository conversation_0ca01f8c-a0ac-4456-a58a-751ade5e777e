import { axiosPrivate } from '@/config/api'
import { useInfiniteQuery } from '@tanstack/react-query'
import { userWorkingScheduleDayEndpoints } from '@/config/api/endpoints'
import { UserWorkingScheduleTypes } from './create.queries'
import { TPaginationWrapper } from '@/shared/types/pagination.type'
import { useSearchParams } from 'react-router-dom'

export const useGetUserWorkingScheduleDay = () => {
	const limit = 10
	const page = 1

	const [searchParams] = useSearchParams()
	const search = searchParams.get('search')

	return useInfiniteQuery({
		queryKey: [userWorkingScheduleDayEndpoints.all, search, 'infinite', page, limit],
		queryFn: async ({ pageParam }) => {
			return (
				await axiosPrivate.get<TPaginationWrapper<UserWorkingScheduleTypes[]>>(
					userWorkingScheduleDayEndpoints.all,
					{
						params: {
							page: pageParam,
							limit,
							search
						}
					}
				)
			).data
		},
		initialPageParam: page,
		getNextPageParam: lastPage => {
			if (lastPage?.meta?.page === lastPage?.meta?.totalPages) return undefined
			return lastPage?.meta?.page + 1
		},
		getPreviousPageParam: lastPage => {
			if (lastPage?.meta?.page === 1) return undefined
			return lastPage?.meta?.page - 1
		}
	})
}
