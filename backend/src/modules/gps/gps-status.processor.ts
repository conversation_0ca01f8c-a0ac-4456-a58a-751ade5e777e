import { InjectQueue, Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Job, Queue } from 'bullmq';
import { PrismaService } from 'src/common';
import { GPS_STATUS } from './interfaces/gps';
import {
  GPS_INFRACTION_CHECK,
  GPS_OFFLINE_CHECK,
  GPS_OFFLINE_CHECK_TIMEOUT_MINUTES,
  GPS_SET_OFFLINE,
  GpsOfflineCheckData,
  GpsSetOfflineData,
} from './interfaces/gps-status';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { TelegramEventKeys } from '../telegram-bot/interfaces/bot.interfaces';
import { InfractionCreateService } from '../infraction/service/infraction-create.service';
import { TimezoneHelper } from '../../helpers/timezone.helper';

@Injectable()
@Processor(GPS_STATUS, { concurrency: 2 })
export class GpsStatus extends WorkerHost implements OnModuleInit {
  private readonly logger = new Logger(GpsStatus.name);
  private readonly OFFLINE_TIMEOUT_MINUTES = GPS_OFFLINE_CHECK_TIMEOUT_MINUTES;
  private readonly schedulerQueueKey = `${GPS_STATUS}:scheduler`;
  private readonly schedulerJobId = 'scheduler-job-gps-status';

  constructor(
    private readonly prismaService: PrismaService,
    private eventEmitter: EventEmitter2,
    private infractionCreateService: InfractionCreateService,
    @InjectQueue(GPS_STATUS) private readonly gpsStatusQueue: Queue,
  ) {
    super();
  }

  async onModuleInit() {
    const every = 60 * 60 * 1000;

    const schedulerJob = await this.gpsStatusQueue.getJobScheduler(this.schedulerJobId);

    if (schedulerJob) {
      await this.gpsStatusQueue.removeJobScheduler(this.schedulerJobId);
    }

    await this.gpsStatusQueue.upsertJobScheduler(
      this.schedulerJobId,
      {
        every,
      },
      {
        name: this.schedulerQueueKey,
      },
    );
  }

  async process(job: Job, token?: string) {
    try {
      switch (job.name) {
        case GPS_OFFLINE_CHECK:
          return await this.checkUserOfflineStatus(job.data as GpsOfflineCheckData);
        case GPS_SET_OFFLINE:
          return await this.setUserOffline(job.data as GpsSetOfflineData);
        case this.schedulerQueueKey:
          this.logger.log('GPS status scheduler job started');
          await this.checkOfflineUsers();
          this.logger.log('GPS status scheduler job completed');
          return;
        case GPS_INFRACTION_CHECK:
          return await this.checkInfraction(job);
        default:
          this.logger.warn(`Noma\`lum job nomi: ${job.name}`);
      }
    } catch (error) {
      this.logger.error(`GPS status job xatoligi: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async checkUserOfflineStatus(data: GpsOfflineCheckData) {
    const { userId, lastLocationTime, timeoutMinutes } = data;
    const now = new Date();
    const diffMinutes = (now.getTime() - new Date(lastLocationTime).getTime()) / (1000 * 60);

    this.logger.debug(`Foydalanuvchi ${userId} offline statusini tekshirish: ${diffMinutes.toFixed(1)} daqiqa`);
    this.logger.debug(`Oxirgi location vaqti: ${lastLocationTime}, Hozirgi vaqt: ${now.toISOString()}`);

    if (diffMinutes < 0) {
      this.logger.warn(`Foydalanuvchi ${userId} uchun oxirgi location vaqti kelajakda: ${lastLocationTime}`);
      return { userId, isOffline: false, diffMinutes: 0 };
    }

    if (diffMinutes >= timeoutMinutes) {
      try {
        await this.gpsStatusQueue.add(
          GPS_SET_OFFLINE,
          {
            userId,
            reason: 'TIMEOUT',
          } as GpsSetOfflineData,
          {
            priority: 1,
            jobId: `set-offline-${userId}`,
            removeOnComplete: 50,
            removeOnFail: 10,
          },
        );
        this.logger.log(`Foydalanuvchi ${userId} offline status-ga o\`tkazildi (${diffMinutes.toFixed(1)} daqiqa)`);
      } catch (error) {
        this.logger.error(`SET_OFFLINE job qo'shishda xatolik: ${error.message}`);
      }
    }

    return { userId, isOffline: diffMinutes >= timeoutMinutes, diffMinutes };
  }

  private async setUserOffline(data: GpsSetOfflineData) {
    const { userId, reason } = data;
    return await this.prismaService.$transaction(async (prisma) => {
      const lastActiveLocation = await prisma.gPSLocationReport.findFirst({
        where: {
          userId,
          status: 'ACTIVE',
        },
        orderBy: { createdAt: 'desc' },
      });

      if (!lastActiveLocation) {
        this.logger.warn(`Foydalanuvchi ${userId} uchun oxirgi aktiv location topilmadi`);
        return { userId, reason, setOffline: false };
      }

      const isAlreadyOffline = lastActiveLocation;

      if (isAlreadyOffline && isAlreadyOffline.type === 'DISABLED') {
        this.logger.warn(`Foydalanuvchi ${userId} allaqachon offline holatda`);
        return { userId, reason, setOffline: false };
      }

      const newerLocation = await prisma.gPSLocationReport.findFirst({
        where: {
          userId,
          createdAt: { gt: lastActiveLocation.createdAt },
        },
        orderBy: { createdAt: 'desc' },
      });
      if (newerLocation) {
        this.logger.warn(`Foydalanuvchi ${userId} ga yangi location keldi, offlinega o\`tkazilmaydi`);
        return { userId, reason, setOffline: false };
      }

      this.eventEmitter.emit(TelegramEventKeys.TELEGRAM_OFFLINE, { userId });

      await prisma.gPSLocationReport.create({
        data: {
          userId,
          type: 'DISABLED',
          status: 'ACTIVE',
          isInArea: lastActiveLocation.isInArea,
          lat: lastActiveLocation.lat,
          lng: lastActiveLocation.lng,
        },
      });
      this.logger.log(`Foydalanuvchi ${userId} offline status oldi (sabab: ${reason})`);
      return { userId, reason, setOffline: true };
    });
  }

  private async checkOfflineUsers() {
    const now = new Date();
    const lastOneHour = new Date(now.getTime() - 60 * 60 * 1000);
    try {
      const lastLocationReports = await this.prismaService.gPSLocationReport.findMany({
        where: {
          status: 'ACTIVE',
          createdAt: {
            gte: lastOneHour,
          },
        },
        select: {
          createdAt: true,
          id: true,
          type: true,
          isInArea: true,
          userId: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      const offlineUsers = lastLocationReports.filter((report) => {
        const diffMinutes = (now.getTime() - new Date(report.createdAt).getTime()) / (1000 * 60);
        return diffMinutes >= this.OFFLINE_TIMEOUT_MINUTES && report.type !== 'DISABLED';
      });

      const uniqueOfflineUsers = new Map<string, (typeof offlineUsers)[number]>();

      for (const report of offlineUsers) {
        if (!uniqueOfflineUsers.has(report.userId)) {
          uniqueOfflineUsers.set(report.userId, report);
        }
      }

      for (const report of uniqueOfflineUsers.values()) {
        await this.gpsStatusQueue.add(
          GPS_SET_OFFLINE,
          {
            userId: report.userId,
            reason: 'TIMEOUT',
          },
          {
            priority: 1,
            jobId: `set-offline-${report.userId}`,
            removeOnComplete: 50,
            removeOnFail: 10,
          },
        );
      }

      this.logger.log(`Offline users checked: ${uniqueOfflineUsers.size} users set to offline`);
    } catch (error) {
      this.logger.error(`Error checking offline users: ${error.message}`);
      this.logger.error(error.stack);
    }
  }

  async checkInfraction(job) {
    const { userId, outsideStartTime, lat, lng } = job.data;

    try {
      const user = await this.prismaService.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          UserWorkingSchedule: {
            select: {
              days: {
                select: {
                  day: true,
                  startTime: true,
                  endTime: true,
                },
              },
            },
          },
        },
      });

      if (!user) {
        this.logger.warn(`Foydalanuvchi topilmadi: ${userId}`);
        return;
      }

      const now = TimezoneHelper.now();
      const todayNumber = now.day();
      const today = todayNumber === 0 ? 7 : todayNumber;

      const todayWorkingSchedule = user.UserWorkingSchedule?.days?.find((day) => day.day === today);

      if (!todayWorkingSchedule) {
        this.logger.debug(`Foydalanuvchi ${userId} bugun ish kuni emas, infraction yozilmaydi`);
        return;
      }

      const startTime = now.format('HH:mm');
      const endTime = now.format('HH:mm');

      const isWorkingTime = now.isBetween(startTime, endTime);

      if (!isWorkingTime) {
        this.logger.debug(`Foydalanuvchi ${userId} ish kuni emas, infraction yozilmaydi`);
        return;
      }

      const latestLocation = await this.prismaService.gPSLocationReport.findFirst({
        where: {
          userId: userId,
          status: 'ACTIVE',
          type: { not: 'DISABLED' },
          isInArea: { not: null },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      if (latestLocation && latestLocation.isInArea === false && !latestLocation.writedInfraction) {
        const now = new Date();
        const timeDifference = (now.getTime() - new Date(outsideStartTime).getTime()) / (1000 * 60); // daqiqa

        if (timeDifference >= 15) {
          await this.infractionCreateService.autoCreateInfractionToUser({
            userId,
            name: 'Hududdan tashqariga chiqish',
            description: `Foydalanuvchi ${Math.round(timeDifference)} daqiqa hududdan tashqarida bo'lgan`,
            data: {
              reason: 'OUTSIDE_AREA_TIMEOUT',
              duration: Math.round(timeDifference),
              outsideStartTime: new Date(outsideStartTime).toISOString(),
              detectedAt: now.toISOString(),
              location: lat && lng ? { lat, lng } : null,
            },
            infractionDate: now.toISOString(),
          });

          // Bu foydalanuvchining barcha hududdan tashqaridagi yozuvlarini yangilash
          await this.prismaService.gPSLocationReport.updateMany({
            where: {
              userId,
              isInArea: false,
              writedInfraction: { not: true },
              createdAt: { gte: outsideStartTime },
            },
            data: {
              writedInfraction: true,
            },
          });

          this.logger.log(
            `Jarima yaratildi: User ${userId} - ${Math.round(timeDifference)} daqiqa hududdan tashqarida`,
          );
        }
      } else if (latestLocation && latestLocation.isInArea === true) {
        // Agar foydalanuvchi hududga qaytib kelgan bo'lsa
        this.logger.log(`User ${userId} hududga qaytib kelgan, jarima yozilmaydi`);
      }
    } catch (error) {
      this.logger.error(`Jarima tekshirish xatoligi userId ${userId}: ${error.message}`);
      throw error;
    }
  }
}
