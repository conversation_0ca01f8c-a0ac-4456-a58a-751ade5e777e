import { ClearOutlined } from '@ant-design/icons'
import { Button, Input } from 'antd'
import { SearchIcon } from 'lucide-react'
import { useState } from 'react'
import { useSearchParams } from 'react-router-dom'

export const Search = () => {
	const [search, setSearch] = useState('')
	const [searchParams, setSearchParams] = useSearchParams()

	return (
		<div className='flex items-center gap-x-2'>
			<Input
				value={search}
				className='!w-[300px]'
				placeholder='Qidirish...'
				onChange={e => setSearch(e.target.value)}
			/>
			<Button
				onClick={() => {
					searchParams.set('search', search)
					setSearchParams(searchParams)
				}}
				type='primary'>
				<SearchIcon size={16} />
				Qidirish
			</Button>
			{searchParams.get('search') && (
				<Button
					onClick={() => {
						setSearch('')
						searchParams.delete('search')
						setSearchParams(searchParams)
					}}>
					<ClearOutlined />
					Tozalash
				</Button>
			)}
		</div>
	)
}
