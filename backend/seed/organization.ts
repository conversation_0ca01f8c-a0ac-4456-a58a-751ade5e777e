import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  const organizationTypes = await prisma.organizationType.findFirst({ where: { name: 'admin' } });
  const grade = await prisma.grade.findFirst({ where: { level: -1 } });

  const organizations = [
    {
      name: 'MBOS',
      typeId: organizationTypes?.id,
      gradeId: grade?.id,
      regionId: '1733',
    },
  ];

  for (const organization of organizations) {
    const existingOrganization = await prisma.organization.findFirst({
      where: {
        name: organization.name,
        grade: {
          level: -1,
        },
      },
    });

    const admin = await prisma.user.findFirst({
      where: {
        username: 'admin',
        fullName: 'Admin User',
        role: 'ADMIN',
      },
    });

    if (!admin) {
      console.log(`Admin user not found. Cannot create organization "${organization.name}".`);
      continue;
    }

    const orgData = {
      name: organization.name,
      type: {
        connect: {
          id: organization.typeId,
        },
      },
      grade: {
        connect: {
          id: organization.gradeId,
        },
      },
      region: {
        connect: {
          id: organization.regionId,
        },
      },
      Worker: {
        connect: {
          id: admin.id,
        },
      },
      Responsible: {
        connect: {
          id: admin.id,
        },
      },
    };

    if (existingOrganization) {
      // Обновить существующую организацию
      await prisma.organization.update({
        where: { id: existingOrganization.id },
        data: orgData,
      });
      console.log(`Organization "${organization.name}" updated.`);
    } else {
      // Создать новую организацию
      await prisma.organization.create({
        data: orgData,
      });
      console.log(`Organization "${organization.name}" created.`);
    }
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
