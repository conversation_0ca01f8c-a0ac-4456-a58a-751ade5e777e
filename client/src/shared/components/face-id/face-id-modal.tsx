import { Button, Modal } from 'antd'
import { useFaceIDStore } from './utils/face-id-modal-store'
import { useGetAllFaceIdByOrg } from '@/config/queries/face-id/getAllByOrg'
import { FaceIdDevicesTable } from './ui/face-id-devices.table'
import { useFaceIDAddStore } from './utils/face-id-add-modal-store'
import { FaceIdAddModal } from './ui/face-id-add.modal'
import { usePermissions } from '@/shared/guards/usePermissions'
import { useAuthMe } from '@/config/queries/auth/verify.queries'

export const FaceIDModal = () => {
	const { open, id, onClose } = useFaceIDStore()
	const { onOpen: addOnOpen } = useFaceIDAddStore()
	const { data: faceIdDevices } = useGetAllFaceIdByOrg(id!)
	const { hasPermission } = usePermissions()
	const { data: user } = useAuthMe()

	return (
		<Modal
			title='Face ID qurilmalar'
			width='1200px'
			footer={false}
			open={open}
			onClose={onClose}
			onCancel={onClose}
			destroyOnClose>
			<div className='w-full flex items-center justify-end'>
				{user && hasPermission(user?.role, ['create:*']) && (
					<Button
						type='primary'
						onClick={() => addOnOpen(id!)}>
						Yangi qurilma qo'shish
					</Button>
				)}
			</div>
			<FaceIdDevicesTable devices={faceIdDevices?.pages?.flatMap(page => page.data) ?? []} />
			<FaceIdAddModal />
		</Modal>
	)
}
