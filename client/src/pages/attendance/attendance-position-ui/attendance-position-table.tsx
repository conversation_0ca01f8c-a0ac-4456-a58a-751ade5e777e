import { useGetAllPositionAttendance } from '@/config/queries/attendance/get-all-attendance-position'
import { Typography, Table, Spin } from 'antd'
import { useMemo, useState, useEffect } from 'react'
import { getDatesBetween } from '@/shared/utils/getDatesBetween'
import { useSearchParams } from 'react-router-dom'

import { useAttendancePositionColumns } from './attendance-position-column'
import dayjs from 'dayjs'
import { DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'
import { AttendancePositionReportInfo } from './attendace-position-report-info'

// Dayjs setup
dayjs.tz.setDefault(DEFAULT_TIMEZONE)

const { Title } = Typography

const AttendancePositionTable = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const startDate = searchParams.get('startDate')
	const endDate = searchParams.get('endDate')

	const currentPage = parseInt(searchParams.get('page') || '1')
	const pageSize = parseInt(searchParams.get('limit') || '20')

	const { data: attendanceData, isLoading } = useGetAllPositionAttendance()
	//@ts-ignore
	const [selectedWorkerId, setSelectedWorkerId] = useState<string | null>(null)

	const updateSearchParams = (newPage: number, newPageSize: number) => {
		const newParams = new URLSearchParams(searchParams)
		newParams.set('page', newPage.toString())
		newParams.set('limit', newPageSize.toString())
		setSearchParams(newParams)
	}

	useEffect(() => {
		if (currentPage !== 1) {
			updateSearchParams(1, pageSize)
		}
	}, [startDate, endDate])

	const days = useMemo(() => {
		if (startDate && endDate) {
			return getDatesBetween(dayjs(startDate).toDate(), dayjs(endDate).toDate())
		}
		if (startDate) {
			return getDatesBetween(dayjs(startDate).toDate(), dayjs().toDate())
		}
		return []
	}, [startDate, endDate])

	const tableData = useMemo(() => {
		if (!attendanceData || !attendanceData.workers) return []

		return attendanceData.workers.map(worker => ({
			key: worker.employee.id,
			id: worker.employee.id,
			fullName: worker.employee.fullName,
			position: worker.employee.position,
			organization: worker?.employee?.organization?.name,
			summary: worker.summary,
			dailyReports: worker.dailyAttendance,
			workSchedule: worker.workSchedule,
			breakSchedule: worker.breakSchedule
		}))
	}, [attendanceData])

	const columns = useAttendancePositionColumns(days, setSelectedWorkerId)

	const handleTableChange = (pagination: any) => {
		updateSearchParams(pagination.current, pagination.pageSize)
	}

	return (
		<>
			<Title
				level={4}
				className='dark:text-gray-200'
				style={{ marginBottom: '20px' }}>
				Lavozim bo'yicha davomat hisoboti
			</Title>

			{attendanceData && attendanceData.reportPeriod && (
				<AttendancePositionReportInfo reportPeriod={attendanceData.reportPeriod} />
			)}

			{isLoading ? (
				<div className='flex justify-center items-center py-20'>
					<Spin
						size='large'
						tip="Ma'lumotlar yuklanmoqda..."
					/>
				</div>
			) : (
				<div className='dark:bg-gray-800 dark:text-gray-200 overflow-hidden rounded-lg'>
					<Table
						bordered
						rowKey='id'
						size='small'
						className='attendance-table overflow-auto'
						scroll={{ x: 'max-content' }}
						columns={columns as any}
						dataSource={tableData}
						locale={{ emptyText: "Ma'lumotlar topilmadi" }}
						pagination={{
							current: currentPage,
							pageSize: pageSize,
							total: attendanceData?.meta?.total || 0,
							showSizeChanger: true,
							pageSizeOptions: [10, 20, 50, 100],
							showTotal: total => `Jami: ${total} xodim`
						}}
						onChange={handleTableChange}
					/>
				</div>
			)}
		</>
	)
}
export default AttendancePositionTable
