import { axiosPrivate } from '@/config/api'
import { organizationEndpoints } from '@/config/api/endpoints'
import { useInfiniteQuery } from '@tanstack/react-query'
import { TUser } from '../users/get-all.queries'
import { ApiResponse } from '../types'
import { useSearchParams } from 'react-router-dom'
import { useState } from 'react'

export enum ROLE {
	worker = 'worker',
	employee = 'employee',
	responsible = 'responsible'
}

export const useGetUsersByOrg = (orgId: string, role: ROLE, limit = 10) => {
	const [searchParams] = useSearchParams()
	const [page, setPage] = useState(1)

	const roles = searchParams.get('roles')
	const search = searchParams.get('search')

	return {
		...useInfiniteQuery({
			queryKey: [
				organizationEndpoints.usersByOrg(orgId, !roles ? role : undefined),
				'infinite',
				{ roles, search }
			],
			queryFn: async ({ pageParam = 1 }) => {
				const response = await axiosPrivate.get<ApiResponse<TUser[]>>(
					`${organizationEndpoints.usersByOrg(orgId, !roles ? role : undefined)}`,
					{
						params: {
							page: pageParam,
							limit,
							role: JSON.parse(roles || '[]').length > 0 ? roles : undefined,
							search
						}
					}
				)
				return response.data
			},
			initialPageParam: page,
			getNextPageParam: (lastPage: ApiResponse<TUser[]>) => {
				if (!lastPage.meta || !lastPage.meta.hasMore) {
					return undefined
				}

				return lastPage.meta.page + 1
			},
			getPreviousPageParam: (lastPage: ApiResponse<TUser[]>) => {
				if (!lastPage.meta || lastPage.meta.page === 1) {
					return undefined
				}
				return lastPage.meta.page - 1
			},
			enabled: !!orgId
		}),
		setPage
	}
}
