import { TOrganization } from '@/config/queries/organizations/get-all.queries'
import { Table } from 'antd'
import { Link } from 'react-router-dom'

type Props = {
	organizations: TOrganization[]
}

export const Organizations = ({ organizations }: Props) => {
	return (
		<div>
			<Table
				columns={[
					{
						title: 'Nomi',
						key: 'name',
						render: (_, rec) => <Link to={`/workspace/organizations/${rec.id}`}>{rec.name}</Link>
					},
					{ title: '<PERSON><PERSON><PERSON>', dataIndex: 'address', key: 'address' },
					{ title: 'Telefoni', dataIndex: 'phone', key: 'phone' }
				]}
				dataSource={organizations}
			/>
		</div>
	)
}
