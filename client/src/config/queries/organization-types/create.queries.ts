import { axiosPrivate } from '@/config/api'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { organizationTypeEndpoints } from '@/config/api/endpoints'
import { notification } from 'antd'

export interface OrganizationTypes {
	id?: string
	name: string
}

export const useCreateOrganizationType = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async (data: OrganizationTypes) =>
			await axiosPrivate.post<ApiResponse<OrganizationTypes>>(organizationTypeEndpoints.all, data),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [organizationTypeEndpoints.all] })
			notification.success({
				message: 'Muva<PERSON>aqiyatli yaratildi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: 'Yarat<PERSON>da xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
