import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/common';
import { AttendanceReportQueryDto } from '../dto/attendance-report.dto';
import { AttendanceHelperService } from './attendance-helper.service';
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import 'dayjs/locale/uz-latn';

dayjs.extend(utc);
dayjs.locale('uz-latn');

@Injectable()
export class AttendanceEmployeeReportService {
  constructor(
    private prismaService: PrismaService,
    private helperService: AttendanceHelperService,
  ) {}

  /**
   * Получает детальный отчет о посещаемости для конкретного сотрудника
   */
  async getEmployeeDetailedAttendanceReport(query: AttendanceReportQueryDto) {
    try {
      const { userId, startDate, endDate } = query;

      if (!userId) {
        throw new NotFoundException('User ID is required');
      }

      // Правильная обработка временной зоны
      const { startDateTime, endDateTime } = this.helperService.processDateRange(startDate, endDate);
      const dateFilter = this.helperService.createDateFilter(startDateTime, endDateTime);

      // Получаем полную информацию о пользователе
      const user = await this.prismaService.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          fullName: true,
          username: true,
          phone: true,
          position: {
            select: {
              id: true,
              name: true,
              description: true,
              type: true,
            },
          },
          role: true,
          AttendanceReport: {
            where: dateFilter,
            select: {
              id: true,
              date: true,
              enter: true,
              exit: true,
              minutes: true,
              description: true,
              earlyExit: true,
              lateEnter: true,
              scheduledEnter: true,
              scheduledExit: true,
              scheduledMinutes: true,
              dayNumber: true,
              underTimeMinutes: true,
              isLate: true,
              isEarlyExit: true,
            },
            orderBy: {
              date: 'asc',
            },
          },
          UserWorkingSchedule: {
            select: {
              id: true,
              days: {
                select: {
                  id: true,
                  day: true,
                  startTime: true,
                  endTime: true,
                },
              },
            },
          },
          Organization: {
            select: {
              id: true,
              name: true,
              phone: true,
              address: true,
              description: true,
              grade: true,
            },
            take: 1,
          },
          MainOrganization: {
            select: {
              name: true,
              id: true,
            },
          },
        },
      });

      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Создаём массив дат для периода
      const periodDates = this.helperService.createPeriodDates(startDateTime, endDateTime);

      // Анализируем каждый день
      const userScheduleDays = user.UserWorkingSchedule?.days || [];
      const userBreak = {
        startHour: 8,
        startMinute: 0,
        endHour: 9,
        endMinute: 0,
      };

      const daysAnalysis = periodDates.map((date: dayjs.Dayjs) => {
        const dayNumber = date.day() === 0 ? 7 : date.day();
        const formattedDate = this.helperService.formatDate(date.toDate());
        const report = user.AttendanceReport.find((report: any) => {
          const reportDate = dayjs.utc(report.date);
          return this.helperService.formatDate(reportDate.toDate()) === formattedDate;
        });
        const scheduleForDay = userScheduleDays.find((day: any) => {
          return Number(day.day) === dayNumber;
        });

        // Логика анализа дней такая же, как в существующем методе
        if (dayNumber === 7) {
          return {
            day: dayNumber,
            date: date.toDate(),
            formattedDate: formattedDate,
            status: 'Dam olish kuni',
            isWorkDay: false,
            cameToWork: false,
            scheduledStartTime: null,
            scheduledEndTime: null,
            actualEnterTime: null,
            actualExitTime: null,
            minutesWorked: 0,
            lateMinutes: 0,
            earlyExitMinutes: 0,
            scheduledEnter: null,
            scheduledExit: null,
            scheduledMinutes: null,
            underTimeMinutes: null,
            isLate: false,
            isEarlyExit: false,
            earlyExitValue: null,
            lateEnterValue: null,
          };
        }
        if (!scheduleForDay && report) {
          // Ish jadvali yo'q, lekin kelgan
          return {
            day: dayNumber,
            date: date.toDate(),
            formattedDate: formattedDate,
            status: 'Xodim ishga keldi',
            isWorkDay: true,
            cameToWork: true,
            scheduledStartTime: null,
            scheduledEndTime: null,
            actualEnterTime: report.enter,
            actualExitTime: report.exit,
            minutesWorked: report.minutes || 0,
            lateMinutes: report.lateEnter || 0,
            earlyExitMinutes: report.earlyExit || 0,
            scheduledEnter: report.scheduledEnter,
            scheduledExit: report.scheduledExit,
            scheduledMinutes: report.scheduledMinutes,
            underTimeMinutes: report.underTimeMinutes,
            isLate: report.isLate,
            isEarlyExit: report.isEarlyExit,
            earlyExitValue: report.earlyExit,
            lateEnterValue: report.lateEnter,
          };
        }
        if (!scheduleForDay) {
          // Ish jadvali yo'q va kelmagan
          return {
            day: dayNumber,
            date: date.toDate(),
            formattedDate: formattedDate,
            status: 'Dam olish kuni',
            isWorkDay: false,
            cameToWork: false,
            scheduledStartTime: null,
            scheduledEndTime: null,
            actualEnterTime: null,
            actualExitTime: null,
            minutesWorked: 0,
            lateMinutes: 0,
            earlyExitMinutes: 0,
            scheduledEnter: null,
            scheduledExit: null,
            scheduledMinutes: null,
            underTimeMinutes: null,
            isLate: false,
            isEarlyExit: false,
            earlyExitValue: null,
            lateEnterValue: null,
          };
        }
        if (!report) {
          // Ish kuni, lekin kelmagan
          return {
            day: dayNumber,
            date: date.toDate(),
            formattedDate: formattedDate,
            status: 'Ishga kelmadi',
            isWorkDay: true,
            cameToWork: false,
            scheduledStartTime: scheduleForDay.startTime,
            scheduledEndTime: scheduleForDay.endTime,
            actualEnterTime: null,
            actualExitTime: null,
            minutesWorked: 0,
            lateMinutes: 0,
            earlyExitMinutes: 0,
            scheduledEnter: null,
            scheduledExit: null,
            scheduledMinutes: null,
            underTimeMinutes: null,
            isLate: false,
            isEarlyExit: false,
            earlyExitValue: null,
            lateEnterValue: null,
            expectedWorkday: true,
          };
        }
        // To'liq AttendanceReport maydonlarini qaytaramiz
        return {
          day: report.dayNumber || dayNumber,
          date: report.date,
          formattedDate: formattedDate,
          status: '',
          isWorkDay: true,
          cameToWork: true,
          scheduledStartTime: report.scheduledEnter,
          scheduledEndTime: report.scheduledExit,
          actualEnterTime: report.enter,
          actualExitTime: report.exit,
          minutesWorked: report.minutes || 0,
          lateMinutes: report.lateEnter || 0,
          earlyExitMinutes: report.earlyExit || 0,
          scheduledEnter: report.scheduledEnter,
          scheduledExit: report.scheduledExit,
          scheduledMinutes: report.scheduledMinutes,
          underTimeMinutes: report.underTimeMinutes,
          isLate: report.isLate,
          isEarlyExit: report.isEarlyExit,
          earlyExitValue: report.earlyExit,
          lateEnterValue: report.lateEnter,
        };
      });

      // Сводная статистика
      const totalWorkDays = daysAnalysis.filter((day: any) => day.isWorkDay).length;
      const totalAttendanceDays = daysAnalysis.filter((day: any) => day.cameToWork).length;
      const totalLateMinutes = daysAnalysis.reduce((sum: number, day: any) => sum + (day.lateMinutes || 0), 0);
      const totalEarlyExitMinutes = daysAnalysis.reduce((sum, day) => sum + (day.earlyExitMinutes || 0), 0);
      const totalMinutesWorked = daysAnalysis.reduce((sum: number, day: any) => sum + (day.minutesWorked || 0), 0);
      const totalWorkMinutesPlanned = daysAnalysis
        .filter((day: any) => day.isWorkDay)
        .reduce((sum: number, day: any) => sum + (day.scheduledMinutes || 0), 0);

      return {
        reportPeriod: {
          startDate: startDateTime.toDate(),
          endDate: endDateTime.toDate(),
        },
        employee: {
          id: user.id,
          fullName: user.fullName,
          position: user.position,
          organization: user.Organization[0] || user.MainOrganization,
        },
        workSchedule:
          user.UserWorkingSchedule?.days?.map((day: any) => ({
            dayNumber: Number(day.day),
            dayName: this.helperService.getDayName(Number(day.day)),
            startTime: day.startTime,
            endTime: day.endTime,
          })) || [],
        breakSchedule: {
          startTime: `${userBreak.startHour.toString().padStart(2, '0')}:${userBreak.startMinute.toString().padStart(2, '0')}`,
          endTime: `${userBreak.endHour.toString().padStart(2, '0')}:${userBreak.endMinute.toString().padStart(2, '0')}`,
        },
        summary: {
          totalWorkDays,
          totalAttendanceDays,
          totalLateMinutes,
          totalEarlyExitMinutes,
          totalMinutesWorked,
          totalWorkMinutesPlanned,
          attendanceRate: totalWorkDays > 0 ? (totalAttendanceDays / totalWorkDays) * 100 : 100,
          missedWorkdays: totalWorkDays - totalAttendanceDays,
          factHours: Math.floor(totalMinutesWorked / 60) + ':' + (totalMinutesWorked % 60).toString().padStart(2, '0'),
        },
        dailyReports: daysAnalysis,
      };
    } catch (error) {
      throw new NotFoundException(`Error fetching employee attendance report: ${error.message}`);
    }
  }
}
