import { Injectable } from '@nestjs/common';
import { AttendanceType, GPSLocationType, RecipientAnswerState } from '@prisma/client';
import { PrismaService } from 'src/common';
import { ApiResponse } from 'src/helpers/apiRespons';

@Injectable()
export class ReferencesService {
  constructor(private readonly prismaService: PrismaService) {}

  async taskTypes() {
    const taskTypes = await this.prismaService.taskType.findMany();
    return taskTypes;
  }

  async grade() {
    const grades = await this.prismaService.grade.findMany({
      where: {
        level: {
          gte: 0,
        },
      },
    });
    return grades;
  }

  async taskStates() {
    const taskStates = await this.prismaService.taskState.findMany();
    return taskStates;
  }

  async attendanceTypes() {
    return AttendanceType;
  }

  async gpsLocationTypes() {
    return GPSLocationType;
  }

  async organizationTypes() {
    const organizationTypes = await this.prismaService.organizationType.findMany();
    return organizationTypes;
  }

  async organizationPositions(grade?: number) {
    let gradeOrganizationType: string | undefined;

    if (grade) {
      const organization = await this.prismaService.organization.findFirst({
        where: {
          grade: {
            level: grade,
          },
        },
      });

      if (organization) {
        gradeOrganizationType = organization.typeId;
      }
    }

    const organizationPositions = await this.prismaService.organizationTypePosition.findMany({
      where: {
        typeId: gradeOrganizationType,
      },
    });

    return organizationPositions;
  }

  async recipientAnswerTypes() {
    const recipientAnswerTypes = await this.prismaService.recipientAnswerType.findMany();
    return recipientAnswerTypes;
  }

  async recipientAnswerStates() {
    return RecipientAnswerState;
  }

  async regions() {
    const regions = await this.prismaService.region.findMany();
    return regions;
  }

  async regionDistrict(id: string) {
    const district = await this.prismaService.district.findMany({ where: { regionId: id } });
    return district;
  }

  async districts() {
    const regions = await this.prismaService.district.findMany();
    return regions;
  }

  async districtSection(id: string) {
    const district = await this.prismaService.section.findMany({ where: { districtId: id } });
    return district;
  }
}
