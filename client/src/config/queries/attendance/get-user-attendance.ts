import { axiosPrivate } from '@/config/api'
import { attendanceUserEndpoints } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'
import { useParams } from 'react-router-dom'
import { TOrganization } from '../organizations/get-all.queries'
import { TUser } from '../users/get-all.queries'
import { TPaginationWrapper } from '@/shared/types/pagination.type'

export type TUserAttendance = {
	id: string
	Organization: TOrganization
	User: TUser
	createdById: string | null
	description: string | null
	organizationId: string
	time: string
	type: 'ENTER' | 'EXIT'
	userId: string
	createdAt: string
	updatedAt: string
}

export const useGetUserAttendance = (userId?: string) => {
	const { id } = useParams()

	return useQuery({
		enabled: !!userId || !!id,
		queryKey: [attendanceUserEndpoints.userAttendance(userId ?? id!), userId, id],
		queryFn: async () => {
			return (
				await axiosPrivate.get<TPaginationWrapper<TUserAttendance[]>>(
					attendanceUserEndpoints.userAttendance(userId ?? id!)
				)
			).data
		}
	})
}
