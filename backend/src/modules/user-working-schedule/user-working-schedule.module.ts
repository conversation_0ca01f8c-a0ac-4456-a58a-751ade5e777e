import { Module } from '@nestjs/common';
import { UserWorkingScheduleService } from './user-working-schedule.service';
import { UserWorkingScheduleController } from './user-working-schedule.controller';
import { PrismaService } from '../../common';

@Module({
  controllers: [UserWorkingScheduleController],
  providers: [UserWorkingScheduleService, PrismaService],
})
export class UserWorkingScheduleModule {}
