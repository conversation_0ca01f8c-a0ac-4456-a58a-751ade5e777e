import { TUser } from '@/config/queries/users/get-all.queries'
import { create } from 'zustand'

interface UsersModalStore {
	open: boolean
	data: TUser | null
	onOpen: (data?: TUser) => void
	onClose: () => void
}

export const useUsersModalStore = create<UsersModalStore>(set => ({
	open: false,
	data: null,
	onOpen: data => set({ open: true, data: data || null }),
	onClose: () => set({ open: false, data: null })
}))
