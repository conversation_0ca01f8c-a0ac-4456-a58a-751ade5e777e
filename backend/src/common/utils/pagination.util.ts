import { Injectable } from '@nestjs/common';
import { Type } from 'class-transformer';
import { IsOptional, IsString } from 'class-validator';

export class PaginationParams {
  @IsOptional()
  @Type(() => Number)
  page?: number;

  @IsOptional()
  @Type(() => Number)
  limit?: number;

  @IsString()
  @IsOptional()
  search?: string;
}

export type PaginatedResult<T> = {
  data: T[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasMore: boolean;
  };
};

// Интерфейс для типизации Prisma моделей
export interface PrismaModel<T, W = any, I = any, O = any, S = any> {
  findMany(args: { where?: W; include?: I; orderBy?: O; skip?: number; take?: number; select?: S }): Promise<T[]>;

  count(args: { where?: W }): Promise<number>;
}

@Injectable()
export class PaginationUtil {
  async paginate<T, W = any, I = any, O = any, S = any>(
    model: PrismaModel<T, W, I, O>,
    params: PaginationParams = {},
    where?: W,
    include?: I,
    orderBy?: O,
    select?: S,
  ): Promise<PaginatedResult<T>> {
    const { page = 1, limit = 10 } = params;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      model.findMany({
        where: where || ({} as W),
        include: include || null,
        orderBy: (orderBy ? { ...orderBy } : { createdAt: 'desc' }) as O,
        skip,
        take: limit,
        select: select || undefined,
      }),
      model.count({ where: where || ({} as W) }),
    ]);

    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
        hasMore: skip + data.length < total,
      },
    };
  }

  paginateFromArray<T>(array: T[], params: PaginationParams = {}): PaginatedResult<T> {
    const { page = 1, limit = 10 } = params;
    const start = (page - 1) * limit;
    const end = start + limit;
    const paginated = array.slice(start, end);

    return {
      data: paginated,
      meta: {
        total: array.length,
        page,
        limit,
        totalPages: Math.ceil(array.length / limit),
        hasMore: end < array.length,
      },
    };
  }
}
