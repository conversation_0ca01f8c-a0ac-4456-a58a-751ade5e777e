import { Injectable } from '@nestjs/common';
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import 'dayjs/locale/uz-latn';
import * as timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.locale('uz-latn');
dayjs.extend(timezone);

const tz = 'Asia/Tashkent';
dayjs.tz.setDefault(tz);

@Injectable()
export class AttendanceHelperService {
  /**
   * Вспомогательный метод для получения названия дня недели по его номеру
   */
  getDayName(dayNumber: number): string {
    const days = ['', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>'];
    return days[dayNumber] || '';
  }

  /**
   * Вспомогательный метод для расчета количества рабочих дней для пользователя
   * в заданном периоде на основе его рабочего графика
   */
  calculateWorkDays(scheduleDays: { day: number }[], startDate: dayjs.Dayjs, endDate: dayjs.Dayjs): number {
    // Если нет рабочих дней в графике, считаем все дни рабочими (кроме воскресенья)
    if (!scheduleDays || scheduleDays.length === 0) {
      scheduleDays = [{ day: 1 }, { day: 2 }, { day: 3 }, { day: 4 }, { day: 5 }, { day: 6 }];
    }

    const workDayNumbers = scheduleDays.map((d) => Number(d.day));
    let currentDate = startDate.clone();
    let workDaysCount = 0;

    while (currentDate.isBefore(endDate) || currentDate.isSame(endDate, 'day')) {
      const dayNumber = currentDate.day() === 0 ? 7 : currentDate.day();
      if (workDayNumbers.includes(dayNumber)) {
        workDaysCount++;
      }
      currentDate = currentDate.add(1, 'day');
    }

    return workDaysCount;
  }

  /**
   * Извлекает время из различных форматов
   */
  extractTimeOnly(timeValue: any): { hour: number; minute: number } | null {
    if (typeof timeValue === 'string') {
      const timeObj = dayjs.utc(timeValue);
      if (timeObj.isValid()) {
        return { hour: timeObj.utc().hour(), minute: timeObj.utc().minute() };
      }
    } else if (timeValue instanceof Date || (typeof timeValue === 'object' && timeValue !== null)) {
      try {
        const timeObj = dayjs.utc(timeValue);
        if (timeObj.isValid()) {
          return { hour: timeObj.utc().hour(), minute: timeObj.utc().minute() };
        }
      } catch (e) {}
    }
    return null;
  }

  /**
   * Форматирует дату в строку
   */
  formatDate(date: Date): string {
    return dayjs.tz(date, tz).format('YYYY-MM-DD');
  }

  currentDate(date: Date | string | dayjs.Dayjs): dayjs.Dayjs {
    return dayjs.tz(date, tz).startOf('day');
  }

  /**
   * Создает массив дат для периода
   */
  createPeriodDates(startDateTime: dayjs.Dayjs, endDateTime: dayjs.Dayjs): dayjs.Dayjs[] {
    const periodDates = [];
    let currentDate = dayjs.tz(startDateTime, tz).startOf('day');

    while (currentDate.isBefore(endDateTime.tz(tz)) || currentDate.isSame(endDateTime.tz(tz), 'day')) {
      periodDates.push(currentDate.clone());
      currentDate = currentDate.add(1, 'day');
    }

    return periodDates;
  }

  /**
   * Обрабатывает временную зону для дат
   */
  processDateRange(startDate?: string, endDate?: string) {
    let startDateTime: dayjs.Dayjs;
    let endDateTime: dayjs.Dayjs;

    if (startDate && endDate) {
      startDateTime = dayjs(startDate);
      endDateTime = dayjs(endDate);

      startDateTime = startDateTime.utc();
      endDateTime = endDateTime.utc();
    } else {
      const now = dayjs().utc();
      endDateTime = now.endOf('day');
      startDateTime = now.subtract(7, 'day').startOf('day');
    }

    return { startDateTime, endDateTime };
  }

  /**
   * Создает фильтр для даты
   */
  createDateFilter(startDateTime: dayjs.Dayjs, endDateTime: dayjs.Dayjs) {
    return {
      date: {
        gte: startDateTime.toDate(),
        lte: endDateTime.toDate(),
      },
    };
  }
}
