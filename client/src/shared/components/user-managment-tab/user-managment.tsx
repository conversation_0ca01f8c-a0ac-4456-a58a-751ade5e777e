import { Button } from 'antd'
import { Users2Icon } from 'lucide-react'
import { useUserManagementStore } from './utils/user-managament-store'
import { useParams } from 'react-router-dom'

interface Props {
	currentId?: string
}

export const UserManagment = ({ currentId }: Props) => {
	const { onOpen } = useUserManagementStore()
	const { id } = useParams()
	return (
		<>
			<Button onClick={() => onOpen(currentId ?? id)}>
				<Users2Icon />
			</Button>
		</>
	)
}
