import { Controller, Get, Post, Body, Patch, Param, Delete, Query, Put } from '@nestjs/common';
import { OrganizationTypePositionService } from './organization-type-position.service';
import { CreateOrganizationTypePositionDto } from './dto/create-organization-type-position.dto';
import { UpdateOrganizationTypePositionDto } from './dto/update-organization-type-position.dto';
import { BaseQueryParams } from 'src/helpers/types';
import { PaginationParams, ProtectedRoute } from 'src/common';

@Controller('organization-type-position')
export class OrganizationTypePositionController {
  constructor(private readonly organizationTypePositionService: OrganizationTypePositionService) {}

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
    onlyAdmin: true,
  })
  @Post()
  async create(@Body() CreateOrganizationTypePositionDto: CreateOrganizationTypePositionDto) {
    return await this.organizationTypePositionService.create(CreateOrganizationTypePositionDto);
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
    onlyAdmin: true,
  })
  @Get()
  async findAll(@Query() query: BaseQueryParams) {
    return await this.organizationTypePositionService.findAll(query);
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
    onlyAdmin: false,
  })
  @Get('/type/:typeId')
  async findOne(@Param('typeId') typeId: string, @Query() query: BaseQueryParams) {
    return await this.organizationTypePositionService.findByOrganizationType(typeId, query);
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
    onlyAdmin: true,
  })
  @Put(':id')
  async update(@Param('id') id: string, @Body() updateOrganizationTypePositionDto: UpdateOrganizationTypePositionDto) {
    return await this.organizationTypePositionService.update(id, updateOrganizationTypePositionDto);
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
    onlyAdmin: true,
  })
  @Delete(':id')
  async remove(@Param('id') id: string) {
    return await this.organizationTypePositionService.remove(id);
  }
}
