import { axiosPrivate } from '@/config/api'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { TaskTypes } from './create.queries'
import { taskTypesEndpoints } from '@/config/api/endpoints'
import { notification } from 'antd'

export const useUpdateTaskType = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async ({ id, ...data }: { id: string }) =>
			(await axiosPrivate.patch<ApiResponse<TaskTypes>>(taskTypesEndpoints.one(id), data)).data,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [taskTypesEndpoints.all] })
			notification.success({
				message: 'Muvaffaqiyatli yangilandi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: '<PERSON><PERSON>sh<PERSON> xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
