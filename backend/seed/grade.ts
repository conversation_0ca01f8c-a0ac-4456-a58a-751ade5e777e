import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  const grades = [
    {
      level: -1,
      name: 'Founder',
      description: 'Founder level',
    },
    { level: 0, name: 'Respublika', description: 'Respublika darajasidagi tashkilotlar' },
    { level: 10, name: 'Viloyat', description: 'Viloyat darajasidagi tashkilotlar' },
    { level: 20, name: 'Tuman', description: 'Tuman darajasidagi tashkilotlar' },
    { level: 30, name: '<PERSON><PERSON><PERSON>', description: 'Mahalla darajasidagi tashkilotlar' },
    { level: 40, name: '<PERSON><PERSON><PERSON>', description: 'Boshqa tashkilotlar' },
  ];

  for (const grade of grades) {
    const existingGrade = await prisma.grade.findUnique({
      where: { name: grade.name },
    });

    if (existingGrade) {
      console.log(`Grade with name "${grade.name}" already exists.`);
      continue;
    }

    await prisma.grade.create({
      data: grade,
    });

    console.log(`Grade with name "${grade.name}" created.`);
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
