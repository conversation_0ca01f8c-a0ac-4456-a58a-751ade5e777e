import { axiosPrivate } from '@/config/api'
import { botEndpoint } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'

import { message, notification } from 'antd'

export const useDeleteBot = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async (id: string) => {
			return await axiosPrivate.delete(botEndpoint.one(id))
		},
		onSuccess: async () => {
			queryClient.invalidateQueries({ queryKey: [botEndpoint.all] })
			notification.success({
				message: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>i oc'hirildi ",
				placement: 'bottomRight'
			})
		},

		onError: async (error: any) => {
			message.error(error?.response?.data?.message)
		}
	})
}
