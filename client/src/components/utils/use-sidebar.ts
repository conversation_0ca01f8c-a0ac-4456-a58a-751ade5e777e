import { create } from 'zustand'

interface SidebarState {
	collapsed: boolean
	isLg: boolean
	setLg: (isLg: boolean) => void
	toggleSidebar: (collapsed?: boolean) => void
}

export const useSidebar = create<SidebarState>(set => {
	window.addEventListener('resize', () => {
		const isMatched = window.matchMedia('(max-width: 992px)').matches
		if (isMatched) {
			set({ collapsed: true })
		} else {
			set({ collapsed: false })
		}
	})
	return {
		collapsed: false,
		isLg: true,
		setLg: (isLg: boolean) => set({ isLg }),
		toggleSidebar: (collapsed?: boolean) => {
			set(state => ({ collapsed: collapsed ?? !state.collapsed }))
		}
	}
})
