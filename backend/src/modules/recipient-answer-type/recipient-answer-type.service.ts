import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { PaginationUtil, PrismaService, QueryUtil } from '../../common';
import { CreateRecipientAnswerTypeDto, UpdateRecipientAnswerTypeDto } from './dto/recipient-answer-type.dto';
import { Status } from '@prisma/client';
import { BaseQueryParams } from 'src/helpers/types';

@Injectable()
export class RecipientAnswerTypeService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly paginationUtil: PaginationUtil,
    private readonly queryUtil: QueryUtil,
  ) {}

  async create(createRecipientAnswerTypeDto: CreateRecipientAnswerTypeDto) {
    const existingRecipientAnswerType = await this.prisma.recipientAnswerType.findUnique({
      where: { name: createRecipientAnswerTypeDto.name },
    });

    if (existingRecipientAnswerType) {
      throw new BadRequestException(`Name "${createRecipientAnswerTypeDto.name}" already exists`);
    }

    const data = await this.prisma.recipientAnswerType.create({ data: createRecipientAnswerTypeDto });
    return data;
  }

  async findAll({ limit, page, search }: BaseQueryParams) {
    const $page = page ? parseInt(page) : 1;
    const $limit = limit ? parseInt(limit) : 10;

    const where = {
      ...this.queryUtil.createSearchQuery(search, ['name', 'id']),
      status: 'ACTIVE' as Status,
    };

    return this.paginationUtil.paginate(this.prisma.recipientAnswerType, { page: $page, limit: $limit }, where);
  }

  async findOne(id: string) {
    const recipientAnswerType = await this.prisma.recipientAnswerType.findUnique({ where: { id } });
    if (!recipientAnswerType) {
      throw new NotFoundException(`RecipientAnswerType with ID "${id}" not found`);
    }
    return recipientAnswerType;
  }

  async update(id: string, updateRecipientAnswerTypeDto: UpdateRecipientAnswerTypeDto) {
    const recipientAnswerType = await this.prisma.recipientAnswerType.findUnique({
      where: { id },
    });

    if (!recipientAnswerType) {
      throw new NotFoundException(`RecipientAnswerType with ID "${id}" not found`);
    }

    if (updateRecipientAnswerTypeDto.name && updateRecipientAnswerTypeDto.name !== recipientAnswerType.name) {
      const existingRecipientAnswerTypeByName = await this.prisma.recipientAnswerType.findUnique({
        where: { name: updateRecipientAnswerTypeDto.name },
      });

      if (existingRecipientAnswerTypeByName) {
        throw new BadRequestException(`Name "${updateRecipientAnswerTypeDto.name}" already exists`);
      }
    }

    return this.prisma.recipientAnswerType.update({
      where: { id },
      data: updateRecipientAnswerTypeDto,
    });
  }

  async changeStatus(id: string, status: Status) {
    const recipientAnswerType = await this.prisma.recipientAnswerType.findUnique({ where: { id } });

    if (!recipientAnswerType) {
      throw new NotFoundException(`RecipientAnswerType with ID "${id}" not found`);
    }

    return this.prisma.recipientAnswerType.update({
      where: { id },
      data: { status },
    });
  }
}
