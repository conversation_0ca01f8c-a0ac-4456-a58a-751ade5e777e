/*
  Warnings:

  - You are about to drop the column `userWorkingScheduleId` on the `UserWorkingScheduleDay` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "UserWorkingScheduleDay" DROP CONSTRAINT "UserWorkingScheduleDay_userWorkingScheduleId_fkey";

-- DropIndex
DROP INDEX "UserWorkingScheduleDay_userWorkingScheduleId_idx";

-- AlterTable
ALTER TABLE "UserWorkingScheduleDay" DROP COLUMN "userWorkingScheduleId";

-- CreateTable
CREATE TABLE "_UserWorkingScheduleToUserWorkingScheduleDay" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_UserWorkingScheduleToUserWorkingScheduleDay_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_UserWorkingScheduleToUserWorkingScheduleDay_B_index" ON "_UserWorkingScheduleToUserWorkingScheduleDay"("B");

-- AddForeignKey
ALTER TABLE "_UserWorkingScheduleToUserWorkingScheduleDay" ADD CONSTRAINT "_UserWorkingScheduleToUserWorkingScheduleDay_A_fkey" FOREIGN KEY ("A") REFERENCES "UserWorkingSchedule"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_UserWorkingScheduleToUserWorkingScheduleDay" ADD CONSTRAINT "_UserWorkingScheduleToUserWorkingScheduleDay_B_fkey" FOREIGN KEY ("B") REFERENCES "UserWorkingScheduleDay"("id") ON DELETE CASCADE ON UPDATE CASCADE;
