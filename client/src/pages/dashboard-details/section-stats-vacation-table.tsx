import { useGetDashboardDetailsVacation } from '@/config/queries/dashboard-details/get-dashboard-details'
import { useGetVacationExcel } from '@/config/queries/dashboard-details/get-dashboard-details-excel'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { Button, Table } from 'antd'
import { DownloadOutlined } from '@ant-design/icons'
import { useSearchParams } from 'react-router-dom'
import dayjs from 'dayjs'

const SectionStatsVacationTable = () => {
	const { data: employees, isLoading } = useGetDashboardDetailsVacation()
	const [searchParams, setSearchParams] = useSearchParams()
	const organizationId = searchParams.get('orgId')
	const vacationExcel = useGetVacationExcel()

	const currentPage = parseInt(searchParams.get('page') || '1')
	const pageSize = parseInt(searchParams.get('limit') || '10')

	const handleDownloadExcel = () => {
		vacationExcel.mutate(organizationId || undefined)
	}

	const columns = [
		{
			key: '#',
			title: '#',
			render: (_: any, _rec: any, index: number) => formatTableIndex(currentPage, pageSize, index)
		},
		{
			title: 'Foydalanuvchi',
			dataIndex: ['user', 'fullName'],
			key: 'user'
		},

		{
			title: 'Boshlanish vaqti',
			dataIndex: 'begin',
			key: 'begin',
			render: (date: string) => dayjs(date).format('YYYY-MM-DD')
		},
		{
			title: 'Tugash vaqti',
			dataIndex: 'end',
			key: 'end',
			render: (date: string) => dayjs(date).format('YYYY-MM-DD')
		},
		{
			title: 'Izoh',
			dataIndex: 'description',
			key: 'description'
		},
		{
			title: 'Lavozimi',
			dataIndex: ['user', 'position', 'name'],
			key: 'positionName'
		},
		{
			title: 'Tashkilot',
			dataIndex: ['user', 'Organization'],
			key: 'organizationName',
			render: (_: any, record: any) =>
				record.user.Organization.map((org: any) => org.name).join(', ')
		}
	]

	const handleTableChange = (pagination: any) => {
		const newPage = pagination.current
		const newPageSize = pagination.pageSize

		// Update URL parameters
		setSearchParams(prev => {
			const newParams = new URLSearchParams(prev)
			newParams.set('page', newPage.toString())
			newParams.set('limit', newPageSize.toString())
			return newParams
		})
	}

	return (
		<div className='p-4'>
			<div className='flex justify-between items-center mb-4'>
				<h1 className='text-2xl font-bold'>Ta'tildagilar ro`yhati</h1>
				<Button
					type='primary'
					icon={<DownloadOutlined />}
					onClick={handleDownloadExcel}
					loading={vacationExcel.isPending}>
					Excel yuklab olish
				</Button>
			</div>
			<Table
				columns={columns}
				dataSource={employees?.data || []}
				rowKey='id'
				pagination={{
					current: currentPage,
					total: employees?.meta?.total,
					showTotal: (total, range) => `${range[0]}-${range[1]} tadan ${total}  ta`,
					pageSize: employees?.meta?.limit
				}}
				loading={isLoading}
				onChange={handleTableChange}
			/>
		</div>
	)
}
export default SectionStatsVacationTable
