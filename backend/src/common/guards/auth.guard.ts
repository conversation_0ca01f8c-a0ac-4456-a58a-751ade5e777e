import { CanActivate, ExecutionContext, HttpException, Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import { PrismaService } from '../prisma';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private jwtService: JwtService,
    private configService: ConfigService,
    private readonly prismaService: PrismaService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const isPublic = this.reflector.getAllAndOverride<boolean>('IS_PUBLIC', [context.getClass(), context.getHandler()]);
    if (isPublic) {
      return true;
    }

    const contextType = this.reflector.getAllAndOverride<'WORKSPACE' | 'PERSONAL'>('CONTEXT', [
      context.getClass(),
      context.getHandler(),
    ]);
    const onlyAdmin = this.reflector.getAllAndOverride<boolean>('ONLY_ADMIN', [
      context.getClass(),
      context.getHandler(),
    ]);

    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);
    if (!token) {
      throw new UnauthorizedException('Token is missing');
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: this.configService.get('JWT_SECRET'),
      });

      const user = await this.prismaService.user.findUnique({
        where: { id: payload.id },
      });
      if (!user) {
        throw new UnauthorizedException('Foydalanuvchi topilmadi');
      }

      if (onlyAdmin && user.role !== 'ADMIN') {
        throw new UnauthorizedException('Эта функция доступна только для администраторов');
      }

      request['user'] = payload;

      if (contextType === 'WORKSPACE') {
        const xOrganizationId = request.headers['x-organization-id'];
        if (!xOrganizationId) {
          throw new UnauthorizedException('Organization not found');
        }

        // Проверка организации
        const organization = await this.prismaService.organization.findUnique({
          where: {
            id: xOrganizationId,
            ...(onlyAdmin ? { grade: { level: -1 } } : {}),
            Responsible: {
              some: {
                id: payload.id,
              },
            },
          },
        });

        if (!organization) {
          throw new UnauthorizedException('Organization not found');
        }

        request['organizationId'] = organization.id;
        request['context'] = 'WORKSPACE';
        return true;
      }

      request['context'] = 'PERSONAL';
      return true;
    } catch (error) {
      this.handleError(error);
    }
  }

  private extractTokenFromHeader(request): string | undefined {
    const [bearer, token] = request.headers.authorization?.split(' ') ?? [];
    return bearer === 'Bearer' ? token : undefined;
  }

  private handleError(error: any): never {
    if (error instanceof HttpException) {
      throw error;
    }

    if (error.name === 'TokenExpiredError') {
      throw new UnauthorizedException('Token has expired');
    }
    if (error.name === 'JsonWebTokenError') {
      throw new UnauthorizedException('Invalid token');
    }
    if (error.name === 'NotBeforeError') {
      throw new UnauthorizedException('Token is not active yet');
    }

    throw new UnauthorizedException('Failed to authenticate token');
  }
}
