FROM node:20-alpine as builder

WORKDIR /app

ARG API_URL
ENV VITE_API_URL=$API_URL

COPY package.json yarn.lock* ./

# Очистка кеша и установка зависимостей
RUN yarn cache clean && \
    yarn install --frozen-lockfile

# Явно установим версию esbuild для предотвращения несоответствия
RUN yarn add esbuild@0.25.1 --dev

COPY . .
RUN yarn build

FROM nginx:alpine

# Установка wget для healthcheck
RUN apk add --no-cache wget

COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]