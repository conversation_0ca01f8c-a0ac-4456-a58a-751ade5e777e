model Attendance {
  id     String @id @default(cuid())
  User   User   @relation(fields: [userId], references: [id])
  userId String

  status Status @default(ACTIVE)

  Organization   Organization @relation(fields: [organizationId], references: [id])
  organizationId String

  time DateTime

  CreatedBy   User?   @relation("AttendanceCreatedBy", fields: [createdById], references: [id])
  createdById String?
  description String?

  type AttendanceType

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@index([organizationId])
  @@index([time])
  // Performance uchun qo'shimcha indexlar
  @@index([userId, type, time]) // Eng muhim: user bo'yicha type va vaqt
  @@index([type, time]) // EXIT/ENTER larni vaqt bo'yicha qidirish
  @@index([userId, time]) // User bo'yicha vaqt oralig'i qidiruvi
  @@index([organizationId, type, time]) // Organization darajasida hisobotlar
  @@index([time, type, userId]) // Kun bo'yicha barcha tiplar
}

enum AttendanceType {
  ENTER
  EXIT
}

model AttendanceReport {
  id String @id @default(cuid())

  User   User?   @relation(fields: [userId], references: [id])
  userId String?

  status Status @default(ACTIVE)

  date DateTime

  description String?

  minutes Int

  enter DateTime
  exit  DateTime

  earlyExit Int? // in minutes
  lateEnter Int? // in minutes

  scheduledEnter   DateTime?
  scheduledExit    DateTime?
  scheduledMinutes Int? // in minutes
  dayNumber        Int? // 1-7

  underTimeMinutes Int? // in minutes

  isLate      Boolean? @default(false) // late enter
  isEarlyExit Boolean? @default(false) // early exit

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@index([date])
  // Performance uchun qo'shimcha indexlar
  @@index([userId, date]) // User bo'yicha sana - eng tez qidiruv
  @@index([date, userId]) // Sana bo'yicha userlar ro'yxati
  @@index([isLate]) // Kechikkanlarni filterlash
  @@index([isEarlyExit]) // Erta ketganlarni filterlash
  @@index([dayNumber, date]) // Hafta kunlari bo'yicha hisobotlar
  @@index([enter, exit]) // Vaqt oralig'i qidiruvi
}
