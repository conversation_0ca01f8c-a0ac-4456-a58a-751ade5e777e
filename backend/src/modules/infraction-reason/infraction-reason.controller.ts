import { <PERSON>, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { InfractionReasonService } from './infraction-reason.service';
import { CreateInfractionReasonDto } from './dto/create-infraction-reason.dto';
import { UpdateInfractionReasonDto } from './dto/update-infraction-reason.dto';
import { ProtectedRoute } from 'src/common';

@Controller('infraction-reason')
export class InfractionReasonController {
  constructor(private readonly service: InfractionReasonService) {}

  @ProtectedRoute({
    isPublic: false,
    context: 'PERSONAL',
  })
  @Post()
  create(@Body() dto: CreateInfractionReasonDto) {
    return this.service.create(dto);
  }

  @ProtectedRoute({
    isPublic: true,
    context: 'WORKSPACE',
  })
  @Get()
  findAll() {
    return this.service.findAll();
  }

  @ProtectedRoute({
    isPublic: true,
    context: 'PERSONAL',
  })
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.service.findOne(id);
  }

  @ProtectedRoute({
    isPublic: false,
    context: 'WORKSPACE',
  })
  @Patch(':id')
  update(@Param('id') id: string, @Body() dto: UpdateInfractionReasonDto) {
    return this.service.update(id, dto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.service.remove(id);
  }
}
