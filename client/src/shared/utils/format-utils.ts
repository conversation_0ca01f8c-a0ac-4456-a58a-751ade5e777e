import { TimezoneHelper } from './timezone.helper'

// Eski funksiyani yangi helper bilan almashtiramiz
export const formatUzTime = TimezoneHelper.formatTime.bind(TimezoneHelper)

// Qo'shimcha format funksiyalari
export const formatUzDate = (date: Date | string | null | undefined) =>
	TimezoneHelper.formatLocal.bind(TimezoneHelper)(date, 'DD.MM.YYYY')

export const formatUzDateTime = TimezoneHelper.formatForDisplay
