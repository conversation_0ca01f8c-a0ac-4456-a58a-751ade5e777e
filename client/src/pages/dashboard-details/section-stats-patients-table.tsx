import { useGetDashboardDetailsPatient } from '@/config/queries/dashboard-details/get-dashboard-details'
import { useGetPatientExcel } from '@/config/queries/dashboard-details/get-dashboard-details-excel'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { Button, Table } from 'antd'
import { DownloadOutlined } from '@ant-design/icons'
import { useSearchParams } from 'react-router-dom'

const SectionStatsPatientsTable = () => {
	const { data: patients, isLoading } = useGetDashboardDetailsPatient()
	const [searchParams, setSearchParams] = useSearchParams()
	const organizationId = searchParams.get('orgId')
	const patientExcel = useGetPatientExcel()

	const currentPage = parseInt(searchParams.get('page') || '1')
	const pageSize = parseInt(searchParams.get('limit') || '10')

	const handleDownloadExcel = () => {
		patientExcel.mutate(organizationId || undefined)
	}

	const columns = [
		{
			key: '#',
			title: '#',
			render: (_: any, _rec: any, index: number) => formatTableIndex(currentPage, pageSize, index)
		},
		{
			title: 'FIO',
			dataIndex: ['user', 'fullName'],
			key: 'fullName'
		},
		{
			title: 'Telefon raqami',
			dataIndex: ['user', 'phone'],
			key: 'phone'
		},
		{
			title: 'Lavozimi',
			dataIndex: ['user', 'position', 'name'],
			key: 'positionName'
		},
		{
			title: 'Boshlanish vaqti',
			dataIndex: 'begin',
			key: 'begin',
			render: (date: string) => date.split('T')[0]
		},
		{
			title: 'Tugash vaqti',
			dataIndex: 'end',
			key: 'end',
			render: (date: string) => date.split('T')[0]
		},
		{
			title: 'Tashkilot',
			dataIndex: ['user', 'Organization'],
			key: 'organizationName',
			render: (_: any, record: any) =>
				record.user.Organization.map((org: any) => org.name).join(', ')
		}
	]

	const handleTableChange = (pagination: any) => {
		const newPage = pagination.current
		const newPageSize = pagination.pageSize

		setSearchParams(prev => {
			const newParams = new URLSearchParams(prev)
			newParams.set('page', newPage.toString())
			newParams.set('limit', newPageSize.toString())
			return newParams
		})
	}

	return (
		<div className='p-4'>
			<div className='flex justify-between items-center mb-4'>
				<h1 className='text-2xl font-bold'>Bemorlar ro`yhati</h1>
				<Button
					type='primary'
					icon={<DownloadOutlined />}
					onClick={handleDownloadExcel}
					loading={patientExcel.isPending}>
					Excel yuklab olish
				</Button>
			</div>
			<Table
				columns={columns}
				dataSource={patients?.data || []}
				rowKey='id'
				pagination={{
					current: currentPage,
					total: patients?.meta?.total,
					showTotal: (total, range) => `${range[0]}-${range[1]} tadan ${total}  ta`
				}}
				loading={isLoading}
				onChange={handleTableChange}
			/>
		</div>
	)
}
export default SectionStatsPatientsTable
