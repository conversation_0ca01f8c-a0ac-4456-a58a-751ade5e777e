import { axiosPrivate } from '@/config/api'
import { taskEndpoints } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'

export const useGetTaskById = (taskId: string | null) => {
	return useQuery({
		queryKey: ['task-by-id', taskId],
		queryFn: async () => {
			if (!taskId) return null
			return (await axiosPrivate.get(taskEndpoints.one(taskId))).data
		},
		enabled: !!taskId,
		staleTime: 5 * 60 * 1000, // 5 minutes
		refetchOnWindowFocus: false
	})
}
