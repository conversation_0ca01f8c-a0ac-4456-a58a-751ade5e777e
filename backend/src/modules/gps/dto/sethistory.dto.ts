import { Type } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsString, ValidateNested } from 'class-validator';

export class SetHistoryDto {
  @IsString()
  @IsNotEmpty()
  lat: string;
  @IsString()
  @IsNotEmpty()
  lng: string;
  @IsString()
  @IsNotEmpty()
  time: string;
}
export class SetLocationDto extends SetHistoryDto {
  @IsBoolean()
  @IsNotEmpty()
  in_area: true;
}

export class SetHistoryBodyDto {
  @ValidateNested({ each: true })
  @Type(() => SetHistoryDto)
  locations: SetHistoryDto[];
}

export class SetLocationBodyDto {
  @ValidateNested({ each: true })
  @Type(() => SetLocationDto)
  locations: SetLocationDto[];
}
