export const formatDate = (dateString: string) => {
	const date = new Date(dateString)
	const now = new Date()
	const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
	if (diffInMinutes < 1) return 'Hozir'
	if (diffInMinutes < 60) return `${diffInMinutes} daqiqa oldin`
	if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} soat oldin`
	return date.toLocaleDateString('uz-UZ', {
		day: '2-digit',
		month: '2-digit',
		year: 'numeric',
		hour: '2-digit',
		minute: '2-digit'
	})
}
