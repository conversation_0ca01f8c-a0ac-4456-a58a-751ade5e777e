import { axiosPrivate } from '@/config/api'
import { vocationEndpoints } from '@/config/api/endpoints'
import { TPaginationWrapper } from '@/shared/types/pagination.type'
import { useQuery } from '@tanstack/react-query'
import { useParams, useSearchParams } from 'react-router-dom'

export type TVocation = {
	id: string
	begin: string
	createdAt: string
	description?: string
	end: string
	state: boolean
	type: 'PATIONS' | 'VACATION'
	status: 'ACTIVE' | 'INACTIVE'
}

export const useGetAllVocations = (id?: string) => {
	const { id: userId } = useParams()
	const [searchParams] = useSearchParams()
	const page = searchParams.get('page')
	const limit = searchParams.get('limit')
	const search = searchParams.get('search')

	return useQuery({
		queryKey: [vocationEndpoints.all, { page, limit, userId, id, search }],
		queryFn: async () => {
			return (
				await axiosPrivate.get<TPaginationWrapper<TVocation[]>>(vocationEndpoints.all, {
					params: { page, limit, userId: id ?? userId, search }
				})
			).data
		}
	})
}
