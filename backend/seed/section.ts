import * as fs from 'node:fs';
import { PrismaClient } from '@prisma/client';
import { join } from 'path';

const prisma = new PrismaClient();

interface SectionData {
  id: number;
  title: string;
  district_id: number;
}

async function main() {
  const locations: SectionData[] = JSON.parse(
    fs.readFileSync(join(process.cwd(), 'json', 'mahalla.json'), {
      encoding: 'utf-8',
    }),
  );

  for (const location of locations) {
    const districtFromDb = await prisma.district.findFirst({
      where: {
        id: location.district_id.toString(),
      },
    });
    const section = await prisma.section.findFirst({
      where: { id: location.id.toString() },
    });

    if (districtFromDb && !section) {
      await prisma.section.create({
        data: {
          id: location.id.toString(),
          name: location.title,
          districtId: districtFromDb.id,
        },
      });
      console.log(`Section "${location.title}" создан`);
    } else {
      console.log(`District ${location.district_id} не удается найти или уже существует`);
    }
  }

  console.log(`все selection созданы успешно`);
}

main()
  .catch((e) => {
    console.error(e);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
