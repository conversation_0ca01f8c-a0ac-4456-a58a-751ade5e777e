import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { PrismaService } from '../../common';
import { TelegramBot } from './TelegramBot';
import { OnEvent } from '@nestjs/event-emitter';
import { TelegramEventKeys } from './interfaces/bot.interfaces';
import { TelegramFrom, TelegramRoot } from './interfaces/telegram.interfaces';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class BotService implements OnModuleInit {
  private readonly logger = new Logger(BotService.name);
  private readonly token: string;
  private readonly webhookUrl: string;
  readonly bot: TelegramBot;

  constructor(
    private readonly prismaService: PrismaService,
    private configService: ConfigService,
  ) {
    this.token = this.configService.get<string>('TELEGRAM_BOT_TOKEN');
    this.webhookUrl = this.configService.get<string>('TELEGRAM_BOT_WEBHOOK_URL');
    this.bot = new TelegramBot({
      token: this.token,
      webhookUrl: this.webhookUrl,
    });
  }

  async onModuleInit() {
    try {
      const info = await this.bot.getWebhookInfo();
      if (info.url !== this.webhookUrl) {
        await this.bot.setWebhook(this.webhookUrl);
        this.logger.log('Telegram webhook установлен успешно');
      } else {
        this.logger.debug({
          message: 'Настройка Telegram webhook',
          currentUrl: info.url,
          expectedUrl: this.webhookUrl,
        });
        this.logger.log('Telegram webhook уже настроен');
      }
    } catch (error) {
      this.logger.error(`Ошибка при настройке webhook: ${error.message}`);
    }
  }

  @OnEvent(TelegramEventKeys.TELEGRAM_HEAR)
  async hear(payload: TelegramRoot) {
    try {
      const { message } = payload;

      // Обработка команды /start
      if (message.text === '/start') {
        await this.onStart(message.chat.id, message.from);
        return;
      }

      // Обработка контактных данных
      if (message.contact) {
        await this.processContactInfo(message.chat.id, message.from, message.contact.phone_number);
      }
    } catch (error) {
      this.logger.error(`Ошибка при обработке сообщения: ${error.message}`);
    }
  }

  /**
   * Обрабатывает команду /start
   */
  async onStart(chatId: number, from?: TelegramFrom) {
    try {
      const userExists = await this.userIdExists(from.id);

      if (!userExists) {
        await this.requestContactInfo(chatId);
      } else {
        await this.sendAlreadyRegisteredMessage(chatId);
      }
    } catch (error) {
      this.logger.error(`Ошибка при обработке команды start: ${error.message}`);
    }
  }

  /**
   * Проверяет существование пользователя по Telegram ID
   */
  private async userIdExists(userId: number): Promise<any> {
    return this.prismaService.user.findFirst({
      where: { telegramId: userId.toString() },
    });
  }

  /**
   * Поиск пользователя по номеру телефона
   */
  private async getUserByPhone(phone: string): Promise<any> {
    return this.prismaService.user.findFirst({
      where: {
        phone: {
          endsWith: phone,
        },
      },
    });
  }

  /**
   * Запрашивает контактную информацию у пользователя
   */
  private async requestContactInfo(chatId: number): Promise<void> {
    await this.bot.sendMessage({
      chat_id: chatId,
      text: "Salom! Siz hali tizimda ro'yxatdan o'tmagansiz.\n\nIltimos, ro'yxatdan o'ting.\n\nTelefon raqamingizni yuboring",
      parse_mode: 'HTML',
      reply_markup: this.bot.createReplyKeyboard(
        [
          [
            {
              text: 'Telefon raqamini yuborish',
              request_contact: true,
            },
          ],
        ],
        {
          resize_keyboard: true,
        },
      ),
    });
  }

  /**
   * Отправляет сообщение о том, что пользователь уже зарегистрирован
   */
  private async sendAlreadyRegisteredMessage(chatId: number): Promise<void> {
    await this.bot.sendMessage({
      chat_id: chatId,
      text: "Siz allaqachon tizimda ro'yxatdan o'tgansiz.",
      parse_mode: 'HTML',
      reply_markup: {
        remove_keyboard: true,
      },
    });
  }

  /**
   * Обрабатывает информацию о контакте
   */
  private async processContactInfo(chatId: number, from: TelegramFrom, phoneNumber: string): Promise<void> {
    const phone = phoneNumber.slice(-9);
    const userByPhone = await this.getUserByPhone(phone);

    if (!userByPhone) {
      await this.bot.sendMessage({
        chat_id: chatId,
        text: 'Telefon raqamingiz tizimda topilmadi.',
        parse_mode: 'HTML',
        reply_markup: {
          remove_keyboard: true,
        },
      });
      return;
    }

    if (userByPhone.telegramId) {
      await this.sendAlreadyRegisteredMessage(chatId);
      return;
    }

    try {
      const user = await this.prismaService.user.update({
        where: { id: userByPhone.id },
        data: {
          telegramId: from.id.toString(),
          phone: phoneNumber,
        },
      });

      await this.bot.sendMessage({
        chat_id: chatId,
        text: `${user.fullName}! Siz muvaffaqiyatli ro'yxatdan o'tdingiz.`,
        parse_mode: 'HTML',
        reply_markup: {
          remove_keyboard: true,
        },
      });
    } catch (error) {
      this.logger.error(`Ошибка при обновлении пользователя: ${error.message}`);

      await this.bot.sendMessage({
        chat_id: chatId,
        text: "Xatolik yuz berdi. Iltimos, qaytadan urinib ko'ring.",
        parse_mode: 'HTML',
        reply_markup: {
          remove_keyboard: true,
        },
      });
    }
  }
}
