import { Button, Form, Modal } from 'antd'
import { useEffect } from 'react'
import { PlusOutlined } from '@ant-design/icons'
import { useTaskStatesModalStore } from '../utils/task-states-store'
import TaskStatesForm from './task-states-form'
import { TaskStates, useCreateTaskStates } from '@/config/queries/task-states/create.queries'
import { useUpdateTaskStates } from '@/config/queries/task-states/update.queries'
import { getFormChanges } from '@/shared/utils/getFormChanges'

const TaskStatesModal = () => {
	const { data, open, onClose } = useTaskStatesModalStore()
	const [form] = Form.useForm()
	const create = useCreateTaskStates()
	const update = useUpdateTaskStates()

	const close = () => {
		form.resetFields()
		onClose()
	}

	useEffect(() => {
		if (data) {
			form.setFieldsValue(data)
		}
	}, [data, form])

	const onFinish = (formData: TaskStates) => {
		if (!data) {
			create
				.mutateAsync({
					...formData,
					order: Number(formData.order)
				})
				.then(close)
		} else {
			if (data && data.id) {
				//@ts-ignore
				const updatedData = getFormChanges(data, formData)
				update
					.mutateAsync({
						id: data.id,
						...updatedData
					} as { id: string })
					.then(close)
			}
		}
	}
	return (
		<div className='flex items-center justify-end'>
			<Button
				type='primary'
				icon={<PlusOutlined />}
				onClick={() => useTaskStatesModalStore.setState({ open: true })}>
				Topshiriq xolatini qo'shish
			</Button>
			<Modal
				open={open}
				onCancel={close}
				destroyOnClose
				onOk={form.submit}
				title={`Xolatni ${data ? 'ni tahrirlash' : "ni qo'shish"}`}
				okText={data ? 'Tahrirlash' : "Qo'shish"}
				cancelText='Bekor qilish'
				modalRender={node => (
					<Form
						layout={'vertical'}
						size='large'
						onFinish={onFinish}
						form={form}>
						{node}
					</Form>
				)}>
				<TaskStatesForm />
			</Modal>
		</div>
	)
}
export default TaskStatesModal
