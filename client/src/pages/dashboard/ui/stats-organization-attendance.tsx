import { Bar } from 'react-chartjs-2'
import { Chart as ChartJS, BarElement, CategoryScale, LinearScale, Tooltip, Legend } from 'chart.js'
import { OrganizationAttendanceStatsType } from '@/config/queries/stats/get.queries'
import { Card } from 'antd'
import { useEffect, useState } from 'react'

ChartJS.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend)

const getRandomColor = () => {
	const r = Math.floor(Math.random() * 256)
	const g = Math.floor(Math.random() * 256)
	const b = Math.floor(Math.random() * 256)
	return `rgba(${r}, ${g}, ${b}, 0.5)`
}
interface StatsOrganizationAttendanceProps {
	organizationAttendanceStats?: OrganizationAttendanceStatsType
	isLoading?: boolean
}

const StatsOrganizationAttendance = ({
	organizationAttendanceStats,
	isLoading = false
}: StatsOrganizationAttendanceProps) => {
	const [chartData, setChartData] = useState<any>(null)
	const [options, setOptions] = useState<any>(null)

	useEffect(() => {
		if (organizationAttendanceStats?.attendance) {
			setChartData({
				labels: organizationAttendanceStats.attendance.map(item => item.position),
				datasets: [
					{
						label: "O'z vaqtida",
						data: organizationAttendanceStats.attendance.map(item => item.onTime),
						backgroundColor: getRandomColor(),
						borderColor: getRandomColor().replace('0.5', '1'),
						borderWidth: 1
					},
					{
						label: 'Kechikkan',
						data: organizationAttendanceStats.attendance.map(item => item.late),
						backgroundColor: getRandomColor(),
						borderColor: getRandomColor().replace('0.5', '1'),
						borderWidth: 1
					},
					{
						label: 'Kelmagan',
						data: organizationAttendanceStats.attendance.map(item => item.absent),
						backgroundColor: getRandomColor(),
						borderColor: getRandomColor().replace('0.5', '1'),
						borderWidth: 1
					},
					{
						label: 'Jami xodimlar',
						data: organizationAttendanceStats.attendance.map(item => item.totalWorkers),
						backgroundColor: getRandomColor(),
						borderColor: getRandomColor().replace('0.5', '1'),
						borderWidth: 1
					}
				]
			})

			setOptions({
				responsive: true,
				maintainAspectRatio: false,
				plugins: {
					legend: { display: true },
					tooltip: { enabled: true }
				},
				scales: {
					x: { beginAtZero: true },
					y: { beginAtZero: true }
				}
			})
		}
	}, [organizationAttendanceStats])

	if (isLoading) {
		return (
			<Card className='grid grid-cols-1 gap-4 p-4 my-2'>
				<div className='p-4 rounded-lg shadow'>
					<h2 className='text-lg font-semibold mb-2'>Tashkilotlar qatnashish statisikasi</h2>
					<p>Ma'lumotlar yuklanmoqda...</p>
				</div>
			</Card>
		)
	}

	if (!organizationAttendanceStats?.attendance) {
		return (
			<Card className='grid grid-cols-1 gap-4 p-4 my-2'>
				<div className=' p-4 rounded-lg shadow'>
					<h2 className='text-lg font-semibold mb-2'>Tashkilotlar qatnashish statisikasi</h2>
					<p>Ma'lumot mavjud emas</p>
				</div>
			</Card>
		)
	}

	return (
		<Card className='grid grid-cols-1 gap-4 p-4'>
			<div
				style={{ height: '450px', width: '100%' }}
				className=' p-4 rounded-lg shadow'>
				<h2 className='text-lg font-semibold mb-2'>Tashkilotlar qatnashish statisikasi</h2>

				{chartData && options && (
					<Bar
						data={chartData}
						options={options}
					/>
				)}
			</div>
		</Card>
	)
}

export default StatsOrganizationAttendance
