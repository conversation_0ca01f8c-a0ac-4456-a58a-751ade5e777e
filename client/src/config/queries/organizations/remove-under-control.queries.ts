import { axiosPrivate } from '@/config/api'
import { organizationEndpoints } from '@/config/api/endpoints'
import { TApiErrorResponse } from '@/shared/types/api-error.type'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { notification } from 'antd'
import { AxiosError, AxiosResponse } from 'axios'

type TRemoveUnderControlOrganization = {
	data: {
		organizations: string[]
	}
	orgId: string
}

export const useRemoveUnderControlOrganization = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({ data, orgId }: TRemoveUnderControlOrganization) => {
			return await axiosPrivate.post(
				organizationEndpoints.underContolOrganizationRemove(orgId),
				data
			)
		},
		onSuccess: async (response: AxiosResponse) => {
			console.log(response)
			queryClient.invalidateQueries({ queryKey: [organizationEndpoints.underContolOrganization()] })
			notification.success({ message: 'Tashkilot muaffaqiyatli ajratildi!' })
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response?.data.message)
			notification.error({ message: 'Tashkilot ajratishda xatolik yuz berdi!' })
		}
	})
}
