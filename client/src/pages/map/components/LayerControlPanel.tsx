import React from 'react'
import { Layers, Eye, EyeOff } from 'lucide-react'

interface LayerControlPanelProps {
	showControls: boolean
	setShowControls: (show: boolean) => void
	areaTypes: string[]
	visibleLayers: Set<string>
	toggleLayerVisibility: (type: string) => void
	showAllLayers: () => void
	hideAllLayers: () => void
	totalFeatures: number
	visibleFeatures: number
}

export const LayerControlPanel: React.FC<LayerControlPanelProps> = ({
	showControls,
	setShowControls,
	areaTypes,
	visibleLayers,
	toggleLayerVisibility,
	showAllLayers,
	hideAllLayers,
	totalFeatures,
	visibleFeatures
}) => {
	return (
		<div className='absolute top-4 right-4 z-[1000] bg-white rounded-lg shadow-lg border border-gray-200'>
			<div className='p-4'>
				<div className='flex items-center justify-between mb-3'>
					<h3 className='text-sm font-semibold text-gray-800 flex items-center'>
						<Layers className='w-4 h-4 mr-2' />
						Управление слоями
					</h3>
					<button
						onClick={() => setShowControls(!showControls)}
						className='p-1 hover:bg-gray-100 rounded'>
						{showControls ? <EyeOff className='w-4 h-4' /> : <Eye className='w-4 h-4' />}
					</button>
				</div>

				{showControls && (
					<div className='space-y-3'>
						{/* Кнопки управления */}
						<div className='flex space-x-2'>
							<button
								onClick={showAllLayers}
								className='px-3 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 transition-colors'>
								Показать все
							</button>
							<button
								onClick={hideAllLayers}
								className='px-3 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600 transition-colors'>
								Скрыть все
							</button>
						</div>

						{/* Список типов областей */}
						<div className='space-y-2 max-h-40 overflow-y-auto'>
							{areaTypes.map(type => (
								<label
									key={type}
									className='flex items-center space-x-2 cursor-pointer'>
									<input
										type='checkbox'
										checked={visibleLayers.has(type)}
										onChange={() => toggleLayerVisibility(type)}
										className='rounded border-gray-300 text-blue-600 focus:ring-blue-500'
									/>
									<span className='text-sm text-gray-700 truncate'>{type}</span>
								</label>
							))}
						</div>

						{/* Информация */}
						<div className='pt-2 border-t border-gray-200'>
							<p className='text-xs text-gray-500'>Всего областей: {totalFeatures}</p>
							<p className='text-xs text-gray-500'>Видимых: {visibleFeatures}</p>
						</div>
					</div>
				)}
			</div>
		</div>
	)
}
