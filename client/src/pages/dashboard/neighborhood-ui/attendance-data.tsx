import { useGetDashboardSectionStatsAttendance } from '@/config/queries/dashboard/section-dashboard'
import { Card, Divider, Row, Col } from 'antd'
import { useNavigate, useSearchParams } from 'react-router-dom'

export default function AttendanceStatsDashboard() {
	const { data: sectionAttendanceStats } = useGetDashboardSectionStatsAttendance()
	const navigate = useNavigate()
	const [searchParams] = useSearchParams()

	// Get orgId from URL or localStorage
	const urlOrgId = searchParams.get('orgId')
	const organization = localStorage.getItem('organization')
	const organizationData = organization ? JSON.parse(organization) : null
	const orgId = urlOrgId || organizationData?.id

	const lateTotal = sectionAttendanceStats?.lateAttendance?.total || 0
	const noTotal = sectionAttendanceStats?.noAttendance?.total || 0

	const defaultStats = [
		{ id: 1, title: 'Mahalla raislari', value: '0' },
		{ id: 2, title: 'Hokim yordamchilari', value: '0' },
		{ id: 3, title: 'Xotin qizlar faoli', value: '0' },
		{ id: 4, title: 'Yoshlar yetakchisi', value: '0' },
		{ id: 5, title: 'Profilaktika inspektori', value: '0' },
		{ id: 6, title: 'Ijtimoiy hodim', value: '0' },
		{ id: 7, title: 'Soliq inspektori', value: '0' }
	]

	// Navigation handlers for totals (WITHOUT positionId - only orgId)
	const handleLateAttendanceClick = () => {
		navigate(`/workspace/dashboard-details/late-attendance?orgId=${orgId}`)
	}

	const handleNoAttendanceClick = () => {
		navigate(`/workspace/dashboard-details/no-attendance?orgId=${orgId}`)
	}

	// Navigation handlers for individual positions (WITH positionId)
	const handleLatePositionClick = (positionId: string) => {
		navigate(`/workspace/dashboard-details/late-attendance?orgId=${orgId}&positionId=${positionId}`)
	}

	const handleNoPositionClick = (positionId: string) => {
		navigate(`/workspace/dashboard-details/no-attendance?orgId=${orgId}&positionId=${positionId}`)
	}

	// Safe checks for list data
	const lateAttendanceList = sectionAttendanceStats?.lateAttendance?.list
	const noAttendanceList = sectionAttendanceStats?.noAttendance?.list
	const hasLateAttendanceData = lateAttendanceList && lateAttendanceList.length > 0
	const hasNoAttendanceData = noAttendanceList && noAttendanceList.length > 0

	return (
		<div className='p-4'>
			<Row gutter={16}>
				<Col
					xs={24}
					md={12}>
					<Card
						bordered={false}
						className='shadow-sm'>
						<div className='flex justify-between items-center mb-4'>
							<h2 className='text-2xl text-gray-600 dark:text-white font-medium'>Kechikkanlar</h2>
							{/* TOTAL - clicks without positionId */}
							<span
								className='text-6xl font-bold text-blue-500 cursor-pointer hover:text-blue-700 transition-colors'
								onClick={handleLateAttendanceClick}
								title="Batafsil ko'rish uchun bosing">
								{lateTotal}
							</span>
						</div>
						<Divider className='my-2' />
						<Row gutter={[16, 16]}>
							{(hasLateAttendanceData ? lateAttendanceList : defaultStats).map(item => (
								<Col
									span={8}
									key={`late-${item.id}`}>
									{/* POSITION CARDS - clicks with positionId */}
									<AttendanceStatCard
										title={'name' in item ? item.name : item.title}
										value={'count' in item ? item.count?.toString() || '0' : item.value}
										onClick={() => {
											if ('id' in item && item.id) {
												handleLatePositionClick(item.id.toString())
											}
										}}
										isClickable={'id' in item && item.id !== undefined}
									/>
								</Col>
							))}
						</Row>
					</Card>
				</Col>

				<Col
					xs={24}
					md={12}>
					<Card
						bordered={false}
						className='shadow-sm'>
						<div className='flex justify-between items-center mb-4'>
							<h2 className='text-2xl text-gray-600 dark:text-white font-medium'>
								Ishga Kelmaganlar
							</h2>
							{/* TOTAL - clicks without positionId */}
							<span
								className='text-6xl font-bold text-blue-500 cursor-pointer hover:text-blue-700 transition-colors'
								onClick={handleNoAttendanceClick}
								title="Batafsil ko'rish uchun bosing">
								{noTotal}
							</span>
						</div>
						<Divider className='my-2' />
						<Row gutter={[16, 16]}>
							{(hasNoAttendanceData ? noAttendanceList : defaultStats).map(item => (
								<Col
									span={8}
									key={`no-${item.id}`}>
									<AttendanceStatCard
										title={'name' in item ? item.name : item.title}
										value={'count' in item ? item.count?.toString() || '0' : item.value}
										onClick={() => handleNoPositionClick(item.id ? item.id.toString() : '')}
										isClickable={true}
									/>
								</Col>
							))}
						</Row>
					</Card>
				</Col>
			</Row>
		</div>
	)
}

function AttendanceStatCard({
	title,
	value,
	onClick,
	isClickable = false
}: {
	title: string
	value: string
	onClick?: () => void
	isClickable?: boolean
}) {
	return (
		<Card
			className={`p-4 rounded-md h-full flex flex-col items-center justify-center text-center transition-colors ${
				isClickable ? 'cursor-pointer hover:bg-gray-50 hover:shadow-md dark:hover:bg-gray-800' : ''
			}`}
			onClick={isClickable ? onClick : undefined}>
			<p className='text-2xl mb-2'>{title}</p>
			<p className='text-4xl font-bold text-gray-700 dark:text-white'>{value}</p>
		</Card>
	)
}
