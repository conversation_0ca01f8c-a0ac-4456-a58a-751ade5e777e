import { axiosPrivate } from '@/config/api'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { userWorkingScheduleEndpoints } from '@/config/api/endpoints'
import { notification } from 'antd'

export interface UserWorkingScheduleTypes {
	id?: string
	name: string
	day: number
	startTime: string
	endTime: string
	userId?: string
}

export const useCreateUserWorkingSchedule = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async (data: UserWorkingScheduleTypes) =>
			(
				await axiosPrivate.post<ApiResponse<UserWorkingScheduleTypes>>(
					userWorkingScheduleEndpoints.all,
					data
				)
			).data,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [userWorkingScheduleEndpoints.all] })
			notification.success({
				message: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>i yaratildi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: 'Yaratishda xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
