import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { LoadingScreen } from '@/shared/ui/suspense'
import { useQueryClient } from '@tanstack/react-query'
import { notification } from 'antd'
import { Navigate, Outlet } from 'react-router-dom'

const RequireAuth = () => {
	const { data: user, isLoading } = useAuthMe()
	const queryClient = useQueryClient()
	const accessToken = localStorage.getItem('accessToken')

	if (!accessToken) {
		queryClient.clear()
		return <Navigate to='/auth/login' />
	}

	if (isLoading) return <LoadingScreen />
	if (user?.id) return <Outlet />

	notification.error({ message: 'Foydalanuvchi topilmadi' })
	return <Navigate to='/auth/login' />
}

export default RequireAuth
