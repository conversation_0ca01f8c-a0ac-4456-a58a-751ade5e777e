import { axiosPrivate } from '@/config/api'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { notification } from 'antd'
import { TVacancy } from './create.vacancy'
import { vacancyEndpoints } from '@/config/api/endpoints'

export const useUpdateVacancy = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async ({ id, ...data }: { id: string }) =>
			(await axiosPrivate.patch<ApiResponse<TVacancy>>(vacancyEndpoints.one(id), data)).data,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [vacancyEndpoints.all] })
			notification.success({
				message: 'Muvaffaqiyatli yangilandi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: 'Yang<PERSON>shda xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
