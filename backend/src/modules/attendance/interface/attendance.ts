export const ATTENDANCE = 'attendance';
export const ATTENDANCE_REPORT = 'attendance-report';
export interface AttendanceData {
  ipAddress: string;
  portNo: number;
  protocol: string;
  macAddress: string;
  channelID: number;
  dateTime: string;
  activePostCount: number;
  eventType: string;
  eventState: string;
  eventDescription: string;
  deviceID: string;
  shortSerialNumber: string;
  AccessControllerEvent: AccessControllerEvent;
}

export interface AccessControllerEvent {
  deviceName: string;
  majorEventType: number;
  subEventType: number;
  name: string;
  cardReaderNo: number;
  employeeNoString: string;
  serialNo: number;
  userType: string;
  currentVerifyMode: string;
  frontSerialNo: number;
  label: string;
  picturesNumber: number;
  purePwdVerifyEnable: boolean;
}
