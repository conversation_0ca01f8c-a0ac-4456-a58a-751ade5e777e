export const getFormChanges = (data: Record<string, unknown>, values: Record<string, unknown>) => {
	const updateValues: Record<string, unknown> = {}
	Object.entries(values).forEach(([key, value]) => {
		const currentValue = updateValues[key]
		if (value && data[key] !== value) {
			if (typeof currentValue === 'object') {
				if (JSON.stringify(currentValue) !== JSON.stringify(value)) {
					updateValues[key] = value
				}
			} else {
				updateValues[key] = value
			}
		}
	})
	return updateValues
}
