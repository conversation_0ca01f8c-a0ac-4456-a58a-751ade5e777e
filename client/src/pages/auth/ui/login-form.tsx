import { Button, Form, Input } from 'antd'
import { Eye, EyeClosedIcon } from 'lucide-react'
import { loginFormValidations } from '@/pages/auth/config/validations'
import { TLoginRequest } from '@/config/queries/auth/login.queries'
import { InputMask } from '@react-input/mask'

type TLoginFormProps = {
	onFinish: (data: TLoginRequest) => void
	isLoading: boolean
}

const LoginForm = ({ onFinish, isLoading }: TLoginFormProps) => {
	function togglePasswordVisibility(visible: boolean) {
		if (visible) return <Eye />
		else return <EyeClosedIcon />
	}

	const handleSubmit = (values: any) => {
		const phoneNumber = values.username.replace(/[^\d]/g, '').slice(3)
		onFinish({
			username: phoneNumber,
			password: values.password
		})
	}

	return (
		<Form
			name='login'
			autoComplete='off'
			onFinish={handleSubmit}
			className='w-full'>
			{/* <Form.Item
				data-testid='login-username-input'
				name='username'
				rules={loginFormValidations.username}>
				<Input
					size='large'
					placeholder='Username'
				/>
			</Form.Item> */}

			<Form.Item
				name='username'
				required>
				<InputMask
					style={{
						width: '100%',
						padding: '8px 11px',
						fontSize: '16px',
						lineHeight: '1.8',
						borderRadius: '6px',
						border: '1px solid gray'
					}}
					defaultValue={'+998'}
					mask='+998 (__) ___-__-__'
					replacement={{ _: /\d/ }}
					placeholder='Telefon raqamini kiriting'
				/>
			</Form.Item>
			<Form.Item
				data-testid='login-password-input'
				name='password'
				rules={loginFormValidations.password}>
				<Input.Password
					size='large'
					placeholder='Parol'
					iconRender={togglePasswordVisibility}
				/>
			</Form.Item>
			<Form.Item>
				<Button
					data-testid='login-submit-button'
					loading={isLoading}
					type='primary'
					htmlType='submit'
					className='w-full'
					variant='filled'>
					Kirish
				</Button>
			</Form.Item>
		</Form>
	)
}
export default LoginForm
