import { axiosPrivate } from '@/config/api'
import { taskTypesEndpoints } from '@/config/api/endpoints'
import { useInfiniteQuery } from '@tanstack/react-query'
import { TaskTypes } from './create.queries'
import { TPaginationWrapper } from '@/shared/types/pagination.type'
import { useSearchParams } from 'react-router-dom'

export const useGetTaskTypes = (_?: string) => {
	const limit = 10
	const page = 1

	const [searchParams] = useSearchParams()
	const search = searchParams.get('search')

	return useInfiniteQuery({
		queryKey: [taskTypesEndpoints.all, 'infinite', search, page, limit, search],
		queryFn: async ({ pageParam }) => {
			const endpoint = search ? taskTypesEndpoints.search(search) : taskTypesEndpoints.all
			return (
				await axiosPrivate.get<TPaginationWrapper<TaskTypes[]>>(endpoint, {
					params: {
						page: pageParam,
						limit,
						search
					}
				})
			).data
		},
		initialPageParam: page,
		getNextPageParam: lastPage => {
			if (lastPage?.meta?.page === lastPage?.meta?.totalPages) return undefined
			return lastPage?.meta?.page + 1
		},
		getPreviousPageParam: lastPage => {
			if (lastPage?.meta?.page === 1) return undefined
			return lastPage?.meta?.page - 1
		}
	})
}
