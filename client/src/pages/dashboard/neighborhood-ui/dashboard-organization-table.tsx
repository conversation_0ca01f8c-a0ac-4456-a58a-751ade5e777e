import { useGetDashboardTable } from '@/config/queries/dashboard/table.queries'
import { LoadingScreen } from '@/shared/ui/suspense'
import { Table } from 'antd'
import { useEffect } from 'react'
import { useSearchParams } from 'react-router-dom'

export const DashboardOrganizationTable = () => {
	const { data: dashboardTable, isLoading } = useGetDashboardTable()
	const [searchParams, setSearchParams] = useSearchParams()
	const xOrgId = localStorage.getItem('organizationId')
	const organization = localStorage.getItem('organization')

	useEffect(() => {
		if (organization) {
			searchParams.set('grade', JSON.parse(organization)?.grade?.level)
		}

		searchParams.set('orgId', JSON.parse(xOrgId ?? ''))
		setSearchParams(searchParams)
	}, [])

	if (isLoading) return <LoadingScreen />

	return (
		<div className='mt-4'>
			<Table
				rowClassName='cursor-pointer'
				dataSource={dashboardTable}
				onRow={record => ({
					onClick: () => {
						searchParams.set('orgId', record.id)
						searchParams.set('grade', JSON.stringify(record.level))
						setSearchParams(searchParams)
					}
				})}
				columns={[
					{
						key: '#',
						title: '#',
						render: (_, __, index) => ++index
					},
					{
						key: 'name',
						title: 'Nomi',
						dataIndex: 'name'
					},
					{
						key: 'pationsCount',
						title: 'Bemorlar soni',
						dataIndex: 'pationsCount'
					},
					{
						key: 'vacationCount',
						title: "Ta'tildagilar soni",
						dataIndex: 'vacationCount'
					},
					{
						key: 'vacancyCount',
						title: 'Vakansiyalar soni',
						dataIndex: 'vacancyCount'
					}
				]}
			/>
		</div>
	)
}
