import { axiosPrivate } from '@/config/api'
import { attendanceUserEndpoints } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'
import { ApiResponse } from '../types'

export interface AttendancePositionResponse {
	position: {
		id: string
		name: string
		description: string
	}
	reportPeriod: {
		startDate: string
		endDate: string
	}
	workers: {
		dailyAttendance?: any
		employee: {
			organization?: any
			id: string
			fullName: string
			position: {
				id: string
				name: string
			}
		}
		organization: {
			name: string
		}
		summary: {
			attendanceRate: number
			planHours: number
			factHours: number
		}
		dailyReports: {
			formattedDate: string
			isWorkDay: boolean
			cameToWork: boolean
			actualEnterTime: string
			actualExitTime: string
			lateMinutes: number
			earlyExitMinutes: number
			minutesWorked: number
		}[]
		workSchedule: any
		breakSchedule: any
	}[]
	meta: {
		total: number
		page: number
		limit: number
		totalPages: number
		hasMore: boolean
	}
}

export const useGetAllPositionAttendance = () => {
	const [searchParams] = useSearchParams()

	const page = searchParams.get('page')
	const limit = searchParams.get('limit')
	const from = searchParams.get('startDate')
	const to = searchParams.get('endDate')
	const positionId = searchParams.get('positionId')

	return useQuery({
		queryKey: [
			attendanceUserEndpoints.attendanceByPosition(positionId || ''),
			page,
			limit,
			from,
			to
		],
		queryFn: async () => {
			return (
				await axiosPrivate.get<ApiResponse<AttendancePositionResponse>>(
					attendanceUserEndpoints.attendanceByPosition(positionId || ''),
					{
						params: {
							startDate: from,
							endDate: to,
							page,
							limit
						}
					}
				)
			).data
		},
		select: data => {
			// Return the data structure that matches what the table expects
			if (!data?.data) return null

			return {
				position: data.data.position,
				reportPeriod: data.data.reportPeriod,
				workers: data.data.workers,
				meta: data.meta
			}
		},
		enabled: !!positionId,
		placeholderData: previousData => previousData
	})
}
