import { Button, Form, Modal } from 'antd'
import { useEffect } from 'react'
import { PlusOutlined } from '@ant-design/icons'
import UserWorkingScheduleDayForm from './user-working-schedule-day-form'
import { useUserWorkingScheduleDayModalStore } from '../utils/user-working-schedule-day-store'
import {
	useCreateUserWorkingScheduleDay,
	UserWorkingScheduleTypes
} from '@/config/queries/user-working-schedule-day/create.queries'
import { useUpdateUserWorkingScheduleDay } from '@/config/queries/user-working-schedule-day/update.queries'
import dayjs from 'dayjs'

const UserWorkingScheduleDayModal = () => {
	return (
		<div className='flex items-center justify-end'>
			<Button
				type='primary'
				icon={<PlusOutlined />}
				onClick={() => useUserWorkingScheduleDayModalStore.setState({ open: true })}>
				Foydalanuvchi ish jadvalini qo'shish
			</Button>
			<UserWorkingScheduleDayModalForm />
		</div>
	)
}
export default UserWorkingScheduleDayModal

export const UserWorkingScheduleDayModalForm = () => {
	const { data, open, onClose } = useUserWorkingScheduleDayModalStore()
	const [form] = Form.useForm()
	const create = useCreateUserWorkingScheduleDay()
	const update = useUpdateUserWorkingScheduleDay()

	const close = () => {
		form.resetFields()
		onClose()
	}

	useEffect(() => {
		if (data) {
			const formattedData = {
				...data,
				startTime: data.startTime ? dayjs(data.startTime) : null,
				endTime: data.endTime ? dayjs(data.endTime) : null
			}
			form.setFieldsValue(formattedData)
			form.setFieldValue('day', data.day.toString())
		}
	}, [data, form])

	const onFinish = (formData: UserWorkingScheduleTypes) => {
		const processedData = {
			...formData,
			day: Number(formData.day),

			startTime: dayjs(formData.startTime).format('YYYY-MM-DDTHH:mm:00.000Z'),
			endTime: dayjs(formData.endTime).format('YYYY-MM-DDTHH:mm:00.000Z')
		}

		if (!data) {
			create.mutateAsync(processedData).then(close)
		} else {
			if (data.id) {
				update
					.mutateAsync({
						...processedData,
						id: data.id
					} as { id: string })
					.then(close)
			}
		}
	}

	return (
		<Modal
			open={open}
			onCancel={close}
			destroyOnClose
			onOk={form.submit}
			title={`Vazifa turi${data ? 'ni tahrirlash' : "ni qo'shish"}`}
			okText={data ? 'Tahrirlash' : "Qo'shish"}
			cancelText='Bekor qilish'
			modalRender={node => (
				<Form
					layout={'vertical'}
					size='large'
					onFinish={onFinish}
					form={form}>
					{node}
				</Form>
			)}>
			<UserWorkingScheduleDayForm />
		</Modal>
	)
}
