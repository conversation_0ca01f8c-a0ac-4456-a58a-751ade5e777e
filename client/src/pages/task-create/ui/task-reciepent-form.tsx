import { <PERSON>, <PERSON>, <PERSON><PERSON>, Form } from 'antd'
import { XCircleIcon } from 'lucide-react'
import { TOrganization } from '@/config/queries/organizations/get-all.queries'
import { TUser } from '@/config/queries/users/get-all.queries'
import { TRecipent } from '../task-create'
import { OrganizationSelector } from './task-organization-select'
import { EmployeeSelector } from './task-employee-select'
import TaskPositionSelect from './task-position-select'

type TRecipientRowProps = {
	recipent: TRecipent
	recipentIndex: number
	recipents: TRecipent[]
	setRecipents: React.Dispatch<React.SetStateAction<TRecipent[]>>
	recipientType: string
	allOrganizations: TOrganization[]
	users: { [key: string]: TUser[] }
	isPending: boolean
	hasNextPage?: boolean
	fetchNextPage?: () => void
	handleOrganizationSelect: (organizationId: string, recipentIndex: number) => void
	handleOrganizationDeselect: (organizationId: string, recipentIndex: number) => void
	handlePositionSelect: (positionId: string, recipentIndex: number) => void
	organizationHasNextPage?: boolean
	organizationFetchNextPage?: () => void
	organizationIsPending?: boolean
}

export const RecipientRow = ({
	recipent,
	recipentIndex,
	recipents,
	setRecipents,
	recipientType,
	allOrganizations,
	users,
	isPending,
	hasNextPage,
	fetchNextPage,
	handleOrganizationSelect,
	handleOrganizationDeselect,
	handlePositionSelect,
	organizationHasNextPage,
	organizationFetchNextPage,
	organizationIsPending
}: TRecipientRowProps) => {
	return (
		<Row
			key={recipentIndex}
			gutter={[14, 14]}
			className='flex items-center'>
			{recipientType !== 'positions' && (
				<OrganizationSelector
					recipientType={recipientType}
					recipent={recipent}
					recipentIndex={recipentIndex}
					allOrganizations={allOrganizations}
					recipents={recipents}
					handleOrganizationSelect={handleOrganizationSelect}
					handleOrganizationDeselect={handleOrganizationDeselect}
					hasNextPage={organizationHasNextPage}
					fetchNextPage={organizationFetchNextPage}
					isPending={organizationIsPending}
				/>
			)}

			<EmployeeSelector
				recipientType={recipientType}
				recipent={recipent}
				recipentIndex={recipentIndex}
				users={users}
				isPending={isPending}
				setRecipents={setRecipents}
				hasNextPage={hasNextPage}
				fetchNextPage={fetchNextPage}
			/>

			{recipientType === 'positions' && (
				<Col md={recipents.length > 1 ? 21 : 24}>
					<Form.Item
						label='Lavozim'
						labelCol={{ span: 24 }}
						rules={[{ required: true, message: 'Lavozimni tanlang' }]}>
						<TaskPositionSelect
							value={recipent.positionId}
							onChange={(positionId: string) => handlePositionSelect(positionId, recipentIndex)}
						/>
					</Form.Item>
				</Col>
			)}

			{(recipientType === 'employees' || recipientType === 'positions') && recipents.length > 1 && (
				<Col
					xs={24}
					sm={24}
					md={2}
					className='mt-6'>
					<Button
						onClick={() =>
							setRecipents(prev => prev?.filter((_, index) => index !== recipentIndex))
						}>
						<XCircleIcon />
					</Button>
				</Col>
			)}
		</Row>
	)
}
