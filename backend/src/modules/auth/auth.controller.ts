import { UserRepository } from 'src/common/repositories/user.repository';
import { Body, Controller, Get, Post, Req, Res } from '@nestjs/common';
import { AuthService } from './auth.service';
import { ProtectedRoute } from 'src/common';
import { LoginDto } from './dto/login.dto';
import { Request, Response } from 'express';
import { Me } from 'src/common/decorators/me.decorator';

@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private userRepository: UserRepository,
  ) {}

  @ProtectedRoute({
    isPublic: true,
  })
  @Post('login')
  async login(@Body() loginDto: LoginDto, @Res() res: Response) {
    const { accessToken, refreshToken } = await this.authService.login(loginDto);
    res
      .cookie('refreshToken', refreshToken, {
        httpOnly: true,
        secure: true,
      })
      .send({ accessToken });
  }

  @ProtectedRoute({
    isPublic: true,
  })
  @Get('refresh')
  async refresh(@Req() req: Request, @Res() res: Response) {
    const refresh = req.cookies?.refreshToken;
    const { refreshToken, accessToken } = await this.authService.refresh(refresh);
    res
      .cookie('refreshToken', refreshToken, {
        httpOnly: true,
        secure: true,
      })
      .send({ accessToken });
  }

  @ProtectedRoute({
    isPublic: false,
  })
  @Get('me')
  async verify(@Me('id') userId: string) {
    const user = this.userRepository.getUserData(userId);
    return user;
  }

  @ProtectedRoute({})
  @Get('logout')
  async logout(@Req() req: Request, @Res() res: Response) {
    const { id } = req['user'];
    res.clearCookie('refreshToken', { httpOnly: true, secure: true });

    const response = await this.authService.logout(id);
    res.send(response);
  }
}
