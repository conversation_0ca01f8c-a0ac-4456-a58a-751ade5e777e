import { axiosPrivate } from '@/config/api'
import { organizationEndpoints, usersEndpoints } from '@/config/api/endpoints'
import { TApiErrorResponse } from '@/shared/types/api-error.type'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { notification } from 'antd'
import { AxiosError, AxiosResponse } from 'axios'
import { ROLE } from './get-users.queries'
import { useSearchParams } from 'react-router-dom'

type TData = {
	[key: string]: string[]
}

type TAddUsersToOrganization = {
	data: TData
	orgId: string
	who: 'workers' | 'employees' | 'responsibles'
}

export const useAddUsersToOrganization = (_: string) => {
	const queryClient = useQueryClient()
	const [searchParams] = useSearchParams()
	const roles = searchParams.get('roles')
	const search = searchParams.get('search')

	return useMutation({
		mutationFn: async ({ data, orgId, who }: TAddUsersToOrganization) => {
			let url = organizationEndpoints.addWorkers(orgId)
			switch (who) {
				case 'workers':
					url = organizationEndpoints.addWorkers(orgId)
					break
				case 'employees':
					url = organizationEndpoints.addEmployees(orgId)
					break
				case 'responsibles':
					url = organizationEndpoints.addResponsibles(orgId)
					break
				default:
					url = organizationEndpoints.addWorkers(orgId)
			}
			return await axiosPrivate.post(url, data)
		},
		onSuccess: async (response: AxiosResponse, { data, orgId }) => {
			console.log(response)
			notification.success({ message: "Muvaffaqiyatli qo'shildi!" })
			try {
				queryClient.invalidateQueries({
					queryKey: [
						organizationEndpoints.usersByOrg(orgId, roles ? ROLE.worker : undefined),
						'infinite',
						{ roles, search }
					]
				})
				queryClient.invalidateQueries({
					queryKey: [
						organizationEndpoints.usersByOrg(orgId, roles ? ROLE.employee : undefined),
						'infinite',
						{ roles, search }
					]
				})
				queryClient.invalidateQueries({
					queryKey: [
						organizationEndpoints.usersByOrg(orgId, roles ? ROLE.responsible : undefined),
						'infinite',
						{ roles, search }
					]
				})
				queryClient.invalidateQueries({
					queryKey: [usersEndpoints.one(data.workers[0])]
				})
				queryClient.invalidateQueries({
					queryKey: [organizationEndpoints.usersByOrg(orgId)]
				})
			} catch {
				console.log('error')
			}
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response?.data.message)
			notification.error({ message: 'Something went wrong while adding users!' })
		}
	})
}
