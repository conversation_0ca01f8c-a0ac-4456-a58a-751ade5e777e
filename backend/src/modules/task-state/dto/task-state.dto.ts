import { IsEnum, IsInt, <PERSON>NotEmpty, <PERSON><PERSON>ptional, IsString } from 'class-validator';
import { Status } from '@prisma/client';
import { PartialType } from '@nestjs/mapped-types';

export class CreateTaskStateDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  key: string;

  @IsInt()
  @IsNotEmpty()
  order: number;

  @IsEnum(Status)
  @IsOptional()
  status?: Status;
}

export class UpdateTaskStateDto extends PartialType(CreateTaskStateDto) {}
