import dayjs from 'dayjs'
import { useCreateVocation } from '@/config/queries/vocation/create.query'
import { Col, DatePicker, Form, Input, Modal, Row, Select } from 'antd'
import { TVocation } from '@/config/queries/vocation/getAll.query'
import { useEffect } from 'react'
import { useUpdateVocation } from '@/config/queries/vocation/update.query'
import { TimeRangePickerProps } from 'antd/lib'

interface EmployeeVocationCreateProps {
	open: boolean
	onClose: () => void
	employeeId: string | null
	record?: TVocation | null
}

const rangePresets: TimeRangePickerProps['presets'] = [
	{
		label: 'Bugun',
		value: [dayjs().startOf('day'), dayjs().endOf('day')]
	},
	{
		label: 'Ertaga',
		value: [dayjs().add(1, 'day').startOf('day'), dayjs().add(1, 'day').endOf('day')]
	},
	{
		label: 'Keyingi 2 kun',
		value: [dayjs().add(1, 'day').startOf('day'), dayjs().add(2, 'day').endOf('day')]
	},
	{
		label: 'keyingi 1 hafta',
		value: [dayjs().add(7, 'day'), dayjs()]
	},
	{
		label: 'keyingi 2 hafta',
		value: [dayjs().add(14, 'day'), dayjs()]
	},
	{
		label: 'keyingi 1 oy',
		value: [dayjs().add(1, 'month'), dayjs()]
	},
	{
		label: 'keyingi 3 oy',
		value: [dayjs().add(3, 'month'), dayjs()]
	},
	{
		label: 'keyingi 6 oy',
		value: [dayjs().add(6, 'month'), dayjs()]
	},
	{
		label: 'Keyingi 1 yil',
		value: [dayjs().add(1, 'year'), dayjs()]
	}
]

export const EmployeeVocationCreate = ({
	open,
	onClose,
	employeeId,
	record
}: EmployeeVocationCreateProps) => {
	const [form] = Form.useForm()
	const { mutateAsync: createVocation } = useCreateVocation()
	const { mutateAsync: updateVocation } = useUpdateVocation()

	useEffect(() => {
		if (record) {
			form.setFieldValue('date', [dayjs(record.begin), dayjs(record.end)])
			form.setFieldValue('description', record.description)
			form.setFieldValue('type', record.type)
		}
	}, [record])

	const onClear = () => {
		form.resetFields()
		onClose()
	}

	const handleSubmit = (values: any) => {
		if (!employeeId) return

		// Extract dates from the date range picker
		const beginDate = values.date?.[0] ? dayjs(values.date[0]).format('YYYY-MM-DD') : null
		const endDate = values.date?.[1] ? dayjs(values.date[1]).format('YYYY-MM-DD') : null

		if (!beginDate || !endDate) {
			console.error('Invalid date selection')
			return
		}

		if (record) {
			if (values.description) {
				updateVocation({
					data: {
						begin: beginDate,
						end: endDate,
						userId: employeeId,
						description: values.description,
						type: values.type
					},
					vocationId: record.id
				}).then(onClear)
			} else {
				updateVocation({
					data: {
						begin: beginDate,
						end: endDate,
						userId: employeeId,
						type: values.type
					},
					vocationId: record.id
				}).then(onClear)
			}
		} else {
			if (values.description) {
				createVocation({
					begin: beginDate,
					end: endDate,
					userId: employeeId,
					description: values.description,
					type: values.type
				}).then(onClear)
			} else {
				createVocation({
					begin: beginDate,
					end: endDate,
					userId: employeeId,
					type: values.type
				}).then(onClear)
			}
		}
	}

	return (
		<Modal
			open={open}
			onCancel={onClose}
			title='Tatil qo`shish'
			okText='Saqlash'
			onOk={form.submit}
			width={750}>
			<Form
				form={form}
				layout='vertical'
				onFinish={handleSubmit}>
				<Row gutter={[16, 16]}>
					<Col span={24}>
						<Form.Item
							name='type'
							label='Tatil olish sababi'
							rules={[{ required: true, message: 'Tatil olish sababini kiriting' }]}>
							<Select
								placeholder='Tatil olish sababi'
								options={[
									{ label: 'Bemorlik sababdan', value: 'PATIONS' },
									{ label: "Ta'til sababdan", value: 'VACATION' },
									{ label: "Qisqa ta'til", value: 'SHORT_VACATION' }
								]}
							/>
						</Form.Item>
					</Col>
					<Col span={24}>
						<Form.Item
							name='description'
							label='Izoh'>
							<Input.TextArea placeholder='Izoh' />
						</Form.Item>
					</Col>
					<Col span={24}>
						<Form.Item
							name='date'
							label='Sanalar'
							rules={[{ required: true, message: 'Sanalarni tanlang' }]}>
							<DatePicker.RangePicker
								allowClear={false}
								className='w-full'
								placeholder={['Boshlanish sanasi', 'Tugash sanasi']}
								presets={rangePresets}
							/>
						</Form.Item>
					</Col>
				</Row>
			</Form>
		</Modal>
	)
}
