import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { Processor, WorkerHost } from '@nestjs/bullmq';
import { ATTENDANCE_REPORT } from './interface/attendance';
import { Job } from 'bullmq';
import { AttendanceType, Status } from '@prisma/client';
import { AttendanceHandler } from './attendance.handler';

interface AttendanceReportJobData {
  id: string;
  userId: string;
  status: Status;
  organizationId: string;
  time: Date;
  createdById: string | null;
  description: string | null;
  type: AttendanceType;
  createdAt: Date;
  updatedAt: Date;
}

type AttendanceReportJobName = 'attendance.ENTER' | 'attendance.EXIT';

@Injectable()
@Processor(ATTENDANCE_REPORT, {
  concurrency: 2,
  limiter: {
    max: 50,
    duration: 1000,
  },
})
export class AttendanceReportProcessor extends WorkerHost {
  logger = new Logger(AttendanceReportProcessor.name);

  constructor(private readonly attendanceHandler: AttendanceHandler) {
    super();
    this.logger.log('AttendanceReportProcessor initialized');
  }

  async process(job: Job<AttendanceReportJobData, unknown, AttendanceReportJobName>) {
    this.logger.debug('Processing attendance report', job.name);
    const jobHandler = await this.jobSwitcher(job);
    if (!jobHandler) {
      this.logger.error(`Job handler for ${job.name} not found`);
      throw new BadRequestException(`Job handler for ${job.name} not found`);
    }
  }

  private async jobSwitcher(job: Job<AttendanceReportJobData, unknown, AttendanceReportJobName>) {
    const jobName = job.name as AttendanceReportJobName;
    switch (jobName) {
      case 'attendance.ENTER':
        this.logger.log(`Attendance Report: Processing ENTER job`);
        return this.handleEnterJob(job);
      case 'attendance.EXIT':
        this.logger.log(`Attendance Report: Processing EXIT job`);
        return this.handleExitJob(job);
      default:
        this.logger.error(`Attendance Report: ${jobName} not found`);
        throw new BadRequestException();
    }
  }

  private async handleEnterJob(job: Job<AttendanceReportJobData>) {
    this.logger.log(`Handling ENTER job for user ${job.data.userId}`);
  }

  private async handleExitJob(job: Job<AttendanceReportJobData>) {
    this.logger.log(`Handling EXIT job for user ${job.data.userId}`);
    return this.attendanceHandler.handleAttendanceCreated(job.data);
  }
}
