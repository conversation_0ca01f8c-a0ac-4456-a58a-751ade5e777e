import { But<PERSON>, Col, DatePicker, Form, Row, TimePicker, notification } from 'antd'
import dayjs from 'dayjs'
import { useState } from 'react'
import AddAttendancePositionSelect from './add-attendance-position-select'
import { useAddAttendanceByPosition } from '@/config/queries/attendance/add-attendance-by-position'
import { DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'

// Setup dayjs with timezone
dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.tz.setDefault(DEFAULT_TIMEZONE)

interface FormValues {
	date: dayjs.Dayjs
	time: dayjs.Dayjs
	positionId: string
}

interface AddAttendancePositionFilterProps {
	onFormChange?: (changedFields: any, allFields: any) => void
}

export default function AddAttendancePositionFilter({
	onFormChange
}: AddAttendancePositionFilterProps) {
	const [form] = Form.useForm<FormValues>()
	const { mutateAsync: addAttendanceByPosition } = useAddAttendanceByPosition()
	const [loading, setLoading] = useState(false)

	const handleSubmit = async (values: FormValues) => {
		if (!values.date || !values.time || !values.positionId) {
			notification.error({ message: "Barcha maydonlarni to'ldiring!" })
			return
		}

		setLoading(true)
		try {
			// Combine date and time timezone conversion

			const dateTime = dayjs
				.tz(`${values.date.format('YYYY-MM-DD')} ${values.time.format('HH:mm')}`, DEFAULT_TIMEZONE)
				.toDate()

			// Call API to add attendance for all users in the selected position
			await addAttendanceByPosition({
				positionId: values.positionId,
				time: dayjs(dateTime)
			})

			form.resetFields()
		} catch (error) {
			// Error notification is handled by the mutation
			console.error('Error adding attendance by position:', error)
		} finally {
			setLoading(false)
		}
	}

	const handleReset = () => {
		form.resetFields()
	}

	return (
		<Form
			form={form}
			className='flex w-full p-4'
			onFinish={handleSubmit}
			onReset={handleReset}
			onFieldsChange={onFormChange}
			layout='vertical'>
			<Row
				className='w-full'
				gutter={[8, 16]}
				align='bottom'>
				<Col xs={5}>
					<Form.Item
						label='Sana'
						name='date'
						rules={[{ required: true, message: 'Sanani tanlang!' }]}>
						<DatePicker
							className='w-full'
							placeholder='Sanani tanlang'
							format='DD.MM.YYYY'
							size='middle'
						/>
					</Form.Item>
				</Col>
				<Col xs={4}>
					<Form.Item
						label='Vaqt'
						name='time'
						rules={[{ required: true, message: 'Vaqtni tanlang!' }]}>
						<TimePicker
							className='w-full'
							placeholder='Vaqtni tanlang'
							format='HH:mm'
							size='middle'
						/>
					</Form.Item>
				</Col>
				<Col xs={9}>
					<Form.Item
						label='Lavozim'
						name='positionId'
						rules={[{ required: true, message: 'Lavozimni tanlang!' }]}>
						<AddAttendancePositionSelect />
					</Form.Item>
				</Col>
				<Col xs={6}>
					<Form.Item label='Amallar'>
						<div className='flex gap-1'>
							<Button
								type='primary'
								htmlType='submit'
								loading={loading}
								size='middle'>
								Qo'shish
							</Button>
							<Button
								htmlType='reset'
								onClick={handleReset}
								size='middle'>
								Tozalash
							</Button>
						</div>
					</Form.Item>
				</Col>
			</Row>
		</Form>
	)
}
