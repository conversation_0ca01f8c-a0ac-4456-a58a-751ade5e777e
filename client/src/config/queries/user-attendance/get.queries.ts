import { axiosPrivate } from '@/config/api'
import { useQuery } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { attendanceUserEndpoints } from '@/config/api/endpoints'

export interface UserAttendance {
	id: string
	userId: string
	attendanceDate: string
	attendanceTime: string
	attendanceType: string
	createdAt: string
	updatedAt: string
	type?: string
	time?: string
	Organization?: {
		name: string
	}

	description?: string
}

export const useGetUserAttendance = (userId: string) => {
	return useQuery({
		queryKey: [attendanceUserEndpoints.userAttendance(userId)],
		queryFn: async () => {
			const { data } = await axiosPrivate.get<ApiResponse<UserAttendance[]>>(
				attendanceUserEndpoints.userAttendance(userId)
			)
			return data
		}
	})
}

export const useGetPersonalAttendance = (personal: string) => {
	return useQuery({
		queryKey: [attendanceUserEndpoints.personalAttendance(personal)],
		queryFn: async () => {
			const { data } = await axiosPrivate.get<ApiResponse<UserAttendance[]>>(
				attendanceUserEndpoints.personalAttendance(personal)
			)
			return data
		}
	})
}
