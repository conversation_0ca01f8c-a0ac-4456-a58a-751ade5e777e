import { DeleteOutlined, EditOutlined } from '@ant-design/icons'
import { Button, Space, Table, Modal, notification } from 'antd'
import { useUsersModalStore } from './utils/users-modal-store'
import { UsersModal } from './ui/users-modal'
import { useGetAllUser } from '@/config/queries/users/get-all.queries'
import { useDeleteUser } from '@/config/queries/users/delete.queries'
import { useNavigate } from 'react-router-dom'
import { Copy, Eye } from 'lucide-react'
import { useState } from 'react'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { Search } from '@/shared/components/search/search'

export const UsersTable = () => {
	const { onOpen } = useUsersModalStore()
	const navigate = useNavigate()
	const [currentPage, setCurrentPage] = useState(1)
	const [pageSize, setPageSize] = useState(10)

	const {
		data: users,
		isLoading: isUsersLoading,
		setPage,
		setLimit
	} = useGetAllUser()

	const deleteUser = useDeleteUser()

	const handleDelete = (userId: string) => {
		Modal.confirm({
			title: 'Ishonchingiz komilmi?',
			content: "Ushbu foydalanuvchini o'chirishni xoxlaysizmi?",
			okText: 'Ha',
			cancelText: "Yo'q",
			onOk: () => {
				deleteUser.mutateAsync(userId)
			}
		})
	}
	const handleCopyId = (id: string, e: React.MouseEvent) => {
		e.stopPropagation()
		try {
			navigator.clipboard.writeText(id)
			notification.success({
				message: 'Muvaffaqiyatli nusxa ko`chirildi'
			})
		} catch {
			notification.error({
				message: 'Nusxa ko`chirishda xatolik yuz berdi'
			})
		}
	}

	const tableData = users?.data || []

	return (
		<Table
			pagination={{
				total: users?.meta?.total || 0,
				pageSize: pageSize,
				current: currentPage,
				showSizeChanger: true,
				pageSizeOptions: [5, 10, 20, 50, 100],
				onChange: (page, size) => {
					setCurrentPage(page)
					setPageSize(size)
					setPage(page)
					setLimit(size)
				}
			}}
			title={() => (
				<div className='flex items-center justify-between'>
					<Search />
					<UsersModal />
				</div>
			)}
			size='small'
			loading={isUsersLoading}
			scroll={{ x: 'max-content' }}
			dataSource={tableData}
			rowKey={rec => rec.id || ''}
			onRow={record => ({
				onClick: e => {
					if (!(e.target instanceof HTMLButtonElement)) {
						navigate(`/workspace/settings/users/${record.id}`)
					}
				}
			})}
			columns={[
				{
					key: '#',
					title: '#',
					render: (_, _rec, index) => formatTableIndex(currentPage, pageSize, index)
				},
				{
					key: 'id',
					title: 'ID',
					dataIndex: 'id',
					className: 'w-20',
					render: (id: string) => (
						<Button
							type='link'
							icon={<Copy size={16} />}
							onClick={e => handleCopyId(id, e)}
							style={{ padding: 0 }}>
							<span className='text-start truncate w-20'>{id}</span>
						</Button>
					)
				},
				{
					key: 'FIO',
					title: 'Nomi',
					dataIndex: 'fullName'
				},
				{
					key: 'username',
					title: 'Telefon raqami',
					dataIndex: 'username'
				},
				{
					key: 'phone',
					title: "Qo'shimcha telefon raqami",
					dataIndex: 'phone'
				},
				{
					key: 'position',
					title: 'Lavozim',
					dataIndex: ['position', 'name']
				},
				{
					key: 'mainOrganization',
					title: 'Asosiy Tashkilot',
					dataIndex: ['MainOrganization', 'name']
				},
				{
					key: 'ResponsibleFor',
					title: "Ma'sul",
					dataIndex: ['ResponsibleFor', 0, 'name']
				},
				{
					key: 'actions',
					title: 'Amallar',
					render: record => (
						<Space>
							<Button
								type='primary'
								icon={<EditOutlined />}
								onClick={e => {
									e.stopPropagation()
									onOpen(record)
								}}
							/>
							<Button
								icon={<Eye />}
								onClick={e => {
									e.stopPropagation()
									navigate(`/workspace/settings/users/${record.id}`)
								}}
							/>
							<Button
								danger
								icon={<DeleteOutlined />}
								onClick={e => {
									e.stopPropagation()
									handleDelete(record.id)
								}}
							/>
						</Space>
					)
				}
			]}
		/>
	)
}
