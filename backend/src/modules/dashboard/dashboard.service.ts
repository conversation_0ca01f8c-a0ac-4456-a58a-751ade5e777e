import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/common';

@Injectable()
export class DashboardService {
  constructor(private prismaService: PrismaService) {}

  async getChildrenCount(organizationId: string) {
    const childrenCount = await this.prismaService.organization.count({
      where: {
        parentId: organizationId,
      },
    });

    return { childrenCount };
  }

  async getUsersCount(organizationId: string) {
    const organization = await this.prismaService.organization.findFirst({
      where: {
        id: organizationId,
      },
      include: {
        Worker: true,
        Employee: true,
        Responsible: true,
      },
    });

    return {
      workersCount: organization.Worker.length,
      employeesCount: organization.Employee.length,
      responsiblesCount: organization.Responsible.length,
    };
  }

  async getTasksCount(organizationId: string) {
    const organization = await this.prismaService.organization.findUnique({
      where: { id: organizationId },
      include: { Task: true, Recipient: true },
    });

    return { inTask: organization.Recipient.length, outTask: organization.Task.length };
  }

  async getTasksState(organizationId: string) {
    const taskState = await this.prismaService.taskState.findMany({
      select: { name: true, Task: { select: { Recipients: { select: { Organization: true } } } } },
    });

    const tasks = taskState.reduce((acc, state, i) => {
      const checkTask = state.Task.find((task) =>
        task.Recipients.some((recipient) => recipient.Organization.id === organizationId),
      );

      acc.push({
        key: i + 1,
        name: state.name,
        count: checkTask ? state.Task.length : 0,
      });

      return acc;
    }, []);

    return tasks;
  }

  async getGpsInfo(organizationId: string) {
    const organization = await this.prismaService.organization.findUnique({
      where: { id: organizationId },
    });
    if (!organization) throw new NotFoundException('Organization not found');
    const userFilter = {
      OR: [
        { Organization: { some: { id: organizationId } } },
        { mainOrganizationId: organizationId },
        { ResponsibleFor: { some: { id: organizationId } } },
      ],
    };

    const positions = await this.prismaService.organizationTypePosition.findMany({
      where: {
        status: 'ACTIVE',
        type: {
          Organization: { some: { id: organizationId } },
        },
        User: { some: { ...userFilter } },
      },
      select: {
        name: true,
        id: true,
        User: {
          select: { GPSLocationReport: { select: { id: true, type: true } } },
        },
      },
    });

    const inCountWithPositions = [];
    const outCountWithPositions = [];
    const disabledCountWithPositions = [];

    positions.forEach((pos) => {
      const gpsReports = pos.User.flatMap((user) => user.GPSLocationReport);

      const inCount = gpsReports.filter((report) => report.type === 'IN').length;
      const outCount = gpsReports.filter((report) => report.type === 'OUT').length;
      const disabledCount = gpsReports.filter((report) => report.type === 'DISABLED').length;

      if (inCount > 0) {
        inCountWithPositions.push({ positionName: pos.name, count: inCount });
      }

      if (outCount > 0) {
        outCountWithPositions.push({ positionName: pos.name, count: outCount });
      }

      if (disabledCount > 0) {
        disabledCountWithPositions.push({ positionName: pos.name, count: disabledCount });
      }
    });

    const inCount = await this.prismaService.gPSLocationReport.count({
      where: { type: 'IN', User: userFilter },
    });
    const outCount = await this.prismaService.gPSLocationReport.count({
      where: { type: 'OUT', User: userFilter },
    });
    const disabledCount = await this.prismaService.gPSLocationReport.count({
      where: { type: 'DISABLED', User: userFilter },
    });

    return {
      inCount,
      outCount,
      disabledCount,
      inCountWithPositions,
      outCountWithPositions,
      disabledCountWithPositions,
    };
  }

  async dashboardTable(organizationId: string) {
    const organization = await this.prismaService.organization.findUnique({
      where: { id: organizationId, status: 'ACTIVE' },
      include: { grade: true },
    });

    if (!organization) throw new NotFoundException('organization not found');

    const gradeLevel = +organization.grade.level + 10;

    const organizations = await this.prismaService.organization.findMany({
      where: { status: 'ACTIVE', grade: { level: gradeLevel }, parentId: organization.id },
      include: { region: true, district: true, section: true, Employee: true },
    });

    return await Promise.all(
      organizations.map(async (el) => {
        const vocation = await this.prismaService.vacation.findMany({
          where: {
            status: 'ACTIVE',
            user: {
              MainOrganization: { id: el.id },
            },
          },
        });

        const patientCount = vocation.reduce((sum, v) => (v.type === 'PATIONS' ? sum + 1 : sum), 0);
        const vacationCount = vocation.length - patientCount;

        const vacancyCount = await this.prismaService.vacancy.count({
          where: { status: 'ACTIVE', Organization: { id: el.id } },
        });

        if (gradeLevel === 10) {
          return {
            name: el.region.name,
            patientCount,
            vacationCount,
            vacancyCount,
            employee: el.Employee.length,
            id: el.id,
            level: gradeLevel,
          };
        } else if (gradeLevel === 20) {
          return {
            name: el.district?.name,
            patientCount,
            vacationCount,
            vacancyCount,
            employee: el.Employee.length,
            id: el.id,
            level: gradeLevel,
          };
        }
      }),
    );
  }
}
