import { Tabs } from 'antd'
import { OrganizationsTable } from './organizations-table'
import { OrganizationDetails } from './ui/organization-details'
import { UserManagmentTab } from '@/shared/components/user-managment-tab/user-managment-modal'
import { UnderControlOrganizationTable } from '../under-control-organization/under-control-organization-table'

import { useState, useEffect } from 'react'
import { useSearchParams, useNavigate } from 'react-router-dom'

const Organizations = () => {
	const [activeTab, setActiveTab] = useState('organizations')
	const [searchParams, setSearchParams] = useSearchParams()
	const navigate = useNavigate()
	const [tabHistory, setTabHistory] = useState(['organizations'])

	useEffect(() => {
		const handlePopState = () => {
			if (tabHistory.length > 1) {
				const newHistory = [...tabHistory]
				newHistory.pop()
				setTabHistory(newHistory)
				setActiveTab(newHistory[newHistory.length - 1])

				if (newHistory[newHistory.length - 1] !== 'users') {
					const newParams = new URLSearchParams(searchParams)
					newParams.delete('roles')
					newParams.delete('userId')
					setSearchParams(newParams)
				}
			} else {
				setActiveTab('organizations')
				setTabHistory(['organizations'])
				const newParams = new URLSearchParams(searchParams)
				newParams.delete('roles')
				newParams.delete('userId')
				setSearchParams(newParams)
			}
		}

		window.addEventListener('popstate', handlePopState)
		return () => window.removeEventListener('popstate', handlePopState)
	}, [tabHistory, searchParams])

	const handleTabChange = (key: string) => {
		setActiveTab(key)
		setTabHistory([...tabHistory, key])
		navigate('', { state: { tab: key } })

		if (key !== 'users') {
			const newParams = new URLSearchParams(searchParams)
			newParams.delete('roles')
			newParams.delete('userId')
			setSearchParams(newParams)
		}
	}

	return (
		<main>
			<OrganizationDetails />
			<div className='w-full flex flex-col gap-y-4'>
				<Tabs
					type='card'
					activeKey={activeTab}
					onChange={handleTabChange}
					items={[
						{
							key: 'organizations',
							label: 'Quyi tashkilotlar',
							children: <OrganizationsTable />
						},
						{
							key: 'under-control-organizations',
							label: 'Boshqa tashkilotlar',
							children: <UnderControlOrganizationTable />
						},
						{
							key: 'users',
							label: 'Foydalanuvchilar',
							children: <UserManagmentTab />
						}
					]}
				/>
			</div>
		</main>
	)
}
export default Organizations
