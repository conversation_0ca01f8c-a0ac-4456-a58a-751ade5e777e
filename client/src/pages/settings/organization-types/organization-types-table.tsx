import { EditOutlined } from '@ant-design/icons'
import { Button, Space, Table } from 'antd'

import { useState } from 'react'
import { useGetOrganizationTypes } from '@/config/queries/organization-types/get.queries'
import OrganizationTypesModal from './ui/organization-types-modal'
import { useOrganizationTypesModalStore } from './utils/organization-types-store'

import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { Search } from '@/shared/components/search/search'

export const OrganizationTypesTable = () => {
	const [currentPage, setCurrentPage] = useState(1)
	const [pageSize, setPageSize] = useState(10)
	const { data: organizationTypes, hasNextPage, fetchNextPage } = useGetOrganizationTypes()
	const onOpen = useOrganizationTypesModalStore(state => state.onOpen)

	return (
		<>
			<Table
				pagination={{
					total: organizationTypes?.pages?.[0]?.meta?.total || 0,
					pageSize: pageSize,
					current: currentPage,
					showSizeChanger: true,
					pageSizeOptions: [5, 10, 20, 50, 100],
					onChange: (page, size) => {
						setCurrentPage(page)
						setPageSize(size)
						if (
							Math.ceil((page * size) / size) > (organizationTypes?.pages?.length || 0) &&
							hasNextPage
						) {
							fetchNextPage()
						}
					}
				}}
				title={() => (
					<div className='flex items-center justify-between'>
						<Search />
						<OrganizationTypesModal />
					</div>
				)}
				size='small'
				scroll={{ x: 'max-content' }}
				dataSource={organizationTypes?.pages?.flatMap(page => page.data) || []}
				rowKey={(rec: { id?: string }) => rec.id || ''}
				columns={[
					{
						key: '#',
						title: '#',
						render: (_, _rec, index) => formatTableIndex(currentPage, pageSize, index)
					},
					{
						key: 'name',
						title: 'Nomi',
						dataIndex: 'name'
					},
					{
						key: 'actions',
						title: 'Amallar',
						render: record => (
							<Space>
								<Button
									type='primary'
									icon={<EditOutlined />}
									onClick={() => onOpen(record)}
								/>
							</Space>
						)
					}
				]}
			/>
		</>
	)
}
