import { Body, Controller, Get, Param, Patch, Post, Put, Query } from '@nestjs/common';
import { OrganizationService } from './organization.service';
import { ProtectedRoute } from 'src/common';
import { BaseQueryParams } from 'src/helpers/types';
import { Context } from 'src/common/decorators/context.decorator';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';

@Controller('organization')
export class OrganizationController {
  constructor(private readonly organizationService: OrganizationService) {}

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE' })
  @Get()
  async getAll(@Query() query: BaseQueryParams, @Context('organizationId') organizationId: string) {
    return await this.organizationService.getAll({ organizationId, ...query });
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE' })
  @Get('under-control')
  async getControlledBy(@Query() query: BaseQueryParams, @Context('organizationId') organizationId: string) {
    return await this.organizationService.getUnderControl({ organizationId, ...query });
  }

  @ProtectedRoute({ isPublic: false })
  @Get('worker/:id')
  async getByWorker(@Param('id') id: string) {
    return await this.organizationService.getByWorker(id);
  }

  @ProtectedRoute({ isPublic: false })
  @Get(':id')
  async getById(@Param('id') id: string) {
    return await this.organizationService.getOne(id);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Post()
  async create(@Body() data: CreateOrganizationDto) {
    return await this.organizationService.create(data);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Put(':id')
  async update(@Body() data: UpdateOrganizationDto, @Param('id') id: string) {
    return await this.organizationService.update(id, data);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Post(':id/add-workers')
  async addWorkers(@Body() data: { workers: string[] }, @Param('id') id: string) {
    return await this.organizationService.addWorkers(id, data.workers);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Post('add-workers')
  async addWorkersToChildren(@Body() data: { workers: string[] }, @Context('organizationId') organizationId: string) {
    return await this.organizationService.addWorkers(organizationId, data.workers);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: false })
  @Post(':id/remove-workers')
  async removeWorkers(@Body() data: { workers: string[] }, @Param('id') id: string) {
    return await this.organizationService.removeWorkers(id, data.workers);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Post('remove-workers')
  async removeWorkersToChildren(
    @Body() data: { workers: string[] },
    @Context('organizationId') organizationId: string,
  ) {
    return await this.organizationService.removeWorkers(organizationId, data.workers);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Post(':id/add-responsibles')
  async addResponsibles(@Body() data: { responsibles: string[] }, @Param('id') organizationId: string) {
    return await this.organizationService.addResponsibles(organizationId, data.responsibles);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Post(':id/remove-responsibles')
  async removeResponsibles(@Body() data: { responsibles: string[] }, @Param('id') id: string) {
    return await this.organizationService.removeResponsibles(id, data.responsibles);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: false })
  @Post(':id/add-employees')
  async addEmployees(@Body() data: { employees: string[] }, @Param('id') id: string) {
    return await this.organizationService.addEmployees(id, data.employees);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Post('add-employees')
  async addEmployeesToChildren(
    @Body() data: { employees: string[] },
    @Context('organizationId') organizationId: string,
  ) {
    return await this.organizationService.addEmployees(organizationId, data.employees);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Post(':id/remove-employees')
  async removeEmployees(@Body() data: { employees: string[] }, @Param('id') id: string) {
    return await this.organizationService.removeEmployees(id, data.employees);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Post('remove-employees')
  async removeEmployeesToChildren(
    @Body() data: { employees: string[] },
    @Context('organizationId') organizationId: string,
  ) {
    return await this.organizationService.removeEmployees(organizationId, data.employees);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Post(':id/create-children')
  async createChild(@Body() data: CreateOrganizationDto, @Param('id') id: string) {
    return await this.organizationService.createChildren(id, data);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Post('create-children')
  async createChildToChildren(@Body() data: CreateOrganizationDto, @Context('organizationId') organizationId: string) {
    return await this.organizationService.createChildren(organizationId, data);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Post(':id/add-under-controls')
  async addUnderControl(
    @Body()
    data: {
      organizations: string[];
    },
    @Param('id') id: string,
  ) {
    return await this.organizationService.addUnderControls(id, data.organizations);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Post('add-under-controls')
  async addUnderControlToChildren(
    @Body()
    data: {
      organizations: string[];
    },
    @Context('organizationId') organizationId: string,
  ) {
    return await this.organizationService.addUnderControls(organizationId, data.organizations);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Post(':id/remove-under-controls')
  async removeUnderControl(@Body() data: { organizations: string[] }, @Param('id') id: string) {
    return await this.organizationService.removeUnderControls(id, data.organizations);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Post('remove-under-controls')
  async removeUnderControlToChildren(
    @Body() data: { organizations: string[] },
    @Context('organizationId') organizationId: string,
  ) {
    return await this.organizationService.removeUnderControls(organizationId, data.organizations);
  }

  @ProtectedRoute({ isPublic: false })
  @Get('users/:orgId')
  async getUsersByOrganization(
    @Param('orgId') orgId: string,
    @Query()
    {
      role,
      page,
      limit,
      search,
    }: { role: ('worker' | 'employee' | 'responsible')[]; page: string; limit: string; search: string },
  ) {
    return await this.organizationService.getUsersByOrganization(orgId, role, page, limit, search);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Get(':id/low-grades')
  async getLowGradeOrganizations(@Param('id') organizationId: string, @Query('search') search?: string) {
    return await this.organizationService.getLowGradeOrganizations(organizationId, search);
  }

  @ProtectedRoute({ isPublic: false, context: 'WORKSPACE', onlyAdmin: true })
  @Put('deactivate/:id')
  async deactivate(@Param('id') organizationId: string) {
    return this.organizationService.deactivateOrganizationById(organizationId);
  }

  //

  @ProtectedRoute({ isPublic: false, context: 'PERSONAL' })
  @Get('inspector/:id')
  async getByIdForInspector(@Param('id') id: string) {
    return await this.organizationService.getOne(id);
  }

  @ProtectedRoute({ isPublic: false, context: 'PERSONAL' })
  @Get(':organizationId/inspector')
  async getAllForInspector(@Query() query: BaseQueryParams, @Param('organizationId') organizationId: string) {
    return await this.organizationService.getAllForInspector({ query, organizationId });
  }
  // @ProtectedRoute({ isPublic: false, context: 'PERSONAL' })
  // @Get(':organizationId/inspector')
  // async getAllForInspector(
  //   @Query() query: BaseQueryParams,
  //   @Param('organizationId') organizationId: string,
  //   @Context() context: any, // Debug uchun
  // ) {
  //   console.log('URL dan kelgan organizationId:', organizationId);
  //   console.log("Context dan kelgan ma'lumotlar:", context);
  //   console.log('Query parametrlari:', query);

  //   return await this.organizationService.getAllForInspector({ query, organizationId });
  // }
}
