import { axiosPrivate } from '@/config/api'
import { organizationEndpoints } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'

type TWorker = {
	id: string
	fullName: string
	username: string
	phone: string
	role: string
	status: string
	positionId: string
	createdAt: string
	updatedAt: string
}

type TWorkersResponse = {
	Worker: TWorker[]
}

export const useGetWorkersForController = (organizationId?: string) => {
	return useQuery({
		queryKey: ['organization-workers-controller', organizationId],
		queryFn: async (): Promise<TWorkersResponse> => {
			if (!organizationId) throw new Error('Organization ID is required')
			const response = await axiosPrivate.get<TWorkersResponse>(
				organizationEndpoints.workers(organizationId)
			)
			return response.data
		},
		enabled: !!organizationId
	})
}
