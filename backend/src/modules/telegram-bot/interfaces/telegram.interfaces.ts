export interface TelegramRoot {
  update_id: number;
  message: TelegramMessage;
}

export interface TelegramMessage {
  message_id: number;
  from: TelegramFrom;
  chat: TelegramChat;
  date: number;
  text?: string;
  entities?: TelegramEntity[];
  contact?: TelegramContact;
}

export interface TelegramFrom {
  id: number;
  is_bot: boolean;
  first_name: string;
  username?: string;
  language_code?: string;
}

export interface TelegramChat {
  id: number;
  first_name: string;
  username?: string;
  type: string;
}

export interface TelegramEntity {
  offset: number;
  length: number;
  type: string;
}

export interface TelegramContact {
  phone_number: string;
  first_name: string;
  user_id: number;
}
