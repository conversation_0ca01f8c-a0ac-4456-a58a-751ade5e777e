import { axiosPrivate } from '@/config/api'
import { recipientEndpoints } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'
import { TRecipient } from '../task/getOne'

export const useGetOneRecipient = (id: string) => {
	return useQuery({
		queryKey: [recipientEndpoints.one(id)],
		queryFn: async () => {
			return (await axiosPrivate.get<TRecipient>(recipientEndpoints.one(id)))?.data
		}
	})
}
