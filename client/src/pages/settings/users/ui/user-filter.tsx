import { Input, Space, Button } from 'antd'
import { SearchOutlined } from '@ant-design/icons'
import { useState } from 'react'
import { useSearchParams } from 'react-router-dom'

const UserFilter = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const [searchValue, setSearchValue] = useState<string>(searchParams.get('search') || '')

	const handleSearch = () => {
		const newParams = new URLSearchParams(searchParams)

		if (searchValue.trim()) {
			newParams.set('search', searchValue.trim())
		} else {
			newParams.delete('search')
		}
		setSearchParams(newParams)
	}

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearchValue(e.target.value)
	}

	const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
		if (e.key === 'Enter') {
			handleSearch()
		}
	}

	const handleClear = () => {
		setSearchValue('')
		const newParams = new URLSearchParams(searchParams)
		newParams.delete('search')
		setSearchParams(newParams)
	}

	return (
		<Space
			direction='horizontal'
			style={{ width: '100%' }}>
			<Input
				placeholder='Foydalanuvchilarni qidirish...'
				value={searchValue}
				onChange={handleInputChange}
				onKeyPress={handleKeyPress}
				style={{ width: '300px' }}
				allowClear
				onPressEnter={handleSearch}
			/>
			<Button
				type='primary'
				icon={<SearchOutlined />}
				onClick={handleSearch}>
				Qidirish
			</Button>
			{searchParams.has('search') && <Button onClick={handleClear}>Tozalash</Button>}
		</Space>
	)
}

export default UserFilter
