import { AttendanceType } from '@prisma/client';
import { IsEnum, IsIn, IsIP, IsMACAddress, IsOptional, IsString } from 'class-validator';
import { Transform, Type } from 'class-transformer';

export class CreateFaceIdDto {
  @IsString()
  orgId: string;
  @IsString()
  @IsMACAddress()
  MAC: string;
  @IsString()
  @IsIP()
  IP: string;
  @IsString()
  @IsEnum(AttendanceType)
  type?: AttendanceType;
}

export class FaceIdQueryDto {
  @IsOptional()
  @IsString()
  search?: string;

  @IsOptional()
  @Type(() => Number)
  page?: number;

  @IsOptional()
  @Type(() => Number)
  limit?: number;

  @IsOptional()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return undefined;
  })
  @IsIn([true, false], { message: 'onlineStatus must be "true" or "false"' })
  onlineStatus?: boolean;
}
