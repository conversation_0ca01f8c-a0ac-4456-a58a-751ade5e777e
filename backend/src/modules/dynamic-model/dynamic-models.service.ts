import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../common';
import { Prisma, Status } from '@prisma/client';

@Injectable()
export class DynamicModelsService {
  constructor(private readonly prisma: PrismaService) {}

  getAllModelNames(): string[] {
    return Object.keys(this.prisma).filter((key) => {
      const model = this.prisma[key];
      return typeof model === 'object' && typeof model.findMany === 'function' && 'status' in model.fields;
    });
  }

  async activateStatus(model: string, id: string): Promise<any> {
    const modelClient = (this.prisma as any)[model];

    if (!modelClient || typeof modelClient.update !== 'function') {
      throw new NotFoundException(`Model '${model}' topilmadi`);
    }

    try {
      return await modelClient.update({
        where: { id },
        data: { status: Status.ACTIVE },
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        throw new NotFoundException(`ID '${id}' uchun ma'lumot topilmadi`);
      }

      throw new BadRequestException(`Ma'lumotni yangilashda xatolik: ${error.message}`);
    }
  }

  async updateModelData(model: string, id: string, data: any): Promise<any> {
    const modelClient = (this.prisma as any)[model];

    if (!modelClient || typeof modelClient.update !== 'function') {
      throw new NotFoundException(`Model '${model}' topilmadi`);
    }

    if (!data || Object.keys(data).length === 0) {
      throw new BadRequestException("Yangilash uchun hech qanday ma'lumot yuborilmadi");
    }

    try {
      return await modelClient.update({
        where: { id },
        data,
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        throw new NotFoundException(`ID '${id}' uchun ma'lumot topilmadi`);
      }
      throw new BadRequestException(`Ma'lumotni yangilashda xatolik: ${error.message}`);
    }
  }

  async getInactiveItems(model: string): Promise<any[]> {
    const modelClient = this.prisma[model];

    if (!modelClient || typeof modelClient.findMany !== 'function') {
      throw new NotFoundException(`Model '${model}' topilmadi`);
    }

    if (!('status' in modelClient.fields)) {
      throw new BadRequestException(`Model '${model}' status maydoni mavjud emas`);
    }

    return await modelClient.findMany({
      where: { status: Status.INACTIVE },
    });
  }

  async forceDelete(model: string, id: string) {
    const modelClient = this.prisma[model];
    if (!modelClient || typeof modelClient.delete !== 'function') {
      throw new NotFoundException(`Model '${model}' topilmadi`);
    }

    try {
      return await modelClient.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        throw new NotFoundException(`ID '${id}' uchun ma'lumot topilmadi`);
      }
      throw new BadRequestException(`Ma'lumotni o'chirishda xatolik: ${error.message}`);
    }
  }
}
