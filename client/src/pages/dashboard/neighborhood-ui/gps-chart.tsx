import { Card } from 'antd'
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, Too<PERSON><PERSON>, <PERSON>A<PERSON><PERSON>, YAxis } from 'recharts'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { useGetDashboardSectionStatsArea } from '@/config/queries/dashboard/section-dashboard'

const chartColors = {
	area: {
		main: '#2563EB',
		bar: '#F97316'
	},
	outside: {
		main: '#2563EB',
		bar: '#FACC15'
	},
	noConnection: {
		main: '#2563EB',
		bar: '#10B981'
	}
}

const defaultStats = [
	{ id: 1, title: 'Mahalla raislari', value: '0' },
	{ id: 2, title: 'Hokim yordamchilari', value: '0' },
	{ id: 3, title: 'Xotin qizlar faoli', value: '0' },
	{ id: 4, title: 'Yoshlar yetakchisi', value: '0' },
	{ id: 5, title: 'Profilaktika inspektori', value: '0' },
	{ id: 6, title: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hodim', value: '0' },
	{ id: 7, title: 'Soliq inspektori', value: '0' }
]

const getPositionColor = (index: number) => {
	const colors = [
		'#4ADE80',
		'#F97316',
		'#FACC15',
		'#818CF8',
		'#10B981',
		'#DC2626',
		'#D946EF',
		'#4F46E5',
		'#EC4899',
		'#8B5CF6'
	]
	return colors[index % colors.length]
}

export const GPSChart = () => {
	const { data: gpsInfo, isLoading, error } = useGetDashboardSectionStatsArea()
	const navigate = useNavigate()
	const [searchParams] = useSearchParams()

	const urlOrgId = searchParams.get('orgId')
	const organization = localStorage.getItem('organization')
	const organizationData = organization ? JSON.parse(organization) : null
	const orgId = urlOrgId || organizationData?.id

	const formatPositionsData = (positions: any[]) => {
		if (!positions || positions.length === 0) {
			return defaultStats.map((stat, index) => ({
				name: stat.title,
				Jami: parseInt(stat.value),
				fill: getPositionColor(index)
			}))
		}
		return positions.map((pos, index) => ({
			name: pos.name,
			Jami: pos.count,
			fill: getPositionColor(index),
			id: pos.id
		}))
	}

	const handleInAreaClick = () => {
		navigate(`/workspace/dashboard-details/in-area?orgId=${orgId}`)
	}

	const handleOutAreaClick = () => {
		navigate(`/workspace/dashboard-details/out-area?orgId=${orgId}`)
	}

	const handleDisconnectedClick = () => {
		navigate(`/workspace/dashboard-details/disabled?orgId=${orgId}`)
	}

	const handleInAreaPositionClick = (positionId: string) => {
		navigate(`/workspace/dashboard-details/in-area?orgId=${orgId}&positionId=${positionId}`)
	}

	const handleOutAreaPositionClick = (positionId: string) => {
		navigate(`/workspace/dashboard-details/out-area?orgId=${orgId}&positionId=${positionId}`)
	}

	const handleDisconnectedPositionClick = (positionId: string) => {
		navigate(`/workspace/dashboard-details/disabled?orgId=${orgId}&positionId=${positionId}`)
	}

	// Safe checks for list data
	const inAreaList = gpsInfo?.inArea?.list
	const outAreaList = gpsInfo?.outArea?.list
	const disconnectedList = gpsInfo?.disconnected?.list
	const hasInAreaData = inAreaList && inAreaList.length > 0
	const hasOutAreaData = outAreaList && outAreaList.length > 0
	const hasDisconnectedData = disconnectedList && disconnectedList.length > 0

	if (isLoading) {
		return <div>Loading...</div>
	}

	if (error) {
		return <div>Error loading GPS data</div>
	}

	return (
		<div className='flex flex-col w-full space-y-8 p-4'>
			<div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
				{/* In Area Card */}
				<Card className='p-6 transition-shadow duration-300 border border-gray-200 rounded-lg'>
					<div className='flex justify-between w-full font-semibold mb-6'>
						<span className='text-2xl lg:text-4xl'>Hududda</span>
						<span
							className='text-2xl lg:text-4xl text-blue-600 cursor-pointer hover:text-blue-800 transition-colors'
							onClick={handleInAreaClick}
							title="Batafsil ko'rish uchun bosing">
							{gpsInfo?.inArea?.total || 0}
						</span>
					</div>
					<div className='overflow-x-auto'>
						<BarChart
							width={450}
							height={300}
							data={formatPositionsData(hasInAreaData ? inAreaList : defaultStats)}
							margin={{
								top: 5,
								right: 30,
								left: 20,
								bottom: 5
							}}>
							<CartesianGrid strokeDasharray='3 3' />
							<XAxis
								dataKey='name'
								tick={{ fontSize: 12 }}
							/>
							<YAxis tick={{ fontSize: 12 }} />
							<Tooltip contentStyle={{ fontSize: '16px', color: '#000000' }} />
							<Bar
								dataKey='Jami'
								fill={chartColors.area.bar}
								cursor='pointer'
								onClick={data => {
									if (data && data.id) {
										handleInAreaPositionClick(data.id.toString())
									}
								}}
							/>
						</BarChart>
					</div>
				</Card>

				{/* Outside Area Card */}
				<Card className='p-6 transition-shadow duration-300 border border-gray-200 rounded-lg'>
					<div className='flex justify-between w-full font-semibold mb-6'>
						<span className='text-2xl lg:text-4xl'>Hududdan tashqarida</span>
						<span
							className='text-2xl lg:text-4xl text-blue-600 cursor-pointer hover:text-blue-800 transition-colors'
							onClick={handleOutAreaClick}
							title="Batafsil ko'rish uchun bosing">
							{gpsInfo?.outArea?.total || 0}
						</span>
					</div>
					<div className='overflow-x-auto'>
						<BarChart
							width={450}
							height={300}
							data={formatPositionsData(hasOutAreaData ? outAreaList : defaultStats)}
							margin={{
								top: 5,
								right: 30,
								left: 20,
								bottom: 5
							}}>
							<CartesianGrid strokeDasharray='3 3' />
							<XAxis
								dataKey='name'
								tick={{ fontSize: 12 }}
							/>
							<YAxis tick={{ fontSize: 12 }} />
							<Tooltip contentStyle={{ fontSize: '16px', color: '#000000' }} />
							<Bar
								dataKey='Jami'
								fill={chartColors.outside.bar}
								cursor='pointer'
								onClick={data => {
									if (data && data.id) {
										handleOutAreaPositionClick(data.id.toString())
									}
								}}
							/>
						</BarChart>
					</div>
				</Card>

				{/* No Connection Card */}
				<Card className='p-6 transition-shadow duration-300 border border-gray-200 rounded-lg'>
					<div className='flex justify-between w-full font-semibold mb-6'>
						<span className='text-2xl lg:text-4xl'>Aloqa yo'q</span>
						<span
							className='text-2xl lg:text-4xl text-blue-600 cursor-pointer hover:text-blue-800 transition-colors'
							onClick={handleDisconnectedClick}
							title="Batafsil ko'rish uchun bosing">
							{gpsInfo?.disconnected?.total || 0}
						</span>
					</div>
					<div className='overflow-x-auto'>
						<BarChart
							width={450}
							height={300}
							data={formatPositionsData(hasDisconnectedData ? disconnectedList : defaultStats)}
							margin={{
								top: 5,
								right: 30,
								left: 20,
								bottom: 5
							}}>
							<CartesianGrid strokeDasharray='3 3' />
							<XAxis
								dataKey='name'
								tick={{ fontSize: 12 }}
							/>
							<YAxis tick={{ fontSize: 12 }} />
							<Tooltip contentStyle={{ fontSize: '16px', color: '#000000' }} />
							<Bar
								dataKey='Jami'
								fill={chartColors.noConnection.bar}
								cursor='pointer'
								onClick={data => {
									if (data && data.id) {
										handleDisconnectedPositionClick(data.id.toString())
									}
								}}
							/>
						</BarChart>
					</div>
				</Card>
			</div>

			<div className='grid grid-cols-4 gap-4 mt-2 p-4 mx-auto text-center'>
				{(hasInAreaData ? inAreaList : defaultStats).map((position, index) => (
					<div
						key={'name' in position ? position.name : position.title}
						className='flex items-center gap-2 cursor-pointer'
						onClick={() => {
							if ('id' in position && position.id) {
								handleInAreaPositionClick(position.id.toString())
							}
						}}>
						<div
							className='w-18 h-6'
							style={{ backgroundColor: getPositionColor(index) }}></div>
						<span className='text-2xl'>{'name' in position ? position.name : position.title}</span>
					</div>
				))}
			</div>
		</div>
	)
}
