import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import * as timezone from 'dayjs/plugin/timezone';
import 'dayjs/locale/uz-latn';

// Dayjs pluginlarini o'rnatish
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.locale('uz-latn');

const DEFAULT_TIMEZONE = 'Asia/Tashkent';

export const formatDate = (date: Date) => {
  return dayjs(date).tz(DEFAULT_TIMEZONE).format('YYYY-MM-DD');
};

export const formatTime = (date: Date) => {
  // dayjs yordamida to'g'ri timezone konvertatsiyasi
  return dayjs(date).tz(DEFAULT_TIMEZONE).format('HH:mm');
};

export const formatDateTime = (date: Date) => {
  return dayjs(date).tz(DEFAULT_TIMEZONE).format('YYYY-MM-DD HH:mm:ss');
};

export const formatDateTimeForDisplay = (date: Date | string) => {
  // Frontend uchun formatlash
  return dayjs(date).tz(DEFAULT_TIMEZONE).format('DD.MM.YYYY HH:mm');
};

export const getTime = (minutes: number) => {
  const hours = Math.floor(minutes / 60);
  const restMinutes = minutes % 60;

  return `${hours} soat` + (restMinutes ? ` ${restMinutes} minut` : '');
};

// UTC saqlash uchun yordamchi funksiya
export const toUTC = (date: Date | string) => {
  return dayjs(date).utc().toDate();
};

// Mahalliy vaqtga o'tkazish uchun yordamchi funksiya
export const toLocalTime = (utcDate: Date | string) => {
  return dayjs(utcDate).tz(DEFAULT_TIMEZONE);
};
