import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>rm, Divider } from 'antd'
import { STATE } from '@/shared/types/state.type'
import { getFileUrl } from '@/shared/utils/getFileUrl'
import { Link } from 'react-router-dom'
import dayjs from 'dayjs'
import { useUpdateControllerRecipientAnswerState } from '@/config/queries/task/update-controller-recipient-state'

interface ControllerRecipientAnswersProps {
	task: any
}

export const ControllerRecipientAnswers = ({ task }: ControllerRecipientAnswersProps) => {
	const { mutateAsync: updateAnswerState, isPending } = useUpdateControllerRecipientAnswerState()

	const handleConfirmAnswer = async (recipientId: string, answerId: string) => {
		await updateAnswerState({
			taskId: task.id,
			recipientId,
			recipientAnswerId: answerId,
			data: { state: STATE.CONFIRMED }
		})
	}

	const handleRejectAnswer = async (recipientId: string, answerId: string, reason: string) => {
		await updateAnswerState({
			taskId: task.id,
			recipientId,
			recipientAnswerId: answerId,
			data: { state: STATE.REJECTED, rejectReason: reason }
		})
	}

	const getStateColor = (state: string) => {
		switch (state) {
			case STATE.PENDING:
				return 'orange'
			case STATE.CONFIRMED:
				return 'green'
			case STATE.REJECTED:
				return 'red'
			default:
				return 'default'
		}
	}

	const getStateText = (state: string) => {
		switch (state) {
			case STATE.PENDING:
				return 'Kutilmoqda'
			case STATE.CONFIRMED:
				return 'Tasdiqlangan'
			case STATE.REJECTED:
				return 'Rad etilgan'
			default:
				return state
		}
	}

	// Get all recipients with answers
	const recipientsWithAnswers = task?.Recipients?.filter((recipient: any) => 
		recipient.Answer && recipient.Answer.length > 0
	) || []

	if (recipientsWithAnswers.length === 0) {
		return (
			<>
				<Divider />
				<div className="px-3">
					<h2 className="text-xl font-bold mb-4">Bajaruvchi javoblari</h2>
					<p className="text-gray-500">Hozircha javoblar mavjud emas</p>
				</div>
			</>
		)
	}

	return (
		<>
			<Divider />
			<div className="px-3">
				<h2 className="text-xl font-bold mb-4">Bajaruvchi javoblari</h2>
				
				<div className="space-y-6">
					{recipientsWithAnswers.map((recipient: any) => (
						<Card
							key={recipient.id}
							title={
								<div className="flex items-center gap-2">
									<span>{recipient.Organization?.name || recipient.User?.fullName}</span>
									<Badge 
										count={recipient.Answer?.filter((a: any) => a.state === STATE.PENDING).length || 0}
										style={{ backgroundColor: '#f50' }}
									/>
								</div>
							}
							className="shadow-sm"
						>
							{recipient.Answer.length === 0 ? (
								<p className="text-gray-500">Javoblar mavjud emas</p>
							) : (
								<div className="space-y-4">
									{recipient.Answer.map((answer: any) => (
										<div
											key={answer.id}
											className="p-4 border rounded-lg bg-gray-50"
										>
											<div className="flex justify-between items-start mb-3">
												<div className="flex-1">
													<div className="flex items-center gap-2 mb-2">
														<Badge 
															color={getStateColor(answer.state)}
															text={getStateText(answer.state)}
														/>
														<span className="text-sm text-gray-500">
															{dayjs(answer.createdAt).format('DD.MM.YYYY HH:mm')}
														</span>
													</div>
													
													<p className="text-base mb-2">
														<strong>Izoh:</strong> {answer.description}
													</p>
													
													{answer.type?.name && (
														<p className="text-sm text-gray-600 mb-2">
															<strong>Natija turi:</strong> {answer.type.name}
														</p>
													)}

													{answer.rejectReason && (
														<p className="text-sm text-red-600 mb-2">
															<strong>Rad etish sababi:</strong> {answer.rejectReason}
														</p>
													)}

													{answer.files && answer.files.length > 0 && (
														<div className="mt-3">
															<p className="text-sm font-medium mb-2">Fayllar:</p>
															<div className="flex flex-wrap gap-2">
																{answer.files.map((file: any) => (
																	<Link
																		key={file.path}
																		to={getFileUrl(file.path)}
																		target="_blank"
																		className="px-3 py-1 bg-blue-100 text-blue-600 rounded text-sm hover:bg-blue-200"
																	>
																		{file.slug}
																	</Link>
																))}
															</div>
														</div>
													)}
												</div>

												{answer.state === STATE.PENDING && (
													<div className="flex gap-2 ml-4">
														<Popconfirm
															title="Haqiqatdan ham bu javobni tasdiqlamoqchimisiz?"
															onConfirm={() => handleConfirmAnswer(recipient.id, answer.id)}
															okText="Ha"
															cancelText="Yo'q"
														>
															<Button 
																type="primary" 
																size="small"
																loading={isPending}
															>
																Tasdiqlash
															</Button>
														</Popconfirm>
														
														<Popconfirm
															title="Rad etish sababini kiriting"
															onConfirm={(_e) => {
																const reason = prompt("Rad etish sababini kiriting:");
																if (reason) {
																	handleRejectAnswer(recipient.id, answer.id, reason);
																}
															}}
															okText="Rad qilish"
															cancelText="Bekor qilish"
														>
															<Button 
																danger 
																size="small"
																loading={isPending}
															>
																Rad qilish
															</Button>
														</Popconfirm>
													</div>
												)}
											</div>
										</div>
									))}
								</div>
							)}
						</Card>
					))}
				</div>
			</div>
		</>
	)
}
