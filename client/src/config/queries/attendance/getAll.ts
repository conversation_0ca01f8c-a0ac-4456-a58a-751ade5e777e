import { axiosPrivate } from '@/config/api'
import { attendanceUserEndpoints } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'

export interface Root {
	reportPeriod: ReportPeriod
	workers: Worker[]
}

export interface ReportPeriod {
	startDate: string
	endDate: string
}

export interface Worker {
	employee: Employee
	workSchedule: WorkSchedule[]
	breakSchedule: BreakSchedule
	summary: Summary
	dailyReports: DailyReport[]
	organization: Organization
}

export interface Employee {
	id: string
	fullName: string
	position: Position
}

export interface Position {
	id: string
	name: string
}

export interface WorkSchedule {
	dayNumber: number
	dayName: string
	startTime: string
	endTime: string
}

export interface BreakSchedule {
	startTime: string
	endTime: string
}

export interface Summary {
	totalWorkDays: number
	totalAttendanceDays: number
	totalLateMinutes: number
	totalEarlyExitMinutes: number
	totalMinutesWorked: number
	totalWorkMinutesPlanned: number
	attendanceRate: number
	missedWorkdays: number
	planHours: string
	factHours: string
}

export interface DailyReport {
	day: number
	date: string
	formattedDate: string
	status: string
	isWorkDay: boolean
	cameToWork: boolean
	scheduledStartTime: string
	scheduledEndTime: string
	actualEnterTime?: string
	actualExitTime?: string
	minutesWorked: number
	lateMinutes: number
	earlyExitMinutes: number
	scheduledEnter?: string
	scheduledExit?: string
	scheduledMinutes?: number
	underTimeMinutes?: number
	isLate: boolean
	isEarlyExit: boolean
	earlyExitValue?: number
	lateEnterValue: any
	expectedWorkday?: boolean
}

export interface Organization {
	id: string
	name: string
}

export const useGetAllAttendance = () => {
	const [searchParams] = useSearchParams()

	const page = searchParams.get('page')
	const limit = searchParams.get('limit')
	const from = searchParams.get('startDate')
	const to = searchParams.get('endDate')
	const organizationId = searchParams.get('organizationId')

	return useQuery({
		queryKey: [attendanceUserEndpoints.attendance, page, limit, from, to, organizationId],
		queryFn: async () => {
			return (
				await axiosPrivate.get<Root>(attendanceUserEndpoints.attendance, {
					params: {
						startDate: from,
						endDate: to,
						organizationId: organizationId?.length ? organizationId : undefined
					}
				})
			).data
		}
	})
}
