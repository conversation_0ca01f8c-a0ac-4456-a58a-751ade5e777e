import { axiosPrivate } from '@/config/api'
import { botEndpoint } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { notification } from 'antd'

export type TBotRequest = {
	id?: string
	key: string
	value: string
}

export const useCreateBot = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async (data: TBotRequest) =>
			(await axiosPrivate.post<ApiResponse<TBotRequest>>(botEndpoint.all, data)).data,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [botEndpoint.all] })
			notification.success({
				message: '<PERSON><PERSON><PERSON>aq<PERSON><PERSON>i yaratildi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: 'Xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
