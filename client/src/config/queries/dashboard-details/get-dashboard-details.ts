import { axiosPrivate } from '@/config/api'
import { dashboardDetails } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'
import { ApiResponse } from '../types'

type Employee = {
	id: string
	fullName: string
	phone: string
	position: {
		name: string
	}
}

type Vacation = {
	id: string
	userId: string
	userName: string
	type: string
	startDate: string
	endDate: string
	status: string
}

type Patient = {
	id: string
	userId: string
	userName: string
	startDate: string
	endDate: string
	status: string
}

type Vacancy = {
	id: string
	title: string
	organizationId: string
	organizationName: string
	status: string
}

type Position = {
	id: string
	name: string
	userCount: number
	users: Employee[]
}

type AttendanceRecord = {
	id: string
	name: string
	count: number
}

type AttendanceData = {
	total: number
	list: AttendanceRecord[]
}

type Section = {
	id: string
	name: string
	address: string
	district: {
		name: string
	}
	region: {
		name: string
	}
	type: {
		name: string
	}
}

export type TDashboardDetailsEmployee = {
	total: number
	list: Employee[]
}

export type TDashboardDetailsVacation = {
	total: number
	list: Vacation[]
}

export type TDashboardDetailsPatient = {
	total: number
	list: Patient[]
}

export type TDashboardDetailsVacancy = {
	total: number
	list: Vacancy[]
}

export type TDashboardDetailsPosition = Position

export type TDashboardDetailsSection = {
	data: Section[]
	meta: {
		total: number
		page: number
		limit: number
		totalPages: number
		hasMore: boolean
	}
}

export type TDashboardDetailsNoAttendance = {
	data: Employee[]
	meta: {
		total: number
		page: number
		limit: number
		totalPages: number
		hasMore: boolean
	}
}

export const useGetDashboardDetailsEmployee = () => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('orgId')
	const page = searchParams.get('page') || '1'
	const limit = searchParams.get('limit') || '10'

	return useQuery({
		queryKey: ['dashboardDetailsEmployee', { organizationId, page, limit }],
		queryFn: async () => {
			return (
				await axiosPrivate.get<ApiResponse<TDashboardDetailsEmployee[]>>(
					dashboardDetails.employee(organizationId ?? ''),
					{
						params: {
							page: parseInt(page),
							limit: parseInt(limit)
						}
					}
				)
			).data
		},
		enabled: !!organizationId
	})
}

export const useGetDashboardDetailsVacation = () => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('orgId')

	return useQuery({
		queryKey: ['dashboardDetailsVacation', { organizationId }],
		queryFn: async () => {
			return (
				await axiosPrivate.get<ApiResponse<TDashboardDetailsVacation[]>>(
					dashboardDetails.vacation(organizationId ?? '')
				)
			).data
		},
		enabled: !!organizationId
	})
}

export const useGetDashboardDetailsShortVacation = () => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('orgId')

	return useQuery({
		queryKey: ['dashboardDetailsShortVacation', { organizationId }],
		queryFn: async () => {
			return (
				await axiosPrivate.get<ApiResponse<TDashboardDetailsVacation[]>>(
					dashboardDetails.shortVacationvacation(organizationId ?? '')
				)
			).data
		},
		enabled: !!organizationId
	})
}

export const useGetDashboardDetailsPatient = () => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('orgId')

	return useQuery({
		queryKey: ['dashboardDetailsPatient', { organizationId }],
		queryFn: async () => {
			return (
				await axiosPrivate.get<ApiResponse<TDashboardDetailsPatient[]>>(
					dashboardDetails.patient(organizationId ?? '')
				)
			).data
		},
		enabled: !!organizationId
	})
}

export const useGetDashboardDetailsVacancy = () => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('orgId')

	return useQuery({
		queryKey: ['dashboardDetailsVacancy', { organizationId }],
		queryFn: async () => {
			return (
				await axiosPrivate.get<ApiResponse<TDashboardDetailsVacancy[]>>(
					dashboardDetails.vacancy(organizationId ?? '')
				)
			).data
		},
		enabled: !!organizationId
	})
}

export const useGetDashboardDetailsPosition = (positionId: string) => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('orgId')

	return useQuery({
		queryKey: ['dashboardDetailsPosition', { organizationId, positionId }],
		queryFn: async () => {
			return (
				await axiosPrivate.get<ApiResponse<TDashboardDetailsPosition[]>>(
					dashboardDetails.position(organizationId ?? '', positionId)
				)
			).data
		},
		enabled: !!organizationId && !!positionId
	})
}

export const useGetDashboardDetailsLateAttendance = (positionId?: string) => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('orgId')

	const [search] = useSearchParams()

	const page = search.get('page')
	const limit = search.get('limit')

	return useQuery({
		queryKey: ['dashboardDetailsLateAttendance', { organizationId, positionId }, page, limit],
		queryFn: async () => {
			return (
				await axiosPrivate.get<ApiResponse<AttendanceData[]>>(
					dashboardDetails.lateAttendance(organizationId ?? '', positionId || ''),
					{
						params: {
							page,
							limit
						}
					}
				)
			).data
		},
		enabled: !!organizationId
	})
}

export const useGetDashboardDetailsNoAttendance = (positionId?: string) => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('orgId')
	const page = searchParams.get('page') || '1'
	const limit = searchParams.get('limit') || '10'

	return useQuery({
		queryKey: ['dashboardDetailsNoAttendance', { organizationId, positionId, page, limit }],
		queryFn: async () => {
			return (
				await axiosPrivate.get<ApiResponse<TDashboardDetailsNoAttendance[]>>(
					dashboardDetails.noAttendance(organizationId ?? '', positionId || ''),
					{
						params: {
							page: parseInt(page),
							limit: parseInt(limit)
						}
					}
				)
			).data
		},
		enabled: !!organizationId
	})
}

export const useGetDashboardDetailsInArea = (positionId?: string) => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('orgId')
	const page = searchParams.get('page') || '1'
	const limit = searchParams.get('limit') || '10'

	return useQuery({
		queryKey: ['dashboardDetailsInArea', { organizationId, positionId, page, limit }],
		queryFn: async () => {
			return (
				await axiosPrivate.get<ApiResponse<any>>(
					dashboardDetails.inArea(organizationId ?? '', positionId || ''),
					{
						params: {
							page: parseInt(page),
							limit: parseInt(limit)
						}
					}
				)
			).data
		},
		enabled: !!organizationId
	})
}

export const useGetDashboardDetailsOutArea = (positionId?: string) => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('orgId')
	const page = searchParams.get('page') || '1'
	const limit = searchParams.get('limit') || '10'

	return useQuery({
		queryKey: ['dashboardDetailsOutArea', { organizationId, positionId, page, limit }],
		queryFn: async () => {
			return (
				await axiosPrivate.get<ApiResponse<any>>(
					dashboardDetails.outArea(organizationId ?? '', positionId || ''),
					{
						params: {
							page: parseInt(page),
							limit: parseInt(limit)
						}
					}
				)
			).data
		},
		enabled: !!organizationId
	})
}

export const useGetDashboardDetailsDisabledArea = (positionId?: string) => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('orgId')
	const page = searchParams.get('page') || '1'
	const limit = searchParams.get('limit') || '10'

	return useQuery({
		queryKey: ['dashboardDetailsDisabledArea', { organizationId, positionId, page, limit }],
		queryFn: async () => {
			return (
				await axiosPrivate.get<ApiResponse<any>>(
					dashboardDetails.disabledArea(organizationId ?? '', positionId || ''),
					{
						params: {
							page: parseInt(page),
							limit: parseInt(limit)
						}
					}
				)
			).data
		},
		enabled: !!organizationId
	})
}
export const useGetDashboardDetailsSection = () => {
	const [searchParams] = useSearchParams()
	const organizationId = searchParams.get('orgId')
	const page = searchParams.get('page') || '1'
	const limit = searchParams.get('limit') || '10'

	return useQuery({
		queryKey: ['dashboardDetailsSection', { organizationId, page, limit }],
		queryFn: async () => {
			return (
				await axiosPrivate.get<ApiResponse<TDashboardDetailsSection[]>>(
					dashboardDetails.section(organizationId ?? ''),
					{
						params: {
							page: parseInt(page),
							limit: parseInt(limit)
						}
					}
				)
			).data
		},
		enabled: !!organizationId
	})
}
