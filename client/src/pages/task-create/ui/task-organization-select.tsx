import { Form, Select, Col } from 'antd'
import { TOrganization } from '@/config/queries/organizations/get-all.queries'
import { TRecipent } from '../task-create'

type TOrganizationSelectorProps = {
	recipientType: string
	recipent: TRecipent
	recipentIndex: number
	allOrganizations: TOrganization[]
	recipents: TRecipent[]
	handleOrganizationSelect: (organizationId: string, recipentIndex: number) => void
	handleOrganizationDeselect: (organizationId: string, recipentIndex: number) => void
	// Add these props for pagination
	hasNextPage?: boolean
	fetchNextPage?: () => void
	isPending?: boolean
}

export const OrganizationSelector = ({
	recipientType,
	recipent,
	recipentIndex,
	allOrganizations,
	recipents,
	handleOrganizationSelect,
	handleOrganizationDeselect,
	hasNextPage,
	fetchNextPage,
	isPending
}: TOrganizationSelectorProps) => {
	return (
		<Col
			xs={24}
			sm={24}
			md={recipientType === 'organizations' ? 24 : 11}>
			<Form.Item
				label='Tashkilot'
				labelCol={{ span: 24 }}
				rules={[{ required: true }]}>
				<Select
					mode={recipientType === 'organizations' ? 'multiple' : undefined}
					value={
						recipientType === 'organizations'
							? recipent?.organizationId === 'all'
								? ['all']
								: recipent?.selectedOrganizations || []
							: recipent?.organizationId
					}
					className='rounded-md'
					allowClear
					loading={isPending}
					onSelect={organizationId => handleOrganizationSelect(organizationId, recipentIndex)}
					onDeselect={organizationId => handleOrganizationDeselect(organizationId, recipentIndex)}
					onPopupScroll={e => {
						const target = e.target as HTMLDivElement
						if (
							target.scrollTop + target.clientHeight >= target.scrollHeight - 5 &&
							hasNextPage &&
							!isPending
						) {
							fetchNextPage?.()
						}
					}}
					options={[
						...(recipientType === 'organizations' ? [{ id: 'all', name: 'Barchasi' }] : []),
						...allOrganizations
					].map(item => ({
						...item,
						disabled:
							recipientType === 'employees' &&
							recipents.some(each => each.organizationId === item.id)
					}))}
					fieldNames={{ label: 'name', value: 'id' }}
				/>
			</Form.Item>
		</Col>
	)
}
