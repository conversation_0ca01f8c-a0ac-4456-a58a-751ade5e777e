import { Typography } from 'antd'

const { Text } = Typography

export const InfoItem = ({ title, value }: { title: string; value?: string }) => (
	<div className='flex items-center justify-between gap-4'>
		<Text className='text-gray-500 font-medium'>{title}:</Text>
		<Text className='text-base font-semibold text-gray-900 dark:text-gray-100'>
			{value || 'Ushbu maʼlumot mavjud emas'}
		</Text>
	</div>
)
