import { EditOutlined } from '@ant-design/icons'
import { Button, Space, Table } from 'antd'
import { Eye } from 'lucide-react'
import { Link } from 'react-router-dom'
import { useOrganizationPositionTypesModalStore } from './utils/organization-position-types-store'
import { useGetOrganizationPositionType } from '@/config/queries/organization-position-types/get.queries'
import OrganizationPositionTypesModal from './ui/organization-position-types-modal'
import { useState } from 'react'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { Search } from '@/shared/components/search/search'

const OrganizationPositionTypesTable = () => {
	const { onOpen } = useOrganizationPositionTypesModalStore()
	const [currentPage, setCurrentPage] = useState(1)
	const [pageSize, setPageSize] = useState(10)
	const {
		data: organizationPositionTypes,
		hasNextPage,
		fetchNextPage,
		setPage
	} = useGetOrganizationPositionType()

	const tableData = organizationPositionTypes?.pages?.flatMap(page => page.data) || []

	return (
		<Table
			pagination={{
				total: organizationPositionTypes?.pages?.[0]?.meta?.total || 0,
				pageSize: pageSize,
				current: currentPage,
				showSizeChanger: true,
				pageSizeOptions: [5, 10, 20, 50, 100],
				onChange: (page, size) => {
					setCurrentPage(page)
					setPageSize(size)
					setPage(page)
					if (
						Math.ceil((page * size) / size) > (organizationPositionTypes?.pages?.length || 0) &&
						hasNextPage
					) {
						fetchNextPage()
					}
				}
			}}
			title={() => (
				<div className='flex items-center justify-between'>
					<Search />
					<OrganizationPositionTypesModal />
				</div>
			)}
			size='small'
			scroll={{ x: 'max-content' }}
			dataSource={tableData}
			rowKey='id'
			columns={[
				{
					key: '#',
					title: '#',
					render: (_, _rec, index) => formatTableIndex(currentPage, pageSize, index)
				},
				{
					key: 'name',
					title: 'Nomi',
					dataIndex: 'name'
				},
				{
					key: 'description',
					title: 'Izoh',
					dataIndex: 'description'
				},
				{
					key: 'typeId',
					title: 'Tashkilot Turi',
					dataIndex: ['type', 'name'],
					filters: [
						...new Map(
							(tableData || []).map(type => [
								type.type.id,
								{ text: type.type.name, value: type.type.id }
							])
						).values()
					],
					onFilter: (value, record) => record.type.id === value,
					sorter: (a, b) => a.type.name.localeCompare(b.type.name)
				},
				{
					key: 'actions',
					title: 'Amallar',
					render: record => (
						<Space>
							<Link to={`/workspace/organization-position-types/${record.id}`}>
								<Eye />
							</Link>
							<Button
								type='primary'
								icon={<EditOutlined />}
								onClick={() => onOpen(record)}
							/>
						</Space>
					)
				}
			]}
		/>
	)
}
export default OrganizationPositionTypesTable
