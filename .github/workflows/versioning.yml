name: Semantic Versioning

on:
  push:
    branches:
      - production

jobs:
  version:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    
    steps:
      - name: Get current date
        id: date
        run: |
          echo "timestamp=$(date -u +'%Y-%m-%d %H:%M:%S')" >> $GITHUB_OUTPUT
          echo "date=$(date -u +'%Y-%m-%d')" >> $GITHUB_OUTPUT

      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.GITHUB_TOKEN }}
      
      - name: Git config
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
      
      - name: Initialize first version if needed
        id: init-version
        run: |
          CHANGELOG_FILE="changelog_temp.md"
          
          if ! git describe --tags --match "v*" 2>/dev/null; then
            echo "No version tags found. Analyzing all commits..."
            
            BREAKING_CHANGES=$(git log --format=%B | grep -E "^.+!:" -c || echo "0")
            BREAKING_FOOTER=$(git log --format=%B | grep -E "BREAKING CHANGE:" -c || echo "0")
            FEATURES=$(git log --format=%B | grep -E "^feat(\(.+\))?:" -c || echo "0")
            FIXES=$(git log --format=%B | grep -E "^fix(\(.+\))?:" -c || echo "0")
            
            {
              echo "### Release Information"
              echo "- Released by: ${{ github.actor }}"
              echo "- Release Date: ${{ steps.date.outputs.timestamp }} UTC"
              echo ""
              echo "### Initial Release"
              echo "Based on analysis of all previous commits:"
              echo "- Breaking Changes: $BREAKING_CHANGES"
              echo "- Features: $FEATURES"
              echo "- Fixes: $FIXES"
              echo ""
              
              if [ "$BREAKING_CHANGES" -gt "0" ] || [ "$BREAKING_FOOTER" -gt "0" ]; then
                echo "#### Breaking Changes"
                git log --format=%B | grep -E "^.+!:|BREAKING CHANGE:"
                echo ""
              fi
              
              if [ "$FEATURES" -gt "0" ]; then
                echo "#### Features"
                git log --format=%B | grep -E "^feat(\(.+\))?:"
                echo ""
              fi
              
              if [ "$FIXES" -gt "0" ]; then
                echo "#### Fixes"
                git log --format=%B | grep -E "^fix(\(.+\))?:"
              fi
            } > "$CHANGELOG_FILE"
            
            if [ "$BREAKING_CHANGES" -gt "0" ] || [ "$BREAKING_FOOTER" -gt "0" ]; then
              INITIAL_VERSION="v1.0.0"
            else
              INITIAL_VERSION="v0.1.0"
            fi
            
            echo "Creating initial version: $INITIAL_VERSION"
            
            {
              echo "initial_version=$INITIAL_VERSION"
              echo "version=${INITIAL_VERSION#v}"
              echo "needs_initial_version=true"
              echo "changelog<<EOF"
              cat "$CHANGELOG_FILE"
              echo "EOF"
            } >> "$GITHUB_OUTPUT"
          else
            echo "needs_initial_version=false" >> "$GITHUB_OUTPUT"
          fi

      - name: Update Package Versions for Initial Release
        if: steps.init-version.outputs.needs_initial_version == 'true'
        run: |
          VERSION="${{ steps.init-version.outputs.version }}"
          TIMESTAMP="${{ steps.date.outputs.timestamp }}"
          AUTHOR="${{ github.actor }}"
          
          # Frontend package.json yangilash
          if [ -f "client/package.json" ]; then
            echo "Updating frontend package.json version to $VERSION"
            TMP_FILE=$(mktemp)
            jq --arg version "$VERSION" \
               --arg date "$TIMESTAMP" \
               --arg author "$AUTHOR" \
               '.version = $version | .buildInfo = { buildDate: $date, buildAuthor: $author }' \
               client/package.json > "$TMP_FILE"
            mv "$TMP_FILE" client/package.json
          fi
          
          # Backend package.json yangilash
          if [ -f "backend/package.json" ]; then
            echo "Updating backend package.json version to $VERSION"
            TMP_FILE=$(mktemp)
            jq --arg version "$VERSION" \
               --arg date "$TIMESTAMP" \
               --arg author "$AUTHOR" \
               '.version = $version | .buildInfo = { buildDate: $date, buildAuthor: $author }' \
               backend/package.json > "$TMP_FILE"
            mv "$TMP_FILE" backend/package.json
          fi
          
          # O'zgarishlarni commit qilish
          git add client/package.json backend/package.json || true
          git commit -m "chore: update package versions to $VERSION" || true
          git push
      
      - name: Create Initial Release
        if: steps.init-version.outputs.needs_initial_version == 'true'
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ steps.init-version.outputs.initial_version }}
          release_name: Release ${{ steps.init-version.outputs.initial_version }}
          body: ${{ steps.init-version.outputs.changelog }}
          draft: false
          prerelease: false
      
      - name: Process new version
        if: steps.init-version.outputs.needs_initial_version == 'false'
        id: process-version
        run: |
          CHANGELOG_FILE="changelog_temp.md"
          
          LATEST_VERSION=$(git describe --tags --match "v*" --abbrev=0)
          echo "Latest version: $LATEST_VERSION"
          
          VERSION_PARTS=(${LATEST_VERSION//./ })
          MAJOR=${VERSION_PARTS[0]#v}
          MINOR=${VERSION_PARTS[1]}
          PATCH=${VERSION_PARTS[2]}
          
          BREAKING_CHANGES=$(git log ${LATEST_VERSION}..HEAD --format=%B | grep -E "^.+!:" -c || echo "0")
          BREAKING_FOOTER=$(git log ${LATEST_VERSION}..HEAD --format=%B | grep -E "BREAKING CHANGE:" -c || echo "0")
          FEATURES=$(git log ${LATEST_VERSION}..HEAD --format=%B | grep -E "^feat(\(.+\))?:" -c || echo "0")
          FIXES=$(git log ${LATEST_VERSION}..HEAD --format=%B | grep -E "^fix(\(.+\))?:" -c || echo "0")
            {
            echo "### Release Information"
            echo "- Released by: ${{ github.actor }}"
            echo "- Release Date: ${{ steps.date.outputs.timestamp }} UTC"
            echo ""
            echo "### Changes since $LATEST_VERSION"
            echo ""
            
            # Используем структуру if-elif-elif для правильного применения SemVer
            if [ "$BREAKING_CHANGES" -gt "0" ] || [ "$BREAKING_FOOTER" -gt "0" ]; then
              echo "#### Breaking Changes"
              git log ${LATEST_VERSION}..HEAD --format=%B | grep -E "^.+!:|BREAKING CHANGE:"
              echo ""
              MAJOR=$((MAJOR + 1))
              MINOR=0
              PATCH=0
            elif [ "$FEATURES" -gt "0" ]; then
              echo "#### Features"
              git log ${LATEST_VERSION}..HEAD --format=%B | grep -E "^feat(\(.+\))?:"
              echo ""
              MINOR=$((MINOR + 1))  # Увеличиваем на 1, а не на количество фич
              PATCH=0
            elif [ "$FIXES" -gt "0" ]; then
              echo "#### Fixes"
              git log ${LATEST_VERSION}..HEAD --format=%B | grep -E "^fix(\(.+\))?:"
              PATCH=$((PATCH + 1))  # Увеличиваем на 1, а не на количество фиксов
            fi
          } > "$CHANGELOG_FILE"
          
          NEW_VERSION="v${MAJOR}.${MINOR}.${PATCH}"
          
          {
            echo "new_version=$NEW_VERSION"
            echo "version=${MAJOR}.${MINOR}.${PATCH}"
            echo "changelog<<EOF"
            cat "$CHANGELOG_FILE"
            echo "EOF"
          } >> "$GITHUB_OUTPUT"

      - name: Update Package Versions for New Release
        if: steps.init-version.outputs.needs_initial_version == 'false'
        run: |
          VERSION="${{ steps.process-version.outputs.version }}"
          TIMESTAMP="${{ steps.date.outputs.timestamp }}"
          AUTHOR="${{ github.actor }}"
          
          # Frontend package.json yangilash
          if [ -f "client/package.json" ]; then
            echo "Updating frontend package.json version to $VERSION"
            TMP_FILE=$(mktemp)
            jq --arg version "$VERSION" \
               --arg date "$TIMESTAMP" \
               --arg author "$AUTHOR" \
               '.version = $version | .buildInfo = { buildDate: $date, buildAuthor: $author }' \
               client/package.json > "$TMP_FILE"
            mv "$TMP_FILE" client/package.json
          fi
          
          # Backend package.json yangilash
          if [ -f "backend/package.json" ]; then
            echo "Updating backend package.json version to $VERSION"
            TMP_FILE=$(mktemp)
            jq --arg version "$VERSION" \
               --arg date "$TIMESTAMP" \
               --arg author "$AUTHOR" \
               '.version = $version | .buildInfo = { buildDate: $date, buildAuthor: $author }' \
               backend/package.json > "$TMP_FILE"
            mv "$TMP_FILE" backend/package.json
          fi
          
          # O'zgarishlarni commit qilish
          git add client/package.json backend/package.json || true
          git commit -m "chore: update package versions to $VERSION" || true
          git push
      
      - name: Create New Release
        if: steps.init-version.outputs.needs_initial_version == 'false'
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: ${{ steps.process-version.outputs.new_version }}
          release_name: Release ${{ steps.process-version.outputs.new_version }}
          body: ${{ steps.process-version.outputs.changelog }}
          draft: false
          prerelease: false