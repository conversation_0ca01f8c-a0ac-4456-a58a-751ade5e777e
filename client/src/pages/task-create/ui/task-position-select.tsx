type TaskPositionSelectProps = {
	value?: string
	onChange?: (value: string) => void
}

import { useEffect, useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { useGetReferenceOrganizationPosition } from '@/config/queries/common/common-queries'
import { Select } from 'antd'

export default function TaskPositionSelect({ value, onChange }: TaskPositionSelectProps) {
	const [, setSearchParams] = useSearchParams()
	const [selectedPosition, setSelectedPosition] = useState<string[]>([])

	const organizationPositions = useGetReferenceOrganizationPosition()

	useEffect(() => {
		const positionId = value
		if (positionId && positionId.trim() !== '') {
			const positions = [positionId].filter(id => id.trim() !== '')
			setSelectedPosition(positions)
		} else {
			setSelectedPosition([])
		}

		setSearchParams(prev => {
			const newParams = { ...Object.fromEntries(prev) }
			newParams.grade = '30'
			return newParams
		})
	}, [value, setSearchParams])

	const handleChange = (value: string | string[]) => {
		const valueArray = Array.isArray(value) ? value : value ? [value] : []
		setSelectedPosition(valueArray)
		if (onChange && valueArray.length > 0) {
			onChange(valueArray[0])
		}

		setSearchParams(prev => {
			const newParams = { ...Object.fromEntries(prev) }
			if (!valueArray || valueArray.length === 0) {
				delete newParams.positionId
			} else {
				newParams.positionId = valueArray[0]
			}
			newParams.grade = '30'
			return newParams
		})
	}

	return (
		<div>
			<Select
				allowClear
				placeholder='Lavozimni tanlang'
				value={selectedPosition}
				onChange={handleChange}
				loading={organizationPositions.isLoading}
				className='w-full'
				showSearch
				filterOption={(input, option) =>
					(option?.label ?? '').toLowerCase().includes(input.toLowerCase())
				}
				maxTagCount='responsive'
				mode={undefined}
				options={
					organizationPositions.data?.map(position => ({
						label: position.name,
						value: position.id
					})) || []
				}
			/>
		</div>
	)
}
