import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  let organizationsType = await prisma.organizationType.findFirst({ where: { name: 'admin' } });
  if (!organizationsType) {
    organizationsType = await prisma.organizationType.create({ data: { name: 'admin' } });
  }
  const positions = [
    {
      name: 'admin',
      typeId: organizationsType?.id,
    },
  ];

  for (const position of positions) {
    const existingPosition = await prisma.organizationTypePosition.findFirst({
      where: { name: position.name },
    });

    if (!existingPosition) {
      await prisma.organizationTypePosition.create({ data: position });
      console.log(`Position "${position.name}" created.`);
    } else {
      console.warn(`Position "${position.name}" already exists. Skipping creation.`);
    }
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
