import { Card, Row, Col, Statistic, Divider, Tag } from 'antd'
import {
	CheckCircleOutlined,
	CloseCircleOutlined,
	WarningOutlined,
	ExclamationCircleOutlined,
	ClockCircleOutlined,
	CalendarOutlined
} from '@ant-design/icons'
import { WorkerDetailTable } from './worker-details-table'
import { WorkSchedule, BreakSchedule } from '@/config/queries/attendance/getAll'

interface WorkerDetailsProps {
	worker: {
		summary: any
		dailyReports: any[]
		fullName: string
		workSchedule: WorkSchedule[]
		breakSchedule: BreakSchedule
	}
	onClose: () => void
}

export const WorkerDetails = ({ worker, onClose }: WorkerDetailsProps) => {
	const { summary, dailyReports, fullName } = worker
	return (
		<Card
			title={
				<div className='flex justify-between items-center'>
					<span>{fullName} - Batafsil ma'lumot</span>
					<Tag
						color='blue'
						className='cursor-pointer'
						onClick={onClose}>
						Yopish
					</Tag>
				</div>
			}
			className='mb-6 dark:bg-gray-800 dark:text-gray-200'>
			<Row gutter={16}>
				<Col span={6}>
					<Statistic
						title="Davomat ko'rsatkichi"
						value={summary.attendanceRate.toFixed(0)}
						suffix='%'
						valueStyle={{
							color:
								summary.attendanceRate >= 90
									? '#3f8600'
									: summary.attendanceRate >= 70
										? '#d46b08'
										: '#cf1322'
						}}
						prefix={<CheckCircleOutlined />}
					/>
				</Col>
				<Col span={6}>
					<Statistic
						title='Jami ish kunlari'
						value={summary.totalWorkDays}
						prefix={<CalendarOutlined />}
					/>
				</Col>
				<Col span={6}>
					<Statistic
						title='Ishga kelgan kunlar'
						value={summary.totalAttendanceDays}
						prefix={<CheckCircleOutlined />}
						valueStyle={{ color: '#3f8600' }}
					/>
				</Col>
				<Col span={6}>
					<Statistic
						title='Qoldirilgan kunlar'
						value={summary.missedWorkdays}
						prefix={<CloseCircleOutlined />}
						valueStyle={{ color: '#cf1322' }}
					/>
				</Col>
			</Row>

			<Divider />

			<Row gutter={16}>
				<Col span={6}>
					<Statistic
						title='Kechikish (daqiqa)'
						value={summary.totalLateMinutes}
						prefix={<WarningOutlined />}
						valueStyle={{ color: summary.totalLateMinutes > 0 ? '#faad14' : '#3f8600' }}
					/>
				</Col>
				<Col span={6}>
					<Statistic
						title='Erta ketish (daqiqa)'
						value={summary.totalEarlyExitMinutes}
						prefix={<ExclamationCircleOutlined />}
						valueStyle={{ color: summary.totalEarlyExitMinutes > 0 ? '#fa541c' : '#3f8600' }}
					/>
				</Col>
				<Col span={6}>
					<Statistic
						title='Jami ishlagan (soat)'
						value={summary.factHours}
						prefix={<ClockCircleOutlined />}
					/>
				</Col>
				<Col span={6}>
					<Statistic
						title="Reja bo'yicha (soat)"
						value={summary.planHours}
						prefix={<ClockCircleOutlined />}
					/>
				</Col>
			</Row>

			<Divider />

			<WorkerDetailTable dailyReports={dailyReports} />
		</Card>
	)
}
