{"name": "backend", "version": "0.116.36", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "seed:user": "yarn ts-node --project ./tsconfig.dev.json ./seed/user.ts", "seed:all": "yarn ts-node --project ./tsconfig.dev.json ./seed/seedAll.ts", "seed:grade": "yarn ts-node --project ./tsconfig.dev.json ./seed/grade.ts"}, "dependencies": {"@bull-board/api": "^6.7.4", "@bull-board/express": "^6.7.4", "@bull-board/nestjs": "^6.7.4", "@keyv/redis": "^4.4.0", "@nestjs/bullmq": "^11.0.2", "@nestjs/cache-manager": "^3.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^4.0.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^3.0.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "*", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^5.0.1", "@prisma/client": "^6.4.1", "@turf/turf": "^7.2.0", "bcrypt": "^5.1.1", "bullmq": "^5.40.2", "cache-manager": "^6.4.0", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cluster": "^0.7.7", "cookie-parser": "^1.4.7", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dayjs-plugin-utc": "^0.1.2", "dotenv": "^16.4.7", "dotenv-cli": "^8.0.0", "exceljs": "^4.4.0", "git-filter-repo": "^0.0.30", "grammy": "^1.36.3", "ms": "^2.1.3", "node-xlsx": "^0.24.0", "ocpp-rpc": "^2.2.0", "prisma": "^6.5.0", "readline": "^1.3.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/ms": "^2.1.0", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "axios": "^1.9.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e", "buildInfo": {"buildDate": "2025-07-14 19:53:18", "buildAuthor": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}