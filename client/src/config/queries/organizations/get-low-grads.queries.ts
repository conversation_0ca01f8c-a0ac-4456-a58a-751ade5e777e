import { axiosPrivate } from '@/config/api'
import { organizationEndpoints } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'

export interface TOrganization {
	id: string
	name: string
	status: string
	description?: string
	Children?: TOrganization[]
}

export const useGetLowGradeOrganizations = (search: string, orgId?: string) => {
	return useQuery({
		enabled: !!orgId,
		queryKey: [organizationEndpoints.lowGradeOrganizations(orgId!, search)],
		queryFn: async () => {
			const response = await axiosPrivate.get<TOrganization[]>(
				organizationEndpoints.lowGradeOrganizations(orgId!, search)
			)
			return response.data
		}
	})
}
