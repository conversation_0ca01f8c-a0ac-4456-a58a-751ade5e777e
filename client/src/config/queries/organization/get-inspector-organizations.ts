import { axiosPrivate } from '@/config/api'
import { organizationEndpoints } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'

interface BaseQueryParams {
	page?: number
	limit?: number
	search?: string
}

export const useGetInspectorOrganizations = (organizationId: string, params?: BaseQueryParams) => {
	return useQuery({
		queryKey: ['inspector-organizations', organizationId, params],
		queryFn: async () => {
			const response = await axiosPrivate.get(
				organizationEndpoints.inspector(organizationId),
				{
					params: {
						page: params?.page || 1,
						limit: params?.limit || 10,
						...(params?.search && { search: params.search })
					}
				}
			)
			return response.data
		},
		enabled: !!organizationId,
		staleTime: 5 * 60 * 1000, // 5 minutes
		refetchOnWindowFocus: false
	})
}
