import { attendanceUserEndpoints, commonEndpoints } from '@/config/api/endpoints'
import { ApiResponse } from '../types'
import { axiosPrivate, axiosPublic } from '@/config/api'
import { useMutation, useQuery } from '@tanstack/react-query'
import { STATUS } from '@/shared/types/status.type'
import { TOrganization } from '../organizations/get-all.queries'
import { useSearchParams } from 'react-router-dom'
import { downloadBlob } from '@/shared/utils/downloadBlob'

export interface ReferenceRegion {
	id: string
	key: string
	name: string
}

export const useGetReferenceRegions = () => {
	return useQuery({
		queryKey: [commonEndpoints.referenceRegion],
		queryFn: async () =>
			(await axiosPrivate.get<ReferenceRegion[]>(commonEndpoints.referenceRegion)).data
	})
}

export const useGetReferenceRegionOne = (id: number) => {
	return useQuery({
		queryKey: [commonEndpoints.referenceRegionOne(id), id],
		queryFn: async () => {
			return (await axiosPrivate.get<ReferenceRegion>(commonEndpoints.referenceRegionOne(id))).data
		}
	})
}

export interface ReferenceDistrict {
	id: string
	key: string
	name: string
}

export const useGetReferenceDistricts = (regionId: number | null | undefined) => {
	return useQuery({
		queryKey: [commonEndpoints.referenceRegionOne(regionId!), regionId],
		queryFn: async () =>
			(await axiosPrivate.get<ReferenceDistrict[]>(commonEndpoints.referenceRegionOne(regionId!)))
				.data,
		enabled: !!regionId
	})
}

export const useGetReferenceDistrictOne = (id: number | null | undefined) => {
	return useQuery({
		queryKey: [commonEndpoints.referenceDistrictOne(id!), id],
		queryFn: async () => {
			return (
				await axiosPrivate.get<ApiResponse<ReferenceDistrict[]>>(
					commonEndpoints.referenceDistrictOne(id!)
				)
			).data
		},
		enabled: !!id
	})
}

export interface ReferenceSection {
	id: string
	key: string
	name: string
}

export const useGetReferenceSections = (districtId: number | null | undefined) => {
	return useQuery({
		queryKey: [commonEndpoints.referenceDistrictOne(districtId!), districtId],
		queryFn: async () =>
			(
				await axiosPrivate.get<ReferenceSection[]>(
					commonEndpoints.referenceDistrictOne(districtId!)
				)
			).data,
		enabled: !!districtId
	})
}

export interface ReferencesTaskType {
	id: string
	key: string
	name: string
}

export const useGetReferencesTaskType = () => {
	return useQuery({
		queryKey: [commonEndpoints.referencesTaskType],
		queryFn: async () =>
			(await axiosPublic.get<ApiResponse<ReferencesTaskType[]>>(commonEndpoints.referencesTaskType))
				.data
	})
}

export interface ReferencesTaskStates {
	id: string
	key: string
	name: string
}

export const useGetReferencesTaskStates = () => {
	return useQuery({
		queryKey: [commonEndpoints.referencesTaskStates],
		queryFn: async () =>
			(
				await axiosPublic.get<ApiResponse<ReferencesTaskStates[]>>(
					commonEndpoints.referencesTaskStates
				)
			).data
	})
}

export interface ReferencesAttendanceTypes {
	id: string
	key: string
	name: string
}

export const useGetReferenceAttendaceTypes = () => {
	return useQuery({
		queryKey: [commonEndpoints.referencesAttendanceTypes],
		queryFn: async () =>
			(
				await axiosPublic.get<ApiResponse<ReferencesAttendanceTypes[]>>(
					commonEndpoints.referencesAttendanceTypes
				)
			).data
	})
}

export interface ReferenceGpsLocationType {
	id: string
	key: string
	name: string
}
export const useGetReferenceGpsLocationType = () => {
	return useQuery({
		queryKey: [commonEndpoints.referenceGpsLocationType],
		queryFn: async () =>
			(
				await axiosPublic.get<ApiResponse<ReferenceGpsLocationType[]>>(
					commonEndpoints.referenceGpsLocationType
				)
			).data
	})
}

export interface ReferencesOrganizationType {
	id: string
	key: string
	name: string
}

export const useGetReferencesOrganizationType = () => {
	return useQuery({
		queryKey: [commonEndpoints.referencesOrganizationType],
		queryFn: async () =>
			(
				await axiosPublic.get<ApiResponse<ReferencesOrganizationType[]>>(
					commonEndpoints.referencesOrganizationType
				)
			).data
	})
}

export interface ReferencesOrganizationPosition {
	id: string
	key: string
	name: string
}

export const useGetReferenceOrganizationPosition = () => {
	const [search] = useSearchParams()

	const grade = search.get('grade')

	const keys = [commonEndpoints.referencesOrganizationPosition, grade].filter(Boolean)

	return useQuery({
		queryKey: keys,
		queryFn: async () =>
			(
				await axiosPrivate.get<ReferencesOrganizationPosition[]>(
					commonEndpoints.referencesOrganizationPosition,
					{
						params: {
							grade
						}
					}
				)
			).data
	})
}

export interface ReferencesRecipientsAnswerType {
	id: string
	key: string
	name: string
}

export const useGetReferencesRecipientsAnswerType = () => {
	return useQuery({
		queryKey: [commonEndpoints.referencesRecipientsAnswerType],
		queryFn: async () =>
			(
				await axiosPublic.get<ApiResponse<ReferencesRecipientsAnswerType[]>>(
					commonEndpoints.referencesRecipientsAnswerType
				)
			).data
	})
}

export interface ReferencesRecipientsAnswerStates {
	id: string
	key: string
	name: string
}

export const useGetReferencesRecipientsAnswerStates = () => {
	return useQuery({
		queryKey: [commonEndpoints.referencesRecipientsAnswerStates],
		queryFn: async () =>
			(
				await axiosPublic.get<ApiResponse<ReferencesRecipientsAnswerStates[]>>(
					commonEndpoints.referencesRecipientsAnswerStates
				)
			).data
	})
}

export interface Grade {
	id: string
	level: number
	status: STATUS
	name: string
	description?: string
	organization: TOrganization[]
}

export const useGetGrade = () => {
	return useQuery({
		queryKey: [commonEndpoints.referencesGrade],
		queryFn: async () => (await axiosPrivate.get<Grade[]>(commonEndpoints.referencesGrade)).data
	})
}

export const useGetPositionAttendanceReportExcel = () => {
	const [searchParams] = useSearchParams()

	const from = searchParams.get('startDate')
	const to = searchParams.get('endDate')
	const positionId = searchParams.get('positionId') || ''

	return useMutation({
		mutationFn: async () => {
			return await axiosPrivate.get(attendanceUserEndpoints.attendancePositionExel(positionId), {
				params: { startDate: from, endDate: to },
				responseType: 'blob'
			})
		},
		onSuccess: data => {
			console.log(data)
			downloadBlob(data.data, data.headers['content-disposition'] || 'report.xlsx')
		}
	})
}
