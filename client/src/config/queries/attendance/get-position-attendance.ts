import { axiosPrivate } from '@/config/api'
import { attendanceUserEndpoints } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { AttendancePositionResponse } from './get-all-attendance-position'

export const useGetPositionAttendance = (selectedPositionId?: string) => {
	// Use provided positionId only - no date parameters
	const positionId = selectedPositionId

	// For add-attendance we don't need pagination
	const page = '1'
	const limit = '100' // Use a larger limit to get all workers

	return useQuery({
		queryKey: ['position-attendance', positionId],
		queryFn: async () => {
			return (
				await axiosPrivate.get<ApiResponse<AttendancePositionResponse>>(
					attendanceUserEndpoints.attendanceByPosition(positionId || ''),
					{
						params: {
							page,
							limit
						}
					}
				)
			).data
		},
		select: data => {
			// Return the data structure that matches what the table expects
			if (!data?.data) return null

			return {
				position: data.data.position,
				reportPeriod: data.data.reportPeriod,
				workers: data.data.workers,
				meta: data.meta
			}
		},
		enabled: !!positionId,
		staleTime: 0
	})
}
