import { Button } from 'antd'
import { Clock } from 'lucide-react'
import { useWorkingScheduleManagementStore } from './utils/working-schedule-managament-store'
import { useParams } from 'react-router-dom'

interface Props {
	currentId?: string
}

export const WorkingScheduleManagment = ({ currentId }: Props) => {
	const { onOpen } = useWorkingScheduleManagementStore()
	const { id } = useParams()
	return (
		<>
			<Button onClick={() => onOpen(currentId ?? id)}>
				<Clock />
			</Button>
		</>
	)
}
