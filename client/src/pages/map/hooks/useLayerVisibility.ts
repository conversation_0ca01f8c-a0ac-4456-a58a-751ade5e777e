import { useState, useMemo } from 'react'
import { FeatureCollection } from 'geojson'

/**
 * Хук для управления видимостью слоев на карте
 */
export const useLayerVisibility = (areaTypes: string[], geoJsonData: FeatureCollection | null) => {
	const [visibleLayers, setVisibleLayers] = useState<Set<string>>(new Set())

	// Переключение видимости слоя
	const toggleLayerVisibility = (type: string) => {
		const newVisibleLayers = new Set(visibleLayers)
		if (newVisibleLayers.has(type)) {
			newVisibleLayers.delete(type)
		} else {
			newVisibleLayers.add(type)
		}
		setVisibleLayers(newVisibleLayers)
	}

	// Показать все слои
	const showAllLayers = () => {
		setVisibleLayers(new Set(areaTypes))
	}

	// Скрыть все слои
	const hideAllLayers = () => {
		setVisibleLayers(new Set())
	}

	// Фильтрация данных по видимым слоям
	const filteredGeoJsonData = useMemo((): FeatureCollection | null => {
		if (!geoJsonData || visibleLayers.size === 0) return geoJsonData

		return {
			type: 'FeatureCollection',
			features: geoJsonData.features.filter(feature => visibleLayers.has(feature.properties?.type))
		}
	}, [geoJsonData, visibleLayers])

	return {
		visibleLayers,
		toggleLayerVisibility,
		showAllLayers,
		hideAllLayers,
		filteredGeoJsonData
	}
}
