import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export interface ContextData {
  organizationId: string;
  context: 'PERSONAL' | 'WORKSPACE';
}

export const Context = createParamDecorator<keyof ContextData>((data, req: ExecutionContext) => {
  const request = req.switchToHttp().getRequest();
  const organizationId = request['organizationId'];
  const context = request['context'];

  const $data = {
    organizationId,
    context,
  };

  if (data) {
    return $data[data];
  }

  return {
    organizationId,
    context,
  };
});
