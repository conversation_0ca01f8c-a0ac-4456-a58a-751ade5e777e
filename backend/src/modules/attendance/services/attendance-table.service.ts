import { Injectable } from '@nestjs/common';
import { PrismaService, QueryUtil } from 'src/common';
import { formatDate, formatTime, getTime } from 'src/helpers/formatDate';
import { FindAttendanceDto } from '../dto/create-attendance.dto';
import { AttendanceHelperService } from './attendance-helper.service';

@Injectable()
export class AttendanceTableService {
  constructor(
    private prismaService: PrismaService,
    private queryUtil: QueryUtil,
    private helperService: AttendanceHelperService,
  ) {}

  async getAttendanceTable(id: string, query: FindAttendanceDto) {
    /* Fetch data */
    const querySearch = this.queryUtil.createDateRangeQuery('date', query.startDate, query.endDate);
    const users = await this.prismaService.user.findMany({
      where: {
        Organization: { some: { id: query.organizationId || id } },
        AttendanceReport: query.endDate || query.startDate ? { some: querySearch } : undefined,
        status: 'ACTIVE',
      },
      select: {
        id: true,
        fullName: true,
        username: true,
        phone: true,
        position: { select: { name: true } },
        role: true,
        AttendanceReport: {
          select: { date: true, enter: true, exit: true, minutes: true, id: true },
        },
        MainOrganization: {
          select: {
            name: true,
          },
        },
      },
    });

    /* Add dates to table */

    const dates = [];

    users.forEach((user) => {
      user.AttendanceReport.forEach((report) => {
        const date = formatDate(report.date);
        if (!dates.includes(date)) {
          dates.push(date);
        }
      });
    });

    /* Add headings */

    const headerRow1 = ["To'liq Ism", 'Tashkilot', 'Lavozim'];
    dates.forEach((date) => {
      headerRow1.push(date, '', '');
    });

    const headerRow2 = ['', '', ''];
    dates.forEach(() => {
      headerRow2.push('Ish vaqti', 'Kelgan', 'Ketgan');
    });

    /* Add data */

    const data = [
      headerRow1,
      headerRow2,
      ...users.map((user) => {
        const reports = dates
          .map((date) => {
            const report = user.AttendanceReport.find((el) => formatDate(el.date) === date);
            if (report) {
              const minutes = getTime(report.minutes);
              const enter = formatTime(report.enter);
              const exit = formatTime(report.exit);

              return [minutes, enter, exit];
            } else {
              return ['', '', ''];
            }
          })
          .flat();
        return [user.fullName, user.MainOrganization?.name, user.position?.name, ...reports];
      }),
    ];

    /* Merge rows and cols */

    const merges = [];

    merges.push(
      { s: { c: 0, r: 0 }, e: { c: 0, r: 1 } },
      { s: { c: 1, r: 0 }, e: { c: 1, r: 1 } },
      { s: { c: 2, r: 0 }, e: { c: 2, r: 1 } },
    );

    let col = 3;
    dates.forEach(() => {
      merges.push({
        s: { c: col, r: 0 },
        e: { c: col + 2, r: 0 },
      });
      col += 3;
    });

    const sheetOptions = { '!merges': merges };

    return { sheetOptions, data };
  }
}
