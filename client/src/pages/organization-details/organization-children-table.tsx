import { DeleteOutlined, DisconnectOutlined } from '@ant-design/icons'
import { <PERSON><PERSON>, Modal, Popconfirm, Space, Table } from 'antd'
import { Link, useSearchParams, useParams } from 'react-router-dom'
// import { useOrganizationsModalStore } from './utils/organizations-modal-store'
import { OrganizationModal } from './ui/organization-modal'
import { usePermissions } from '@/shared/guards/usePermissions'
import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { LoadingScreen } from '@/shared/ui/suspense'
import { Eye } from 'lucide-react'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'
import { useGetOneOrganization } from '@/config/queries/organizations/getOne'
import { TOrganization } from '@/config/queries/organizations/get-all.queries'
import { useMemo, useState } from 'react'
import { Search } from '@/shared/components/search/search'
import { useDeActiveOrganization } from '@/config/queries/organizations/update.queries'
import { useRemoveUnderControlOrganization } from '@/config/queries/organizations/remove-under-control.queries'

type TOrganizationChildrenTable = {
	type: string
}

export const OrganizationChildrenTable = ({ type = 'child' }: TOrganizationChildrenTable) => {
	const { hasPermission } = usePermissions()
	// const { onOpen } = useOrganizationsModalStore()
	const { mutateAsync: deactivateOrganization, isPending: isDeleting } = useDeActiveOrganization()
	const { mutateAsync: removeUnderControlOrganization, isPending } =
		useRemoveUnderControlOrganization()

	const { data: user, isLoading } = useAuthMe()
	const { data: organization } = useGetOneOrganization()
	const [currentPage, setCurrentPage] = useState(1)
	const [pageSize, setPageSize] = useState(10)

	const [searchParams, setSearchParams] = useSearchParams()
	const searchQuery = searchParams.get('search')
	const { id: orgId } = useParams()

	const dataSource = useMemo(() => {
		let data = type === 'child' ? organization?.Children : organization?.UnderControl

		if (searchQuery && data) {
			data = data.filter(
				item =>
					item.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
					item.address?.toLowerCase().includes(searchQuery.toLowerCase()) ||
					item.grade?.name?.toLowerCase().includes(searchQuery.toLowerCase())
			)
		}

		return data
	}, [organization, type, searchQuery])

	const page = searchParams.get('page') || 1
	const limit = searchParams.get('limit') || 10

	if (isLoading || !user?.role) return <LoadingScreen />

	const handleDelete = async (organizationId: string) => {
		if (type === 'child') {
			try {
				await deactivateOrganization({
					id: organizationId,
					data: {} // Empty data object as required by the API
				})
			} catch (error) {
				console.error('Error deleting organization:', error)
			}
		} else {
			Modal.confirm({
				title: 'Haqiqatan ham bu Tashkilotni ajratmoqchimisiz?',
				okText: 'Ajratish',
				cancelText: 'Bekor qilish',
				onOk: () =>
					removeUnderControlOrganization({
						orgId: orgId!,
						data: { organizations: [organizationId] }
					})
			})
		}
	}

	return (
		<Table
			title={() => (
				<>
					<OrganizationModal type={type} />
					<Search />
				</>
			)}
			size='small'
			scroll={{ x: 'max-content' }}
			dataSource={dataSource}
			pagination={{
				defaultPageSize: 10,
				defaultCurrent: 1,
				pageSizeOptions: [5, 10, 20, 50, 100],
				showSizeChanger: true,
				pageSize: Number(limit),
				current: Number(page),
				total: dataSource?.length,
				onChange(page, pageSize) {
					setCurrentPage(page)
					setPageSize(pageSize)
					searchParams.set('page', page.toString())
					searchParams.set('limit', pageSize.toString())
					setSearchParams(searchParams.toString())
				}
			}}
			rowKey={rec => rec.id || ''}
			columns={[
				{
					key: '#',
					title: '#',
					render: (_: any, _rec: any, index: number) =>
						formatTableIndex(currentPage, pageSize, index)
				},
				{
					key: 'name',
					title: 'Nomi',
					dataIndex: 'name',
					render: (_: any, rec: { id: any; name: string }) => (
						<Link to={`/workspace/organizations/${rec.id}`}>{rec.name}</Link>
					)
				},

				{
					key: 'address',
					title: 'Manzili',
					dataIndex: 'address'
				},

				{
					key: 'grade',
					title: 'Lavozimi',
					dataIndex: ['grade', 'name']
				},
				{
					key: 'user-count',
					title: 'Foydalanuvchilar soni',
					dataIndex: 'workerCount'
				},
				{
					key: 'actions',
					title: 'Amallar',
					render: (record: TOrganization | undefined) => (
						<Space>
							<Link to={`/workspace/organizations/${record?.id}`}>
								<Eye />
							</Link>
							{user && hasPermission(user.role, ['update:*', 'delete:*']) && (
								<>
									{/* <Button
										type='primary'
										icon={<EditOutlined />}
										onClick={() => onOpen(record)}
									/> */}

									{type === 'child' ? (
										<Popconfirm
											title="Tashkilotni o'chirish"
											description="Haqiqatan ham bu tashkilotni o'chirmoqchimisiz?"
											onConfirm={() => handleDelete(record?.id || '')}
											okText='Ha'
											cancelText="Yo'q"
											okButtonProps={{ loading: isDeleting }}>
											<Button
												type='primary'
												danger
												icon={<DeleteOutlined />}
												loading={isDeleting}
											/>
										</Popconfirm>
									) : (
										<Button
											danger
											loading={isPending}
											icon={<DisconnectOutlined />}
											onClick={() => handleDelete(record?.id || '')}
										/>
									)}
								</>
							)}
						</Space>
					)
				}
			]}
		/>
	)
}
