import { axiosPrivate } from '@/config/api'
import { infractionEndpoints } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'
import { ApiResponse } from '../types'

export interface TInfractionReason {
	id: string
	fileId?: string
	name?: string
	description?: string
	infractionId: string
	createdAt: string
	updatedAt: string
	file?: {
		id: string
		path: string
	}
}

export interface TInfraction {
	id: string
	userId: string
	name?: string
	description?: string
	data?: any
	infractionDate: string
	createdAt: string
	updatedAt: string
	user: {
		id: string
		fullName: string
		phone: string
		avatar?: {
			path: string
		}
		organization?: {
			id: string
			name: string
		}
		Organization?: Array<{
			id: string
			name: string
		}>
		MainOrganization?: {
			name: string
		} | null
		position?: {
			id: string
			name: string
		}
	}
	InfractionReason: TInfractionReason[]
}

export type TInfractionResponse = ApiResponse<TInfraction[]>


export const useGetAllInfractions = () => {
	return useQuery({
		queryKey: ['infractions', 'all'],
		queryFn: async (): Promise<TInfractionResponse> => {
			const response = await axiosPrivate.get<TInfractionResponse>(infractionEndpoints.all)
			return response.data
		}
	})
}

export const useGetMyInfractions = () => {
	return useQuery({
		queryKey: ['infractions', 'mine'],
		queryFn: async (): Promise<TInfractionResponse> => {
			const response = await axiosPrivate.get<TInfractionResponse>(infractionEndpoints.me)
			return response.data
		}
	})
}

export const useGetInfractions = (
	workspace: 'personal' | 'organization',
	params?: { page?: number; limit?: number }
) => {
	return useQuery({
		queryKey: ['infractions', workspace, params?.page, params?.limit],
		queryFn: async (): Promise<TInfractionResponse | TInfraction[]> => {
			const endpoint = workspace === 'personal' ? infractionEndpoints.me : infractionEndpoints.all
			if (workspace === 'personal') {
				const response = await axiosPrivate.get<TInfraction[]>(endpoint)
				return response.data
			} else {
				const response = await axiosPrivate.get<TInfractionResponse>(endpoint, {
					params: {
						page: params?.page || 1,
						limit: params?.limit || 20
					}
				})
				return response.data
			}
		}
	})
}


export const useGetInfractionById = (id: string) => {
	return useQuery({
		queryKey: ['infraction', id],
		queryFn: async (): Promise<TInfraction> => {
			const response = await axiosPrivate.get<TInfraction>(infractionEndpoints.one(id))
			return response.data
		},
		enabled: !!id
	})
}

export const useGetInfractionByIdWorkspace = (id: string) => {
	return useQuery({
		queryKey: ['infraction', 'workspace', id],
		queryFn: async (): Promise<TInfraction> => {
			const response = await axiosPrivate.get<TInfraction>(infractionEndpoints.oneWorkspace(id))
			return response.data
		},
		enabled: !!id
	})
}
