describe('logout spec', () => {
	beforeEach(() => {
		cy.login()
		cy.visit('/')
	})

	it('should not logout', () => {
		cy.getById('logout-button').click()
		cy.get('.ant-popconfirm-buttons > .ant-btn-default').click()
		cy.isPathname('/')
	})

	it('should logout', () => {
		cy.getById('logout-button').click()
		cy.get('.ant-popconfirm-buttons > .ant-btn-primary').click()
		cy.isPathname('/auth/login')
	})
})
