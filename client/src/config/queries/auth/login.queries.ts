import { axiosPublic } from '@/config/api/api'
import { authEndpoints } from '@/config/api/endpoints'
import { useMutation } from '@tanstack/react-query'
import { notification } from 'antd'
import { AxiosError, AxiosResponse } from 'axios'
import { jwtDecode, JwtPayload } from 'jwt-decode'
import { compareAsc } from 'date-fns'
import { useNavigate } from 'react-router-dom'
import { TApiErrorResponse } from '@/shared/types/api-error.type'

export type TLoginRequest = {
	username: string
	password: string
}
export type TLoginResponse = {
	accessToken: string
}

export type TRefreshResponse = {
	accessToken: string
}

export const useLogin = () => {
	const navigate = useNavigate()

	return useMutation({
		mutationKey: [authEndpoints.login],
		mutationFn: async (data: TLoginRequest) => {
			return await axiosPublic.post(authEndpoints.login, data)
		},
		onSuccess: async (response: AxiosResponse<TLoginResponse>) => {
			localStorage.setItem('accessToken', response.data.accessToken)
			notification.success({
				message: "Muvaffaqiyatli ro'yxatdan o'tdingiz"
			})
			navigate('/')
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			const messages = response?.data.message

			if (Array.isArray(messages)) {
				messages.forEach(message => notification.error({ message }))
			} else {
				notification.error({
					message: messages
				})
			}
		}
	})
}

export const useRefresh = () => {
	return useMutation({
		mutationKey: [authEndpoints.refresh],
		mutationFn: async () => {
			return await axiosPublic.post(authEndpoints.refresh)
		},
		onSuccess: async (response: AxiosResponse<TRefreshResponse>) => {
			localStorage.setItem('accessToken', response.data.accessToken)
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			const messages = response?.data.message

			if (Array.isArray(messages)) {
				messages.forEach(message => notification.error({ message }))
			} else {
				notification.error({
					message: messages
				})
			}
		}
	})
}

export function isLoggedIn(): boolean {
	const token = localStorage.getItem('accessToken')
	if (!token) return false
	const decoded = jwtDecode<JwtPayload>(token)
	return compareAsc(Number(decoded.exp) * 1000, new Date()) === 1
}
