import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import { join } from 'path';

const prisma = new PrismaClient();

interface RegionData {
  id: number;
  title: string;
}

async function main() {
  const regions: RegionData[] = JSON.parse(
    fs.readFileSync(join(process.cwd(), 'json', 'region.json'), {
      encoding: 'utf-8',
    }),
  );

  for (const region of regions) {
    await prisma.region.upsert({
      where: { id: region.id.toString() },
      update: {},
      create: {
        id: region.id.toString(),
        name: region.title,
      },
    });
    console.log(`Region ${region.title} qo'shildi`);
  }

  console.log('All regions have been seeded successfully.');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
