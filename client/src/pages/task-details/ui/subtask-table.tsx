import { TTask } from '@/config/queries/task/getOne'
import { useTaskStates } from '@/shared/hooks/useTaskStates'
import { LoadingScreen } from '@/shared/ui/suspense'
import { DeleteOutlined } from '@ant-design/icons'
import { Badge, Button, Modal, Segmented, Table } from 'antd'
import { useNavigate } from 'react-router-dom'

type TSubTaskTable = {
	data: TTask[]
}

export const SubTaskTable = ({ data }: TSubTaskTable) => {
	const navigate = useNavigate()
	const { status, segmentOptions, handleSegmentChange, filteredData, isLoading } = useTaskStates()

	const handleRowClick = (record: { id: string | number }) => {
		navigate(`/workspace/tasks/${record.id}`)
	}

	if (isLoading) return <LoadingScreen />

	const columns = [
		{
			title: 'ID',
			dataIndex: 'id',
			key: 'id',
			width: 100,
			render: (_: unknown, __: unknown, index: number) => ++index
		},
		{
			title: 'Sarlavhasi',
			dataIndex: 'name',
			key: 'name',
			width: 150
		},
		{
			title: 'Izoh',
			dataIndex: 'description',
			key: 'description',
			width: 200
		},
		{
			title: 'Tashkilot',
			dataIndex: ['CreatedByOrganization', 'name'],
			key: 'createdByOrg',
			width: 150
		},
		{
			title: 'Turi',
			dataIndex: ['TaskType', 'name'],
			key: 'taskType',
			width: 150
		},
		{
			title: 'Holati',
			dataIndex: ['TaskState', 'name'],
			key: 'state',
			width: 150,
			render: (state: string) =>
				state ? (
					<Badge
						style={{
							backgroundColor: '#6366F1',
							borderRadius: '4px',
							display: 'inline-block',
							textAlign: 'center',
							fontSize: '16px'
						}}
						count={state}
					/>
				) : null
		},
		{
			title: 'Amallar',
			key: 'actions',
			width: 100,
			render: (record: { id: string | number }) => (
				<Button
					danger
					icon={<DeleteOutlined />}
					onClick={e => {
						e.stopPropagation()
						if (record && record.id) {
							Modal.confirm({
								title: 'Are you sure you want to delete this task?',
								okText: 'Delete',
								cancelText: 'Cancel',
								onOk: () => {
									console.log('Delete task:', record.id)
								}
							})
						}
					}}
				/>
			)
		}
	]

	return (
		<div className='p-3 rounded-lg shadow-sm dark:bg-primary'>
			<div className='mb-4'>
				<Segmented
					//@ts-ignore
					options={segmentOptions}
					value={status}
					onChange={handleSegmentChange}
				/>
			</div>
			<Table
				columns={columns}
				dataSource={filteredData(data)}
				rowKey='id'
				onRow={record => ({
					//@ts-ignore
					onClick: () => handleRowClick(record as { id: string | number }),
					style: { cursor: 'pointer' }
				})}
			/>
		</div>
	)
}
