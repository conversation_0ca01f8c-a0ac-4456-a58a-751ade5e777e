import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../prisma';
import { Prisma } from '@prisma/client';

// Интерфейс для работы с пользовательскими данными
export interface WithUser {
  getUserData(user: any): Promise<any>;
}

@Injectable()
export class UserRepository {
  baseSelect: Prisma.UserSelect = {
    id: true,
    phone: true,
    fullName: true,
    username: true,
    avatar: true,
    role: true,
    MainOrganization: {
      select: {
        id: true,
        name: true,
      },
    },
    Organization: {
      select: {
        id: true,
        name: true,
      },
    },
    ResponsibleFor: {
      select: {
        id: true,
        name: true,
        grade: true,
        region: true,
        district: true,
        section: true,
      },
    },
  };

  constructor(private prismaService: PrismaService) {}

  async getUserData(userId: string, select?: Prisma.UserSelect) {
    const combinedSelect = { ...this.baseSelect, ...select, role: true };
    const user = await this.prismaService.user.findUnique({
      where: {
        id: userId,
      },
      select: combinedSelect,
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    return user;
  }
}
