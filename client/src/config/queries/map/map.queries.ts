import { axiosPrivate } from '@/config/api'
import { useQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'
import { useMemo } from 'react'

export interface Response {
	id: string
	area: Area[]
	region_id: string
	district_id: any
	section_id: any
	type: string
	lat: string
	lng: string
}

export interface Area {
	type: string
	geometry: Geometry
}

export interface Geometry {
	type: string
	coordinates: number[][][][]
}

export const useGetCoordinates = () => {
	const [search] = useSearchParams()

	const regionId = search.get('regionId')
	const districtId = search.get('districtId')
	const sectionId = search.get('sectionId')

	// Мемоизация правильных ID для оптимизации
	const { correctDistrictId, correctSectionId } = useMemo(() => {
		const correctDistrictId = districtId
			? districtId.length > 3
				? districtId.substring(districtId.length - 3)
				: districtId
			: null

		const correctSectionId = sectionId
			? sectionId.length > 6
				? sectionId.substring(sectionId.length - 6)
				: sectionId
			: null

		return { correctDistrictId, correctSectionId }
	}, [districtId, sectionId])

	return useQuery({
		queryKey: ['coordinates', { regionId, correctDistrictId, correctSectionId }],
		queryFn: async () =>
			(
				await axiosPrivate.get<Response[]>('coords', {
					params: { regionId, districtId: correctDistrictId, sectionId: correctSectionId }
				})
			).data
		// enabled: !!regionId || !!correctDistrictId || !!correctSectionId
	})
}
