import { axiosPrivate } from '@/config/api'
import { fileEndpoints } from '@/config/api/endpoints'
import { useMutation } from '@tanstack/react-query'

type TUploadFile = {
	id: string
	path: string
}

export const useUploadFile = () => {
	return useMutation({
		mutationFn: async (file: File) => {
			const formData = new FormData()
			formData.append('file', file)
			const response = await axiosPrivate.post<TUploadFile[]>(fileEndpoints.upload, formData, {
				headers: {
					'Content-Type': 'multipart/form-data'
				}
			})
			return response.data
		}
	})
}
