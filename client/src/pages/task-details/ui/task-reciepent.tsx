import { Divide<PERSON>, <PERSON>, Button } from 'antd'
import RecipientUpdate from '../ui/recipient-update'
import { RecipientsList } from './task-recipientsl-ist'
import { useLocation } from 'react-router-dom'
import ControllerRecipientUpdate from '../../task-controller-details/ui/controller-recipient-update'

interface TaskRecipientsProps {
	filteredRecipients: any[]
	recipientCounts: { total: number; read: number; unread: number }
	readStatusFilter: 'all' | 'read' | 'unread'
	setReadStatusFilter: (filter: 'all' | 'read' | 'unread') => void
	showAllRecipients: boolean
	setShowAllRecipients: (show: boolean) => void
	isOwnTask: boolean
	navigate: (path: string) => void
	currentRecipient: any
	task: any
}

export const TaskRecipients = ({
	filteredRecipients,
	recipientCounts,
	readStatusFilter,
	setReadStatusFilter,
	showAllRecipients,
	setShowAllRecipients,
	isOwnTask,
	navigate,
	currentRecipient,
	task
}: TaskRecipientsProps) => {
	const location = useLocation()
	const isControllerContext = location.pathname.includes('/task-controller/')
	return (
		<>
			<Divider />
			<div className='px-3'>
				<div className='flex items-center justify-between mb-3'>
					<div className='flex items-center gap-4'>
						<h1 className='font-bold text-lg '>
							Bajaruvchilar ro'yhati ({recipientCounts.total}):
						</h1>

						<Select
							value={readStatusFilter}
							onChange={setReadStatusFilter}
							className='w-40 '
							options={[
								{ label: `Barchasi (${recipientCounts.total})`, value: 'all' },
								{ label: `Jarayonda (${recipientCounts.read})`, value: 'read' },
								{ label: `Ko'rilmagan (${recipientCounts.unread})`, value: 'unread' }
							]}
						/>
					</div>

					{isOwnTask && (
						<div>
							{isControllerContext ? (
								<ControllerRecipientUpdate task={task} />
							) : (
								<RecipientUpdate />
							)}
						</div>
					)}
				</div>

				<RecipientsList
					filteredRecipients={filteredRecipients}
					readStatusFilter={readStatusFilter}
					isOwnTask={isOwnTask}
					navigate={navigate}
					currentRecipient={currentRecipient}
					task={task}
				/>

				{recipientCounts.total > 4 && (
					<div className='flex justify-center mt-4'>
						<Button
							type='link'
							onClick={() => setShowAllRecipients(!showAllRecipients)}
							className='text-blue-500 hover:text-blue-7'>
							{showAllRecipients
								? `Kamroq ko'rsatish`
								: `Yana ${recipientCounts.total - 4} ta ko'rsatish`}
						</Button>
					</div>
				)}
			</div>
		</>
	)
}
