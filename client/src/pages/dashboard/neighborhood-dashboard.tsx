import { AlertCircle, Home, User, UserCog, UserRound, Users } from 'lucide-react'
import PositionCard from './neighborhood-ui/position-card'
import NeighborhoodStatCard from './neighborhood-ui/stats-cards'

import AttendanceStatsDashboard from './neighborhood-ui/attendance-data'
import { GPSChart } from './neighborhood-ui/gps-chart'
import { DashboardOrganizationTable } from './neighborhood-ui/dashboard-organization-table'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { useGetDashboardSectionStats } from '@/config/queries/dashboard/section-dashboard'
import { useEffect } from 'react'

const NeighborhoodDashboard = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const navigate = useNavigate()
	const { data: sectionStats } = useGetDashboardSectionStats()
	console.log('sectionStats', sectionStats)

	const organization = localStorage.getItem('organization')
	const grade = searchParams.get('grade')

	useEffect(() => {
		const organizationData = organization ? JSON.parse(organization) : null
		const orgId = organizationData?.id
		if (orgId && !searchParams.get('orgId')) {
			setSearchParams(prev => {
				prev.set('orgId', orgId)
				return prev
			})
		}
	}, [organization, searchParams, setSearchParams])

	const urlOrgId = searchParams.get('orgId')
	const organizationData = organization ? JSON.parse(organization) : null
	const orgId = urlOrgId || organizationData?.id

	const defaultPositions = [
		{ title: 'Mahalla raislari', value: '0' },
		{ title: 'Hokim yordamchilari', value: '0' },
		{ title: 'Xotin qizlar faoli', value: '0' },
		{ title: 'Yoshlar yetakchisi', value: '0' },
		{ title: 'Profilaktika inspektori', value: '0' },
		{ title: 'Ijtimoiy hodim', value: '0' },
		{ title: 'Soliq inspektori', value: '0' }
	]

	const positionData =
		sectionStats?.data?.positionsCount && sectionStats.data.positionsCount.length > 0
			? sectionStats.data.positionsCount.map(position => ({
					title: position.name || '',
					value: position.userCount?.toString() || '0',
					id: position.id
				}))
			: defaultPositions

	const handleEmployeeClick = () => {
		navigate(`/workspace/dashboard-details/employee?orgId=${orgId}`)
	}

	const handleSectionClick = () => {
		navigate(`/workspace/dashboard-details/section?orgId=${orgId}`)
	}

	const handleVacationClick = () => {
		navigate(`/workspace/dashboard-details/vacation?orgId=${orgId}`)
	}

	const handlePatientsClick = () => {
		navigate(`/workspace/dashboard-details/patients?orgId=${orgId}`)
	}

	const handleVacancyClick = () => {
		navigate(`/workspace/dashboard-details/vacancy?orgId=${orgId}`)
	}
	const handleShortVacationClick = () => {
		navigate(`/workspace/dashboard-details/short-vacation?orgId=${orgId}`)
	}
	if (!organization) return null

	return (
		<>
			{JSON.parse(grade ?? JSON.parse(organization)?.grade?.level ?? '') !== 20 ? (
				<DashboardOrganizationTable />
			) : (
				<div>
					<div className='mx-auto p-4'>
						<div className='grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3 mb-8'>
							<NeighborhoodStatCard
								title='Mahallalar soni'
								value={sectionStats?.data?.sectionCount?.toString() || '0'}
								icon={<Home className='h-8 w-8 text-blue-500' />}
								onClick={handleSectionClick}
							/>
							<NeighborhoodStatCard
								title='Hodimlar soni'
								value={sectionStats?.data?.employeeCount?.toString() || '0'}
								icon={<User className='h-8 w-8 text-green-500' />}
								onClick={handleEmployeeClick}
							/>
							<NeighborhoodStatCard
								title="Ta'tilda"
								value={sectionStats?.data?.vacationCount?.toString() || '0'}
								icon={<UserRound className='h-8 w-8 text-orange-500' />}
								onClick={handleVacationClick}
							/>
							<NeighborhoodStatCard
								title="Qisqa ta'til"
								value={sectionStats?.data?.shortVacationCount?.toString() || '0'}
								icon={<Users className='h-8 w-8 text-purple-500' />}
								onClick={handleShortVacationClick}
							/>
							<NeighborhoodStatCard
								title='Bemorlar'
								value={sectionStats?.data?.patientCount?.toString() || '0'}
								icon={<AlertCircle className='h-8 w-8 text-red-500' />}
								onClick={handlePatientsClick}
							/>
							<NeighborhoodStatCard
								title='Vakant'
								value={sectionStats?.data?.vacancyCount?.toString() || '0'}
								icon={<UserCog className='h-8 w-8 text-gray-500' />}
								onClick={handleVacancyClick}
							/>
						</div>
						<PositionCard positions={positionData} />
					</div>
					<AttendanceStatsDashboard />
					<GPSChart />
				</div>
			)}
		</>
	)
}
export default NeighborhoodDashboard
