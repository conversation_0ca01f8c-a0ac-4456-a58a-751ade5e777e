import { IsInt, IsOptional, IsS<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';

export class CreateOrganizationDto {
  @IsString()
  @MinLength(3)
  name: string;

  @IsString()
  @MinLength(3)
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  address?: string;

  @IsString()
  @IsOptional()
  phone?: string;

  @IsInt()
  @Min(1)
  @Max(4)
  @IsOptional()
  sector?: number;

  @IsInt()
  @Min(1)
  @Max(4)
  @IsOptional()
  sectorResponsible?: number;

  @IsString()
  typeId: string;

  @IsString()
  regionId: string;

  @IsString()
  @IsOptional()
  @IsOptional()
  districtId?: string;

  @IsOptional()
  @IsString()
  @IsOptional()
  sectionId?: string;

  @IsString()
  gradeId: string;
}
