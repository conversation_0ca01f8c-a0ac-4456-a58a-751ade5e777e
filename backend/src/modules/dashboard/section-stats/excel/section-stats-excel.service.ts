import { Injectable, NotFoundException } from '@nestjs/common';
import { SectionStatsService } from '../section-stats.service';
import { FindAllDto } from '../dto/find-all.dto';
import { PaginationUtil, PrismaService, QueryUtil } from '../../../../common';
import { GPSLocationType, IVacation, Prisma } from '@prisma/client';
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import * as timezone from 'dayjs/plugin/timezone';
import { TimezoneHelper } from 'src/helpers/timezone.helper';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.locale('uz');
dayjs.tz.setDefault('Asia/Tashkent');

@Injectable()
export class SectionStatsExcelService {
  constructor(
    private readonly sectionStatsService: SectionStatsService,
    private readonly prismaService: PrismaService,
    private readonly queryUtil: QueryUtil,
    private readonly paginationUtil: PaginationUtil,
  ) {}

  // worked
  async getSectionExcel(organizationId: string, params: FindAllDto) {
    const childrenIds = await this.getChildrenIds(organizationId);
    const searchQuery = this.queryUtil.createSearchQuery(params.search, ['name', 'description', 'phone']);

    return this.prismaService.organization.findMany({
      where: {
        status: 'ACTIVE',
        grade: { level: 30 },
        id: { in: childrenIds },
        ...searchQuery,
      },
      select: {
        id: true,
        name: true,
        address: true,
        description: true,
        createdAt: true,
        phone: true,
        region: { select: { name: true } },
        district: { select: { name: true } },
        section: { select: { name: true } },
        type: { select: { name: true } },
      },
    });
  }

  // worked
  async getPositionsExcel(organizationId: string, params: FindAllDto, positionId?: string) {
    const GRADE = 30;
    const organization = await this.prismaService.organization.findUnique({
      where: { id: organizationId, status: 'ACTIVE' },
      select: {
        Children: {
          where: {
            status: 'ACTIVE',
            grade: {
              level: GRADE,
            },
          },
          select: {
            Worker: {
              where: {
                positionId,
                status: 'ACTIVE',
              },
              select: {
                id: true,
                fullName: true,
                phone: true,
                Organization: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
                position: {
                  select: {
                    id: true,
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
    });

    if (!organization) {
      throw new NotFoundException('Organization not found');
    }

    return organization.Children.flatMap((child) => child.Worker);
  }

  // worked
  async getAllLateAttendance(organizationId: string, params: FindAllDto, positionId?: string) {
    const childrenIds = await this.getChildrenIds(organizationId);
    const startDate = dayjs().utc().startOf('day').toDate();
    const endDate = dayjs().utc().endOf('day').toDate();

    let positionFilter: string[] = [];

    if (positionId) {
      const isValid = await this.prismaService.organizationTypePosition.findFirst({
        where: {
          id: positionId,
          typeId: { in: await this.getOrganizationTypeIds(childrenIds) },
        },
        select: { id: true },
      });

      if (!isValid) return [];
      positionFilter = [positionId];
    } else {
      const positions = await this.getOrganizationPositions(childrenIds);
      positionFilter = positions.map((p) => p.id);
    }

    const search = params.search?.trim();
    const searchFilter = search
      ? {
          OR: [
            { username: { contains: search, mode: Prisma.QueryMode.insensitive } },
            { fullName: { contains: search, mode: Prisma.QueryMode.insensitive } },
            { phone: { contains: search, mode: Prisma.QueryMode.insensitive } },
          ],
        }
      : {};

    const users = await this.prismaService.user.findMany({
      where: {
        status: 'ACTIVE',
        positionId: { in: positionFilter },
        Organization: {
          some: { id: { in: childrenIds } },
        },
        AttendanceReport: {
          some: {
            date: {
              gte: startDate,
              lte: endDate,
            },
            isLate: true,
          },
        },
        ...searchFilter,
      },
      orderBy: { id: 'asc' },
      select: {
        id: true,
        fullName: true,
        username: true,
        phone: true,
        role: true,
        position: {
          select: {
            id: true,
            name: true,
            description: true,
            type: true,
          },
        },
        AttendanceReport: {
          where: {
            date: {
              gte: startDate,
              lte: endDate,
            },
            isLate: true,
          },
          select: {
            id: true,
            date: true,
            enter: true,
            exit: true,
            minutes: true,
            description: true,
          },
        },
        Organization: {
          select: {
            id: true,
            name: true,
            phone: true,
            address: true,
            description: true,
            grade: true,
          },
        },
      },
    });

    return users;
  }

  // worked
  async getAllNoAttendance(organizationId: string, params: FindAllDto, positionId?: string) {
    const childrenIds = await this.getChildrenIds(organizationId);
    const { start, end } = TimezoneHelper.getUTCRangeForLocalDay();

    const userFilter: any = {
      status: 'ACTIVE',
      AND: [],
    };

    if (positionId) {
      userFilter.AND.push({ positionId });
    }

    userFilter.AND.push({
      OR: [
        { mainOrganizationId: { in: childrenIds } },
        {
          Organization: {
            some: {
              id: { in: childrenIds },
            },
          },
        },
      ],
    });

    userFilter.AND.push({
      AttendanceReport: {
        none: {
          date: {
            gte: start,
            lte: end,
          },
        },
      },
    });

    if (params.search) {
      const search = params.search;
      userFilter.AND.push({
        OR: [
          { username: { contains: search, mode: 'insensitive' } },
          { fullName: { contains: search, mode: 'insensitive' } },
          { phone: { contains: search, mode: 'insensitive' } },
          {
            id: {
              equals: isNaN(+search) ? undefined : +search,
            },
          },
        ],
      });
    }

    const users = await this.prismaService.user.findMany({
      where: userFilter,
      orderBy: { id: 'asc' },
      select: {
        id: true,
        fullName: true,
        username: true,
        phone: true,
        positionId: true,
        position: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
        AttendanceReport: true,
        Organization: {
          where: {
            id: { in: childrenIds },
          },
          select: {
            id: true,
            name: true,
            phone: true,
            address: true,
            description: true,
            grade: true,
          },
        },
      },
    });

    return users;
  }

  // worked
  async getEmployeeExcel(organizationId: string, params: FindAllDto) {
    const childrenIds = await this.getChildrenIds(organizationId);
    const searchQuery = this.queryUtil.createSearchQuery(params.search, ['username', 'fullName', 'phone']);

    const employees = await this.prismaService.user.findMany({
      where: {
        status: 'ACTIVE',
        Organization: { some: { id: { in: childrenIds } } },
        ...searchQuery,
      },
      select: {
        id: true,
        fullName: true,
        phone: true,
        position: {
          select: {
            name: true,
          },
        },
        Organization: {
          select: {
            name: true,
          },
        },
      },
    });

    return employees;
  }

  // worked
  async getVacationListForExcel(organizationId: string, type: IVacation) {
    const childrenIds = await this.getChildrenIds(organizationId);

    return this.prismaService.vacation.findMany({
      where: {
        status: 'ACTIVE',
        type,
        OR: [
          { user: { mainOrganizationId: { in: childrenIds } } },
          {
            user: {
              Organization: {
                some: { id: { in: childrenIds } },
              },
            },
          },
        ],
      },
      select: {
        id: true,
        begin: true,
        end: true,
        state: true,
        type: true,
        description: true,
        createdAt: true,
        user: {
          select: {
            id: true,
            fullName: true,
            avatar: true,
            phone: true,
            position: true,
            Organization: { select: { id: true, name: true } },
          },
        },
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }

  // worked
  async getVacancyAll(organizationId: string, params: FindAllDto) {
    const childrenIds = await this.getChildrenIds(organizationId);

    return this.prismaService.vacancy.findMany({
      where: {
        status: 'ACTIVE',
        Organization: { id: { in: childrenIds } },
      },
      select: {
        id: true,
        createdAt: true,
        Organization: {
          select: {
            id: true,
            name: true,
            address: true,
            description: true,
            createdAt: true,
            phone: true,
            region: true,
            district: true,
            section: true,
            type: true,
          },
        },
        OrganizationTypePosition: {
          select: {
            id: true,
            name: true,
            description: true,
            createdAt: true,
          },
        },
      },
    });
  }

  // worked
  async getVacancyExcel(organizationId: string, params: FindAllDto) {
    const childrenIds = await this.getChildrenIds(organizationId);

    return this.prismaService.vacancy.findMany({
      where: {
        status: 'ACTIVE',
        Organization: { id: { in: childrenIds } },
      },
      select: {
        id: true,
        createdAt: true,
        Organization: {
          select: {
            name: true,
            address: true,
            description: true,
            region: { select: { name: true } },
            district: { select: { name: true } },
            section: { select: { name: true } },
          },
        },
        OrganizationTypePosition: {
          select: {
            name: true,
          },
        },
      },
    });
  }

  async getAreaExcel(organizationId: string, params: FindAllDto, type: GPSLocationType, positionId?: string) {
    const childrenIds = await this.getChildrenIds(organizationId);
    const organizationTypePositions = await this.getOrganizationPositions(childrenIds, positionId);
    const searchQuery = this.queryUtil.createSearchQuery(params.search, ['username', 'fullName', 'phone']);

    const allUsers = await this.prismaService.user.findMany({
      where: {
        status: 'ACTIVE',
        positionId: { in: organizationTypePositions.map(({ id }) => id) },
        Organization: {
          some: { id: { in: childrenIds } },
        },
        ...searchQuery,
      },
      select: {
        id: true,
        fullName: true,
        username: true,
        phone: true,
        position: { select: { name: true } },
        role: true,
        Organization: { select: { name: true } },
        GPSLocationReport: {
          orderBy: { createdAt: 'desc' },
          take: 1,
          select: { createdAt: true, type: true },
        },
      },
    });

    return allUsers.filter((user) => user.GPSLocationReport.length > 0 && user.GPSLocationReport[0].type === type);
  }

  private async getChildrenIds(organizationId: string) {
    const organization = await this.prismaService.organization.findUnique({
      where: {
        id: organizationId,
        status: 'ACTIVE',
        grade: { level: 20 },
      },
      select: {
        Children: {
          where: { grade: { level: 30 } },
          select: { id: true, region: true, district: true, section: true, Employee: true },
        },
      },
    });
    if (!organization) throw new NotFoundException('organization not found');
    return organization.Children.map(({ id }) => id);
  }

  private async getOrganizationPositions(childrenIds: string[], positionId?: string) {
    return this.prismaService.organizationTypePosition.findMany({
      where: {
        status: 'ACTIVE',
        ...(positionId ? { id: positionId } : {}),
      },
      select: {
        name: true,
        id: true,
        _count: {
          select: { User: true },
        },
        User: {
          where: {
            status: 'ACTIVE',
            Organization: {
              some: { id: { in: childrenIds } },
            },
          },
          select: {
            id: true,
            AttendanceReport: true,
            Attendance: true,
            UserWorkingSchedule: {
              include: { days: true },
            },
            GPSLocationReport: {
              orderBy: { createdAt: 'desc' },
              take: 1,
            },
          },
        },
      },
    });
  }

  private async getOrganizationTypeIds(orgIds: string[]): Promise<string[]> {
    const organizations = await this.prismaService.organization.findMany({
      where: {
        id: { in: orgIds },
      },
      select: { typeId: true },
    });

    return [...new Set(organizations.map((org) => org.typeId))];
  }
}
