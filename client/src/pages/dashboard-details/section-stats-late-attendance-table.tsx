import { useGetDashboardDetailsLateAttendance } from '@/config/queries/dashboard-details/get-dashboard-details'
import { useGetLateAttendanceExcel } from '@/config/queries/dashboard-details/get-dashboard-details-excel'
import { formatTableIndex } from '@/shared/utils/formatPaginationTableIndex'

import { Button, Table, Tag, Tooltip } from 'antd'
import { DownloadOutlined } from '@ant-design/icons'
import { useSearchParams } from 'react-router-dom'
import dayjs from 'dayjs'
import { TimezoneHelper, DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'

dayjs.tz.setDefault(DEFAULT_TIMEZONE)

const SectionStatsLateAttendanceTable = () => {
	const [searchParams, setSearchParams] = useSearchParams()
	const positionId = searchParams.get('positionId')
	const organizationId = searchParams.get('orgId')

	const { data: section, isLoading } = useGetDashboardDetailsLateAttendance(positionId || '')
	const lateAttendanceExcel = useGetLateAttendanceExcel()

	const currentPage = parseInt(searchParams.get('page') || '1')
	const pageSize = parseInt(searchParams.get('limit') || '10')

	const handleDownloadExcel = () => {
		lateAttendanceExcel.mutate({
			orgId: organizationId || undefined,
			posId: positionId || undefined
		})
	}

	const columns = [
		{
			key: '#',
			title: '#',
			render: (_: any, _rec: any, index: number) => formatTableIndex(currentPage, pageSize, index)
		},
		{
			title: 'FIO',
			dataIndex: 'fullName',
			key: 'fullName'
		},
		{
			title: 'Telefon raqami',
			dataIndex: 'phone',
			key: 'phone'
		},
		{
			title: 'Lavozimi',
			dataIndex: ['position', 'name'],
			key: 'positionName'
		},
		{
			title: 'Tashkilot',
			dataIndex: ['Organization'],
			key: 'organizationName',
			render: (orgs: any[]) => orgs?.map(org => org.name).join(', ')
		},
		{
			title: 'Kelgan vaqti',
			dataIndex: ['AttendanceReport', '0', 'enter'],
			render: (enter: string) => {
				if (!enter) return '-'
				const time = TimezoneHelper.formatTime(enter)
				return (
					<Tooltip title='Kechikkan'>
						<Tag color='orange'>{time}</Tag>
					</Tooltip>
				)
			}
		}
	]

	const handleTableChange = (pagination: any) => {
		const newPage = pagination.current
		const newPageSize = pagination.pageSize

		setSearchParams(prev => {
			const newParams = new URLSearchParams(prev)
			newParams.set('page', newPage.toString())
			newParams.set('limit', newPageSize.toString())
			return newParams
		})
	}

	const getTitle = () => {
		if (positionId) {
			return 'Kechikkanlar ro`yhati (Lavozim bo`yicha)'
		}
		return 'Kechikkanlar ro`yhati (Umumiy)'
	}

	return (
		<div className='p-4'>
			<div className='flex justify-between items-center mb-4'>
				<h1 className='text-2xl font-bold'>{getTitle()}</h1>
				<Button
					type='primary'
					icon={<DownloadOutlined />}
					onClick={handleDownloadExcel}
					loading={lateAttendanceExcel.isPending}>
					Excel yuklab olish
				</Button>
			</div>
			<Table
				columns={columns}
				dataSource={section?.data || []}
				rowKey='id'
				loading={isLoading}
				pagination={{
					current: currentPage,
					pageSize: pageSize,
					total: section?.meta?.total || 0,
					showTotal: (total, range) => `${range[0]}-${range[1]} tadan ${total}  ta`,
					pageSizeOptions: ['10', '20', '50', '100']
				}}
				onChange={handleTableChange}
			/>
		</div>
	)
}

export default SectionStatsLateAttendanceTable
