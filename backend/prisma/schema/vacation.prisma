model Vacation {
    id          String    @id @default(cuid())
    user        User      @relation(fields: [userId], references: [id])
    userId      String
    description String?
    type        IVacation
    state       <PERSON><PERSON><PERSON>   @default(false)
    begin       DateTime
    end         DateTime

    status    Status   @default(ACTIVE)
    createdAt DateTime @default(now())
    updatedAt DateTime @updatedAt

    @@index([userId])
}

enum IVacation {
    PATIONS
    VACATION
    SHORT_VACATION
}
