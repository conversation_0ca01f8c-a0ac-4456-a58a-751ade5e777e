import { Typography } from 'antd'
import { MapPinOff } from 'lucide-react'
import LocationItem from './LocationItem'
import { LocationItem as LocationItemType } from '../types/location.types'

const { Text } = Typography

interface LocationListProps {
	filteredData: LocationItemType[]
	formatDate: (dateString: string) => string
	onUserClick?: (user: LocationItemType) => void
}

export default function LocationList({ filteredData, formatDate, onUserClick }: LocationListProps) {
	if (filteredData.length === 0) {
		return (
			<div className='text-center py-8'>
				<MapPinOff
					className='mx-auto text-gray-400 dark:text-gray-500 mb-4'
					size={48}
				/>
				<Text className='text-gray-500 dark:text-gray-400'>
					Filtr shartlariga mos ma'lumotlar topilmadi
				</Text>
			</div>
		)
	}

	return (
		<div className='space-y-3'>
			{filteredData.map((location, i) => (
				<LocationItem
					key={`${location.id}${i}`}
					location={location}
					formatDate={formatDate}
					onUserClick={onUserClick}
				/>
			))}
		</div>
	)
}
