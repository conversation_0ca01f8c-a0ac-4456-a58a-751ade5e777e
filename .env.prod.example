# Postgres
POSTGRES_HOST=mnazorat-postgres
POSTGRES_PORT=5432
POSTGRES_USER=prod_user
POSTGRES_PASSWORD=secure_password
POSTGRES_DB=mnazorat_db

# Redis
GLOBAL_REDIS_HOST=mnazorat-redis
GLOBAL_REDIS_PORT=6379
GLOBAL_REDIS_PASSWORD=secure_password

# Backend
BACKEND_PORT=8000
BACKEND_NODE_ENV=production
BACKEND_TELEGRAM_BOT_TOKEN=**********************************************
BACKEND_TELEGRAM_BOT_WEBHOOK_URL=https://api.new.mnazorat.uz/api/v1/telegram/webhook

# Frontend
API_URL=https://your-domain.com/api