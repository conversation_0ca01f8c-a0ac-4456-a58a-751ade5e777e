import { axiosPrivate } from '@/config/api'
import { authEndpoints } from '@/config/api/endpoints'
import { ROLE } from '@/shared/types/role.type'
import { useQuery } from '@tanstack/react-query'

type TAuthVerifyResponse = {
	id: string
	role: ROLE
	user?: {
		grade: string
		level: number
	}
	username: string
	fullName?: string
	avatar?: { path: string }

	phone: string
	ResponsibleFor: {
		id: string
		name: string
		grade: {
			level: number
		}
		region: {
			id: string
			name: string
		}
		district?: {
			id: string
			name: string
		}
		section?: {
			id: string
			name: string
		}
	}[]
	Organization: {
		id: string
		name: string
	}
	MainOrganization: {
		id: string
		name: string
	}
}

export const useAuthMe = () => {
	return useQuery({
		queryKey: [authEndpoints.me],
		queryFn: async () => {
			return (await axiosPrivate.get<TAuthVerifyResponse>(authEndpoints.me)).data
		},
		refetchInterval: 300000,
		staleTime: 300000,
		gcTime: 300000
	})
}
