import { TaskTypes } from '@/config/queries/task-types/create.queries'
import { create } from 'zustand'

interface TaskTypesModalStore {
	open: boolean
	data: TaskTypes | null
	onOpen: (data?: TaskTypes) => void
	onClose: () => void
}

export const useTaskTypesModalStore = create<TaskTypesModalStore>(set => ({
	open: false,
	data: null,
	onOpen: data => set({ open: true, data: data || null }),
	onClose: () => set({ open: false, data: null })
}))
