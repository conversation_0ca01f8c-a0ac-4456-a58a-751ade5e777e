import { useGetAllUsers, useSearchUsers } from '@/config/queries/users/get-all.queries'
import { TValidationFormProps } from '@/shared/types/validation-form.type'
import { Form, Select, Spin } from 'antd'
import { useFaceIDAddStore } from '../utils/face-id-add-modal-store'
import { ROLE, useGetUsersByOrg } from '@/config/queries/organizations/get-users.queries'
import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import debounce from 'lodash/debounce'
import { useSearchParams } from 'react-router-dom'

const { Item } = Form

interface User {
	id: string
	fullName: string
	username?: string
	phone?: string
	[key: string]: any
}

type TProps = TValidationFormProps & {
	userIds: string[]
	setUserIds: (userIds: string[]) => void
}

export const FaceIdAddForm = ({ validation, userIds, setUserIds }: TProps) => {
	const { id: organizationId, who } = useFaceIDAddStore()

	const [searchQuery, setSearchQuery] = useState('')
	const [page, setPage] = useState(1)
	const [searchParams, setSearchParams] = useSearchParams()
	const [data, setData] = useState<User[]>([])
	const [hasMore, setHasMore] = useState(true)
	const [isRefreshing, setIsRefreshing] = useState(false)

	const prevOrgIdRef = useRef<string | null>(null)

	const storedOrgId = localStorage.getItem('organizationId')
	const parsedOrgId = useMemo(() => {
		try {
			return storedOrgId ? JSON.parse(storedOrgId) : ''
		} catch (error) {
			console.error('Ошибка при парсинге ID организации:', error)
			return ''
		}
	}, [storedOrgId])

	const effectiveOrgId = organizationId || parsedOrgId || ''

	useEffect(() => {
		if (who === 'responsibles') return

		const currentParams: Record<string, string> = {}
		searchParams.forEach((value, key) => {
			currentParams[key] = value
		})

		const currentPage = parseInt(currentParams.page || '1')
		const trimmedQuery = searchQuery.trim()

		if (page !== currentPage || currentParams.search !== trimmedQuery) {
			const newParams: Record<string, string> = {
				...currentParams,
				page: page.toString()
			}

			if (trimmedQuery) {
				newParams.search = trimmedQuery
			} else if (newParams.search) {
				delete newParams.search
			}

			setSearchParams(newParams, { replace: true })
		}
	}, [page, searchQuery, searchParams, setSearchParams, who])

	const usersData = useGetAllUsers()

	const trimmedSearchQuery = searchQuery.trim()
	const searchData = useSearchUsers(trimmedSearchQuery)

	const users = trimmedSearchQuery && searchData.data ? searchData.data : usersData.data

	const isLoadingUsers = trimmedSearchQuery ? searchData.isLoading : usersData.isLoading

	const isFetchingUsers = trimmedSearchQuery ? searchData.isFetching : usersData.isFetching

	const usersError = trimmedSearchQuery ? searchData.error : usersData.error

	const workersData = useGetUsersByOrg(effectiveOrgId, ROLE.worker)
	const workers = workersData.data
	const isLoadingWorkers = workersData.isLoading
	const workersError = workersData.error

	const filterNullValues = useCallback(<T,>(items: (T | null | undefined)[]): T[] => {
		return items.filter((item): item is T => item !== null && item !== undefined)
	}, [])

	const refreshList = useCallback(() => {
		if (isRefreshing) return

		setIsRefreshing(true)

		try {
			if (who === 'responsibles') {
				const workerData = workers?.pages.flatMap(page => page.data) || []
				setData(filterNullValues<User>(workerData))
			} else if (!trimmedSearchQuery) {
				const userData = usersData.data?.pages.flatMap((page: any) => page.data) || []
				setData(filterNullValues<User>(userData))
			}

			if (who !== 'responsibles' && users && 'meta' in users && users.meta?.totalPages) {
				setHasMore(page < users.meta.totalPages)
			}
		} catch (error) {
			console.error('Ошибка при обновлении списка:', error)
		} finally {
			setIsRefreshing(false)
		}
	}, [
		who,
		workers,
		usersData.data,
		users,
		page,
		trimmedSearchQuery,
		filterNullValues,
		isRefreshing
	])

	const handleSearch = useCallback(
		(value: string) => {
			setSearchQuery(value)
			setPage(1)
			if ((value.trim() && !trimmedSearchQuery) || (!value.trim() && trimmedSearchQuery)) {
				setData([])
			}
		},
		[trimmedSearchQuery]
	)

	const handleClear = useCallback(() => {
		if (trimmedSearchQuery) {
			setSearchQuery('')
			setPage(1)
			setData([])
			setIsRefreshing(true)

			usersData
				.refetch()
				.catch(error => console.error('Ошибка при обновлении данных:', error))
				.finally(() => {
					setTimeout(() => {
						refreshList()
						setIsRefreshing(false)
					}, 100)
				})
		}
	}, [trimmedSearchQuery, usersData, refreshList])

	const debouncedSearch = useMemo(() => debounce(handleSearch, 300), [handleSearch])

	// eslint-disable-next-line react-hooks/exhaustive-deps
	const handlePopupScroll = useCallback(
		debounce((e: React.UIEvent<HTMLDivElement>) => {
			const target = e.target as HTMLDivElement
			const isNearBottom = target.scrollTop + target.offsetHeight >= target.scrollHeight - 10

			if (
				!isLoadingUsers &&
				!isFetchingUsers &&
				!isRefreshing &&
				hasMore &&
				isNearBottom &&
				who !== 'responsibles'
			) {
				setPage(prevPage => prevPage + 1)
			}
		}, 100),
		[isLoadingUsers, isFetchingUsers, hasMore, who, isRefreshing]
	)

	const handleSelect = useCallback(
		(id: string) => {
			setUserIds([...userIds, id])
		},
		[userIds, setUserIds]
	)

	const handleDeselect = useCallback(
		(id: string) => {
			setUserIds(userIds.filter(userId => userId !== id))
		},
		[userIds, setUserIds]
	)

	useEffect(() => {
		if (prevOrgIdRef.current !== effectiveOrgId) {
			prevOrgIdRef.current = effectiveOrgId
			if (who === 'responsibles') {
				setData([])
				setPage(1)
			}
		}

		const workerData = workers?.pages.flatMap(data => data.data) || []
		const userData = users && 'data' in users ? users.data : []

		if (who === 'responsibles') {
			setData(filterNullValues<User>(workerData))
			setHasMore(false)
		} else {
			if (page === 1) {
				setData(filterNullValues<User>(userData))
			} else {
				setData(prevData => [
					...prevData,
					...filterNullValues<User>(userData).filter(
						user => !prevData.some(item => item.id === user.id)
					)
				])
			}

			setHasMore(
				users && 'meta' in users && users.meta?.totalPages ? page < users.meta.totalPages : true
			)
		}
	}, [who, workers, users, page, filterNullValues, effectiveOrgId])

	useEffect(() => {
		if (usersError) {
			console.error('Ошибка при загрузке пользователей:', usersError)
		}

		if (workersError) {
			console.error('Ошибка при загрузке работников:', workersError)
		}
	}, [usersError, workersError])

	const isLoading = isLoadingUsers || isLoadingWorkers || isRefreshing
	const noDataContent = isLoading ? <Spin size='small' /> : "Ma'lumot topilmadi"

	return (
		<div className='face-id-add-form'>
			<Item
				name='users'
				label='Ishchilarni tanlang'
				rules={validation.type}>
				<Select
					mode='multiple'
					allowClear
					loading={isLoading}
					options={data}
					fieldNames={{ label: 'fullName', value: 'id' }}
					onSelect={handleSelect}
					onDeselect={handleDeselect}
					showSearch
					filterOption={false}
					onSearch={debouncedSearch}
					onClear={handleClear}
					placeholder='Qidirish uchun yozing...'
					onPopupScroll={handlePopupScroll}
					notFoundContent={noDataContent}
					listHeight={300}
				/>
			</Item>
		</div>
	)
}
