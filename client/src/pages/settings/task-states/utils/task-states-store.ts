import { TaskStates } from '@/config/queries/task-states/create.queries'
import { create } from 'zustand'

interface TaskStatesModalStore {
	open: boolean
	data: TaskStates | null
	onOpen: (data?: TaskStates) => void
	onClose: () => void
}

export const useTaskStatesModalStore = create<TaskStatesModalStore>(set => ({
	open: false,
	data: null,
	onOpen: data => set({ open: true, data: data || null }),
	onClose: () => set({ open: false, data: null })
}))
