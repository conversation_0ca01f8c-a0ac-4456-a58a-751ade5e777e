import { axiosPrivate } from '@/config/api'
import { organizationPositionTypeEndpoints } from '@/config/api/endpoints'
import { useInfiniteQuery } from '@tanstack/react-query'
import { useSearchParams } from 'react-router-dom'

export const useGetAllOrganizationTypePostionByTypeId = (typeId?: string) => {
	const limit = 10
	const page = 1
	const [searchParams] = useSearchParams()
	const search = searchParams.get('search')

	return useInfiniteQuery({
		enabled: !!typeId,
		queryKey: [
			organizationPositionTypeEndpoints.byTypeId(typeId ?? ''),
			'infinite',
			page,
			limit,
			search,
			typeId
		],
		queryFn: async ({ pageParam }) => {
			return (
				await axiosPrivate.get(
					search
						? organizationPositionTypeEndpoints.searchByTypeId(typeId ?? '', search)
						: organizationPositionTypeEndpoints.byTypeId(typeId ?? ''),
					{
						params: {
							page: pageParam,
							limit
						}
					}
				)
			).data
		},
		initialPageParam: page,
		getNextPageParam: lastPage => {
			if (lastPage?.meta?.page === lastPage?.meta?.totalPages) return undefined
			return lastPage?.meta?.page + 1
		},
		getPreviousPageParam: lastPage => {
			if (lastPage?.meta?.page === 1) return undefined
			return lastPage?.meta?.page - 1
		}
	})
}
