import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { Dropdown, Space, Typography } from 'antd'
import { MenuProps } from 'antd'
import { ChevronDown } from 'lucide-react'
import { useState, useEffect, useMemo } from 'react'
import { Link, useLocation, useNavigate } from 'react-router-dom'

interface Workspace {
	id: string
	name: string
	avatar: string
	members?: number
}

export const WorkspaceSelect = () => {
	const { data: meData } = useAuthMe()
	const [currentWorkspace, setCurrentWorkspace] = useState<Workspace>()
	const navigate = useNavigate()
	const { pathname } = useLocation()

	const personalWorkspace: Workspace = {
		id: 'personal',
		name: 'Shaxsiy',
		avatar: 'Sh'
	}

	const workspaces = useMemo(() => {
		const responsibleFor = meData?.ResponsibleFor || []
		return Array.isArray(responsibleFor)
			? [
					personalWorkspace,
					...responsibleFor.map((org: { id: string; name: string }) => ({
						...org,
						avatar: org.name[0]?.toUpperCase() || 'O',
						members: responsibleFor.length
					}))
				]
			: [personalWorkspace]
	}, [meData?.ResponsibleFor, personalWorkspace])

	useEffect(() => {
		const organizationId = localStorage.getItem('organizationId')
		if (organizationId?.length) {
			const parsedOrganizationId = JSON.parse(organizationId)
			const workspace = workspaces.find(item => item.id === parsedOrganizationId)
			if (workspace) {
				setCurrentWorkspace(workspace)
				if (workspace.id === 'personal' && !pathname.startsWith('/personal')) {
					navigate('/personal')
				} else if (workspace.id !== 'personal' && !pathname.startsWith('/workspace')) {
					navigate('/workspace')
				}
			} else {
				setCurrentWorkspace(workspaces[0])
				if (!pathname.startsWith('/personal')) {
					navigate('/personal')
				}
			}
		} else {
			setCurrentWorkspace(workspaces[0])
			if (!pathname.startsWith('/personal')) {
				navigate('/personal')
			}
		}
	}, [])

	const handleWorkspaceChange = (workspace?: Workspace) => {
		setCurrentWorkspace(workspace)
	}

	const items: MenuProps['items'] = [
		...workspaces.map(workspace => ({
			key: workspace.id,
			label: (
				<Link
					onClickCapture={() => {
						if (workspace.id === 'personal') {
							localStorage.removeItem('organizationId')
						} else {
							if (workspace.id) {
								localStorage.setItem('organizationId', JSON.stringify(workspace.id))
								localStorage.setItem('organization', JSON.stringify(workspace))
							} else {
								localStorage.removeItem('organizationId')
							}
						}
					}}
					to={workspace.id === 'personal' ? '/personal' : `/workspace`}
					className='flex items-center py-2 px-1 rounded-md transition-colors'
					onClick={() => handleWorkspaceChange(workspace)}>
					<div className='bg-teal-500 flex items-center justify-center rounded-md p-1 px-3'>
						{workspace.avatar}
					</div>

					<div className='ml-2 flex-1'>
						<Typography.Text className='font-medium '>{workspace.name}</Typography.Text>
						<Typography.Text className='text-gray-900 text-xs block'>
							{workspace.id === 'personal' ? 'Shaxsiy' : "Ma'sul"}
						</Typography.Text>
					</div>
					{workspace.id === currentWorkspace?.id && (
						<div className='w-1.5 h-1.5 rounded-full bg-blue-500 mr-1'></div>
					)}
				</Link>
			)
		}))
	]

	return (
		<Dropdown
			menu={{ items }}
			trigger={['click']}
			dropdownRender={menu => <div className=' rounded-lg p-2 min-w-[340px]'>{menu}</div>}>
			<div
				onClick={e => e.preventDefault()}
				className='cursor-pointer w-full rounded-md p-1 '>
				<Space className='flex items-center justify-start w-full'>
					<div className='bg-teal-500 flex items-center justify-center py-1 rounded-md px-3'>
						{currentWorkspace?.avatar}
					</div>
					<Typography.Text className='truncate select-none  flex-1 font-medium '>
						{currentWorkspace?.name}
					</Typography.Text>
					<ChevronDown
						size={16}
						className='text-gray-400'
					/>
				</Space>
			</div>
		</Dropdown>
	)
}
