import { Injectable } from '@nestjs/common';
import * as ExcelJS from 'exceljs';
import { join } from 'path';
import * as fs from 'fs';
import { format } from 'date-fns';

@Injectable()
export class TaskExcelService {
  async exportTasksToExcel(tasks: any[]): Promise<string> {
    const workbook = new ExcelJS.Workbook();

    workbook.creator = 'Monitoring Nazorat';
    workbook.lastModifiedBy = 'Monitoring Nazorat';
    workbook.created = new Date();
    workbook.modified = new Date();

    const worksheet = workbook.addWorksheet('Задачи', {
      properties: {
        tabColor: { argb: '2F75B5' },
        defaultRowHeight: 18,
      },
    });

    worksheet.columns = [
      { header: 'Название задачи', key: 'name', width: 30, style: { alignment: { wrapText: true } } },
      { header: 'Описание', key: 'description', width: 40, style: { alignment: { wrapText: true } } },
      { header: 'Дата создания', key: 'createdAt', width: 20, style: { alignment: { horizontal: 'center' } } },
      { header: 'Срок выполнения', key: 'dueDate', width: 15, style: { alignment: { horizontal: 'center' } } },
      { header: 'Статус', key: 'status', width: 15, style: { alignment: { horizontal: 'center' } } },
      { header: 'Выполнена', key: 'isCompleted', width: 15, style: { alignment: { horizontal: 'center' } } },
      { header: 'Дата выполнения', key: 'completedAt', width: 15, style: { alignment: { horizontal: 'center' } } },
      { header: 'Организация создателя', key: 'createdByOrg', width: 25, style: { alignment: { wrapText: true } } },
      { header: 'Регион', key: 'region', width: 20, style: { alignment: { wrapText: true } } },
      { header: 'Район', key: 'district', width: 20, style: { alignment: { wrapText: true } } },
      { header: 'Создатель', key: 'createdBy', width: 25, style: { alignment: { horizontal: 'left' } } },
      { header: 'Телефон создателя', key: 'createdByPhone', width: 15, style: { alignment: { horizontal: 'left' } } },
      { header: 'Должность создателя', key: 'createdByPosition', width: 20, style: { alignment: { wrapText: true } } },
      {
        header: 'Всего получателей',
        key: 'totalRecipients',
        width: 17,
        style: { alignment: { horizontal: 'center' } },
      },
      { header: 'Выполнено', key: 'completedCount', width: 15, style: { alignment: { horizontal: 'center' } } },
      { header: 'Не выполнено', key: 'notCompletedCount', width: 15, style: { alignment: { horizontal: 'center' } } },
    ];

    const headerRow = worksheet.getRow(1);
    headerRow.font = { bold: true, size: 12, color: { argb: 'FFFFFFFF' } };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '2F75B5' },
    };
    headerRow.alignment = { horizontal: 'center', vertical: 'middle' };
    headerRow.height = 24;

    worksheet.autoFilter = {
      from: { row: 1, column: 1 },
      to: { row: 1, column: worksheet.columns.length },
    };

    const borderStyle = {
      top: { style: 'thin' as const },
      left: { style: 'thin' as const },
      bottom: { style: 'thin' as const },
      right: { style: 'thin' as const },
    };

    let rowIndex = 1;
    for (const task of tasks) {
      rowIndex++;
      const taskState = task.TaskState ? task.TaskState.name : 'Не указано';

      const totalRecipients = task.Recipients?.length || 0;
      const completedCount = task.Recipients?.filter((r) => r.isCompleted)?.length || 0;
      const notCompletedCount = totalRecipients - completedCount;

      const row = {
        name: task.name,
        description: task.description || '',
        createdAt: task.createdAt ? format(new Date(task.createdAt), 'dd.MM.yyyy HH:mm') : '',
        dueDate: task.dueDate ? format(new Date(task.dueDate), 'dd.MM.yyyy') : '',
        status: taskState,
        isCompleted: task.isCompleted ? 'Да' : 'Нет',
        completedAt: task.completedAt ? format(new Date(task.completedAt), 'dd.MM.yyyy HH:mm') : '',
        createdByOrg: task.CreatedByOrganization ? task.CreatedByOrganization.name : '',
        region: task.CreatedByOrganization ? task.CreatedByOrganization.region?.name : '',
        district: task.CreatedByOrganization ? task.CreatedByOrganization.district?.name : '',
        createdBy: task.CreatedBy ? task.CreatedBy.fullName : '',
        createdByPhone: task.CreatedBy ? task.CreatedBy.phone : '',
        createdByPosition: task.CreatedBy ? task.CreatedBy.position.name : '',
        totalRecipients,
        completedCount,
        notCompletedCount,
      };
      const currentRow = worksheet.addRow(row);

      currentRow.eachCell((cell) => {
        cell.border = borderStyle;
      });

      if (rowIndex % 2 === 0) {
        currentRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'F2F2F2' },
        };
      }

      const isCompletedCell = currentRow.getCell(6);
      if (row.isCompleted === 'Да') {
        isCompletedCell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'C6EFCE' },
        };
        isCompletedCell.font = { color: { argb: '006100' } };
      } else {
        isCompletedCell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: 'FFC7CE' },
        };
        isCompletedCell.font = { color: { argb: '9C0006' } };
      }

      if (task.Recipients && task.Recipients.length > 0) {
        const taskName = task.name?.substring(0, 20) || ''; // Ограничиваем длину названия задачи
        const creationDate = task.createdAt ? format(new Date(task.createdAt), 'dd.MM.yyyy') : '';

        const baseSheetName = `${taskName}-${creationDate}-ID${task.id || rowIndex}`;

        const isExists = workbook.getWorksheet(baseSheetName);

        const sheetName = isExists ? `${baseSheetName}-${Date.now().toString().slice(-5)}` : baseSheetName;

        const recipientsSheet = workbook.addWorksheet(sheetName, {
          properties: {
            tabColor: { argb: '548235' },
            defaultRowHeight: 18,
          },
        });

        const headerRowIndex = 1;

        recipientsSheet.columns = [
          {
            header: 'Организация',
            key: 'orgName',
            width: 30,
            style: { alignment: { wrapText: true } },
            headerCount: 3,
          },
          { header: 'Регион', key: 'region', width: 20, style: { alignment: { wrapText: true } } },
          { header: 'Район', key: 'district', width: 20, style: { alignment: { wrapText: true } } },
          { header: 'Секция', key: 'section', width: 20, style: { alignment: { wrapText: true } } },
          { header: 'Адрес', key: 'address', width: 30, style: { alignment: { wrapText: true } } },
          { header: 'Пользователь', key: 'userName', width: 30, style: { alignment: { horizontal: 'left' } } },
          { header: 'Телефон', key: 'phone', width: 15, style: { alignment: { horizontal: 'left' } } },
          { header: 'Должность', key: 'position', width: 20, style: { alignment: { wrapText: true } } },
          { header: 'Прочитано', key: 'isRead', width: 10, style: { alignment: { horizontal: 'center' } } },
          { header: 'Выполнено', key: 'isCompleted', width: 10, style: { alignment: { horizontal: 'center' } } },
          { header: 'Статус ответа', key: 'answerState', width: 15, style: { alignment: { horizontal: 'center' } } },
          { header: 'Дата ответа', key: 'answerDate', width: 15, style: { alignment: { horizontal: 'center' } } },
        ];

        const recipientHeaderRow = recipientsSheet.getRow(headerRowIndex);
        recipientHeaderRow.font = { bold: true, size: 12, color: { argb: 'FFFFFFFF' } };
        recipientHeaderRow.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: { argb: '548235' },
        };

        recipientHeaderRow.alignment = { horizontal: 'center', vertical: 'middle' };
        recipientHeaderRow.height = 24;

        recipientsSheet.autoFilter = {
          from: { row: headerRowIndex, column: 1 },
          to: { row: headerRowIndex, column: recipientsSheet.columns.length },
        };

        let recipientRowIndex = headerRowIndex;
        for (const recipient of task.Recipients) {
          recipientRowIndex++;
          const recipientRow = {
            orgName: recipient.Organization ? recipient.Organization.name : '',
            region: recipient.Organization ? recipient.Organization.region?.name : '',
            district: recipient.Organization ? recipient.Organization.district?.name : '',
            section: recipient.Organization ? recipient.Organization.section?.name : '',
            address: recipient.Organization ? recipient.Organization.address : '',
            userName: recipient.User ? recipient.User.fullName : '',
            phone: recipient.User ? recipient.User.phone : '',
            position: recipient.User ? recipient.User.position.name : '',
            isRead: recipient.isRead ? 'Да' : 'Нет',
            isCompleted: recipient.isCompleted ? 'Да' : 'Нет',
            answerState: recipient.Answer && recipient.Answer.length > 0 ? recipient.Answer[0].state : '',
            answerDate:
              recipient.Answer && recipient.Answer.length > 0 && recipient.Answer[0].createdAt
                ? format(new Date(recipient.Answer[0].createdAt), 'dd.MM.yyyy HH:mm')
                : '',
          };
          const currentRecipientRow = recipientsSheet.addRow(recipientRow);

          currentRecipientRow.eachCell((cell) => {
            cell.border = borderStyle;
          });

          if (recipientRowIndex % 2 === 0) {
            currentRecipientRow.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'F2F2F2' },
            };
          }

          const isReadCell = currentRecipientRow.getCell(9);
          if (recipientRow.isRead === 'Да') {
            isReadCell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'C6EFCE' },
            };
            isReadCell.font = { color: { argb: '006100' } };
          } else {
            isReadCell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFC7CE' },
            };
            isReadCell.font = { color: { argb: '9C0006' } };
          }

          const isCompletedRecipientCell = currentRecipientRow.getCell(10);
          if (recipientRow.isCompleted === 'Да') {
            isCompletedRecipientCell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'C6EFCE' },
            };
            isCompletedRecipientCell.font = { color: { argb: '006100' } };
          } else {
            isCompletedRecipientCell.fill = {
              type: 'pattern',
              pattern: 'solid',
              fgColor: { argb: 'FFC7CE' },
            };
            isCompletedRecipientCell.font = { color: { argb: '9C0006' } };
          }
        }
      }
    }

    worksheet.views = [{ state: 'frozen', xSplit: 0, ySplit: 1 }];

    const excelDir = join(process.cwd(), 'uploads', 'excel');
    if (!fs.existsSync(excelDir)) {
      fs.mkdirSync(excelDir, { recursive: true });
    }

    const fileName = `tasks_${format(new Date(), 'yyyy-MM-dd_HH-mm-ss')}.xlsx`;
    const filePath = join(excelDir, fileName);
    await workbook.xlsx.writeFile(filePath);

    return filePath;
  }
}
