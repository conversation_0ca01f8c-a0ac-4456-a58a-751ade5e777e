import { Form, Input, Row, TimePicker } from 'antd'

const UserWorkingScheduleDayForm = () => {
	return (
		<>
			<Form.Item
				name='name'
				label='Nomi'
				rules={[
					{ required: true, message: 'Nomini kiriting minimal 3 ta belgidan iborat', min: 2 }
				]}>
				<Input
					placeholder='Nomini kiriting'
					size='middle'
				/>
			</Form.Item>
			<Form.Item
				name='day'
				label='Kuni'
				rules={[
					{
						required: true,
						message: 'Kunini kiriting minimal 1 ta belgidan iborat',
						min: 1,
						max: 7
					}
				]}>
				<Input
					placeholder='Kunni kiriting (1-7)'
					size='middle'
					type='number'
					min={1}
					max={7}
				/>
			</Form.Item>
			<Row
				gutter={16}
				className='flex justify-between gap-x-4'>
				<Form.Item
					className='flex-1'
					name='startTime'
					label='Boshlanish vaqti'
					rules={[{ required: true, message: 'Boshlanish vaqtini kiriting' }]}>
					<TimePicker
						className='w-full'
						placeholder='Boshlanish vaqtini kiriting'
						size='middle'
						format='HH:mm'
					/>
				</Form.Item>
				<Form.Item
					className='flex-1'
					name='endTime'
					label='Tugash vaqti'
					rules={[{ required: true, message: 'Tugash vaqtini kiriting' }]}>
					<TimePicker
						className='w-full'
						placeholder='Tugash vaqtini kiriting'
						size='middle'
						format='HH:mm'
					/>
				</Form.Item>
			</Row>
		</>
	)
}
export default UserWorkingScheduleDayForm
