import { axiosPrivate } from '@/config/api'
import { faceIdEndpoints } from '@/config/api/endpoints'
import { TPaginationWrapper } from '@/shared/types/pagination.type'
import { useInfiniteQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { useSearchParams } from 'react-router-dom'
import { TFAceIdResponse } from './create'

export const useGetAllFaceIdDevices = () => {
	const [searchParams] = useSearchParams()

	const search = searchParams.get('search')
	const onlineStatus = searchParams.get('onlineStatus')
	const limit = 10
	const [page, setPage] = useState(1)

	const query = useInfiniteQuery({
		queryKey: [faceIdEndpoints.all, 'infinite', search, onlineStatus, page, limit],
		queryFn: async ({ pageParam }) => {
			const endpoint = onlineStatus ? faceIdEndpoints.filter(onlineStatus) : faceIdEndpoints.all

			return (
				await axiosPrivate.get<TPaginationWrapper<TFAceIdResponse[]>>(endpoint, {
					params: { page: pageParam, limit, search }
				})
			).data
		},
		initialPageParam: page,
		getNextPageParam: lastPage => {
			if (lastPage?.meta?.page === lastPage?.meta?.totalPages) return undefined
			return lastPage?.meta?.page + 1
		},
		getPreviousPageParam: lastPage => {
			if (lastPage?.meta?.page === 1) return undefined
			return lastPage?.meta?.page - 1
		}
	})

	return {
		...query,
		setPage
	}
}
