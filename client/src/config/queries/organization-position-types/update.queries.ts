import { axiosPrivate } from '@/config/api'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ApiResponse } from '../types'
import { notification } from 'antd'
import { OrganizationPositionTypes } from './create.queries'
import { organizationPositionTypeEndpoints } from '@/config/api/endpoints'

export const useUpdateOrganizationPositionType = () => {
	const queryClient = useQueryClient()
	return useMutation({
		mutationFn: async ({ id, ...data }: { id: string }) =>
			(
				await axiosPrivate.put<ApiResponse<OrganizationPositionTypes>>(
					organizationPositionTypeEndpoints.one(id),
					data
				)
			).data,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: [organizationPositionTypeEndpoints.all] })
			notification.success({
				message: 'Muvaffaqiyatli yangilandi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: 'Yang<PERSON>shda xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
