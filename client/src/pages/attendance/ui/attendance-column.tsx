import { useMemo } from 'react'
import { Badge, Progress, Tag, Tooltip, Typography } from 'antd'
import dayjs from 'dayjs'
import 'dayjs/locale/uz'

import { DailyReport } from '@/config/queries/attendance/getAll'
import { ClockCircleOutlined } from '@ant-design/icons'
import { convertMinutesToHours } from '@/shared/utils/convertMinutesToHours'
import { TimezoneHelper, DEFAULT_TIMEZONE } from '@/shared/utils/timezone.helper'

// Dayjs setup bir marta bajariladi
dayjs.tz.setDefault(DEFAULT_TIMEZONE)

const { Text } = Typography

export const useAttendanceColumns = (
	days: string[],
	setSelectedWorkerId: (id: string | null) => void
) => {
	return useMemo(() => {
		return [
			{
				title: '#',
				render: (_: any, __: any, index: number) => ++index,
				width: 50,
				fixed: 'left',
				className: 'bg-gray-100 dark:bg-gray-700 dark:text-gray-200'
			},
			{
				title: 'F.I.O',
				dataIndex: 'fullName',
				fixed: 'left',
				width: 180,
				className: 'bg-gray-50 dark:bg-gray-700 dark:text-gray-200 font-medium',
				render: (text: string, record: any) => (
					<div
						className='cursor-pointer hover:text-blue-500'
						onClick={() => setSelectedWorkerId(record.id)}>
						{text}
					</div>
				)
			},
			{
				title: 'Lavozim',
				dataIndex: ['position', 'name'],
				width: 150
			},
			{
				title: 'Tashkilot',
				dataIndex: 'organization',
				width: 150,
				className: 'dark:text-gray-300'
			},
			{
				title: 'Davomat',
				width: 100,
				className: 'dark:text-gray-300',
				render: (record: any) => {
					const { summary } = record
					const attendanceRate = summary.attendanceRate
					const color = attendanceRate >= 90 ? 'green' : attendanceRate >= 70 ? 'orange' : 'red'

					return (
						<Tooltip title={`${attendanceRate.toFixed(0)}% davomat`}>
							<Progress
								type='circle'
								percent={attendanceRate}
								width={40}
								strokeColor={color}
								format={percent => `${percent?.toFixed(0)}%`}
							/>
						</Tooltip>
					)
				}
			},
			{
				title: 'Soatlar',
				width: 150,
				className: 'dark:text-gray-300',
				render: (record: any) => {
					const { summary } = record
					return (
						<div className='text-center'>
							<Tooltip title='Rejalashtirilgan/Haqiqiy'>
								<div className='flex items-center'>
									<Tag color='blue'>{summary.planHours}</Tag>
									<span className='mx-1'>/</span>
									<Tag color={summary.factHours >= summary.planHours ? 'green' : 'orange'}>
										{summary.factHours}
									</Tag>
								</div>
							</Tooltip>
						</div>
					)
				}
			},
			...days.map(day => {
				const dayObj = dayjs(day).tz(DEFAULT_TIMEZONE)
				const dateHeader = (
					<div className='text-center font-medium px-2 py-1 bg-blue-50 dark:bg-blue-900/30 dark:text-blue-200 rounded'>
						<div>{dayObj.format('DD.MM.YYYY')}</div>
						<div className='text-xs text-gray-500 dark:text-gray-400'>
							{dayObj.locale('uz').format('dddd')}
						</div>
					</div>
				)

				const getReportForDate = (record: any) => {
					return record.dailyReports.find((r: DailyReport) => {
						const reportDate = dayjs(r.formattedDate).format('YYYY-MM-DD')
						const currentDay = dayjs(day).tz(DEFAULT_TIMEZONE).format('YYYY-MM-DD')

						return reportDate === currentDay
					})
				}

				return {
					key: day,
					title: dateHeader,
					className: 'date-column-group',
					children: [
						{
							title: (
								<div className='text-center bg-green-50 dark:bg-green-900/20 py-1'>
									<span className='text-green-600 dark:text-green-400 font-medium'>Kelgan</span>
								</div>
							),
							width: 140,
							className: 'enter-time-column',
							render: (record: any) => {
								const report = getReportForDate(record)
								console.log('report', report)
								if (!report) {
									return <div className='text-center text-gray-400'>-</div>
								}

								if (!report.isWorkDay) {
									return (
										<div className='text-center'>
											<Text
												type='secondary'
												italic>
												Dam
											</Text>
										</div>
									)
								}

								if (!report.cameToWork) {
									return (
										<div className='text-center'>
											<Badge status='error' />
										</div>
									)
								}

								const enterTime = TimezoneHelper.formatTime(report.actualEnterTime)

								return (
									<div className='text-center'>
										{report.lateMinutes > 0 ? (
											<Tooltip title={`${report.lateMinutes} daqiqa kech kelgan`}>
												<Tag
													color='orange'
													className='font-medium'>
													{enterTime}
												</Tag>
											</Tooltip>
										) : (
											<Tag
												color='green'
												className='font-medium'>
												{enterTime}
											</Tag>
										)}
									</div>
								)
							}
						},
						{
							width: 140,
							title: (
								<div className='text-center bg-red-50 dark:bg-red-900/20 py-1'>
									<span className='text-red-600 dark:text-red-400 font-medium'>Ketgan</span>
								</div>
							),
							className: 'exit-time-column',
							render: (record: any) => {
								const report = getReportForDate(record)

								if (!report) {
									return <div className='text-center text-gray-400'>-</div>
								}

								if (!report.isWorkDay) {
									return (
										<div className='text-center'>
											<Text
												type='secondary'
												italic>
												Dam
											</Text>
										</div>
									)
								}

								if (!report.cameToWork) {
									return (
										<div className='text-center'>
											<Badge status='error' />
										</div>
									)
								}

								const exitTime = TimezoneHelper.formatTime(report.actualExitTime)

								if (!exitTime) {
									return <div className='text-center text-gray-400'>-</div>
								}

								return (
									<div className='text-center'>
										{report.earlyExitMinutes > 0 ? (
											<Tooltip title={`${report.earlyExitMinutes} daqiqa erta ketgan`}>
												<Tag
													color='volcano'
													className='font-medium'>
													{exitTime}
												</Tag>
											</Tooltip>
										) : (
											<Tag
												color='red'
												className='font-medium'>
												{exitTime}
											</Tag>
										)}
									</div>
								)
							}
						},
						{
							width: 140,
							title: (
								<div className='text-center bg-purple-50 dark:bg-purple-900/20 py-1'>
									<span className='text-purple-600 dark:text-purple-400 font-medium'>
										<ClockCircleOutlined className='mr-1' />
										Ishlagan
									</span>
								</div>
							),
							className: 'work-time-column',
							render: (record: any) => {
								const report = getReportForDate(record)

								if (!report) {
									return <div className='text-center text-gray-400'>-</div>
								}

								if (!report.isWorkDay) {
									return (
										<div className='text-center'>
											<Text
												type='secondary'
												italic>
												Dam
											</Text>
										</div>
									)
								}

								if (!report.cameToWork || !report.minutesWorked) {
									return (
										<div className='text-center'>
											<Tag color='red'>0 s</Tag>
										</div>
									)
								}

								const workTime = convertMinutesToHours(report.minutesWorked)

								return (
									<div className='text-center'>
										<Tag
											color='purple'
											className='font-medium'>
											{workTime}
										</Tag>
									</div>
								)
							}
						}
					]
				}
			})
		]
	}, [days, setSelectedWorkerId])
}
