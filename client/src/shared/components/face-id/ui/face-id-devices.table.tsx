import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { useDeleteFaceIdDevice } from '@/config/queries/face-id/delete'
import { FaceId } from '@/config/queries/face-id/getAllByOrg'
import { usePermissions } from '@/shared/guards/usePermissions'
import { FaceIDType } from '@/shared/types/face-id.type'
import { STATUS } from '@/shared/types/status.type'
import { LoadingScreen } from '@/shared/ui/suspense'
import { DeleteOutlined } from '@ant-design/icons'
import { Button, Popconfirm, Table } from 'antd'
import { ArrowDown, ArrowUp } from 'lucide-react'

type TFaceIdDeviceTable = {
	devices: FaceId[]
}

export const FaceIdDevicesTable = ({ devices }: TFaceIdDeviceTable) => {
	const { hasPermission } = usePermissions()
	const { data: user, isLoading } = useAuthMe()
	const { mutateAsync: deleteDevice, isPending: isDeleting } = useDeleteFaceIdDevice()

	if (isLoading || !user?.role) return <LoadingScreen />

	const handleDelete = (id: string) => {
		deleteDevice(id)
	}

	return (
		<Table
			dataSource={devices.filter(rec => rec.status === STATUS.ACTIVE)}
			columns={[
				{ key: '#', title: '#', render: (_, __, index) => ++index },
				{ key: 'MAC', title: 'MAC', dataIndex: 'MAC' },
				{ key: 'IP', title: 'IP', dataIndex: 'IP' },
				{
					key: 'type',
					title: 'Turi',
					render: rec =>
						rec.type === FaceIDType.ENTER ? (
							<div className='flex items-center gap-x-1'>
								<ArrowUp /> Kirish
							</div>
						) : (
							<div className='flex items-center gap-x-1'>
								<ArrowDown />
								Chiqish
							</div>
						)
				},
				hasPermission(user.role, ['delete:*', 'update:*'])
					? {
							key: 'actions',
							title: 'Amallar',
							className: 'w-[300px]',
							render: rec => (
								<div className='w-full flex items-center gap-x-3 justify-center'>
									<Popconfirm
										title='Haqiqatdan ham face id qurilmasini ochirib tashlamoqchimisiz?'
										okText='ha'
										cancelText="yo'q"
										onConfirm={() => handleDelete(rec.id)}
										okButtonProps={{ disabled: isDeleting }}>
										<Button
											danger
											loading={isDeleting}
											icon={<DeleteOutlined />}
										/>
									</Popconfirm>
								</div>
							)
						}
					: { key: 'actions' }
			]}
		/>
	)
}
