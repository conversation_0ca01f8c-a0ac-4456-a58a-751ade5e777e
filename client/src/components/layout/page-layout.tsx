import { Layout as AntLayout } from 'antd'
import { Outlet, useLocation } from 'react-router-dom'
import { useSidebar } from '../utils/use-sidebar'
import { Sidebar } from '../sidebar'
import { HeaderTitleProvider } from '@/providers/header-title-providers'
import { Header } from '../header'
import { useEffect } from 'react'
import { FaceIDModal } from '@/shared/components/face-id/face-id-modal'
import { UserManagmentModal } from '@/shared/components/user-managment/user-managment-modal'
import { WorkingScheduleManagmentModal } from '@/shared/components/working-schedule-managment/working-schedule-managment-modal'

type TLayout = {
	workspace: 'personal' | 'organization'
}

export const Layout = ({ workspace }: TLayout) => {
	const { isLg } = useSidebar()
	const { pathname } = useLocation()

	useEffect(() => {
		if (pathname !== '/workspace') {
			localStorage.removeItem('breadCrumbOrgs')
		}
	}, [pathname])

	return (
		<HeaderTitleProvider>
			<AntLayout className='h-screen'>
				<Sidebar workspace={workspace} />
				<AntLayout
					style={{
						marginInlineStart: isLg ? 0 : 240
						// overflow: 'initial',
					}}>
					<Header />
					<AntLayout.Content className='z-0 p-4 h-full overflow-y-auto'>
						<Outlet />
						<FaceIDModal />
						<UserManagmentModal />
						<WorkingScheduleManagmentModal />
					</AntLayout.Content>
				</AntLayout>
			</AntLayout>
		</HeaderTitleProvider>
	)
}
