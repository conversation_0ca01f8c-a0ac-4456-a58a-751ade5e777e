import { TaskTypes } from '@/config/queries/task-types/create.queries'
import { Button, Form, Modal } from 'antd'
import { useEffect } from 'react'
import { PlusOutlined } from '@ant-design/icons'
import { useOrganizationTypesModalStore } from '../utils/organization-types-store'
import { useCreateOrganizationType } from '@/config/queries/organization-types/create.queries'
import { useUpdateOrganizationType } from '@/config/queries/organization-types/update.queries'
import OrganizationTypesForm from './organization-types-form'

const OrganizationTypesModal = () => {
	const { data, open, onClose } = useOrganizationTypesModalStore()
	const [form] = Form.useForm()
	const create = useCreateOrganizationType()
	const update = useUpdateOrganizationType()

	const close = () => {
		form.resetFields()
		onClose()
	}

	useEffect(() => {
		if (data) {
			form.setFieldsValue(data)
		}
	}, [data, form])

	const onFinish = (formData: TaskTypes) => {
		if (!data) {
			create.mutateAsync({ ...formData }).then(close)
		} else {
			if (data.id) {
				update.mutateAsync({ id: data.id, ...formData }).then(close)
			}
		}
	}

	return (
		<div className='flex items-center justify-end'>
			<Button
				type='primary'
				icon={<PlusOutlined />}
				onClick={() => useOrganizationTypesModalStore.setState({ open: true })}>
				Tashkilot turlarini qo'shish
			</Button>
			<Modal
				open={open}
				onCancel={close}
				destroyOnClose
				onOk={form.submit}
				title={`Tashkilot turi${data ? 'ni tahrirlash' : "ni qo'shish"}`}
				okText={data ? 'Tahrirlash' : "Qo'shish"}
				cancelText='Bekor qilish'
				modalRender={node => (
					<Form
						layout={'vertical'}
						size='large'
						onFinish={onFinish}
						form={form}>
						{node}
					</Form>
				)}>
				<OrganizationTypesForm />
			</Modal>
		</div>
	)
}
export default OrganizationTypesModal
