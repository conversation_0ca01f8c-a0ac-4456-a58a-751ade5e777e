services:
  # 1. Сначала запускаем базы данных
  postgres-db:
    image: postgres:15-alpine
    container_name: mnazorat-postgres
    restart: always
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - backup_data:/var/lib/postgresql/backup
      - ./postgres-init.sql:/docker-entrypoint-initdb.d/postgres-init.sql
    env_file:
      - .env
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
    command: >
      postgres
      -c max_connections=300
      -c shared_buffers=2GB
      -c effective_cache_size=6GB
      -c work_mem=16MB
      -c maintenance_work_mem=512MB
      -c wal_buffers=64MB
      -c checkpoint_completion_target=0.9
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
      -c max_worker_processes=6
      -c max_parallel_workers=6
      -c max_parallel_workers_per_gather=4
      -c max_parallel_maintenance_workers=4
      -c temp_buffers=32MB
      -c default_statistics_target=100
      -c autovacuum=on
      -c autovacuum_max_workers=6
      -c autovacuum_naptime=30s
      -c checkpoint_timeout=15min
      -c max_wal_size=4GB
      -c min_wal_size=1GB
      -c log_connections=on
      -c log_disconnections=on
      -c log_lock_waits=on
      -c log_temp_files=0
      -c log_checkpoints=on
      -c log_autovacuum_min_duration=0
      -c idle_in_transaction_session_timeout=30min
      -c statement_timeout=5min
      -c lock_timeout=30s
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}" ]
      interval: 10s
      timeout: 5s
      retries: 5

  backup:
    build:
      context: ./backup
      dockerfile: Dockerfile.production
    container_name: mnazorat-backup
    env_file:
      - .env
    restart: unless-stopped
    depends_on:
      - postgres-db
    environment:
      - S3_ENDPOINT=${S3_ENDPOINT}
      - S3_BUCKET=yhxb-mahalla.uz
      - S3_FOLDER=mnazorat_backups
      - DB_HOST=${POSTGRES_HOST}
      - DB_PORT=${POSTGRES_PORT}
      - DB_USER=${POSTGRES_USER}
      - DB_PASSWORD=${POSTGRES_PASSWORD}
      - DB_NAME=${POSTGRES_DB}
    volumes:
      - backup_data:/backups
    networks:
      - default

  # 2. Redis также запускаем на раннем этапе
  redis:
    image: redis:7-alpine
    container_name: mnazorat-redis
    restart: always
    volumes:
      - redis_data:/data
    command: >
      redis-server
      --requirepass ${GLOBAL_REDIS_PASSWORD}
      --appendonly yes
      --maxmemory 512mb
      --maxmemory-policy allkeys-lru
      --save 900 1
      --save 300 10
    healthcheck:
      test: [ "CMD", "redis-cli", "-a", "${GLOBAL_REDIS_PASSWORD}", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 5

  # 3. Запускаем backend после БД
  backend:
    container_name: mnazorat-backend
    build:
      context: ./backend
      dockerfile: Dockerfile.production
    volumes:
      - static_files:/app/static
    restart: always
    env_file:
      - ./backend/.env
    depends_on:
      postgres-db:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      NODE_ENV: production
      # Оптимизация Node.js для 6GB RAM
      NODE_OPTIONS: "--max-old-space-size=1024 --max-semi-space-size=128"
      UV_THREADPOOL_SIZE: 8
    networks:
      - default
    healthcheck:
      test: [ "CMD-SHELL", "wget -q --spider http://localhost:8000/api/v1/health || exit 1" ]
      interval: 30s
      timeout: 10s
      retries: 5

    # 4. Затем frontend
  frontend:
    container_name: mnazorat-frontend
    build:
      context: ./client
      dockerfile: Dockerfile.production
      args:
        - API_URL=${API_URL}
        - VITE_IMAGE_URL=${API_URL}
    restart: always
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: [ 'CMD', 'nginx', '-t' ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  # 5. Nginx после фронтенда и бэкенда
  nginx:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    container_name: mnazorat-nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    depends_on:
      frontend:
        condition: service_healthy

  # 6. В последнюю очередь certbot
  certbot:
    container_name: mnazorat-certbot
    image: certbot/certbot
    restart: unless-stopped
    volumes:
      - ./certbot/conf:/etc/letsencrypt
      - ./certbot/www:/var/www/certbot
    entrypoint: "/bin/sh -c 'trap exit TERM; while :; do certbot renew; sleep 12h & wait $${!}; done;'"
    depends_on:
      - nginx

  prometheus:
    image: prom/prometheus:latest
    container_name: mnazorat-prometheus
    restart: unless-stopped
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
    ports:
      - "9090:9090"
    networks:
      - default

  grafana:
    image: grafana/grafana:latest
    container_name: mnazorat-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
    depends_on:
      - prometheus
    networks:
      - default

  redis_exporter:
    image: oliver006/redis_exporter:latest
    container_name: mnazorat-redis-exporter
    restart: unless-stopped
    env_file:
      - .env
    environment:
      REDIS_ADDR: redis://${GLOBAL_REDIS_HOST}:${GLOBAL_REDIS_PORT}
      REDIS_PASSWORD: ${GLOBAL_REDIS_PASSWORD}
    ports:
      - "9121:9121"
    networks:
      - default


  postgres_exporter:
    image: prometheuscommunity/postgres-exporter
    container_name: mnazorat-postgres-exporter
    restart: unless-stopped
    env_file:
      - .env
    environment:
      DATA_SOURCE_NAME: "postgresql://${POSTGRES_EXPORTER_USER}:${POSTGRES_EXPORTER_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}?sslmode=disable"
    ports:
      - "9187:9187"
    networks:
      - default

  node_exporter:
    image: prom/node-exporter
    container_name: mnazorat-node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    networks:
      - default

  process_exporter:
    image: ncabatoff/process-exporter:latest
    container_name: process-exporter
    restart: unless-stopped
    ports:
      - "9256:9256"
    volumes:
      - /proc:/host/proc
      - ./process-exporter.yml:/config.yml
    command:
      - --procfs=/host/proc
      - --config.path=/config.yml
    networks:
      - default



volumes:
  postgres_data:
  redis_data:
  static_files:
  backup_data:
  grafana_data:


networks:
  default:
    driver: bridge