import { axiosPrivate } from '@/config/api'
import { organizationEndpoints } from '@/config/api/endpoints'
import { useInfiniteQuery } from '@tanstack/react-query'
import { TUser } from '../users/get-all.queries'
import { useSearchParams } from 'react-router-dom'
import { useState } from 'react'
import { TPaginationWrapper } from '@/shared/types/pagination.type'

export const useGetOrganizationWorkers = (organizationId?: string) => {
	const limit = 10
	const [page, setPage] = useState(1)
	const [searchParams] = useSearchParams()
	const search = searchParams.get('search')

	const query = useInfiniteQuery({
		queryKey: [organizationEndpoints.usersByOrg, 'infinite', organizationId, search, page, limit],
		queryFn: async ({ pageParam }) => {
			if (!organizationId) throw new Error('Organization ID is required')

			return (
				await axiosPrivate.get<TPaginationWrapper<TUser[]>>(
					organizationEndpoints.usersByOrg(organizationId),
					{
						params: {
							page: pageParam,
							limit,
							search
						}
					}
				)
			).data
		},
		initialPageParam: page,
		getNextPageParam: lastPage => {
			if (lastPage?.meta?.page === lastPage?.meta?.totalPages) return undefined
			return lastPage?.meta?.page + 1
		},
		getPreviousPageParam: lastPage => {
			if (lastPage?.meta?.page === 1) return undefined
			return lastPage?.meta?.page - 1
		},
		enabled: !!organizationId
	})

	return {
		...query,
		setPage
	}
}
