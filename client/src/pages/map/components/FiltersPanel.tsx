import { useGetReferenceOrganizationPosition } from '@/config/queries/common/common-queries'
import { Card, Select, Button, Space, Typography } from 'antd'
import { Filter, FilterX } from 'lucide-react'
import { useCallback, useEffect, useMemo } from 'react'
import { useSearchParams } from 'react-router-dom'

const { Text } = Typography

export default function FiltersPanel() {
	const positions = useGetReferenceOrganizationPosition()
	const [search, setSearch] = useSearchParams()

	useEffect(() => {
		const grade = search.get('grade')

		if (!grade) {
			search.set('grade', '30')
		}

		setSearch(search.toString())
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [])

	// Мемоизированные значения для select-ов
	const statusValue = useMemo(() => {
		const isInArea = search.get('isInArea')
		if (!isInArea) return -1
		try {
			return JSON.parse(isInArea) ? 1 : 0
		} catch {
			return -1
		}
	}, [search])

	const gpsStatusValue = useMemo(() => {
		const isDisabled = search.get('isDisabled')
		if (!isDisabled) return -1
		try {
			return JSON.parse(isDisabled) ? 1 : 0
		} catch {
			return -1
		}
	}, [search])

	const positionValue = useMemo(() => {
		return search.get('positionId') || 'all'
	}, [search])
	// Мемоизированные опции
	const statusOptions = useMemo(
		() => [
			{ value: -1, label: 'Barchasi' },
			{ value: 1, label: 'Hududda' },
			{ value: 0, label: 'Hududdan tashqarida' }
		],
		[]
	)

	const gpsStatusOptions = useMemo(
		() => [
			{ value: -1, label: 'Barchasi' },
			{ value: 1, label: "Aloqa yo'q" },
			{ value: 0, label: 'Aloqa Bor' }
		],
		[]
	)

	const positionOptions = useMemo(
		() => [
			{ value: 'all', label: 'Barchasi' },
			...(positions.data?.map(pos => ({
				value: pos.id,
				label: pos.name
			})) || [])
		],
		[positions.data]
	)

	// Оптимизированные обработчики с useCallback
	const onSelectPosition = useCallback(
		(value: string) => {
			const newSearch = new URLSearchParams(search)
			if (value === 'all') {
				newSearch.delete('positionId')
			} else {
				newSearch.set('positionId', value)
			}
			setSearch(newSearch)
		},
		[search, setSearch]
	)

	const onSelectStatus = useCallback(
		(value: number) => {
			const newSearch = new URLSearchParams(search)
			if (value < 0) {
				newSearch.delete('isInArea')
			} else {
				newSearch.set('isInArea', value === 1 ? 'true' : 'false')
			}
			setSearch(newSearch)
		},
		[search, setSearch]
	)

	const onSelectGpsStatus = useCallback(
		(value: number) => {
			const newSearch = new URLSearchParams(search)
			if (value < 0) {
				newSearch.delete('isDisabled')
			} else {
				newSearch.set('isDisabled', value === 1 ? 'true' : 'false')
			}
			setSearch(newSearch)
		},
		[search, setSearch]
	)

	const onClearFilters = useCallback(() => {
		const newSearch = new URLSearchParams(search)
		newSearch.delete('positionId')
		newSearch.delete('isInArea')
		newSearch.delete('isDisabled')
		setSearch(newSearch)
	}, [search, setSearch])

	return (
		<Card className='bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-gray-200 dark:border-gray-700'>
			<Space
				direction='vertical'
				className='w-full'
				size='middle'>
				<div className='flex items-center justify-between'>
					<div className='flex items-center gap-2 text-gray-700 dark:text-gray-300'>
						<Filter size={16} />
						<Text
							strong
							className='text-gray-800 dark:text-gray-200'>
							Filtrlar
						</Text>
					</div>
					<div className='flex items-center gap-2'>
						<Button
							type='text'
							onClick={onClearFilters}
							icon={<FilterX size={16} />}
							className='text-gray-600 dark:text-gray-400'
							size='small'>
							Tozalash
						</Button>
					</div>
				</div>

				<div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
					<div>
						<Text className='block mb-2 text-gray-600 dark:text-gray-400 text-sm'>Holat</Text>
						<Select
							className='w-full'
							onChange={onSelectStatus}
							value={statusValue}
							placeholder='Holatni tanlang'
							options={statusOptions}
						/>
					</div>
					<div>
						<Text className='block mb-2 text-gray-600 dark:text-gray-400 text-sm'>GPS</Text>
						<Select
							className='w-full'
							onChange={onSelectGpsStatus}
							value={gpsStatusValue}
							placeholder='Holatni tanlang'
							options={gpsStatusOptions}
						/>
					</div>

					<div>
						<Text className='block mb-2 text-gray-600 dark:text-gray-400 text-sm'>Lavozim</Text>
						<Select
							className='w-full'
							placeholder='Lavozimni tanlang'
							options={positionOptions}
							onChange={onSelectPosition}
							value={positionValue}
						/>
					</div>
				</div>
			</Space>
		</Card>
	)
}
