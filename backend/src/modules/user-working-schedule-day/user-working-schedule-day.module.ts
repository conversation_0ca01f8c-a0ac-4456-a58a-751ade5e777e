import { Module } from '@nestjs/common';
import { UserWorkingScheduleDayService } from './user-working-schedule-day.service';
import { UserWorkingScheduleDayController } from './user-working-schedule-day.controller';
import { PrismaService } from '../../common';

@Module({
  controllers: [UserWorkingScheduleDayController],
  providers: [UserWorkingScheduleDayService, PrismaService],
})
export class UserWorkingScheduleDayModule {}
