model FaceIDDevice {
  id     String @id @default(cuid())
  MAC    String @unique
  IP     String
  status Status @default(ACTIVE)

  Organization   Organization   @relation(fields: [organizationId], references: [id])
  organizationId String
  type           AttendanceType @default(ENTER)
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  @@index([organizationId])
}
