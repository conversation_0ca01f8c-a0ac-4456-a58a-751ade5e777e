import { axiosPrivate } from '@/config/api'
import { infractionReasonEndpoints } from '@/config/api/endpoints'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'

export interface TInfractionReasonRequest {
	name: string
	description: string
	infractionId: string
	files?: string[]
}

export interface TInfractionReason {
	id: string
	name: string
	description: string
	infractionId: string
	fileId?: string
	createdAt: string
	updatedAt: string
	file?: {
		id: string
		path: string
		name: string
	}
	Infraction?: {
		id: string
		name?: string
		description?: string
		infractionDate: string
	}
}

/**
 * Hook to get a single infraction reason by ID
 */
export const useGetInfractionReasonById = (id: string) => {
	return useQuery({
		queryKey: ['infraction-reason', id],
		queryFn: async (): Promise<TInfractionReason> => {
			const response = await axiosPrivate.get<TInfractionReason>(infractionReasonEndpoints.one(id))
			return response.data
		},
		enabled: !!id
	})
}

/**
 * Hook to create a new infraction reason
 */
export const useCreateInfractionReason = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async (data: TInfractionReasonRequest): Promise<TInfractionReason> => {
			const response = await axiosPrivate.post<TInfractionReason>(
				infractionReasonEndpoints.create,
				data
			)
			return response.data
		},
		onSuccess: (data) => {
			// Invalidate infraction queries to refresh the data
			queryClient.invalidateQueries({ queryKey: ['infractions'] })
			queryClient.invalidateQueries({ queryKey: ['infraction', data.infractionId] })
		}
	})
}
