import { axiosPrivate } from '@/config/api'
import { vocationEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { notification } from 'antd'

export type TVocationCreate = {
	userId: string
	type: 'PATIONS' | 'VACATION'
	description?: string
	begin: string
	end: string
}

export const useCreateVocation = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async (data: TVocationCreate) => {
			return await axiosPrivate.post(vocationEndpoints.all, data)
		},
		onSuccess: async () => {
			queryClient.invalidateQueries({ queryKey: [vocationEndpoints.all] })
			notification.success({
				message: 'Muvaffaqiyatli yaratildi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: '<PERSON><PERSON><PERSON><PERSON> xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
