import { useCallback } from 'react'
import { useMapNavigation } from './useMapNavigation'

export const useMapInteraction = () => {
	const { navigateToRegion, navigateToDistrict, navigateToSection, getCurrentLevel } =
		useMapNavigation()

	const handleAreaClick = useCallback(
		(feature: any) => {
			const properties = feature.properties
			if (!properties) return

			const currentLevel = getCurrentLevel()
			const featureType = properties.type

			// Определяем логику навигации в зависимости от текущего уровня и типа области
			switch (currentLevel) {
				case 'overview':
					// На общем уровне кликаем только по регионам
					if (featureType === 'REGION') {
						navigateToRegion(properties.region_id)
					}
					break

				case 'region':
					// На уровне региона кликаем по любым районам
					if (featureType === 'DISTRICT') {
						navigateToDistrict(properties.region_id, properties.district_id)
					}
					break

				case 'district':
					// На уровне района кликаем по любым секциям
					if (featureType === 'SECTION') {
						navigateToSection(properties.region_id, properties.district_id, properties.section_id)
					}
					break

				case 'section':
					// На уровне секции больше некуда переходить
					// Можно показать детальную информацию
					break
			}
		},
		[getCurrentLevel, navigateToRegion, navigateToDistrict, navigateToSection]
	)

	const isAreaClickable = useCallback(
		(feature: any) => {
			const properties = feature.properties
			if (!properties) return false

			const currentLevel = getCurrentLevel()
			const featureType = properties.type

			switch (currentLevel) {
				case 'overview':
					return featureType === 'REGION'

				case 'region':
					// На уровне региона все районы кликабельны (убираем строгую проверку на regionId)
					return featureType === 'DISTRICT'

				case 'district':
					// На уровне района все секции кликабельны (убираем строгую проверку на regionId и districtId)
					return featureType === 'SECTION'

				case 'section':
					return false // На самом нижнем уровне ничего не кликабельно для навигации

				default:
					return false
			}
		},
		[getCurrentLevel]
	)

	return {
		handleAreaClick,
		isAreaClickable,
		...useMapNavigation()
	}
}
