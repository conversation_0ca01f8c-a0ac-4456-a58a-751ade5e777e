import { axiosPrivate } from '@/config/api'
import { organizationEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { TOrganization } from './get-all.queries'
import { AxiosError, AxiosResponse } from 'axios'
import { TApiErrorResponse } from '@/shared/types/api-error.type'
import { notification } from 'antd'
import { TApiUpdate } from '@/shared/types/api-update.type'

export type TOrganizationRequest = {
	organizations: string[]
}
export type TOrganizationResponse = TOrganization

export const useCreateUnderControlOrganization = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async ({ data, id }: TApiUpdate<TOrganizationRequest>) => {
			return await axiosPrivate.post(organizationEndpoints.underControlOrganizationCreate(id), data)
		},
		onSuccess: async (response: AxiosResponse<TOrganizationResponse>) => {
			console.log(response)
			queryClient.invalidateQueries({ queryKey: [organizationEndpoints.underContolOrganization] })
			notification.success({ message: 'Tashkilot muaffaqiyatli biriktirildi!' })
		},
		onError: async ({ response }: AxiosError<TApiErrorResponse>) => {
			console.log(response?.data.message)
			notification.error({ message: 'Tashkilot biriktirishda xatolik yuz berdi!' })
		}
	})
}
