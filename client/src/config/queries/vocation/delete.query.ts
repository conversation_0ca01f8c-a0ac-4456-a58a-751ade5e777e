import { axiosPrivate } from '@/config/api'
import { vocationEndpoints } from '@/config/api/endpoints'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { notification } from 'antd'

export const useDeleteVocation = () => {
	const queryClient = useQueryClient()

	return useMutation({
		mutationFn: async (vocationId: string) => {
			return await axiosPrivate.delete(vocationEndpoints.one(vocationId))
		},
		onSuccess: async () => {
			queryClient.invalidateQueries({ queryKey: [vocationEndpoints.all] })
			notification.success({
				message: 'Muvaffaqiyatli yaratildi',
				placement: 'bottomRight'
			})
		},
		onError: () => {
			notification.error({
				message: 'O`chirishda xatolik yuz berdi',
				placement: 'bottomRight'
			})
		}
	})
}
