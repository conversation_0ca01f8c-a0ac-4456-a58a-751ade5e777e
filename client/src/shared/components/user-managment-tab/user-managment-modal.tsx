import { useEffect } from 'react'
import { UserManagementTable } from './ui/user-managment.table'
import { FaceIdAddModal } from './ui/user-managment-add.modal' // ← BU MUHIM
import { useUserManagementStore } from './utils/user-managament-store'

interface UserManagmentTabProps {
	organizationId?: string
}

export const UserManagmentTab = ({ organizationId }: UserManagmentTabProps) => {
	const { setId } = useUserManagementStore()

	useEffect(() => {
		if (organizationId) {
			setId(organizationId)
		}
	}, [organizationId, setId])

	return (
		<>
			<UserManagementTable />
			<FaceIdAddModal />
		</>
	)
}
