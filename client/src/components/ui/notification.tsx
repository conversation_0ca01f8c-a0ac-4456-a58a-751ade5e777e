import { <PERSON>Filled, EyeFilled } from '@ant-design/icons'
import { <PERSON><PERSON>, Button, Dropdown, Empty, Flex, Toolt<PERSON>, Typography, Divider } from 'antd'
import dayjs from 'dayjs'
import {
	NotificationType,
	useGetNotifications,
	useReadNotification
} from '@/config/queries/notification/notification.queries'
import { useAuthMe } from '@/config/queries/auth/verify.queries'
import { LoadingScreen } from '@/shared/ui/suspense'
import { useNavigate } from 'react-router-dom'

export default function Notification() {
	const { data: currentUser, isLoading } = useAuthMe()
	const { data: notifications, isLoading: loading } = useGetNotifications()
	const read = useReadNotification()
	const navigate = useNavigate()

	const unreadCount = notifications?.filter(item => !item.read).length

	const handleMarkAsRead = async (id: string) => {
		await read.mutateAsync([id])
	}

	const handleMarkAllAsRead = async () => {
		const ids = notifications?.filter(item => !item.read).map(item => item.id)
		if (ids) {
			await read.mutateAsync(ids)
		}
	}

	const getIconColor = (type: NotificationType) => {
		switch (type) {
			case NotificationType.TASK_ASSIGNED:
				return '!text-blue-600'
			case NotificationType.ANSWER_CREATED:
				return '!text-orange-600'
			case NotificationType.ANSWER_CONFIRMED:
				return '!text-green-600'
			case NotificationType.ANSWER_REJECTED:
			default:
				return '!text-gray-600'
		}
	}

	const notificationItems = notifications?.map(notification => ({
		onClick: () => {
			const content = notification.Content

			if (content.type === 'task_assigned') {
				if (content.workspace === 'personal') {
					navigate(`/personal/tasks/${content.id}`)
				}
				if (content.workspace === 'organization') {
					if (currentUser?.ResponsibleFor.find(org => org.id === content.recipientOrgId)) {
						localStorage.setItem('organizationId', JSON.stringify(content.recipientOrgId))
						navigate(`/workspace/tasks/${content.id}`)
					}
				}
			}
		},
		key: notification.id,
		label: (
			<Flex vertical>
				<div className='text-xs font-semibold opacity-50'>
					{notification.Organization.name} •{' '}
					{dayjs(notification.createdAt).format('DD.MM.YYYY HH:mm')}
				</div>
				<Tooltip title={notification.message}>
					<div
						className={`line-clamp-1 text-base ${notification.read ? 'opacity-60' : 'font-medium'}`}>
						{notification.message}
					</div>
				</Tooltip>
			</Flex>
		),
		icon: <BellFilled className={`!text-xs ${getIconColor(notification.type)}`} />,
		extra: (
			<Button
				variant='text'
				color='primary'
				size='small'
				icon={<EyeFilled />}
				onClick={e => {
					e.stopPropagation()
					handleMarkAsRead(notification.id)
				}}
				disabled={notification.read}
			/>
		),
		className: notification.read ? 'opacity-60' : ''
	}))

	if (isLoading) return <LoadingScreen />

	return (
		<>
			<Dropdown
				menu={{
					className: '!items-start',
					rootClassName: 'w-[400px]',
					items: loading
						? [
								{
									key: 'loading',
									label: <div className='py-3 text-center'>Yuklanmoqda...</div>
								}
							]
						: notificationItems?.length
							? notificationItems
							: [
									{
										key: 'empty',
										label: <Empty description="Sizda hech qanday bildirishnomalar yo'q" />
									}
								]
				}}
				dropdownRender={menu => (
					<div className='bg-white dark:bg-[#1E1F2E] rounded-lg shadow-lg p-2'>
						<Flex
							justify='space-between'
							align='center'
							className='px-3 py-2'>
							<Typography.Title
								level={5}
								style={{ margin: 0 }}>
								Bildirishnomalar
							</Typography.Title>
							{!!unreadCount && unreadCount >= 1 && !loading && (
								<Button
									type='link'
									size='small'
									onClick={e => {
										e.stopPropagation()
										handleMarkAllAsRead()
									}}>
									Hammasi o'qilgan deb belgilash
								</Button>
							)}
						</Flex>
						<Divider style={{ margin: '0 0 8px 0' }} />
						{menu}
					</div>
				)}
				trigger={['click']}>
				{unreadCount && unreadCount > 0 ? (
					<Badge
						count={unreadCount}
						offset={[-35, 5]}
						showZero
						color='orange'>
						<Button
							shape='circle'
							color='blue'
							variant='text'
							icon={<BellFilled className='!text-xl' />}
						/>
					</Badge>
				) : (
					<Button
						shape='circle'
						color='blue'
						variant='text'
						icon={<BellFilled className='!text-xl' />}
					/>
				)}
			</Dropdown>
		</>
	)
}
