import { axiosPrivate } from '@/config/api'
import { attendanceUserEndpoints } from '@/config/api/endpoints'
import { useMutation } from '@tanstack/react-query'
import { notification } from 'antd'
import type { Dayjs } from 'dayjs'

type TAddAttendanceByPosition = {
	positionId: string
	time: Dayjs
}

export const useAddAttendanceByPosition = () => {
	return useMutation({
		mutationFn: async (data: TAddAttendanceByPosition) => {
			return await axiosPrivate.post(attendanceUserEndpoints.addAttendance, {
				positionId: data.positionId,
				time: data.time.format()
			})
		},
		onSuccess: () => {
			notification.success({ message: "Lavozim bo'yicha davomat muvaffaqiyatli qo'shildi!" })
		},
		onError: () => {
			notification.error({ message: "Davomat qo'shishda xatolik yuz berdi!" })
		}
	})
}
