
model Vacancy {
  id                          String @id @default(cuid())

  Organization                Organization @relation(fields: [organizationId], references: [id])
  organizationId              String

  OrganizationTypePosition    OrganizationTypePosition @relation(fields: [organizationTypePositionId], references: [id])
  organizationTypePositionId  String

  status Status @default(ACTIVE)

  createdAt                   DateTime @default(now())
  updatedAt                   DateTime @updatedAt
}