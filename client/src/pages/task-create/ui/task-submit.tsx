// import { <PERSON>, But<PERSON> } from 'antd'
import { <PERSON><PERSON>, <PERSON> } from 'antd'
import { TRecipent } from '../task-create'

type TSubmitButtonProps = {
	recipents: TRecipent[]
	recipientType: string
	isUploading?: boolean
}

export const SubmitButton = ({
	recipents,
	recipientType,
	isUploading = false
}: TSubmitButtonProps) => {
	const hasValidRecipients =
		recipents.filter(item => {
			if (recipientType === 'positions') {
				return !!item.positionId
			}
			return !!item.organizationId
		}).length > 0

	// Only disable if there are no valid recipients OR if files are actively uploading
	const isDisabled = !hasValidRecipients || isUploading

	return (
		<Row className='flex items-center justify-end'>
			<Button
				disabled={isDisabled}
				type='primary'
				htmlType='submit'
				loading={isUploading}>
				{isUploading ? 'Fayllar yuklanmoqda...' : 'Yuborish'}
			</Button>
		</Row>
	)
}
