import { Form, Input, Select, Row, Col, <PERSON><PERSON>, <PERSON> } from 'antd'
import { useGetTaskTypes } from '@/config/queries/task-types/get-all.queries'
import { TaskTypes } from '@/config/queries/task-types/create.queries'

import { useState, useEffect } from 'react'
import { useGetAllOrganizations } from '@/config/queries/organizations/get-all.queries'
import { LoadingScreen } from '@/shared/ui/suspense'
import { useGetUnderControlOrganizations } from '@/config/queries/under-control-organizations/get-queries'
import { TUser } from '@/config/queries/users/get-all.queries'
import { TRecipent } from '@/pages/task-create/task-create'
import { TTaskRequest } from '@/config/queries/task/create'
import { useNavigate, useParams } from 'react-router-dom'
import { useCreateSubTask } from '@/config/queries/task/createSubtask'
import { useFileUpload } from '@/shared/components/file-upload/file-upload'
import { removeDuplicates } from '@/shared/utils/removeDuplicatedData'
import { useGetOneOrganization } from '@/config/queries/organizations/getOne'
import { useGetOrganizationWorkers } from '@/config/queries/organizations/get-workers.queries'
import { RecipientTypeSelector } from '@/pages/task-create/ui/task-reciepent-select'
import { TaskControllerSelect } from '@/pages/task-create/ui/task-controller-select'
import { RecipientRow } from '@/pages/task-create/ui/task-reciepent-form'
import { AddRecipientButton } from '@/pages/task-create/ui/task-add-reciepent'
import { TOrganization } from '@/config/queries/organizations/get-all.queries'

type TUsersState = {
	[key: string]: TUser[]
}

type TTaskCreateForm = {
	recipents: TRecipent[]
	setRecipents: React.Dispatch<React.SetStateAction<TRecipent[]>>
}

export default function SubTaskCreateForm({ recipents, setRecipents }: TTaskCreateForm) {
	const { id } = useParams()
	const taskType = useGetTaskTypes()
	const orgId = localStorage.getItem('organizationId')
	const parsedOrgId = orgId ? JSON.parse(orgId) : null
	const { data: selfOrganization, isLoading: isLoadingSelfOrganization } =
		useGetOneOrganization(parsedOrgId)
	const { data: children, isLoading: isLoadingChildren } = useGetAllOrganizations()
	const { data: underControls, isLoading: isLoadingUnderControls } =
		useGetUnderControlOrganizations()
	const { fileIds, render: FileUpload, clearFiles } = useFileUpload()

	const [users, setUsers] = useState<TUsersState>({})
	const [selectedOrgForWorkers, setSelectedOrgForWorkers] = useState<string>('')
	const [recipientType, setRecipientType] = useState('organizations')
	const workersQuery = useGetOrganizationWorkers(selectedOrgForWorkers)

	useEffect(() => {
		if (workersQuery.data && selectedOrgForWorkers) {
			const allWorkers = workersQuery.data.pages.flatMap(page => page.data)
			setUsers(prev => ({
				...prev,
				[selectedOrgForWorkers]: removeDuplicates(allWorkers)
			}))
		}
	}, [workersQuery.data, selectedOrgForWorkers])

	const { mutateAsync: createSubTask } = useCreateSubTask(id!)
	const [form] = Form.useForm()
	const navigate = useNavigate()

	const isSelfOrganization = selfOrganization?.id === parsedOrgId

	const handleRecipientTypeChange = (e: any) => {
		const newType = e.target.value
		setRecipientType(newType)

		setRecipents([
			{
				organizationId: '',
				userId: [],
				...(newType === 'positions' && { positionId: '' })
			}
		])
		setUsers({})
		setSelectedOrgForWorkers('')
	}

	const handleOrganizationSelect = async (organizationId: string, recipentIndex: number) => {
		if (recipientType === 'organizations') {
			if (organizationId === 'all') {
				setRecipents([
					{
						organizationId: 'all',
						userId: []
					}
				])
			} else {
				const currentRecipent = recipents[recipentIndex]
				const currentSelected = Array.isArray(currentRecipent?.selectedOrganizations)
					? currentRecipent.selectedOrganizations
					: []

				const newSelected = currentSelected.includes(organizationId)
					? currentSelected.filter(id => id !== organizationId)
					: [...currentSelected, organizationId]

				setRecipents(
					recipents.map((item, index) =>
						index === recipentIndex
							? {
									...item,
									organizationId: newSelected.length > 0 ? 'selected' : '',
									selectedOrganizations: newSelected
								}
							: item
					)
				)
			}
		} else {
			setSelectedOrgForWorkers(organizationId)
			setRecipents(
				recipents.map((item, index) =>
					index === recipentIndex ? { ...item, organizationId, userId: [] } : item
				)
			)
		}
	}

	const handleOrganizationDeselect = (organizationId: string, recipentIndex: number) => {
		if (recipientType === 'organizations') {
			if (organizationId === 'all') {
				setRecipents(
					recipents.map((item, index) =>
						index === recipentIndex
							? {
									...item,
									organizationId: '',
									selectedOrganizations: []
								}
							: item
					)
				)
			} else {
				const currentRecipent = recipents[recipentIndex]
				const currentSelected = currentRecipent?.selectedOrganizations || []
				const newSelected = currentSelected.filter(id => id !== organizationId)

				setRecipents(
					recipents.map((item, index) =>
						index === recipentIndex
							? {
									...item,
									organizationId: newSelected.length > 0 ? 'selected' : '',
									selectedOrganizations: newSelected
								}
							: item
					)
				)
			}
		}
	}

	const handlePositionSelect = (positionId: string, recipentIndex: number) => {
		setRecipents(
			recipents.map((item, index) => (index === recipentIndex ? { ...item, positionId } : item))
		)
	}

	if (isLoadingChildren || isLoadingUnderControls || isLoadingSelfOrganization)
		return <LoadingScreen />

	if (!id) return null

	// Merge organizations similar to task create
	const childrenOrgs = children?.pages?.flatMap(page => page.data) ?? []
	const underControlOrgs = underControls?.pages?.flatMap(page => page.data) ?? []

	// Remove duplicates while preserving order (children first)
	const uniqueOrgs = [...childrenOrgs]
	underControlOrgs.forEach(underControlOrg => {
		if (!uniqueOrgs.some(org => org.id === underControlOrg.id)) {
			uniqueOrgs.push(underControlOrg)
		}
	})

	const allOrganizations = [
		...(recipientType === 'employees'
			? [
					{
						...selfOrganization,
						id: parsedOrgId,
						name: 'Mazkur tashkilotning o`zi'
					}
				]
			: []),
		...uniqueOrgs
	]

	return (
		<Card className='p-2'>
			<Form
				form={form}
				onFinish={async (values: TTaskRequest) => {
					const taskData: TTaskRequest = {
						name: values.name,
						description: values.description,
						taskStateId: values.taskStateId,
						taskTypeId: values.taskTypeId,
						dueDate: new Date(values.dueDate).toISOString(),
						files: fileIds,
						controllerId: values.controllerId,
						recipients: recipents
							.filter(item => {
								if (recipientType === 'positions') {
									return item.positionId
								}
								return item.organizationId
							})
							.map(item => {
								if (recipientType === 'positions') {
									return { positionId: item.positionId }
								}
								if (recipientType === 'organizations') {
									if (item.organizationId === 'all') {
										return { organizationId: 'all' }
									}

									if (item.selectedOrganizations?.length) {
										return item.selectedOrganizations.map(orgId => ({ organizationId: orgId }))
									}
									return { organizationId: item.organizationId }
								}
								return item.userId.length
									? { organizationId: item.organizationId, userId: item.userId }
									: { organizationId: item.organizationId }
							})
							.flat()
					}
					await createSubTask({ id, data: taskData }).then(() => {
						form.resetFields()
						setRecipents([
							{
								organizationId: '',
								userId: []
							}
						])
						clearFiles()
						navigate(`/workspace/tasks/${id}`)
					})
				}}>
				<RecipientTypeSelector
					recipientType={recipientType}
					handleRecipientTypeChange={handleRecipientTypeChange}
					isSelfOrganization={isSelfOrganization}
				/>

				<Row gutter={[14, 14]}>
					<Col
						xs={24}
						sm={24}
						md={12}>
						<Form.Item
							name='taskTypeId'
							label='Topshiriq turi'
							labelCol={{ span: 24 }}>
							<Select
								className='rounded-md'
								options={taskType.data?.pages?.[0]?.data?.map((type: TaskTypes) => ({
									label: type.name,
									value: type.id
								}))}
							/>
						</Form.Item>
					</Col>

					<Col
						xs={24}
						sm={24}
						md={12}>
						<Form.Item
							name='dueDate'
							label='Muddati'
							labelCol={{ span: 24 }}
							rules={[{ required: true }]}>
							<Input
								type='date'
								className='rounded-md'
							/>
						</Form.Item>
					</Col>
				</Row>

				{recipents.map((recipent, recipentIndex) => (
					<RecipientRow
						key={recipentIndex}
						recipent={recipent}
						recipentIndex={recipentIndex}
						recipents={recipents}
						setRecipents={setRecipents}
						recipientType={recipientType}
						allOrganizations={allOrganizations as TOrganization[]}
						users={users}
						isPending={workersQuery.isFetching}
						hasNextPage={workersQuery.hasNextPage}
						fetchNextPage={workersQuery.fetchNextPage}
						organizationHasNextPage={false}
						organizationFetchNextPage={() => {}}
						organizationIsPending={false}
						handleOrganizationSelect={handleOrganizationSelect}
						handleOrganizationDeselect={handleOrganizationDeselect}
						handlePositionSelect={handlePositionSelect}
					/>
				))}
				<AddRecipientButton
					recipents={recipents}
					setRecipents={setRecipents}
					recipientType={recipientType}
				/>

				<Form.Item
					name='name'
					label='Topshiriq nomi'
					labelCol={{ span: 24 }}
					rules={[{ required: true }]}>
					<Input className='rounded-md' />
				</Form.Item>

				<TaskControllerSelect organizationId={parsedOrgId} />

				<Form.Item
					name='description'
					label='Izoh'
					labelCol={{ span: 24 }}
					rules={[{ required: true }]}>
					<Input.TextArea className='rounded-md !h-48' />
				</Form.Item>

				<FileUpload />

				<Row className='flex items-center justify-end'>
					<Button
						// disabled={!recipents.filter(item => !!item.organizationId).length}
						type='primary'
						htmlType='submit'>
						Yuborish
					</Button>
				</Row>
			</Form>
		</Card>
	)
}
