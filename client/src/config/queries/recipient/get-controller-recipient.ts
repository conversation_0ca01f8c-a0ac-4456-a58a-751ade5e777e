import { axiosPrivate } from '@/config/api'
import { taskEndpoints } from '@/config/api/endpoints'
import { useQuery } from '@tanstack/react-query'

export const useGetControllerRecipient = (taskId: string, recipientId: string) => {
	return useQuery({
		queryKey: ['controller-recipient', taskId, recipientId],
		queryFn: async () => {
			const response = await axiosPrivate.get(
				taskEndpoints.taskRecipientsController(recipientId)
			)
			return response.data
		},
		enabled: !!taskId && !!recipientId,
		staleTime: 5 * 60 * 1000, // 5 minutes
		refetchOnWindowFocus: false
	})
}
