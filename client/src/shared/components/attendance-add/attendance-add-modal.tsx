import { Form, Modal } from 'antd'
import { useForm } from 'antd/es/form/Form'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import { AttendanceAddForm } from './attendance-add-form'
import 'dayjs/locale/uz-latn'
import { useAddAttendance } from '@/config/queries/attendance/add-attendance'
import { useParams, useSearchParams } from 'react-router-dom'

dayjs.extend(utc)
dayjs.extend(timezone)
dayjs.locale('uz-latn')
const timeZone = 'Asia/Tashkent'
dayjs.tz.setDefault(timeZone)

type Props = {
	isOpen: boolean
	closeFn: () => void
}

export const AttendanceAddModal = ({ isOpen, closeFn }: Props) => {
	const { id } = useParams()
	const [attendanceForm] = useForm()
	const [searchParams] = useSearchParams()
	const { mutateAsync: addAttendance } = useAddAttendance()
	const selectedUserId = searchParams.get('userId')

	const handleAttendanceForm = (values: { date: string; time: string }) => {
		const date = new Date()
		date.setDate(new Date(values.date).getDate())
		date.setTime(new Date(values.time).getTime())

		const userId = selectedUserId || id
		if (userId) {
			addAttendance({ userId, time: dayjs(date) })
			closeFn()
			attendanceForm.resetFields()
		}
	}

	return (
		<Modal
			okText='Saqlash'
			onCancel={closeFn}
			title='Kelgan vaqtni kiritish'
			open={isOpen}
			onOk={() => attendanceForm.submit()}>
			<Form
				className='w-full'
				layout='vertical'
				form={attendanceForm}
				onFinish={handleAttendanceForm}>
				<AttendanceAddForm />
			</Form>
		</Modal>
	)
}
