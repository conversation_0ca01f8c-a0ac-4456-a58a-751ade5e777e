import { PrismaClient, UserRole } from '@prisma/client';
import * as bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  const adminPassword = await bcrypt.hash('1234', 10);
  const position = await prisma.organizationTypePosition.findFirst();

  const users = [
    {
      fullName: 'Admin User',
      username: 'admin',
      password: adminPassword,
      phone: '+998900000000',
      positionId: position?.id,
    },
  ];

  for (const user of users) {
    const existingUser = await prisma.user.findUnique({
      where: { username: user.username },
    });

    const userData = {
      fullName: user.fullName,
      username: user.username,
      password: user.password,
      phone: user.phone,
      role: 'ADMIN' as UserRole,
      position: {
        connect: {
          id: position?.id,
        },
      },
    };

    if (existingUser) {
      // Обновить существующего пользователя
      await prisma.user.update({
        where: { id: existingUser.id },
        data: userData,
      });
      console.log(`User "${user.username}" updated.`);
    } else {
      // Создать нового пользователя
      await prisma.user.create({
        data: userData,
      });
      console.log(`User "${user.username}" created.`);
    }
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
